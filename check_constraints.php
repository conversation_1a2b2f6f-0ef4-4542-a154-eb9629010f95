<?php
// Include database configuration
require_once 'app/config/config.php';

// Check constraints on inventory table
try {
    echo "Checking constraints on inventory table...\n";
    
    $query = "SELECT 
                tc.CONSTRAINT_NAME, 
                tc.CONSTRAINT_TYPE,
                kcu.COLUMN_NAME,
                kcu.REFERENCED_TABLE_NAME,
                kcu.REFERENCED_COLUMN_NAME
              FROM information_schema.TABLE_CONSTRAINTS tc
              JOIN information_schema.KEY_COLUMN_USAGE kcu
                ON tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
              WHERE tc.TABLE_SCHEMA = DATABASE()
                AND tc.TABLE_NAME = 'inventory'
                AND tc.CONSTRAINT_TYPE != 'CHECK'";
    
    $result = $conn->query($query);
    
    if ($result->rowCount() > 0) {
        echo "Found constraints:\n";
        echo "--------------------\n";
        
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "Constraint Name: " . $row['CONSTRAINT_NAME'] . "\n";
            echo "Constraint Type: " . $row['CONSTRAINT_TYPE'] . "\n";
            echo "Column: " . $row['COLUMN_NAME'] . "\n";
            
            if ($row['REFERENCED_TABLE_NAME']) {
                echo "References: " . $row['REFERENCED_TABLE_NAME'] . "." . $row['REFERENCED_COLUMN_NAME'] . "\n";
            }
            
            echo "--------------------\n";
        }
    } else {
        echo "No constraints found on inventory table.\n";
    }
    
    // Check for unique indexes
    echo "\nChecking for unique indexes on inventory table...\n";
    
    $query = "SHOW INDEXES FROM inventory WHERE Non_unique = 0";
    $result = $conn->query($query);
    
    if ($result->rowCount() > 0) {
        echo "Found unique indexes:\n";
        echo "--------------------\n";
        
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "Index Name: " . $row['Key_name'] . "\n";
            echo "Column: " . $row['Column_name'] . "\n";
            echo "--------------------\n";
        }
    } else {
        echo "No unique indexes found on inventory table.\n";
    }
    
    // Check for unique indexes on brand column
    echo "\nChecking for indexes on brand column...\n";
    
    $query = "SHOW INDEXES FROM inventory WHERE Column_name = 'brand_name'";
    $result = $conn->query($query);
    
    if ($result->rowCount() > 0) {
        echo "Found indexes on brand_name column:\n";
        echo "--------------------\n";
        
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "Index Name: " . $row['Key_name'] . "\n";
            echo "Unique: " . ($row['Non_unique'] == 0 ? 'Yes' : 'No') . "\n";
            echo "--------------------\n";
        }
    } else {
        echo "No indexes found on brand_name column.\n";
    }
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
