{"version": 3, "sources": ["../../scss/bootstrap-grid.scss", "../../scss/_containers.scss", "../../scss/mixins/_container.scss", "bootstrap-grid.css", "../../scss/mixins/_breakpoints.scss", "../../scss/_variables.scss", "../../scss/_grid.scss", "../../scss/mixins/_grid.scss", "../../scss/mixins/_utilities.scss", "../../scss/utilities/_api.scss"], "names": [], "mappings": "AAAA;;;;;EAAA;ACME;;;;;;;ECHA,qBAAA;EAEA,WAAA;EACA,2CAAA;EACA,0CAAA;EACA,kBAAA;EACA,iBAAA;ACUF;;AC4CI;EH5CE;IACE,gBIuSe;EFnSrB;AACF;ACsCI;EH5CE;IACE,gBIuSe;EF9RrB;AACF;ACiCI;EH5CE;IACE,gBIuSe;EFzRrB;AACF;AC4BI;EH5CE;IACE,iBIuSe;EFpRrB;AACF;ACuBI;EH5CE;IACE,iBIuSe;EF/QrB;AACF;AGxCE;ECAA,qBAAA;EACA,gBAAA;EACA,aAAA;EACA,eAAA;EACA,yCAAA;EACA,2CAAA;EACA,0CAAA;AJ2CF;AG9CI;ECQF,sBAAA;EAIA,cAAA;EACA,WAAA;EACA,eAAA;EACA,2CAAA;EACA,0CAAA;EACA,8BAAA;AJsCF;;AIGM;EACE,YAAA;AJAR;;AIGM;EApCJ,cAAA;EACA,WAAA;AJqCF;;AIvBE;EACE,cAAA;EACA,WAAA;AJ0BJ;;AI5BE;EACE,cAAA;EACA,UAAA;AJ+BJ;;AIjCE;EACE,cAAA;EACA,qBAAA;AJoCJ;;AItCE;EACE,cAAA;EACA,UAAA;AJyCJ;;AI3CE;EACE,cAAA;EACA,UAAA;AJ8CJ;;AIhDE;EACE,cAAA;EACA,qBAAA;AJmDJ;;AIpBM;EAhDJ,cAAA;EACA,WAAA;AJwEF;;AInBU;EA3DR,cAAA;EACA,oBAAA;AJkFF;;AIxBU;EA3DR,cAAA;EACA,qBAAA;AJuFF;;AI7BU;EA3DR,cAAA;EACA,UAAA;AJ4FF;;AIlCU;EA3DR,cAAA;EACA,qBAAA;AJiGF;;AIvCU;EA3DR,cAAA;EACA,qBAAA;AJsGF;;AI5CU;EA3DR,cAAA;EACA,UAAA;AJ2GF;;AIjDU;EA3DR,cAAA;EACA,qBAAA;AJgHF;;AItDU;EA3DR,cAAA;EACA,qBAAA;AJqHF;;AI3DU;EA3DR,cAAA;EACA,UAAA;AJ0HF;;AIhEU;EA3DR,cAAA;EACA,qBAAA;AJ+HF;;AIrEU;EA3DR,cAAA;EACA,qBAAA;AJoIF;;AI1EU;EA3DR,cAAA;EACA,WAAA;AJyIF;;AIvEY;EAxDV,0BAAA;AJmIF;;AI3EY;EAxDV,2BAAA;AJuIF;;AI/EY;EAxDV,gBAAA;AJ2IF;;AInFY;EAxDV,2BAAA;AJ+IF;;AIvFY;EAxDV,2BAAA;AJmJF;;AI3FY;EAxDV,gBAAA;AJuJF;;AI/FY;EAxDV,2BAAA;AJ2JF;;AInGY;EAxDV,2BAAA;AJ+JF;;AIvGY;EAxDV,gBAAA;AJmKF;;AI3GY;EAxDV,2BAAA;AJuKF;;AI/GY;EAxDV,2BAAA;AJ2KF;;AIxGQ;;EAEE,gBAAA;AJ2GV;;AIxGQ;;EAEE,gBAAA;AJ2GV;;AIlHQ;;EAEE,sBAAA;AJqHV;;AIlHQ;;EAEE,sBAAA;AJqHV;;AI5HQ;;EAEE,qBAAA;AJ+HV;;AI5HQ;;EAEE,qBAAA;AJ+HV;;AItIQ;;EAEE,mBAAA;AJyIV;;AItIQ;;EAEE,mBAAA;AJyIV;;AIhJQ;;EAEE,qBAAA;AJmJV;;AIhJQ;;EAEE,qBAAA;AJmJV;;AI1JQ;;EAEE,mBAAA;AJ6JV;;AI1JQ;;EAEE,mBAAA;AJ6JV;;AChNI;EGGE;IACE,YAAA;EJiNN;;EI9MI;IApCJ,cAAA;IACA,WAAA;EJsPA;;EIxOA;IACE,cAAA;IACA,WAAA;EJ2OF;;EI7OA;IACE,cAAA;IACA,UAAA;EJgPF;;EIlPA;IACE,cAAA;IACA,qBAAA;EJqPF;;EIvPA;IACE,cAAA;IACA,UAAA;EJ0PF;;EI5PA;IACE,cAAA;IACA,UAAA;EJ+PF;;EIjQA;IACE,cAAA;IACA,qBAAA;EJoQF;;EIrOI;IAhDJ,cAAA;IACA,WAAA;EJyRA;;EIpOQ;IA3DR,cAAA;IACA,oBAAA;EJmSA;;EIzOQ;IA3DR,cAAA;IACA,qBAAA;EJwSA;;EI9OQ;IA3DR,cAAA;IACA,UAAA;EJ6SA;;EInPQ;IA3DR,cAAA;IACA,qBAAA;EJkTA;;EIxPQ;IA3DR,cAAA;IACA,qBAAA;EJuTA;;EI7PQ;IA3DR,cAAA;IACA,UAAA;EJ4TA;;EIlQQ;IA3DR,cAAA;IACA,qBAAA;EJiUA;;EIvQQ;IA3DR,cAAA;IACA,qBAAA;EJsUA;;EI5QQ;IA3DR,cAAA;IACA,UAAA;EJ2UA;;EIjRQ;IA3DR,cAAA;IACA,qBAAA;EJgVA;;EItRQ;IA3DR,cAAA;IACA,qBAAA;EJqVA;;EI3RQ;IA3DR,cAAA;IACA,WAAA;EJ0VA;;EIxRU;IAxDV,cAAA;EJoVA;;EI5RU;IAxDV,0BAAA;EJwVA;;EIhSU;IAxDV,2BAAA;EJ4VA;;EIpSU;IAxDV,gBAAA;EJgWA;;EIxSU;IAxDV,2BAAA;EJoWA;;EI5SU;IAxDV,2BAAA;EJwWA;;EIhTU;IAxDV,gBAAA;EJ4WA;;EIpTU;IAxDV,2BAAA;EJgXA;;EIxTU;IAxDV,2BAAA;EJoXA;;EI5TU;IAxDV,gBAAA;EJwXA;;EIhUU;IAxDV,2BAAA;EJ4XA;;EIpUU;IAxDV,2BAAA;EJgYA;;EI7TM;;IAEE,gBAAA;EJgUR;;EI7TM;;IAEE,gBAAA;EJgUR;;EIvUM;;IAEE,sBAAA;EJ0UR;;EIvUM;;IAEE,sBAAA;EJ0UR;;EIjVM;;IAEE,qBAAA;EJoVR;;EIjVM;;IAEE,qBAAA;EJoVR;;EI3VM;;IAEE,mBAAA;EJ8VR;;EI3VM;;IAEE,mBAAA;EJ8VR;;EIrWM;;IAEE,qBAAA;EJwWR;;EIrWM;;IAEE,qBAAA;EJwWR;;EI/WM;;IAEE,mBAAA;EJkXR;;EI/WM;;IAEE,mBAAA;EJkXR;AACF;ACtaI;EGGE;IACE,YAAA;EJsaN;;EInaI;IApCJ,cAAA;IACA,WAAA;EJ2cA;;EI7bA;IACE,cAAA;IACA,WAAA;EJgcF;;EIlcA;IACE,cAAA;IACA,UAAA;EJqcF;;EIvcA;IACE,cAAA;IACA,qBAAA;EJ0cF;;EI5cA;IACE,cAAA;IACA,UAAA;EJ+cF;;EIjdA;IACE,cAAA;IACA,UAAA;EJodF;;EItdA;IACE,cAAA;IACA,qBAAA;EJydF;;EI1bI;IAhDJ,cAAA;IACA,WAAA;EJ8eA;;EIzbQ;IA3DR,cAAA;IACA,oBAAA;EJwfA;;EI9bQ;IA3DR,cAAA;IACA,qBAAA;EJ6fA;;EIncQ;IA3DR,cAAA;IACA,UAAA;EJkgBA;;EIxcQ;IA3DR,cAAA;IACA,qBAAA;EJugBA;;EI7cQ;IA3DR,cAAA;IACA,qBAAA;EJ4gBA;;EIldQ;IA3DR,cAAA;IACA,UAAA;EJihBA;;EIvdQ;IA3DR,cAAA;IACA,qBAAA;EJshBA;;EI5dQ;IA3DR,cAAA;IACA,qBAAA;EJ2hBA;;EIjeQ;IA3DR,cAAA;IACA,UAAA;EJgiBA;;EIteQ;IA3DR,cAAA;IACA,qBAAA;EJqiBA;;EI3eQ;IA3DR,cAAA;IACA,qBAAA;EJ0iBA;;EIhfQ;IA3DR,cAAA;IACA,WAAA;EJ+iBA;;EI7eU;IAxDV,cAAA;EJyiBA;;EIjfU;IAxDV,0BAAA;EJ6iBA;;EIrfU;IAxDV,2BAAA;EJijBA;;EIzfU;IAxDV,gBAAA;EJqjBA;;EI7fU;IAxDV,2BAAA;EJyjBA;;EIjgBU;IAxDV,2BAAA;EJ6jBA;;EIrgBU;IAxDV,gBAAA;EJikBA;;EIzgBU;IAxDV,2BAAA;EJqkBA;;EI7gBU;IAxDV,2BAAA;EJykBA;;EIjhBU;IAxDV,gBAAA;EJ6kBA;;EIrhBU;IAxDV,2BAAA;EJilBA;;EIzhBU;IAxDV,2BAAA;EJqlBA;;EIlhBM;;IAEE,gBAAA;EJqhBR;;EIlhBM;;IAEE,gBAAA;EJqhBR;;EI5hBM;;IAEE,sBAAA;EJ+hBR;;EI5hBM;;IAEE,sBAAA;EJ+hBR;;EItiBM;;IAEE,qBAAA;EJyiBR;;EItiBM;;IAEE,qBAAA;EJyiBR;;EIhjBM;;IAEE,mBAAA;EJmjBR;;EIhjBM;;IAEE,mBAAA;EJmjBR;;EI1jBM;;IAEE,qBAAA;EJ6jBR;;EI1jBM;;IAEE,qBAAA;EJ6jBR;;EIpkBM;;IAEE,mBAAA;EJukBR;;EIpkBM;;IAEE,mBAAA;EJukBR;AACF;AC3nBI;EGGE;IACE,YAAA;EJ2nBN;;EIxnBI;IApCJ,cAAA;IACA,WAAA;EJgqBA;;EIlpBA;IACE,cAAA;IACA,WAAA;EJqpBF;;EIvpBA;IACE,cAAA;IACA,UAAA;EJ0pBF;;EI5pBA;IACE,cAAA;IACA,qBAAA;EJ+pBF;;EIjqBA;IACE,cAAA;IACA,UAAA;EJoqBF;;EItqBA;IACE,cAAA;IACA,UAAA;EJyqBF;;EI3qBA;IACE,cAAA;IACA,qBAAA;EJ8qBF;;EI/oBI;IAhDJ,cAAA;IACA,WAAA;EJmsBA;;EI9oBQ;IA3DR,cAAA;IACA,oBAAA;EJ6sBA;;EInpBQ;IA3DR,cAAA;IACA,qBAAA;EJktBA;;EIxpBQ;IA3DR,cAAA;IACA,UAAA;EJutBA;;EI7pBQ;IA3DR,cAAA;IACA,qBAAA;EJ4tBA;;EIlqBQ;IA3DR,cAAA;IACA,qBAAA;EJiuBA;;EIvqBQ;IA3DR,cAAA;IACA,UAAA;EJsuBA;;EI5qBQ;IA3DR,cAAA;IACA,qBAAA;EJ2uBA;;EIjrBQ;IA3DR,cAAA;IACA,qBAAA;EJgvBA;;EItrBQ;IA3DR,cAAA;IACA,UAAA;EJqvBA;;EI3rBQ;IA3DR,cAAA;IACA,qBAAA;EJ0vBA;;EIhsBQ;IA3DR,cAAA;IACA,qBAAA;EJ+vBA;;EIrsBQ;IA3DR,cAAA;IACA,WAAA;EJowBA;;EIlsBU;IAxDV,cAAA;EJ8vBA;;EItsBU;IAxDV,0BAAA;EJkwBA;;EI1sBU;IAxDV,2BAAA;EJswBA;;EI9sBU;IAxDV,gBAAA;EJ0wBA;;EIltBU;IAxDV,2BAAA;EJ8wBA;;EIttBU;IAxDV,2BAAA;EJkxBA;;EI1tBU;IAxDV,gBAAA;EJsxBA;;EI9tBU;IAxDV,2BAAA;EJ0xBA;;EIluBU;IAxDV,2BAAA;EJ8xBA;;EItuBU;IAxDV,gBAAA;EJkyBA;;EI1uBU;IAxDV,2BAAA;EJsyBA;;EI9uBU;IAxDV,2BAAA;EJ0yBA;;EIvuBM;;IAEE,gBAAA;EJ0uBR;;EIvuBM;;IAEE,gBAAA;EJ0uBR;;EIjvBM;;IAEE,sBAAA;EJovBR;;EIjvBM;;IAEE,sBAAA;EJovBR;;EI3vBM;;IAEE,qBAAA;EJ8vBR;;EI3vBM;;IAEE,qBAAA;EJ8vBR;;EIrwBM;;IAEE,mBAAA;EJwwBR;;EIrwBM;;IAEE,mBAAA;EJwwBR;;EI/wBM;;IAEE,qBAAA;EJkxBR;;EI/wBM;;IAEE,qBAAA;EJkxBR;;EIzxBM;;IAEE,mBAAA;EJ4xBR;;EIzxBM;;IAEE,mBAAA;EJ4xBR;AACF;ACh1BI;EGGE;IACE,YAAA;EJg1BN;;EI70BI;IApCJ,cAAA;IACA,WAAA;EJq3BA;;EIv2BA;IACE,cAAA;IACA,WAAA;EJ02BF;;EI52BA;IACE,cAAA;IACA,UAAA;EJ+2BF;;EIj3BA;IACE,cAAA;IACA,qBAAA;EJo3BF;;EIt3BA;IACE,cAAA;IACA,UAAA;EJy3BF;;EI33BA;IACE,cAAA;IACA,UAAA;EJ83BF;;EIh4BA;IACE,cAAA;IACA,qBAAA;EJm4BF;;EIp2BI;IAhDJ,cAAA;IACA,WAAA;EJw5BA;;EIn2BQ;IA3DR,cAAA;IACA,oBAAA;EJk6BA;;EIx2BQ;IA3DR,cAAA;IACA,qBAAA;EJu6BA;;EI72BQ;IA3DR,cAAA;IACA,UAAA;EJ46BA;;EIl3BQ;IA3DR,cAAA;IACA,qBAAA;EJi7BA;;EIv3BQ;IA3DR,cAAA;IACA,qBAAA;EJs7BA;;EI53BQ;IA3DR,cAAA;IACA,UAAA;EJ27BA;;EIj4BQ;IA3DR,cAAA;IACA,qBAAA;EJg8BA;;EIt4BQ;IA3DR,cAAA;IACA,qBAAA;EJq8BA;;EI34BQ;IA3DR,cAAA;IACA,UAAA;EJ08BA;;EIh5BQ;IA3DR,cAAA;IACA,qBAAA;EJ+8BA;;EIr5BQ;IA3DR,cAAA;IACA,qBAAA;EJo9BA;;EI15BQ;IA3DR,cAAA;IACA,WAAA;EJy9BA;;EIv5BU;IAxDV,cAAA;EJm9BA;;EI35BU;IAxDV,0BAAA;EJu9BA;;EI/5BU;IAxDV,2BAAA;EJ29BA;;EIn6BU;IAxDV,gBAAA;EJ+9BA;;EIv6BU;IAxDV,2BAAA;EJm+BA;;EI36BU;IAxDV,2BAAA;EJu+BA;;EI/6BU;IAxDV,gBAAA;EJ2+BA;;EIn7BU;IAxDV,2BAAA;EJ++BA;;EIv7BU;IAxDV,2BAAA;EJm/BA;;EI37BU;IAxDV,gBAAA;EJu/BA;;EI/7BU;IAxDV,2BAAA;EJ2/BA;;EIn8BU;IAxDV,2BAAA;EJ+/BA;;EI57BM;;IAEE,gBAAA;EJ+7BR;;EI57BM;;IAEE,gBAAA;EJ+7BR;;EIt8BM;;IAEE,sBAAA;EJy8BR;;EIt8BM;;IAEE,sBAAA;EJy8BR;;EIh9BM;;IAEE,qBAAA;EJm9BR;;EIh9BM;;IAEE,qBAAA;EJm9BR;;EI19BM;;IAEE,mBAAA;EJ69BR;;EI19BM;;IAEE,mBAAA;EJ69BR;;EIp+BM;;IAEE,qBAAA;EJu+BR;;EIp+BM;;IAEE,qBAAA;EJu+BR;;EI9+BM;;IAEE,mBAAA;EJi/BR;;EI9+BM;;IAEE,mBAAA;EJi/BR;AACF;ACriCI;EGGE;IACE,YAAA;EJqiCN;;EIliCI;IApCJ,cAAA;IACA,WAAA;EJ0kCA;;EI5jCA;IACE,cAAA;IACA,WAAA;EJ+jCF;;EIjkCA;IACE,cAAA;IACA,UAAA;EJokCF;;EItkCA;IACE,cAAA;IACA,qBAAA;EJykCF;;EI3kCA;IACE,cAAA;IACA,UAAA;EJ8kCF;;EIhlCA;IACE,cAAA;IACA,UAAA;EJmlCF;;EIrlCA;IACE,cAAA;IACA,qBAAA;EJwlCF;;EIzjCI;IAhDJ,cAAA;IACA,WAAA;EJ6mCA;;EIxjCQ;IA3DR,cAAA;IACA,oBAAA;EJunCA;;EI7jCQ;IA3DR,cAAA;IACA,qBAAA;EJ4nCA;;EIlkCQ;IA3DR,cAAA;IACA,UAAA;EJioCA;;EIvkCQ;IA3DR,cAAA;IACA,qBAAA;EJsoCA;;EI5kCQ;IA3DR,cAAA;IACA,qBAAA;EJ2oCA;;EIjlCQ;IA3DR,cAAA;IACA,UAAA;EJgpCA;;EItlCQ;IA3DR,cAAA;IACA,qBAAA;EJqpCA;;EI3lCQ;IA3DR,cAAA;IACA,qBAAA;EJ0pCA;;EIhmCQ;IA3DR,cAAA;IACA,UAAA;EJ+pCA;;EIrmCQ;IA3DR,cAAA;IACA,qBAAA;EJoqCA;;EI1mCQ;IA3DR,cAAA;IACA,qBAAA;EJyqCA;;EI/mCQ;IA3DR,cAAA;IACA,WAAA;EJ8qCA;;EI5mCU;IAxDV,cAAA;EJwqCA;;EIhnCU;IAxDV,0BAAA;EJ4qCA;;EIpnCU;IAxDV,2BAAA;EJgrCA;;EIxnCU;IAxDV,gBAAA;EJorCA;;EI5nCU;IAxDV,2BAAA;EJwrCA;;EIhoCU;IAxDV,2BAAA;EJ4rCA;;EIpoCU;IAxDV,gBAAA;EJgsCA;;EIxoCU;IAxDV,2BAAA;EJosCA;;EI5oCU;IAxDV,2BAAA;EJwsCA;;EIhpCU;IAxDV,gBAAA;EJ4sCA;;EIppCU;IAxDV,2BAAA;EJgtCA;;EIxpCU;IAxDV,2BAAA;EJotCA;;EIjpCM;;IAEE,gBAAA;EJopCR;;EIjpCM;;IAEE,gBAAA;EJopCR;;EI3pCM;;IAEE,sBAAA;EJ8pCR;;EI3pCM;;IAEE,sBAAA;EJ8pCR;;EIrqCM;;IAEE,qBAAA;EJwqCR;;EIrqCM;;IAEE,qBAAA;EJwqCR;;EI/qCM;;IAEE,mBAAA;EJkrCR;;EI/qCM;;IAEE,mBAAA;EJkrCR;;EIzrCM;;IAEE,qBAAA;EJ4rCR;;EIzrCM;;IAEE,qBAAA;EJ4rCR;;EInsCM;;IAEE,mBAAA;EJssCR;;EInsCM;;IAEE,mBAAA;EJssCR;AACF;AKhxCM;EAEI,0BAAA;ALixCV;;AKnxCM;EAEI,gCAAA;ALqxCV;;AKvxCM;EAEI,yBAAA;ALyxCV;;AK3xCM;EAEI,wBAAA;AL6xCV;;AK/xCM;EAEI,yBAAA;ALiyCV;;AKnyCM;EAEI,6BAAA;ALqyCV;;AKvyCM;EAEI,8BAAA;ALyyCV;;AK3yCM;EAEI,wBAAA;AL6yCV;;AK/yCM;EAEI,+BAAA;ALizCV;;AKnzCM;EAEI,wBAAA;ALqzCV;;AKvzCM;EAEI,yBAAA;ALyzCV;;AK3zCM;EAEI,8BAAA;AL6zCV;;AK/zCM;EAEI,iCAAA;ALi0CV;;AKn0CM;EAEI,sCAAA;ALq0CV;;AKv0CM;EAEI,yCAAA;ALy0CV;;AK30CM;EAEI,uBAAA;AL60CV;;AK/0CM;EAEI,uBAAA;ALi1CV;;AKn1CM;EAEI,yBAAA;ALq1CV;;AKv1CM;EAEI,yBAAA;ALy1CV;;AK31CM;EAEI,0BAAA;AL61CV;;AK/1CM;EAEI,4BAAA;ALi2CV;;AKn2CM;EAEI,kCAAA;ALq2CV;;AKv2CM;EAEI,sCAAA;ALy2CV;;AK32CM;EAEI,oCAAA;AL62CV;;AK/2CM;EAEI,kCAAA;ALi3CV;;AKn3CM;EAEI,yCAAA;ALq3CV;;AKv3CM;EAEI,wCAAA;ALy3CV;;AK33CM;EAEI,wCAAA;AL63CV;;AK/3CM;EAEI,kCAAA;ALi4CV;;AKn4CM;EAEI,gCAAA;ALq4CV;;AKv4CM;EAEI,8BAAA;ALy4CV;;AK34CM;EAEI,gCAAA;AL64CV;;AK/4CM;EAEI,+BAAA;ALi5CV;;AKn5CM;EAEI,oCAAA;ALq5CV;;AKv5CM;EAEI,kCAAA;ALy5CV;;AK35CM;EAEI,gCAAA;AL65CV;;AK/5CM;EAEI,uCAAA;ALi6CV;;AKn6CM;EAEI,sCAAA;ALq6CV;;AKv6CM;EAEI,iCAAA;ALy6CV;;AK36CM;EAEI,2BAAA;AL66CV;;AK/6CM;EAEI,iCAAA;ALi7CV;;AKn7CM;EAEI,+BAAA;ALq7CV;;AKv7CM;EAEI,6BAAA;ALy7CV;;AK37CM;EAEI,+BAAA;AL67CV;;AK/7CM;EAEI,8BAAA;ALi8CV;;AKn8CM;EAEI,oBAAA;ALq8CV;;AKv8CM;EAEI,mBAAA;ALy8CV;;AK38CM;EAEI,mBAAA;AL68CV;;AK/8CM;EAEI,mBAAA;ALi9CV;;AKn9CM;EAEI,mBAAA;ALq9CV;;AKv9CM;EAEI,mBAAA;ALy9CV;;AK39CM;EAEI,mBAAA;AL69CV;;AK/9CM;EAEI,mBAAA;ALi+CV;;AKn+CM;EAEI,oBAAA;ALq+CV;;AKv+CM;EAEI,0BAAA;ALy+CV;;AK3+CM;EAEI,yBAAA;AL6+CV;;AK/+CM;EAEI,uBAAA;ALi/CV;;AKn/CM;EAEI,yBAAA;ALq/CV;;AKv/CM;EAEI,uBAAA;ALy/CV;;AK3/CM;EAEI,uBAAA;AL6/CV;;AK//CM;EAEI,0BAAA;EAAA,yBAAA;ALkgDV;;AKpgDM;EAEI,gCAAA;EAAA,+BAAA;ALugDV;;AKzgDM;EAEI,+BAAA;EAAA,8BAAA;AL4gDV;;AK9gDM;EAEI,6BAAA;EAAA,4BAAA;ALihDV;;AKnhDM;EAEI,+BAAA;EAAA,8BAAA;ALshDV;;AKxhDM;EAEI,6BAAA;EAAA,4BAAA;AL2hDV;;AK7hDM;EAEI,6BAAA;EAAA,4BAAA;ALgiDV;;AKliDM;EAEI,wBAAA;EAAA,2BAAA;ALqiDV;;AKviDM;EAEI,8BAAA;EAAA,iCAAA;AL0iDV;;AK5iDM;EAEI,6BAAA;EAAA,gCAAA;AL+iDV;;AKjjDM;EAEI,2BAAA;EAAA,8BAAA;ALojDV;;AKtjDM;EAEI,6BAAA;EAAA,gCAAA;ALyjDV;;AK3jDM;EAEI,2BAAA;EAAA,8BAAA;AL8jDV;;AKhkDM;EAEI,2BAAA;EAAA,8BAAA;ALmkDV;;AKrkDM;EAEI,wBAAA;ALukDV;;AKzkDM;EAEI,8BAAA;AL2kDV;;AK7kDM;EAEI,6BAAA;AL+kDV;;AKjlDM;EAEI,2BAAA;ALmlDV;;AKrlDM;EAEI,6BAAA;ALulDV;;AKzlDM;EAEI,2BAAA;AL2lDV;;AK7lDM;EAEI,2BAAA;AL+lDV;;AKjmDM;EAEI,0BAAA;ALmmDV;;AKrmDM;EAEI,gCAAA;ALumDV;;AKzmDM;EAEI,+BAAA;AL2mDV;;AK7mDM;EAEI,6BAAA;AL+mDV;;AKjnDM;EAEI,+BAAA;ALmnDV;;AKrnDM;EAEI,6BAAA;ALunDV;;AKznDM;EAEI,6BAAA;AL2nDV;;AK7nDM;EAEI,2BAAA;AL+nDV;;AKjoDM;EAEI,iCAAA;ALmoDV;;AKroDM;EAEI,gCAAA;ALuoDV;;AKzoDM;EAEI,8BAAA;AL2oDV;;AK7oDM;EAEI,gCAAA;AL+oDV;;AKjpDM;EAEI,8BAAA;ALmpDV;;AKrpDM;EAEI,8BAAA;ALupDV;;AKzpDM;EAEI,yBAAA;AL2pDV;;AK7pDM;EAEI,+BAAA;AL+pDV;;AKjqDM;EAEI,8BAAA;ALmqDV;;AKrqDM;EAEI,4BAAA;ALuqDV;;AKzqDM;EAEI,8BAAA;AL2qDV;;AK7qDM;EAEI,4BAAA;AL+qDV;;AKjrDM;EAEI,4BAAA;ALmrDV;;AKrrDM;EAEI,qBAAA;ALurDV;;AKzrDM;EAEI,2BAAA;AL2rDV;;AK7rDM;EAEI,0BAAA;AL+rDV;;AKjsDM;EAEI,wBAAA;ALmsDV;;AKrsDM;EAEI,0BAAA;ALusDV;;AKzsDM;EAEI,wBAAA;AL2sDV;;AK7sDM;EAEI,2BAAA;EAAA,0BAAA;ALgtDV;;AKltDM;EAEI,iCAAA;EAAA,gCAAA;ALqtDV;;AKvtDM;EAEI,gCAAA;EAAA,+BAAA;AL0tDV;;AK5tDM;EAEI,8BAAA;EAAA,6BAAA;AL+tDV;;AKjuDM;EAEI,gCAAA;EAAA,+BAAA;ALouDV;;AKtuDM;EAEI,8BAAA;EAAA,6BAAA;ALyuDV;;AK3uDM;EAEI,yBAAA;EAAA,4BAAA;AL8uDV;;AKhvDM;EAEI,+BAAA;EAAA,kCAAA;ALmvDV;;AKrvDM;EAEI,8BAAA;EAAA,iCAAA;ALwvDV;;AK1vDM;EAEI,4BAAA;EAAA,+BAAA;AL6vDV;;AK/vDM;EAEI,8BAAA;EAAA,iCAAA;ALkwDV;;AKpwDM;EAEI,4BAAA;EAAA,+BAAA;ALuwDV;;AKzwDM;EAEI,yBAAA;AL2wDV;;AK7wDM;EAEI,+BAAA;AL+wDV;;AKjxDM;EAEI,8BAAA;ALmxDV;;AKrxDM;EAEI,4BAAA;ALuxDV;;AKzxDM;EAEI,8BAAA;AL2xDV;;AK7xDM;EAEI,4BAAA;AL+xDV;;AKjyDM;EAEI,2BAAA;ALmyDV;;AKryDM;EAEI,iCAAA;ALuyDV;;AKzyDM;EAEI,gCAAA;AL2yDV;;AK7yDM;EAEI,8BAAA;AL+yDV;;AKjzDM;EAEI,gCAAA;ALmzDV;;AKrzDM;EAEI,8BAAA;ALuzDV;;AKzzDM;EAEI,4BAAA;AL2zDV;;AK7zDM;EAEI,kCAAA;AL+zDV;;AKj0DM;EAEI,iCAAA;ALm0DV;;AKr0DM;EAEI,+BAAA;ALu0DV;;AKz0DM;EAEI,iCAAA;AL20DV;;AK70DM;EAEI,+BAAA;AL+0DV;;AKj1DM;EAEI,0BAAA;ALm1DV;;AKr1DM;EAEI,gCAAA;ALu1DV;;AKz1DM;EAEI,+BAAA;AL21DV;;AK71DM;EAEI,6BAAA;AL+1DV;;AKj2DM;EAEI,+BAAA;ALm2DV;;AKr2DM;EAEI,6BAAA;ALu2DV;;ACn1DI;EItBE;IAEI,0BAAA;EL42DR;;EK92DI;IAEI,gCAAA;ELg3DR;;EKl3DI;IAEI,yBAAA;ELo3DR;;EKt3DI;IAEI,wBAAA;ELw3DR;;EK13DI;IAEI,yBAAA;EL43DR;;EK93DI;IAEI,6BAAA;ELg4DR;;EKl4DI;IAEI,8BAAA;ELo4DR;;EKt4DI;IAEI,wBAAA;ELw4DR;;EK14DI;IAEI,+BAAA;EL44DR;;EK94DI;IAEI,wBAAA;ELg5DR;;EKl5DI;IAEI,yBAAA;ELo5DR;;EKt5DI;IAEI,8BAAA;ELw5DR;;EK15DI;IAEI,iCAAA;EL45DR;;EK95DI;IAEI,sCAAA;ELg6DR;;EKl6DI;IAEI,yCAAA;ELo6DR;;EKt6DI;IAEI,uBAAA;ELw6DR;;EK16DI;IAEI,uBAAA;EL46DR;;EK96DI;IAEI,yBAAA;ELg7DR;;EKl7DI;IAEI,yBAAA;ELo7DR;;EKt7DI;IAEI,0BAAA;ELw7DR;;EK17DI;IAEI,4BAAA;EL47DR;;EK97DI;IAEI,kCAAA;ELg8DR;;EKl8DI;IAEI,sCAAA;ELo8DR;;EKt8DI;IAEI,oCAAA;ELw8DR;;EK18DI;IAEI,kCAAA;EL48DR;;EK98DI;IAEI,yCAAA;ELg9DR;;EKl9DI;IAEI,wCAAA;ELo9DR;;EKt9DI;IAEI,wCAAA;ELw9DR;;EK19DI;IAEI,kCAAA;EL49DR;;EK99DI;IAEI,gCAAA;ELg+DR;;EKl+DI;IAEI,8BAAA;ELo+DR;;EKt+DI;IAEI,gCAAA;ELw+DR;;EK1+DI;IAEI,+BAAA;EL4+DR;;EK9+DI;IAEI,oCAAA;ELg/DR;;EKl/DI;IAEI,kCAAA;ELo/DR;;EKt/DI;IAEI,gCAAA;ELw/DR;;EK1/DI;IAEI,uCAAA;EL4/DR;;EK9/DI;IAEI,sCAAA;ELggER;;EKlgEI;IAEI,iCAAA;ELogER;;EKtgEI;IAEI,2BAAA;ELwgER;;EK1gEI;IAEI,iCAAA;EL4gER;;EK9gEI;IAEI,+BAAA;ELghER;;EKlhEI;IAEI,6BAAA;ELohER;;EKthEI;IAEI,+BAAA;ELwhER;;EK1hEI;IAEI,8BAAA;EL4hER;;EK9hEI;IAEI,oBAAA;ELgiER;;EKliEI;IAEI,mBAAA;ELoiER;;EKtiEI;IAEI,mBAAA;ELwiER;;EK1iEI;IAEI,mBAAA;EL4iER;;EK9iEI;IAEI,mBAAA;ELgjER;;EKljEI;IAEI,mBAAA;ELojER;;EKtjEI;IAEI,mBAAA;ELwjER;;EK1jEI;IAEI,mBAAA;EL4jER;;EK9jEI;IAEI,oBAAA;ELgkER;;EKlkEI;IAEI,0BAAA;ELokER;;EKtkEI;IAEI,yBAAA;ELwkER;;EK1kEI;IAEI,uBAAA;EL4kER;;EK9kEI;IAEI,yBAAA;ELglER;;EKllEI;IAEI,uBAAA;ELolER;;EKtlEI;IAEI,uBAAA;ELwlER;;EK1lEI;IAEI,0BAAA;IAAA,yBAAA;EL6lER;;EK/lEI;IAEI,gCAAA;IAAA,+BAAA;ELkmER;;EKpmEI;IAEI,+BAAA;IAAA,8BAAA;ELumER;;EKzmEI;IAEI,6BAAA;IAAA,4BAAA;EL4mER;;EK9mEI;IAEI,+BAAA;IAAA,8BAAA;ELinER;;EKnnEI;IAEI,6BAAA;IAAA,4BAAA;ELsnER;;EKxnEI;IAEI,6BAAA;IAAA,4BAAA;EL2nER;;EK7nEI;IAEI,wBAAA;IAAA,2BAAA;ELgoER;;EKloEI;IAEI,8BAAA;IAAA,iCAAA;ELqoER;;EKvoEI;IAEI,6BAAA;IAAA,gCAAA;EL0oER;;EK5oEI;IAEI,2BAAA;IAAA,8BAAA;EL+oER;;EKjpEI;IAEI,6BAAA;IAAA,gCAAA;ELopER;;EKtpEI;IAEI,2BAAA;IAAA,8BAAA;ELypER;;EK3pEI;IAEI,2BAAA;IAAA,8BAAA;EL8pER;;EKhqEI;IAEI,wBAAA;ELkqER;;EKpqEI;IAEI,8BAAA;ELsqER;;EKxqEI;IAEI,6BAAA;EL0qER;;EK5qEI;IAEI,2BAAA;EL8qER;;EKhrEI;IAEI,6BAAA;ELkrER;;EKprEI;IAEI,2BAAA;ELsrER;;EKxrEI;IAEI,2BAAA;EL0rER;;EK5rEI;IAEI,0BAAA;EL8rER;;EKhsEI;IAEI,gCAAA;ELksER;;EKpsEI;IAEI,+BAAA;ELssER;;EKxsEI;IAEI,6BAAA;EL0sER;;EK5sEI;IAEI,+BAAA;EL8sER;;EKhtEI;IAEI,6BAAA;ELktER;;EKptEI;IAEI,6BAAA;ELstER;;EKxtEI;IAEI,2BAAA;EL0tER;;EK5tEI;IAEI,iCAAA;EL8tER;;EKhuEI;IAEI,gCAAA;ELkuER;;EKpuEI;IAEI,8BAAA;ELsuER;;EKxuEI;IAEI,gCAAA;EL0uER;;EK5uEI;IAEI,8BAAA;EL8uER;;EKhvEI;IAEI,8BAAA;ELkvER;;EKpvEI;IAEI,yBAAA;ELsvER;;EKxvEI;IAEI,+BAAA;EL0vER;;EK5vEI;IAEI,8BAAA;EL8vER;;EKhwEI;IAEI,4BAAA;ELkwER;;EKpwEI;IAEI,8BAAA;ELswER;;EKxwEI;IAEI,4BAAA;EL0wER;;EK5wEI;IAEI,4BAAA;EL8wER;;EKhxEI;IAEI,qBAAA;ELkxER;;EKpxEI;IAEI,2BAAA;ELsxER;;EKxxEI;IAEI,0BAAA;EL0xER;;EK5xEI;IAEI,wBAAA;EL8xER;;EKhyEI;IAEI,0BAAA;ELkyER;;EKpyEI;IAEI,wBAAA;ELsyER;;EKxyEI;IAEI,2BAAA;IAAA,0BAAA;EL2yER;;EK7yEI;IAEI,iCAAA;IAAA,gCAAA;ELgzER;;EKlzEI;IAEI,gCAAA;IAAA,+BAAA;ELqzER;;EKvzEI;IAEI,8BAAA;IAAA,6BAAA;EL0zER;;EK5zEI;IAEI,gCAAA;IAAA,+BAAA;EL+zER;;EKj0EI;IAEI,8BAAA;IAAA,6BAAA;ELo0ER;;EKt0EI;IAEI,yBAAA;IAAA,4BAAA;ELy0ER;;EK30EI;IAEI,+BAAA;IAAA,kCAAA;EL80ER;;EKh1EI;IAEI,8BAAA;IAAA,iCAAA;ELm1ER;;EKr1EI;IAEI,4BAAA;IAAA,+BAAA;ELw1ER;;EK11EI;IAEI,8BAAA;IAAA,iCAAA;EL61ER;;EK/1EI;IAEI,4BAAA;IAAA,+BAAA;ELk2ER;;EKp2EI;IAEI,yBAAA;ELs2ER;;EKx2EI;IAEI,+BAAA;EL02ER;;EK52EI;IAEI,8BAAA;EL82ER;;EKh3EI;IAEI,4BAAA;ELk3ER;;EKp3EI;IAEI,8BAAA;ELs3ER;;EKx3EI;IAEI,4BAAA;EL03ER;;EK53EI;IAEI,2BAAA;EL83ER;;EKh4EI;IAEI,iCAAA;ELk4ER;;EKp4EI;IAEI,gCAAA;ELs4ER;;EKx4EI;IAEI,8BAAA;EL04ER;;EK54EI;IAEI,gCAAA;EL84ER;;EKh5EI;IAEI,8BAAA;ELk5ER;;EKp5EI;IAEI,4BAAA;ELs5ER;;EKx5EI;IAEI,kCAAA;EL05ER;;EK55EI;IAEI,iCAAA;EL85ER;;EKh6EI;IAEI,+BAAA;ELk6ER;;EKp6EI;IAEI,iCAAA;ELs6ER;;EKx6EI;IAEI,+BAAA;EL06ER;;EK56EI;IAEI,0BAAA;EL86ER;;EKh7EI;IAEI,gCAAA;ELk7ER;;EKp7EI;IAEI,+BAAA;ELs7ER;;EKx7EI;IAEI,6BAAA;EL07ER;;EK57EI;IAEI,+BAAA;EL87ER;;EKh8EI;IAEI,6BAAA;ELk8ER;AACF;AC/6EI;EItBE;IAEI,0BAAA;ELu8ER;;EKz8EI;IAEI,gCAAA;EL28ER;;EK78EI;IAEI,yBAAA;EL+8ER;;EKj9EI;IAEI,wBAAA;ELm9ER;;EKr9EI;IAEI,yBAAA;ELu9ER;;EKz9EI;IAEI,6BAAA;EL29ER;;EK79EI;IAEI,8BAAA;EL+9ER;;EKj+EI;IAEI,wBAAA;ELm+ER;;EKr+EI;IAEI,+BAAA;ELu+ER;;EKz+EI;IAEI,wBAAA;EL2+ER;;EK7+EI;IAEI,yBAAA;EL++ER;;EKj/EI;IAEI,8BAAA;ELm/ER;;EKr/EI;IAEI,iCAAA;ELu/ER;;EKz/EI;IAEI,sCAAA;EL2/ER;;EK7/EI;IAEI,yCAAA;EL+/ER;;EKjgFI;IAEI,uBAAA;ELmgFR;;EKrgFI;IAEI,uBAAA;ELugFR;;EKzgFI;IAEI,yBAAA;EL2gFR;;EK7gFI;IAEI,yBAAA;EL+gFR;;EKjhFI;IAEI,0BAAA;ELmhFR;;EKrhFI;IAEI,4BAAA;ELuhFR;;EKzhFI;IAEI,kCAAA;EL2hFR;;EK7hFI;IAEI,sCAAA;EL+hFR;;EKjiFI;IAEI,oCAAA;ELmiFR;;EKriFI;IAEI,kCAAA;ELuiFR;;EKziFI;IAEI,yCAAA;EL2iFR;;EK7iFI;IAEI,wCAAA;EL+iFR;;EKjjFI;IAEI,wCAAA;ELmjFR;;EKrjFI;IAEI,kCAAA;ELujFR;;EKzjFI;IAEI,gCAAA;EL2jFR;;EK7jFI;IAEI,8BAAA;EL+jFR;;EKjkFI;IAEI,gCAAA;ELmkFR;;EKrkFI;IAEI,+BAAA;ELukFR;;EKzkFI;IAEI,oCAAA;EL2kFR;;EK7kFI;IAEI,kCAAA;EL+kFR;;EKjlFI;IAEI,gCAAA;ELmlFR;;EKrlFI;IAEI,uCAAA;ELulFR;;EKzlFI;IAEI,sCAAA;EL2lFR;;EK7lFI;IAEI,iCAAA;EL+lFR;;EKjmFI;IAEI,2BAAA;ELmmFR;;EKrmFI;IAEI,iCAAA;ELumFR;;EKzmFI;IAEI,+BAAA;EL2mFR;;EK7mFI;IAEI,6BAAA;EL+mFR;;EKjnFI;IAEI,+BAAA;ELmnFR;;EKrnFI;IAEI,8BAAA;ELunFR;;EKznFI;IAEI,oBAAA;EL2nFR;;EK7nFI;IAEI,mBAAA;EL+nFR;;EKjoFI;IAEI,mBAAA;ELmoFR;;EKroFI;IAEI,mBAAA;ELuoFR;;EKzoFI;IAEI,mBAAA;EL2oFR;;EK7oFI;IAEI,mBAAA;EL+oFR;;EKjpFI;IAEI,mBAAA;ELmpFR;;EKrpFI;IAEI,mBAAA;ELupFR;;EKzpFI;IAEI,oBAAA;EL2pFR;;EK7pFI;IAEI,0BAAA;EL+pFR;;EKjqFI;IAEI,yBAAA;ELmqFR;;EKrqFI;IAEI,uBAAA;ELuqFR;;EKzqFI;IAEI,yBAAA;EL2qFR;;EK7qFI;IAEI,uBAAA;EL+qFR;;EKjrFI;IAEI,uBAAA;ELmrFR;;EKrrFI;IAEI,0BAAA;IAAA,yBAAA;ELwrFR;;EK1rFI;IAEI,gCAAA;IAAA,+BAAA;EL6rFR;;EK/rFI;IAEI,+BAAA;IAAA,8BAAA;ELksFR;;EKpsFI;IAEI,6BAAA;IAAA,4BAAA;ELusFR;;EKzsFI;IAEI,+BAAA;IAAA,8BAAA;EL4sFR;;EK9sFI;IAEI,6BAAA;IAAA,4BAAA;ELitFR;;EKntFI;IAEI,6BAAA;IAAA,4BAAA;ELstFR;;EKxtFI;IAEI,wBAAA;IAAA,2BAAA;EL2tFR;;EK7tFI;IAEI,8BAAA;IAAA,iCAAA;ELguFR;;EKluFI;IAEI,6BAAA;IAAA,gCAAA;ELquFR;;EKvuFI;IAEI,2BAAA;IAAA,8BAAA;EL0uFR;;EK5uFI;IAEI,6BAAA;IAAA,gCAAA;EL+uFR;;EKjvFI;IAEI,2BAAA;IAAA,8BAAA;ELovFR;;EKtvFI;IAEI,2BAAA;IAAA,8BAAA;ELyvFR;;EK3vFI;IAEI,wBAAA;EL6vFR;;EK/vFI;IAEI,8BAAA;ELiwFR;;EKnwFI;IAEI,6BAAA;ELqwFR;;EKvwFI;IAEI,2BAAA;ELywFR;;EK3wFI;IAEI,6BAAA;EL6wFR;;EK/wFI;IAEI,2BAAA;ELixFR;;EKnxFI;IAEI,2BAAA;ELqxFR;;EKvxFI;IAEI,0BAAA;ELyxFR;;EK3xFI;IAEI,gCAAA;EL6xFR;;EK/xFI;IAEI,+BAAA;ELiyFR;;EKnyFI;IAEI,6BAAA;ELqyFR;;EKvyFI;IAEI,+BAAA;ELyyFR;;EK3yFI;IAEI,6BAAA;EL6yFR;;EK/yFI;IAEI,6BAAA;ELizFR;;EKnzFI;IAEI,2BAAA;ELqzFR;;EKvzFI;IAEI,iCAAA;ELyzFR;;EK3zFI;IAEI,gCAAA;EL6zFR;;EK/zFI;IAEI,8BAAA;ELi0FR;;EKn0FI;IAEI,gCAAA;ELq0FR;;EKv0FI;IAEI,8BAAA;ELy0FR;;EK30FI;IAEI,8BAAA;EL60FR;;EK/0FI;IAEI,yBAAA;ELi1FR;;EKn1FI;IAEI,+BAAA;ELq1FR;;EKv1FI;IAEI,8BAAA;ELy1FR;;EK31FI;IAEI,4BAAA;EL61FR;;EK/1FI;IAEI,8BAAA;ELi2FR;;EKn2FI;IAEI,4BAAA;ELq2FR;;EKv2FI;IAEI,4BAAA;ELy2FR;;EK32FI;IAEI,qBAAA;EL62FR;;EK/2FI;IAEI,2BAAA;ELi3FR;;EKn3FI;IAEI,0BAAA;ELq3FR;;EKv3FI;IAEI,wBAAA;ELy3FR;;EK33FI;IAEI,0BAAA;EL63FR;;EK/3FI;IAEI,wBAAA;ELi4FR;;EKn4FI;IAEI,2BAAA;IAAA,0BAAA;ELs4FR;;EKx4FI;IAEI,iCAAA;IAAA,gCAAA;EL24FR;;EK74FI;IAEI,gCAAA;IAAA,+BAAA;ELg5FR;;EKl5FI;IAEI,8BAAA;IAAA,6BAAA;ELq5FR;;EKv5FI;IAEI,gCAAA;IAAA,+BAAA;EL05FR;;EK55FI;IAEI,8BAAA;IAAA,6BAAA;EL+5FR;;EKj6FI;IAEI,yBAAA;IAAA,4BAAA;ELo6FR;;EKt6FI;IAEI,+BAAA;IAAA,kCAAA;ELy6FR;;EK36FI;IAEI,8BAAA;IAAA,iCAAA;EL86FR;;EKh7FI;IAEI,4BAAA;IAAA,+BAAA;ELm7FR;;EKr7FI;IAEI,8BAAA;IAAA,iCAAA;ELw7FR;;EK17FI;IAEI,4BAAA;IAAA,+BAAA;EL67FR;;EK/7FI;IAEI,yBAAA;ELi8FR;;EKn8FI;IAEI,+BAAA;ELq8FR;;EKv8FI;IAEI,8BAAA;ELy8FR;;EK38FI;IAEI,4BAAA;EL68FR;;EK/8FI;IAEI,8BAAA;ELi9FR;;EKn9FI;IAEI,4BAAA;ELq9FR;;EKv9FI;IAEI,2BAAA;ELy9FR;;EK39FI;IAEI,iCAAA;EL69FR;;EK/9FI;IAEI,gCAAA;ELi+FR;;EKn+FI;IAEI,8BAAA;ELq+FR;;EKv+FI;IAEI,gCAAA;ELy+FR;;EK3+FI;IAEI,8BAAA;EL6+FR;;EK/+FI;IAEI,4BAAA;ELi/FR;;EKn/FI;IAEI,kCAAA;ELq/FR;;EKv/FI;IAEI,iCAAA;ELy/FR;;EK3/FI;IAEI,+BAAA;EL6/FR;;EK//FI;IAEI,iCAAA;ELigGR;;EKngGI;IAEI,+BAAA;ELqgGR;;EKvgGI;IAEI,0BAAA;ELygGR;;EK3gGI;IAEI,gCAAA;EL6gGR;;EK/gGI;IAEI,+BAAA;ELihGR;;EKnhGI;IAEI,6BAAA;ELqhGR;;EKvhGI;IAEI,+BAAA;ELyhGR;;EK3hGI;IAEI,6BAAA;EL6hGR;AACF;AC1gGI;EItBE;IAEI,0BAAA;ELkiGR;;EKpiGI;IAEI,gCAAA;ELsiGR;;EKxiGI;IAEI,yBAAA;EL0iGR;;EK5iGI;IAEI,wBAAA;EL8iGR;;EKhjGI;IAEI,yBAAA;ELkjGR;;EKpjGI;IAEI,6BAAA;ELsjGR;;EKxjGI;IAEI,8BAAA;EL0jGR;;EK5jGI;IAEI,wBAAA;EL8jGR;;EKhkGI;IAEI,+BAAA;ELkkGR;;EKpkGI;IAEI,wBAAA;ELskGR;;EKxkGI;IAEI,yBAAA;EL0kGR;;EK5kGI;IAEI,8BAAA;EL8kGR;;EKhlGI;IAEI,iCAAA;ELklGR;;EKplGI;IAEI,sCAAA;ELslGR;;EKxlGI;IAEI,yCAAA;EL0lGR;;EK5lGI;IAEI,uBAAA;EL8lGR;;EKhmGI;IAEI,uBAAA;ELkmGR;;EKpmGI;IAEI,yBAAA;ELsmGR;;EKxmGI;IAEI,yBAAA;EL0mGR;;EK5mGI;IAEI,0BAAA;EL8mGR;;EKhnGI;IAEI,4BAAA;ELknGR;;EKpnGI;IAEI,kCAAA;ELsnGR;;EKxnGI;IAEI,sCAAA;EL0nGR;;EK5nGI;IAEI,oCAAA;EL8nGR;;EKhoGI;IAEI,kCAAA;ELkoGR;;EKpoGI;IAEI,yCAAA;ELsoGR;;EKxoGI;IAEI,wCAAA;EL0oGR;;EK5oGI;IAEI,wCAAA;EL8oGR;;EKhpGI;IAEI,kCAAA;ELkpGR;;EKppGI;IAEI,gCAAA;ELspGR;;EKxpGI;IAEI,8BAAA;EL0pGR;;EK5pGI;IAEI,gCAAA;EL8pGR;;EKhqGI;IAEI,+BAAA;ELkqGR;;EKpqGI;IAEI,oCAAA;ELsqGR;;EKxqGI;IAEI,kCAAA;EL0qGR;;EK5qGI;IAEI,gCAAA;EL8qGR;;EKhrGI;IAEI,uCAAA;ELkrGR;;EKprGI;IAEI,sCAAA;ELsrGR;;EKxrGI;IAEI,iCAAA;EL0rGR;;EK5rGI;IAEI,2BAAA;EL8rGR;;EKhsGI;IAEI,iCAAA;ELksGR;;EKpsGI;IAEI,+BAAA;ELssGR;;EKxsGI;IAEI,6BAAA;EL0sGR;;EK5sGI;IAEI,+BAAA;EL8sGR;;EKhtGI;IAEI,8BAAA;ELktGR;;EKptGI;IAEI,oBAAA;ELstGR;;EKxtGI;IAEI,mBAAA;EL0tGR;;EK5tGI;IAEI,mBAAA;EL8tGR;;EKhuGI;IAEI,mBAAA;ELkuGR;;EKpuGI;IAEI,mBAAA;ELsuGR;;EKxuGI;IAEI,mBAAA;EL0uGR;;EK5uGI;IAEI,mBAAA;EL8uGR;;EKhvGI;IAEI,mBAAA;ELkvGR;;EKpvGI;IAEI,oBAAA;ELsvGR;;EKxvGI;IAEI,0BAAA;EL0vGR;;EK5vGI;IAEI,yBAAA;EL8vGR;;EKhwGI;IAEI,uBAAA;ELkwGR;;EKpwGI;IAEI,yBAAA;ELswGR;;EKxwGI;IAEI,uBAAA;EL0wGR;;EK5wGI;IAEI,uBAAA;EL8wGR;;EKhxGI;IAEI,0BAAA;IAAA,yBAAA;ELmxGR;;EKrxGI;IAEI,gCAAA;IAAA,+BAAA;ELwxGR;;EK1xGI;IAEI,+BAAA;IAAA,8BAAA;EL6xGR;;EK/xGI;IAEI,6BAAA;IAAA,4BAAA;ELkyGR;;EKpyGI;IAEI,+BAAA;IAAA,8BAAA;ELuyGR;;EKzyGI;IAEI,6BAAA;IAAA,4BAAA;EL4yGR;;EK9yGI;IAEI,6BAAA;IAAA,4BAAA;ELizGR;;EKnzGI;IAEI,wBAAA;IAAA,2BAAA;ELszGR;;EKxzGI;IAEI,8BAAA;IAAA,iCAAA;EL2zGR;;EK7zGI;IAEI,6BAAA;IAAA,gCAAA;ELg0GR;;EKl0GI;IAEI,2BAAA;IAAA,8BAAA;ELq0GR;;EKv0GI;IAEI,6BAAA;IAAA,gCAAA;EL00GR;;EK50GI;IAEI,2BAAA;IAAA,8BAAA;EL+0GR;;EKj1GI;IAEI,2BAAA;IAAA,8BAAA;ELo1GR;;EKt1GI;IAEI,wBAAA;ELw1GR;;EK11GI;IAEI,8BAAA;EL41GR;;EK91GI;IAEI,6BAAA;ELg2GR;;EKl2GI;IAEI,2BAAA;ELo2GR;;EKt2GI;IAEI,6BAAA;ELw2GR;;EK12GI;IAEI,2BAAA;EL42GR;;EK92GI;IAEI,2BAAA;ELg3GR;;EKl3GI;IAEI,0BAAA;ELo3GR;;EKt3GI;IAEI,gCAAA;ELw3GR;;EK13GI;IAEI,+BAAA;EL43GR;;EK93GI;IAEI,6BAAA;ELg4GR;;EKl4GI;IAEI,+BAAA;ELo4GR;;EKt4GI;IAEI,6BAAA;ELw4GR;;EK14GI;IAEI,6BAAA;EL44GR;;EK94GI;IAEI,2BAAA;ELg5GR;;EKl5GI;IAEI,iCAAA;ELo5GR;;EKt5GI;IAEI,gCAAA;ELw5GR;;EK15GI;IAEI,8BAAA;EL45GR;;EK95GI;IAEI,gCAAA;ELg6GR;;EKl6GI;IAEI,8BAAA;ELo6GR;;EKt6GI;IAEI,8BAAA;ELw6GR;;EK16GI;IAEI,yBAAA;EL46GR;;EK96GI;IAEI,+BAAA;ELg7GR;;EKl7GI;IAEI,8BAAA;ELo7GR;;EKt7GI;IAEI,4BAAA;ELw7GR;;EK17GI;IAEI,8BAAA;EL47GR;;EK97GI;IAEI,4BAAA;ELg8GR;;EKl8GI;IAEI,4BAAA;ELo8GR;;EKt8GI;IAEI,qBAAA;ELw8GR;;EK18GI;IAEI,2BAAA;EL48GR;;EK98GI;IAEI,0BAAA;ELg9GR;;EKl9GI;IAEI,wBAAA;ELo9GR;;EKt9GI;IAEI,0BAAA;ELw9GR;;EK19GI;IAEI,wBAAA;EL49GR;;EK99GI;IAEI,2BAAA;IAAA,0BAAA;ELi+GR;;EKn+GI;IAEI,iCAAA;IAAA,gCAAA;ELs+GR;;EKx+GI;IAEI,gCAAA;IAAA,+BAAA;EL2+GR;;EK7+GI;IAEI,8BAAA;IAAA,6BAAA;ELg/GR;;EKl/GI;IAEI,gCAAA;IAAA,+BAAA;ELq/GR;;EKv/GI;IAEI,8BAAA;IAAA,6BAAA;EL0/GR;;EK5/GI;IAEI,yBAAA;IAAA,4BAAA;EL+/GR;;EKjgHI;IAEI,+BAAA;IAAA,kCAAA;ELogHR;;EKtgHI;IAEI,8BAAA;IAAA,iCAAA;ELygHR;;EK3gHI;IAEI,4BAAA;IAAA,+BAAA;EL8gHR;;EKhhHI;IAEI,8BAAA;IAAA,iCAAA;ELmhHR;;EKrhHI;IAEI,4BAAA;IAAA,+BAAA;ELwhHR;;EK1hHI;IAEI,yBAAA;EL4hHR;;EK9hHI;IAEI,+BAAA;ELgiHR;;EKliHI;IAEI,8BAAA;ELoiHR;;EKtiHI;IAEI,4BAAA;ELwiHR;;EK1iHI;IAEI,8BAAA;EL4iHR;;EK9iHI;IAEI,4BAAA;ELgjHR;;EKljHI;IAEI,2BAAA;ELojHR;;EKtjHI;IAEI,iCAAA;ELwjHR;;EK1jHI;IAEI,gCAAA;EL4jHR;;EK9jHI;IAEI,8BAAA;ELgkHR;;EKlkHI;IAEI,gCAAA;ELokHR;;EKtkHI;IAEI,8BAAA;ELwkHR;;EK1kHI;IAEI,4BAAA;EL4kHR;;EK9kHI;IAEI,kCAAA;ELglHR;;EKllHI;IAEI,iCAAA;ELolHR;;EKtlHI;IAEI,+BAAA;ELwlHR;;EK1lHI;IAEI,iCAAA;EL4lHR;;EK9lHI;IAEI,+BAAA;ELgmHR;;EKlmHI;IAEI,0BAAA;ELomHR;;EKtmHI;IAEI,gCAAA;ELwmHR;;EK1mHI;IAEI,+BAAA;EL4mHR;;EK9mHI;IAEI,6BAAA;ELgnHR;;EKlnHI;IAEI,+BAAA;ELonHR;;EKtnHI;IAEI,6BAAA;ELwnHR;AACF;ACrmHI;EItBE;IAEI,0BAAA;EL6nHR;;EK/nHI;IAEI,gCAAA;ELioHR;;EKnoHI;IAEI,yBAAA;ELqoHR;;EKvoHI;IAEI,wBAAA;ELyoHR;;EK3oHI;IAEI,yBAAA;EL6oHR;;EK/oHI;IAEI,6BAAA;ELipHR;;EKnpHI;IAEI,8BAAA;ELqpHR;;EKvpHI;IAEI,wBAAA;ELypHR;;EK3pHI;IAEI,+BAAA;EL6pHR;;EK/pHI;IAEI,wBAAA;ELiqHR;;EKnqHI;IAEI,yBAAA;ELqqHR;;EKvqHI;IAEI,8BAAA;ELyqHR;;EK3qHI;IAEI,iCAAA;EL6qHR;;EK/qHI;IAEI,sCAAA;ELirHR;;EKnrHI;IAEI,yCAAA;ELqrHR;;EKvrHI;IAEI,uBAAA;ELyrHR;;EK3rHI;IAEI,uBAAA;EL6rHR;;EK/rHI;IAEI,yBAAA;ELisHR;;EKnsHI;IAEI,yBAAA;ELqsHR;;EKvsHI;IAEI,0BAAA;ELysHR;;EK3sHI;IAEI,4BAAA;EL6sHR;;EK/sHI;IAEI,kCAAA;ELitHR;;EKntHI;IAEI,sCAAA;ELqtHR;;EKvtHI;IAEI,oCAAA;ELytHR;;EK3tHI;IAEI,kCAAA;EL6tHR;;EK/tHI;IAEI,yCAAA;ELiuHR;;EKnuHI;IAEI,wCAAA;ELquHR;;EKvuHI;IAEI,wCAAA;ELyuHR;;EK3uHI;IAEI,kCAAA;EL6uHR;;EK/uHI;IAEI,gCAAA;ELivHR;;EKnvHI;IAEI,8BAAA;ELqvHR;;EKvvHI;IAEI,gCAAA;ELyvHR;;EK3vHI;IAEI,+BAAA;EL6vHR;;EK/vHI;IAEI,oCAAA;ELiwHR;;EKnwHI;IAEI,kCAAA;ELqwHR;;EKvwHI;IAEI,gCAAA;ELywHR;;EK3wHI;IAEI,uCAAA;EL6wHR;;EK/wHI;IAEI,sCAAA;ELixHR;;EKnxHI;IAEI,iCAAA;ELqxHR;;EKvxHI;IAEI,2BAAA;ELyxHR;;EK3xHI;IAEI,iCAAA;EL6xHR;;EK/xHI;IAEI,+BAAA;ELiyHR;;EKnyHI;IAEI,6BAAA;ELqyHR;;EKvyHI;IAEI,+BAAA;ELyyHR;;EK3yHI;IAEI,8BAAA;EL6yHR;;EK/yHI;IAEI,oBAAA;ELizHR;;EKnzHI;IAEI,mBAAA;ELqzHR;;EKvzHI;IAEI,mBAAA;ELyzHR;;EK3zHI;IAEI,mBAAA;EL6zHR;;EK/zHI;IAEI,mBAAA;ELi0HR;;EKn0HI;IAEI,mBAAA;ELq0HR;;EKv0HI;IAEI,mBAAA;ELy0HR;;EK30HI;IAEI,mBAAA;EL60HR;;EK/0HI;IAEI,oBAAA;ELi1HR;;EKn1HI;IAEI,0BAAA;ELq1HR;;EKv1HI;IAEI,yBAAA;ELy1HR;;EK31HI;IAEI,uBAAA;EL61HR;;EK/1HI;IAEI,yBAAA;ELi2HR;;EKn2HI;IAEI,uBAAA;ELq2HR;;EKv2HI;IAEI,uBAAA;ELy2HR;;EK32HI;IAEI,0BAAA;IAAA,yBAAA;EL82HR;;EKh3HI;IAEI,gCAAA;IAAA,+BAAA;ELm3HR;;EKr3HI;IAEI,+BAAA;IAAA,8BAAA;ELw3HR;;EK13HI;IAEI,6BAAA;IAAA,4BAAA;EL63HR;;EK/3HI;IAEI,+BAAA;IAAA,8BAAA;ELk4HR;;EKp4HI;IAEI,6BAAA;IAAA,4BAAA;ELu4HR;;EKz4HI;IAEI,6BAAA;IAAA,4BAAA;EL44HR;;EK94HI;IAEI,wBAAA;IAAA,2BAAA;ELi5HR;;EKn5HI;IAEI,8BAAA;IAAA,iCAAA;ELs5HR;;EKx5HI;IAEI,6BAAA;IAAA,gCAAA;EL25HR;;EK75HI;IAEI,2BAAA;IAAA,8BAAA;ELg6HR;;EKl6HI;IAEI,6BAAA;IAAA,gCAAA;ELq6HR;;EKv6HI;IAEI,2BAAA;IAAA,8BAAA;EL06HR;;EK56HI;IAEI,2BAAA;IAAA,8BAAA;EL+6HR;;EKj7HI;IAEI,wBAAA;ELm7HR;;EKr7HI;IAEI,8BAAA;ELu7HR;;EKz7HI;IAEI,6BAAA;EL27HR;;EK77HI;IAEI,2BAAA;EL+7HR;;EKj8HI;IAEI,6BAAA;ELm8HR;;EKr8HI;IAEI,2BAAA;ELu8HR;;EKz8HI;IAEI,2BAAA;EL28HR;;EK78HI;IAEI,0BAAA;EL+8HR;;EKj9HI;IAEI,gCAAA;ELm9HR;;EKr9HI;IAEI,+BAAA;ELu9HR;;EKz9HI;IAEI,6BAAA;EL29HR;;EK79HI;IAEI,+BAAA;EL+9HR;;EKj+HI;IAEI,6BAAA;ELm+HR;;EKr+HI;IAEI,6BAAA;ELu+HR;;EKz+HI;IAEI,2BAAA;EL2+HR;;EK7+HI;IAEI,iCAAA;EL++HR;;EKj/HI;IAEI,gCAAA;ELm/HR;;EKr/HI;IAEI,8BAAA;ELu/HR;;EKz/HI;IAEI,gCAAA;EL2/HR;;EK7/HI;IAEI,8BAAA;EL+/HR;;EKjgII;IAEI,8BAAA;ELmgIR;;EKrgII;IAEI,yBAAA;ELugIR;;EKzgII;IAEI,+BAAA;EL2gIR;;EK7gII;IAEI,8BAAA;EL+gIR;;EKjhII;IAEI,4BAAA;ELmhIR;;EKrhII;IAEI,8BAAA;ELuhIR;;EKzhII;IAEI,4BAAA;EL2hIR;;EK7hII;IAEI,4BAAA;EL+hIR;;EKjiII;IAEI,qBAAA;ELmiIR;;EKriII;IAEI,2BAAA;ELuiIR;;EKziII;IAEI,0BAAA;EL2iIR;;EK7iII;IAEI,wBAAA;EL+iIR;;EKjjII;IAEI,0BAAA;ELmjIR;;EKrjII;IAEI,wBAAA;ELujIR;;EKzjII;IAEI,2BAAA;IAAA,0BAAA;EL4jIR;;EK9jII;IAEI,iCAAA;IAAA,gCAAA;ELikIR;;EKnkII;IAEI,gCAAA;IAAA,+BAAA;ELskIR;;EKxkII;IAEI,8BAAA;IAAA,6BAAA;EL2kIR;;EK7kII;IAEI,gCAAA;IAAA,+BAAA;ELglIR;;EKllII;IAEI,8BAAA;IAAA,6BAAA;ELqlIR;;EKvlII;IAEI,yBAAA;IAAA,4BAAA;EL0lIR;;EK5lII;IAEI,+BAAA;IAAA,kCAAA;EL+lIR;;EKjmII;IAEI,8BAAA;IAAA,iCAAA;ELomIR;;EKtmII;IAEI,4BAAA;IAAA,+BAAA;ELymIR;;EK3mII;IAEI,8BAAA;IAAA,iCAAA;EL8mIR;;EKhnII;IAEI,4BAAA;IAAA,+BAAA;ELmnIR;;EKrnII;IAEI,yBAAA;ELunIR;;EKznII;IAEI,+BAAA;EL2nIR;;EK7nII;IAEI,8BAAA;EL+nIR;;EKjoII;IAEI,4BAAA;ELmoIR;;EKroII;IAEI,8BAAA;ELuoIR;;EKzoII;IAEI,4BAAA;EL2oIR;;EK7oII;IAEI,2BAAA;EL+oIR;;EKjpII;IAEI,iCAAA;ELmpIR;;EKrpII;IAEI,gCAAA;ELupIR;;EKzpII;IAEI,8BAAA;EL2pIR;;EK7pII;IAEI,gCAAA;EL+pIR;;EKjqII;IAEI,8BAAA;ELmqIR;;EKrqII;IAEI,4BAAA;ELuqIR;;EKzqII;IAEI,kCAAA;EL2qIR;;EK7qII;IAEI,iCAAA;EL+qIR;;EKjrII;IAEI,+BAAA;ELmrIR;;EKrrII;IAEI,iCAAA;ELurIR;;EKzrII;IAEI,+BAAA;EL2rIR;;EK7rII;IAEI,0BAAA;EL+rIR;;EKjsII;IAEI,gCAAA;ELmsIR;;EKrsII;IAEI,+BAAA;ELusIR;;EKzsII;IAEI,6BAAA;EL2sIR;;EK7sII;IAEI,+BAAA;EL+sIR;;EKjtII;IAEI,6BAAA;ELmtIR;AACF;AChsII;EItBE;IAEI,0BAAA;ELwtIR;;EK1tII;IAEI,gCAAA;EL4tIR;;EK9tII;IAEI,yBAAA;ELguIR;;EKluII;IAEI,wBAAA;ELouIR;;EKtuII;IAEI,yBAAA;ELwuIR;;EK1uII;IAEI,6BAAA;EL4uIR;;EK9uII;IAEI,8BAAA;ELgvIR;;EKlvII;IAEI,wBAAA;ELovIR;;EKtvII;IAEI,+BAAA;ELwvIR;;EK1vII;IAEI,wBAAA;EL4vIR;;EK9vII;IAEI,yBAAA;ELgwIR;;EKlwII;IAEI,8BAAA;ELowIR;;EKtwII;IAEI,iCAAA;ELwwIR;;EK1wII;IAEI,sCAAA;EL4wIR;;EK9wII;IAEI,yCAAA;ELgxIR;;EKlxII;IAEI,uBAAA;ELoxIR;;EKtxII;IAEI,uBAAA;ELwxIR;;EK1xII;IAEI,yBAAA;EL4xIR;;EK9xII;IAEI,yBAAA;ELgyIR;;EKlyII;IAEI,0BAAA;ELoyIR;;EKtyII;IAEI,4BAAA;ELwyIR;;EK1yII;IAEI,kCAAA;EL4yIR;;EK9yII;IAEI,sCAAA;ELgzIR;;EKlzII;IAEI,oCAAA;ELozIR;;EKtzII;IAEI,kCAAA;ELwzIR;;EK1zII;IAEI,yCAAA;EL4zIR;;EK9zII;IAEI,wCAAA;ELg0IR;;EKl0II;IAEI,wCAAA;ELo0IR;;EKt0II;IAEI,kCAAA;ELw0IR;;EK10II;IAEI,gCAAA;EL40IR;;EK90II;IAEI,8BAAA;ELg1IR;;EKl1II;IAEI,gCAAA;ELo1IR;;EKt1II;IAEI,+BAAA;ELw1IR;;EK11II;IAEI,oCAAA;EL41IR;;EK91II;IAEI,kCAAA;ELg2IR;;EKl2II;IAEI,gCAAA;ELo2IR;;EKt2II;IAEI,uCAAA;ELw2IR;;EK12II;IAEI,sCAAA;EL42IR;;EK92II;IAEI,iCAAA;ELg3IR;;EKl3II;IAEI,2BAAA;ELo3IR;;EKt3II;IAEI,iCAAA;ELw3IR;;EK13II;IAEI,+BAAA;EL43IR;;EK93II;IAEI,6BAAA;ELg4IR;;EKl4II;IAEI,+BAAA;ELo4IR;;EKt4II;IAEI,8BAAA;ELw4IR;;EK14II;IAEI,oBAAA;EL44IR;;EK94II;IAEI,mBAAA;ELg5IR;;EKl5II;IAEI,mBAAA;ELo5IR;;EKt5II;IAEI,mBAAA;ELw5IR;;EK15II;IAEI,mBAAA;EL45IR;;EK95II;IAEI,mBAAA;ELg6IR;;EKl6II;IAEI,mBAAA;ELo6IR;;EKt6II;IAEI,mBAAA;ELw6IR;;EK16II;IAEI,oBAAA;EL46IR;;EK96II;IAEI,0BAAA;ELg7IR;;EKl7II;IAEI,yBAAA;ELo7IR;;EKt7II;IAEI,uBAAA;ELw7IR;;EK17II;IAEI,yBAAA;EL47IR;;EK97II;IAEI,uBAAA;ELg8IR;;EKl8II;IAEI,uBAAA;ELo8IR;;EKt8II;IAEI,0BAAA;IAAA,yBAAA;ELy8IR;;EK38II;IAEI,gCAAA;IAAA,+BAAA;EL88IR;;EKh9II;IAEI,+BAAA;IAAA,8BAAA;ELm9IR;;EKr9II;IAEI,6BAAA;IAAA,4BAAA;ELw9IR;;EK19II;IAEI,+BAAA;IAAA,8BAAA;EL69IR;;EK/9II;IAEI,6BAAA;IAAA,4BAAA;ELk+IR;;EKp+II;IAEI,6BAAA;IAAA,4BAAA;ELu+IR;;EKz+II;IAEI,wBAAA;IAAA,2BAAA;EL4+IR;;EK9+II;IAEI,8BAAA;IAAA,iCAAA;ELi/IR;;EKn/II;IAEI,6BAAA;IAAA,gCAAA;ELs/IR;;EKx/II;IAEI,2BAAA;IAAA,8BAAA;EL2/IR;;EK7/II;IAEI,6BAAA;IAAA,gCAAA;ELggJR;;EKlgJI;IAEI,2BAAA;IAAA,8BAAA;ELqgJR;;EKvgJI;IAEI,2BAAA;IAAA,8BAAA;EL0gJR;;EK5gJI;IAEI,wBAAA;EL8gJR;;EKhhJI;IAEI,8BAAA;ELkhJR;;EKphJI;IAEI,6BAAA;ELshJR;;EKxhJI;IAEI,2BAAA;EL0hJR;;EK5hJI;IAEI,6BAAA;EL8hJR;;EKhiJI;IAEI,2BAAA;ELkiJR;;EKpiJI;IAEI,2BAAA;ELsiJR;;EKxiJI;IAEI,0BAAA;EL0iJR;;EK5iJI;IAEI,gCAAA;EL8iJR;;EKhjJI;IAEI,+BAAA;ELkjJR;;EKpjJI;IAEI,6BAAA;ELsjJR;;EKxjJI;IAEI,+BAAA;EL0jJR;;EK5jJI;IAEI,6BAAA;EL8jJR;;EKhkJI;IAEI,6BAAA;ELkkJR;;EKpkJI;IAEI,2BAAA;ELskJR;;EKxkJI;IAEI,iCAAA;EL0kJR;;EK5kJI;IAEI,gCAAA;EL8kJR;;EKhlJI;IAEI,8BAAA;ELklJR;;EKplJI;IAEI,gCAAA;ELslJR;;EKxlJI;IAEI,8BAAA;EL0lJR;;EK5lJI;IAEI,8BAAA;EL8lJR;;EKhmJI;IAEI,yBAAA;ELkmJR;;EKpmJI;IAEI,+BAAA;ELsmJR;;EKxmJI;IAEI,8BAAA;EL0mJR;;EK5mJI;IAEI,4BAAA;EL8mJR;;EKhnJI;IAEI,8BAAA;ELknJR;;EKpnJI;IAEI,4BAAA;ELsnJR;;EKxnJI;IAEI,4BAAA;EL0nJR;;EK5nJI;IAEI,qBAAA;EL8nJR;;EKhoJI;IAEI,2BAAA;ELkoJR;;EKpoJI;IAEI,0BAAA;ELsoJR;;EKxoJI;IAEI,wBAAA;EL0oJR;;EK5oJI;IAEI,0BAAA;EL8oJR;;EKhpJI;IAEI,wBAAA;ELkpJR;;EKppJI;IAEI,2BAAA;IAAA,0BAAA;ELupJR;;EKzpJI;IAEI,iCAAA;IAAA,gCAAA;EL4pJR;;EK9pJI;IAEI,gCAAA;IAAA,+BAAA;ELiqJR;;EKnqJI;IAEI,8BAAA;IAAA,6BAAA;ELsqJR;;EKxqJI;IAEI,gCAAA;IAAA,+BAAA;EL2qJR;;EK7qJI;IAEI,8BAAA;IAAA,6BAAA;ELgrJR;;EKlrJI;IAEI,yBAAA;IAAA,4BAAA;ELqrJR;;EKvrJI;IAEI,+BAAA;IAAA,kCAAA;EL0rJR;;EK5rJI;IAEI,8BAAA;IAAA,iCAAA;EL+rJR;;EKjsJI;IAEI,4BAAA;IAAA,+BAAA;ELosJR;;EKtsJI;IAEI,8BAAA;IAAA,iCAAA;ELysJR;;EK3sJI;IAEI,4BAAA;IAAA,+BAAA;EL8sJR;;EKhtJI;IAEI,yBAAA;ELktJR;;EKptJI;IAEI,+BAAA;ELstJR;;EKxtJI;IAEI,8BAAA;EL0tJR;;EK5tJI;IAEI,4BAAA;EL8tJR;;EKhuJI;IAEI,8BAAA;ELkuJR;;EKpuJI;IAEI,4BAAA;ELsuJR;;EKxuJI;IAEI,2BAAA;EL0uJR;;EK5uJI;IAEI,iCAAA;EL8uJR;;EKhvJI;IAEI,gCAAA;ELkvJR;;EKpvJI;IAEI,8BAAA;ELsvJR;;EKxvJI;IAEI,gCAAA;EL0vJR;;EK5vJI;IAEI,8BAAA;EL8vJR;;EKhwJI;IAEI,4BAAA;ELkwJR;;EKpwJI;IAEI,kCAAA;ELswJR;;EKxwJI;IAEI,iCAAA;EL0wJR;;EK5wJI;IAEI,+BAAA;EL8wJR;;EKhxJI;IAEI,iCAAA;ELkxJR;;EKpxJI;IAEI,+BAAA;ELsxJR;;EKxxJI;IAEI,0BAAA;EL0xJR;;EK5xJI;IAEI,gCAAA;EL8xJR;;EKhyJI;IAEI,+BAAA;ELkyJR;;EKpyJI;IAEI,6BAAA;ELsyJR;;EKxyJI;IAEI,+BAAA;EL0yJR;;EK5yJI;IAEI,6BAAA;EL8yJR;AACF;AMpzJA;EDGM;IAEI,0BAAA;ELmzJR;;EKrzJI;IAEI,gCAAA;ELuzJR;;EKzzJI;IAEI,yBAAA;EL2zJR;;EK7zJI;IAEI,wBAAA;EL+zJR;;EKj0JI;IAEI,yBAAA;ELm0JR;;EKr0JI;IAEI,6BAAA;ELu0JR;;EKz0JI;IAEI,8BAAA;EL20JR;;EK70JI;IAEI,wBAAA;EL+0JR;;EKj1JI;IAEI,+BAAA;ELm1JR;;EKr1JI;IAEI,wBAAA;ELu1JR;AACF", "file": "bootstrap-grid.css", "sourcesContent": ["/*!\n * Bootstrap Grid v5.0.0-alpha3 (https://getbootstrap.com/)\n * Copyright 2011-2020 The Bootstrap Authors\n * Copyright 2011-2020 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n\n$include-column-box-sizing: true !default;\n\n@import \"functions\";\n@import \"variables\";\n\n@import \"mixins/lists\";\n@import \"mixins/breakpoints\";\n@import \"mixins/container\";\n@import \"mixins/grid\";\n@import \"mixins/utilities\";\n\n@import \"vendor/rfs\";\n\n@import \"containers\";\n@import \"grid\";\n\n@import \"utilities\";\n// Only use the utilities we need\n// stylelint-disable-next-line scss/dollar-variable-default\n$utilities: map-get-multiple(\n  $utilities,\n  (\n    \"display\",\n    \"order\",\n    \"flex\",\n    \"flex-direction\",\n    \"flex-grow\",\n    \"flex-shrink\",\n    \"flex-wrap\",\n    \"justify-content\",\n    \"align-items\",\n    \"align-content\",\n    \"align-self\",\n    \"margin\",\n    \"margin-x\",\n    \"margin-y\",\n    \"margin-top\",\n    \"margin-right\",\n    \"margin-bottom\",\n    \"margin-left\",\n    \"negative-margin\",\n    \"negative-margin-x\",\n    \"negative-margin-y\",\n    \"negative-margin-top\",\n    \"negative-margin-right\",\n    \"negative-margin-bottom\",\n    \"negative-margin-left\",\n    \"padding\",\n    \"padding-x\",\n    \"padding-y\",\n    \"padding-top\",\n    \"padding-right\",\n    \"padding-bottom\",\n    \"padding-left\",\n  )\n);\n\n@import \"utilities/api\";\n", "// Container widths\n//\n// Set the container width, and override it for fixed navbars in media queries.\n\n@if $enable-grid-classes {\n  // Single container class with breakpoint max-widths\n  .container,\n  // 100% wide container at all breakpoints\n  .container-fluid {\n    @include make-container();\n  }\n\n  // Responsive containers that are 100% wide until a breakpoint\n  @each $breakpoint, $container-max-width in $container-max-widths {\n    .container-#{$breakpoint} {\n      @extend .container-fluid;\n    }\n\n    @include media-breakpoint-up($breakpoint, $grid-breakpoints) {\n      %responsive-container-#{$breakpoint} {\n        max-width: $container-max-width;\n      }\n\n      // Extend each breakpoint which is smaller or equal to the current breakpoint\n      $extend-breakpoint: true;\n\n      @each $name, $width in $grid-breakpoints {\n        @if ($extend-breakpoint) {\n          .container#{breakpoint-infix($name, $grid-breakpoints)} {\n            @extend %responsive-container-#{$breakpoint};\n          }\n\n          // Once the current breakpoint is reached, stop extending\n          @if ($breakpoint == $name) {\n            $extend-breakpoint: false;\n          }\n        }\n      }\n    }\n  }\n}\n", "// Container mixins\n\n@mixin make-container($gutter: $container-padding-x) {\n  --bs-gutter-x: #{$gutter};\n\n  width: 100%;\n  padding-right: calc(var(--bs-gutter-x) / 2); // stylelint-disable-line function-disallowed-list\n  padding-left: calc(var(--bs-gutter-x) / 2); // stylelint-disable-line function-disallowed-list\n  margin-right: auto;\n  margin-left: auto;\n}\n", "/*!\n * Bootstrap Grid v5.0.0-alpha3 (https://getbootstrap.com/)\n * Copyright 2011-2020 The Bootstrap Authors\n * Copyright 2011-2020 Twitter, Inc.\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n */\n.container,\n.container-fluid,\n.container-xxl,\n.container-xl,\n.container-lg,\n.container-md,\n.container-sm {\n  --bs-gutter-x: 1.5rem;\n  width: 100%;\n  padding-right: calc(var(--bs-gutter-x) / 2);\n  padding-left: calc(var(--bs-gutter-x) / 2);\n  margin-right: auto;\n  margin-left: auto;\n}\n\n@media (min-width: 576px) {\n  .container-sm, .container {\n    max-width: 540px;\n  }\n}\n@media (min-width: 768px) {\n  .container-md, .container-sm, .container {\n    max-width: 720px;\n  }\n}\n@media (min-width: 992px) {\n  .container-lg, .container-md, .container-sm, .container {\n    max-width: 960px;\n  }\n}\n@media (min-width: 1200px) {\n  .container-xl, .container-lg, .container-md, .container-sm, .container {\n    max-width: 1140px;\n  }\n}\n@media (min-width: 1400px) {\n  .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {\n    max-width: 1320px;\n  }\n}\n.row {\n  --bs-gutter-x: 1.5rem;\n  --bs-gutter-y: 0;\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: calc(var(--bs-gutter-y) * -1);\n  margin-right: calc(var(--bs-gutter-x) / -2);\n  margin-left: calc(var(--bs-gutter-x) / -2);\n}\n.row > * {\n  box-sizing: border-box;\n  flex-shrink: 0;\n  width: 100%;\n  max-width: 100%;\n  padding-right: calc(var(--bs-gutter-x) / 2);\n  padding-left: calc(var(--bs-gutter-x) / 2);\n  margin-top: var(--bs-gutter-y);\n}\n\n.col {\n  flex: 1 0 0%;\n}\n\n.row-cols-auto > * {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n.row-cols-1 > * {\n  flex: 0 0 auto;\n  width: 100%;\n}\n\n.row-cols-2 > * {\n  flex: 0 0 auto;\n  width: 50%;\n}\n\n.row-cols-3 > * {\n  flex: 0 0 auto;\n  width: 33.3333333333%;\n}\n\n.row-cols-4 > * {\n  flex: 0 0 auto;\n  width: 25%;\n}\n\n.row-cols-5 > * {\n  flex: 0 0 auto;\n  width: 20%;\n}\n\n.row-cols-6 > * {\n  flex: 0 0 auto;\n  width: 16.6666666667%;\n}\n\n.col-auto {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n.col-1 {\n  flex: 0 0 auto;\n  width: 8.3333333333%;\n}\n\n.col-2 {\n  flex: 0 0 auto;\n  width: 16.6666666667%;\n}\n\n.col-3 {\n  flex: 0 0 auto;\n  width: 25%;\n}\n\n.col-4 {\n  flex: 0 0 auto;\n  width: 33.3333333333%;\n}\n\n.col-5 {\n  flex: 0 0 auto;\n  width: 41.6666666667%;\n}\n\n.col-6 {\n  flex: 0 0 auto;\n  width: 50%;\n}\n\n.col-7 {\n  flex: 0 0 auto;\n  width: 58.3333333333%;\n}\n\n.col-8 {\n  flex: 0 0 auto;\n  width: 66.6666666667%;\n}\n\n.col-9 {\n  flex: 0 0 auto;\n  width: 75%;\n}\n\n.col-10 {\n  flex: 0 0 auto;\n  width: 83.3333333333%;\n}\n\n.col-11 {\n  flex: 0 0 auto;\n  width: 91.6666666667%;\n}\n\n.col-12 {\n  flex: 0 0 auto;\n  width: 100%;\n}\n\n.offset-1 {\n  margin-left: 8.3333333333%;\n}\n\n.offset-2 {\n  margin-left: 16.6666666667%;\n}\n\n.offset-3 {\n  margin-left: 25%;\n}\n\n.offset-4 {\n  margin-left: 33.3333333333%;\n}\n\n.offset-5 {\n  margin-left: 41.6666666667%;\n}\n\n.offset-6 {\n  margin-left: 50%;\n}\n\n.offset-7 {\n  margin-left: 58.3333333333%;\n}\n\n.offset-8 {\n  margin-left: 66.6666666667%;\n}\n\n.offset-9 {\n  margin-left: 75%;\n}\n\n.offset-10 {\n  margin-left: 83.3333333333%;\n}\n\n.offset-11 {\n  margin-left: 91.6666666667%;\n}\n\n.g-0,\n.gx-0 {\n  --bs-gutter-x: 0;\n}\n\n.g-0,\n.gy-0 {\n  --bs-gutter-y: 0;\n}\n\n.g-1,\n.gx-1 {\n  --bs-gutter-x: 0.25rem;\n}\n\n.g-1,\n.gy-1 {\n  --bs-gutter-y: 0.25rem;\n}\n\n.g-2,\n.gx-2 {\n  --bs-gutter-x: 0.5rem;\n}\n\n.g-2,\n.gy-2 {\n  --bs-gutter-y: 0.5rem;\n}\n\n.g-3,\n.gx-3 {\n  --bs-gutter-x: 1rem;\n}\n\n.g-3,\n.gy-3 {\n  --bs-gutter-y: 1rem;\n}\n\n.g-4,\n.gx-4 {\n  --bs-gutter-x: 1.5rem;\n}\n\n.g-4,\n.gy-4 {\n  --bs-gutter-y: 1.5rem;\n}\n\n.g-5,\n.gx-5 {\n  --bs-gutter-x: 3rem;\n}\n\n.g-5,\n.gy-5 {\n  --bs-gutter-y: 3rem;\n}\n\n@media (min-width: 576px) {\n  .col-sm {\n    flex: 1 0 0%;\n  }\n\n  .row-cols-sm-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .row-cols-sm-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .row-cols-sm-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .row-cols-sm-3 > * {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .row-cols-sm-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .row-cols-sm-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n\n  .row-cols-sm-6 > * {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-sm-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .col-sm-1 {\n    flex: 0 0 auto;\n    width: 8.3333333333%;\n  }\n\n  .col-sm-2 {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-sm-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .col-sm-4 {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .col-sm-5 {\n    flex: 0 0 auto;\n    width: 41.6666666667%;\n  }\n\n  .col-sm-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .col-sm-7 {\n    flex: 0 0 auto;\n    width: 58.3333333333%;\n  }\n\n  .col-sm-8 {\n    flex: 0 0 auto;\n    width: 66.6666666667%;\n  }\n\n  .col-sm-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n\n  .col-sm-10 {\n    flex: 0 0 auto;\n    width: 83.3333333333%;\n  }\n\n  .col-sm-11 {\n    flex: 0 0 auto;\n    width: 91.6666666667%;\n  }\n\n  .col-sm-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .offset-sm-0 {\n    margin-left: 0;\n  }\n\n  .offset-sm-1 {\n    margin-left: 8.3333333333%;\n  }\n\n  .offset-sm-2 {\n    margin-left: 16.6666666667%;\n  }\n\n  .offset-sm-3 {\n    margin-left: 25%;\n  }\n\n  .offset-sm-4 {\n    margin-left: 33.3333333333%;\n  }\n\n  .offset-sm-5 {\n    margin-left: 41.6666666667%;\n  }\n\n  .offset-sm-6 {\n    margin-left: 50%;\n  }\n\n  .offset-sm-7 {\n    margin-left: 58.3333333333%;\n  }\n\n  .offset-sm-8 {\n    margin-left: 66.6666666667%;\n  }\n\n  .offset-sm-9 {\n    margin-left: 75%;\n  }\n\n  .offset-sm-10 {\n    margin-left: 83.3333333333%;\n  }\n\n  .offset-sm-11 {\n    margin-left: 91.6666666667%;\n  }\n\n  .g-sm-0,\n.gx-sm-0 {\n    --bs-gutter-x: 0;\n  }\n\n  .g-sm-0,\n.gy-sm-0 {\n    --bs-gutter-y: 0;\n  }\n\n  .g-sm-1,\n.gx-sm-1 {\n    --bs-gutter-x: 0.25rem;\n  }\n\n  .g-sm-1,\n.gy-sm-1 {\n    --bs-gutter-y: 0.25rem;\n  }\n\n  .g-sm-2,\n.gx-sm-2 {\n    --bs-gutter-x: 0.5rem;\n  }\n\n  .g-sm-2,\n.gy-sm-2 {\n    --bs-gutter-y: 0.5rem;\n  }\n\n  .g-sm-3,\n.gx-sm-3 {\n    --bs-gutter-x: 1rem;\n  }\n\n  .g-sm-3,\n.gy-sm-3 {\n    --bs-gutter-y: 1rem;\n  }\n\n  .g-sm-4,\n.gx-sm-4 {\n    --bs-gutter-x: 1.5rem;\n  }\n\n  .g-sm-4,\n.gy-sm-4 {\n    --bs-gutter-y: 1.5rem;\n  }\n\n  .g-sm-5,\n.gx-sm-5 {\n    --bs-gutter-x: 3rem;\n  }\n\n  .g-sm-5,\n.gy-sm-5 {\n    --bs-gutter-y: 3rem;\n  }\n}\n@media (min-width: 768px) {\n  .col-md {\n    flex: 1 0 0%;\n  }\n\n  .row-cols-md-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .row-cols-md-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .row-cols-md-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .row-cols-md-3 > * {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .row-cols-md-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .row-cols-md-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n\n  .row-cols-md-6 > * {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-md-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .col-md-1 {\n    flex: 0 0 auto;\n    width: 8.3333333333%;\n  }\n\n  .col-md-2 {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-md-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .col-md-4 {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .col-md-5 {\n    flex: 0 0 auto;\n    width: 41.6666666667%;\n  }\n\n  .col-md-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .col-md-7 {\n    flex: 0 0 auto;\n    width: 58.3333333333%;\n  }\n\n  .col-md-8 {\n    flex: 0 0 auto;\n    width: 66.6666666667%;\n  }\n\n  .col-md-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n\n  .col-md-10 {\n    flex: 0 0 auto;\n    width: 83.3333333333%;\n  }\n\n  .col-md-11 {\n    flex: 0 0 auto;\n    width: 91.6666666667%;\n  }\n\n  .col-md-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .offset-md-0 {\n    margin-left: 0;\n  }\n\n  .offset-md-1 {\n    margin-left: 8.3333333333%;\n  }\n\n  .offset-md-2 {\n    margin-left: 16.6666666667%;\n  }\n\n  .offset-md-3 {\n    margin-left: 25%;\n  }\n\n  .offset-md-4 {\n    margin-left: 33.3333333333%;\n  }\n\n  .offset-md-5 {\n    margin-left: 41.6666666667%;\n  }\n\n  .offset-md-6 {\n    margin-left: 50%;\n  }\n\n  .offset-md-7 {\n    margin-left: 58.3333333333%;\n  }\n\n  .offset-md-8 {\n    margin-left: 66.6666666667%;\n  }\n\n  .offset-md-9 {\n    margin-left: 75%;\n  }\n\n  .offset-md-10 {\n    margin-left: 83.3333333333%;\n  }\n\n  .offset-md-11 {\n    margin-left: 91.6666666667%;\n  }\n\n  .g-md-0,\n.gx-md-0 {\n    --bs-gutter-x: 0;\n  }\n\n  .g-md-0,\n.gy-md-0 {\n    --bs-gutter-y: 0;\n  }\n\n  .g-md-1,\n.gx-md-1 {\n    --bs-gutter-x: 0.25rem;\n  }\n\n  .g-md-1,\n.gy-md-1 {\n    --bs-gutter-y: 0.25rem;\n  }\n\n  .g-md-2,\n.gx-md-2 {\n    --bs-gutter-x: 0.5rem;\n  }\n\n  .g-md-2,\n.gy-md-2 {\n    --bs-gutter-y: 0.5rem;\n  }\n\n  .g-md-3,\n.gx-md-3 {\n    --bs-gutter-x: 1rem;\n  }\n\n  .g-md-3,\n.gy-md-3 {\n    --bs-gutter-y: 1rem;\n  }\n\n  .g-md-4,\n.gx-md-4 {\n    --bs-gutter-x: 1.5rem;\n  }\n\n  .g-md-4,\n.gy-md-4 {\n    --bs-gutter-y: 1.5rem;\n  }\n\n  .g-md-5,\n.gx-md-5 {\n    --bs-gutter-x: 3rem;\n  }\n\n  .g-md-5,\n.gy-md-5 {\n    --bs-gutter-y: 3rem;\n  }\n}\n@media (min-width: 992px) {\n  .col-lg {\n    flex: 1 0 0%;\n  }\n\n  .row-cols-lg-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .row-cols-lg-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .row-cols-lg-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .row-cols-lg-3 > * {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .row-cols-lg-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .row-cols-lg-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n\n  .row-cols-lg-6 > * {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-lg-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .col-lg-1 {\n    flex: 0 0 auto;\n    width: 8.3333333333%;\n  }\n\n  .col-lg-2 {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-lg-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .col-lg-4 {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .col-lg-5 {\n    flex: 0 0 auto;\n    width: 41.6666666667%;\n  }\n\n  .col-lg-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .col-lg-7 {\n    flex: 0 0 auto;\n    width: 58.3333333333%;\n  }\n\n  .col-lg-8 {\n    flex: 0 0 auto;\n    width: 66.6666666667%;\n  }\n\n  .col-lg-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n\n  .col-lg-10 {\n    flex: 0 0 auto;\n    width: 83.3333333333%;\n  }\n\n  .col-lg-11 {\n    flex: 0 0 auto;\n    width: 91.6666666667%;\n  }\n\n  .col-lg-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .offset-lg-0 {\n    margin-left: 0;\n  }\n\n  .offset-lg-1 {\n    margin-left: 8.3333333333%;\n  }\n\n  .offset-lg-2 {\n    margin-left: 16.6666666667%;\n  }\n\n  .offset-lg-3 {\n    margin-left: 25%;\n  }\n\n  .offset-lg-4 {\n    margin-left: 33.3333333333%;\n  }\n\n  .offset-lg-5 {\n    margin-left: 41.6666666667%;\n  }\n\n  .offset-lg-6 {\n    margin-left: 50%;\n  }\n\n  .offset-lg-7 {\n    margin-left: 58.3333333333%;\n  }\n\n  .offset-lg-8 {\n    margin-left: 66.6666666667%;\n  }\n\n  .offset-lg-9 {\n    margin-left: 75%;\n  }\n\n  .offset-lg-10 {\n    margin-left: 83.3333333333%;\n  }\n\n  .offset-lg-11 {\n    margin-left: 91.6666666667%;\n  }\n\n  .g-lg-0,\n.gx-lg-0 {\n    --bs-gutter-x: 0;\n  }\n\n  .g-lg-0,\n.gy-lg-0 {\n    --bs-gutter-y: 0;\n  }\n\n  .g-lg-1,\n.gx-lg-1 {\n    --bs-gutter-x: 0.25rem;\n  }\n\n  .g-lg-1,\n.gy-lg-1 {\n    --bs-gutter-y: 0.25rem;\n  }\n\n  .g-lg-2,\n.gx-lg-2 {\n    --bs-gutter-x: 0.5rem;\n  }\n\n  .g-lg-2,\n.gy-lg-2 {\n    --bs-gutter-y: 0.5rem;\n  }\n\n  .g-lg-3,\n.gx-lg-3 {\n    --bs-gutter-x: 1rem;\n  }\n\n  .g-lg-3,\n.gy-lg-3 {\n    --bs-gutter-y: 1rem;\n  }\n\n  .g-lg-4,\n.gx-lg-4 {\n    --bs-gutter-x: 1.5rem;\n  }\n\n  .g-lg-4,\n.gy-lg-4 {\n    --bs-gutter-y: 1.5rem;\n  }\n\n  .g-lg-5,\n.gx-lg-5 {\n    --bs-gutter-x: 3rem;\n  }\n\n  .g-lg-5,\n.gy-lg-5 {\n    --bs-gutter-y: 3rem;\n  }\n}\n@media (min-width: 1200px) {\n  .col-xl {\n    flex: 1 0 0%;\n  }\n\n  .row-cols-xl-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .row-cols-xl-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .row-cols-xl-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .row-cols-xl-3 > * {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .row-cols-xl-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .row-cols-xl-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n\n  .row-cols-xl-6 > * {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-xl-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .col-xl-1 {\n    flex: 0 0 auto;\n    width: 8.3333333333%;\n  }\n\n  .col-xl-2 {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-xl-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .col-xl-4 {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .col-xl-5 {\n    flex: 0 0 auto;\n    width: 41.6666666667%;\n  }\n\n  .col-xl-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .col-xl-7 {\n    flex: 0 0 auto;\n    width: 58.3333333333%;\n  }\n\n  .col-xl-8 {\n    flex: 0 0 auto;\n    width: 66.6666666667%;\n  }\n\n  .col-xl-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n\n  .col-xl-10 {\n    flex: 0 0 auto;\n    width: 83.3333333333%;\n  }\n\n  .col-xl-11 {\n    flex: 0 0 auto;\n    width: 91.6666666667%;\n  }\n\n  .col-xl-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .offset-xl-0 {\n    margin-left: 0;\n  }\n\n  .offset-xl-1 {\n    margin-left: 8.3333333333%;\n  }\n\n  .offset-xl-2 {\n    margin-left: 16.6666666667%;\n  }\n\n  .offset-xl-3 {\n    margin-left: 25%;\n  }\n\n  .offset-xl-4 {\n    margin-left: 33.3333333333%;\n  }\n\n  .offset-xl-5 {\n    margin-left: 41.6666666667%;\n  }\n\n  .offset-xl-6 {\n    margin-left: 50%;\n  }\n\n  .offset-xl-7 {\n    margin-left: 58.3333333333%;\n  }\n\n  .offset-xl-8 {\n    margin-left: 66.6666666667%;\n  }\n\n  .offset-xl-9 {\n    margin-left: 75%;\n  }\n\n  .offset-xl-10 {\n    margin-left: 83.3333333333%;\n  }\n\n  .offset-xl-11 {\n    margin-left: 91.6666666667%;\n  }\n\n  .g-xl-0,\n.gx-xl-0 {\n    --bs-gutter-x: 0;\n  }\n\n  .g-xl-0,\n.gy-xl-0 {\n    --bs-gutter-y: 0;\n  }\n\n  .g-xl-1,\n.gx-xl-1 {\n    --bs-gutter-x: 0.25rem;\n  }\n\n  .g-xl-1,\n.gy-xl-1 {\n    --bs-gutter-y: 0.25rem;\n  }\n\n  .g-xl-2,\n.gx-xl-2 {\n    --bs-gutter-x: 0.5rem;\n  }\n\n  .g-xl-2,\n.gy-xl-2 {\n    --bs-gutter-y: 0.5rem;\n  }\n\n  .g-xl-3,\n.gx-xl-3 {\n    --bs-gutter-x: 1rem;\n  }\n\n  .g-xl-3,\n.gy-xl-3 {\n    --bs-gutter-y: 1rem;\n  }\n\n  .g-xl-4,\n.gx-xl-4 {\n    --bs-gutter-x: 1.5rem;\n  }\n\n  .g-xl-4,\n.gy-xl-4 {\n    --bs-gutter-y: 1.5rem;\n  }\n\n  .g-xl-5,\n.gx-xl-5 {\n    --bs-gutter-x: 3rem;\n  }\n\n  .g-xl-5,\n.gy-xl-5 {\n    --bs-gutter-y: 3rem;\n  }\n}\n@media (min-width: 1400px) {\n  .col-xxl {\n    flex: 1 0 0%;\n  }\n\n  .row-cols-xxl-auto > * {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .row-cols-xxl-1 > * {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .row-cols-xxl-2 > * {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .row-cols-xxl-3 > * {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .row-cols-xxl-4 > * {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .row-cols-xxl-5 > * {\n    flex: 0 0 auto;\n    width: 20%;\n  }\n\n  .row-cols-xxl-6 > * {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-xxl-auto {\n    flex: 0 0 auto;\n    width: auto;\n  }\n\n  .col-xxl-1 {\n    flex: 0 0 auto;\n    width: 8.3333333333%;\n  }\n\n  .col-xxl-2 {\n    flex: 0 0 auto;\n    width: 16.6666666667%;\n  }\n\n  .col-xxl-3 {\n    flex: 0 0 auto;\n    width: 25%;\n  }\n\n  .col-xxl-4 {\n    flex: 0 0 auto;\n    width: 33.3333333333%;\n  }\n\n  .col-xxl-5 {\n    flex: 0 0 auto;\n    width: 41.6666666667%;\n  }\n\n  .col-xxl-6 {\n    flex: 0 0 auto;\n    width: 50%;\n  }\n\n  .col-xxl-7 {\n    flex: 0 0 auto;\n    width: 58.3333333333%;\n  }\n\n  .col-xxl-8 {\n    flex: 0 0 auto;\n    width: 66.6666666667%;\n  }\n\n  .col-xxl-9 {\n    flex: 0 0 auto;\n    width: 75%;\n  }\n\n  .col-xxl-10 {\n    flex: 0 0 auto;\n    width: 83.3333333333%;\n  }\n\n  .col-xxl-11 {\n    flex: 0 0 auto;\n    width: 91.6666666667%;\n  }\n\n  .col-xxl-12 {\n    flex: 0 0 auto;\n    width: 100%;\n  }\n\n  .offset-xxl-0 {\n    margin-left: 0;\n  }\n\n  .offset-xxl-1 {\n    margin-left: 8.3333333333%;\n  }\n\n  .offset-xxl-2 {\n    margin-left: 16.6666666667%;\n  }\n\n  .offset-xxl-3 {\n    margin-left: 25%;\n  }\n\n  .offset-xxl-4 {\n    margin-left: 33.3333333333%;\n  }\n\n  .offset-xxl-5 {\n    margin-left: 41.6666666667%;\n  }\n\n  .offset-xxl-6 {\n    margin-left: 50%;\n  }\n\n  .offset-xxl-7 {\n    margin-left: 58.3333333333%;\n  }\n\n  .offset-xxl-8 {\n    margin-left: 66.6666666667%;\n  }\n\n  .offset-xxl-9 {\n    margin-left: 75%;\n  }\n\n  .offset-xxl-10 {\n    margin-left: 83.3333333333%;\n  }\n\n  .offset-xxl-11 {\n    margin-left: 91.6666666667%;\n  }\n\n  .g-xxl-0,\n.gx-xxl-0 {\n    --bs-gutter-x: 0;\n  }\n\n  .g-xxl-0,\n.gy-xxl-0 {\n    --bs-gutter-y: 0;\n  }\n\n  .g-xxl-1,\n.gx-xxl-1 {\n    --bs-gutter-x: 0.25rem;\n  }\n\n  .g-xxl-1,\n.gy-xxl-1 {\n    --bs-gutter-y: 0.25rem;\n  }\n\n  .g-xxl-2,\n.gx-xxl-2 {\n    --bs-gutter-x: 0.5rem;\n  }\n\n  .g-xxl-2,\n.gy-xxl-2 {\n    --bs-gutter-y: 0.5rem;\n  }\n\n  .g-xxl-3,\n.gx-xxl-3 {\n    --bs-gutter-x: 1rem;\n  }\n\n  .g-xxl-3,\n.gy-xxl-3 {\n    --bs-gutter-y: 1rem;\n  }\n\n  .g-xxl-4,\n.gx-xxl-4 {\n    --bs-gutter-x: 1.5rem;\n  }\n\n  .g-xxl-4,\n.gy-xxl-4 {\n    --bs-gutter-y: 1.5rem;\n  }\n\n  .g-xxl-5,\n.gx-xxl-5 {\n    --bs-gutter-x: 3rem;\n  }\n\n  .g-xxl-5,\n.gy-xxl-5 {\n    --bs-gutter-y: 3rem;\n  }\n}\n.d-inline {\n  display: inline !important;\n}\n\n.d-inline-block {\n  display: inline-block !important;\n}\n\n.d-block {\n  display: block !important;\n}\n\n.d-grid {\n  display: grid !important;\n}\n\n.d-table {\n  display: table !important;\n}\n\n.d-table-row {\n  display: table-row !important;\n}\n\n.d-table-cell {\n  display: table-cell !important;\n}\n\n.d-flex {\n  display: flex !important;\n}\n\n.d-inline-flex {\n  display: inline-flex !important;\n}\n\n.d-none {\n  display: none !important;\n}\n\n.flex-fill {\n  flex: 1 1 auto !important;\n}\n\n.flex-row {\n  flex-direction: row !important;\n}\n\n.flex-column {\n  flex-direction: column !important;\n}\n\n.flex-row-reverse {\n  flex-direction: row-reverse !important;\n}\n\n.flex-column-reverse {\n  flex-direction: column-reverse !important;\n}\n\n.flex-grow-0 {\n  flex-grow: 0 !important;\n}\n\n.flex-grow-1 {\n  flex-grow: 1 !important;\n}\n\n.flex-shrink-0 {\n  flex-shrink: 0 !important;\n}\n\n.flex-shrink-1 {\n  flex-shrink: 1 !important;\n}\n\n.flex-wrap {\n  flex-wrap: wrap !important;\n}\n\n.flex-nowrap {\n  flex-wrap: nowrap !important;\n}\n\n.flex-wrap-reverse {\n  flex-wrap: wrap-reverse !important;\n}\n\n.justify-content-start {\n  justify-content: flex-start !important;\n}\n\n.justify-content-end {\n  justify-content: flex-end !important;\n}\n\n.justify-content-center {\n  justify-content: center !important;\n}\n\n.justify-content-between {\n  justify-content: space-between !important;\n}\n\n.justify-content-around {\n  justify-content: space-around !important;\n}\n\n.justify-content-evenly {\n  justify-content: space-evenly !important;\n}\n\n.align-items-start {\n  align-items: flex-start !important;\n}\n\n.align-items-end {\n  align-items: flex-end !important;\n}\n\n.align-items-center {\n  align-items: center !important;\n}\n\n.align-items-baseline {\n  align-items: baseline !important;\n}\n\n.align-items-stretch {\n  align-items: stretch !important;\n}\n\n.align-content-start {\n  align-content: flex-start !important;\n}\n\n.align-content-end {\n  align-content: flex-end !important;\n}\n\n.align-content-center {\n  align-content: center !important;\n}\n\n.align-content-between {\n  align-content: space-between !important;\n}\n\n.align-content-around {\n  align-content: space-around !important;\n}\n\n.align-content-stretch {\n  align-content: stretch !important;\n}\n\n.align-self-auto {\n  align-self: auto !important;\n}\n\n.align-self-start {\n  align-self: flex-start !important;\n}\n\n.align-self-end {\n  align-self: flex-end !important;\n}\n\n.align-self-center {\n  align-self: center !important;\n}\n\n.align-self-baseline {\n  align-self: baseline !important;\n}\n\n.align-self-stretch {\n  align-self: stretch !important;\n}\n\n.order-first {\n  order: -1 !important;\n}\n\n.order-0 {\n  order: 0 !important;\n}\n\n.order-1 {\n  order: 1 !important;\n}\n\n.order-2 {\n  order: 2 !important;\n}\n\n.order-3 {\n  order: 3 !important;\n}\n\n.order-4 {\n  order: 4 !important;\n}\n\n.order-5 {\n  order: 5 !important;\n}\n\n.order-last {\n  order: 6 !important;\n}\n\n.m-0 {\n  margin: 0 !important;\n}\n\n.m-1 {\n  margin: 0.25rem !important;\n}\n\n.m-2 {\n  margin: 0.5rem !important;\n}\n\n.m-3 {\n  margin: 1rem !important;\n}\n\n.m-4 {\n  margin: 1.5rem !important;\n}\n\n.m-5 {\n  margin: 3rem !important;\n}\n\n.m-auto {\n  margin: auto !important;\n}\n\n.mx-0 {\n  margin-right: 0 !important;\n  margin-left: 0 !important;\n}\n\n.mx-1 {\n  margin-right: 0.25rem !important;\n  margin-left: 0.25rem !important;\n}\n\n.mx-2 {\n  margin-right: 0.5rem !important;\n  margin-left: 0.5rem !important;\n}\n\n.mx-3 {\n  margin-right: 1rem !important;\n  margin-left: 1rem !important;\n}\n\n.mx-4 {\n  margin-right: 1.5rem !important;\n  margin-left: 1.5rem !important;\n}\n\n.mx-5 {\n  margin-right: 3rem !important;\n  margin-left: 3rem !important;\n}\n\n.mx-auto {\n  margin-right: auto !important;\n  margin-left: auto !important;\n}\n\n.my-0 {\n  margin-top: 0 !important;\n  margin-bottom: 0 !important;\n}\n\n.my-1 {\n  margin-top: 0.25rem !important;\n  margin-bottom: 0.25rem !important;\n}\n\n.my-2 {\n  margin-top: 0.5rem !important;\n  margin-bottom: 0.5rem !important;\n}\n\n.my-3 {\n  margin-top: 1rem !important;\n  margin-bottom: 1rem !important;\n}\n\n.my-4 {\n  margin-top: 1.5rem !important;\n  margin-bottom: 1.5rem !important;\n}\n\n.my-5 {\n  margin-top: 3rem !important;\n  margin-bottom: 3rem !important;\n}\n\n.my-auto {\n  margin-top: auto !important;\n  margin-bottom: auto !important;\n}\n\n.mt-0 {\n  margin-top: 0 !important;\n}\n\n.mt-1 {\n  margin-top: 0.25rem !important;\n}\n\n.mt-2 {\n  margin-top: 0.5rem !important;\n}\n\n.mt-3 {\n  margin-top: 1rem !important;\n}\n\n.mt-4 {\n  margin-top: 1.5rem !important;\n}\n\n.mt-5 {\n  margin-top: 3rem !important;\n}\n\n.mt-auto {\n  margin-top: auto !important;\n}\n\n.mr-0 {\n  margin-right: 0 !important;\n}\n\n.mr-1 {\n  margin-right: 0.25rem !important;\n}\n\n.mr-2 {\n  margin-right: 0.5rem !important;\n}\n\n.mr-3 {\n  margin-right: 1rem !important;\n}\n\n.mr-4 {\n  margin-right: 1.5rem !important;\n}\n\n.mr-5 {\n  margin-right: 3rem !important;\n}\n\n.mr-auto {\n  margin-right: auto !important;\n}\n\n.mb-0 {\n  margin-bottom: 0 !important;\n}\n\n.mb-1 {\n  margin-bottom: 0.25rem !important;\n}\n\n.mb-2 {\n  margin-bottom: 0.5rem !important;\n}\n\n.mb-3 {\n  margin-bottom: 1rem !important;\n}\n\n.mb-4 {\n  margin-bottom: 1.5rem !important;\n}\n\n.mb-5 {\n  margin-bottom: 3rem !important;\n}\n\n.mb-auto {\n  margin-bottom: auto !important;\n}\n\n.ml-0 {\n  margin-left: 0 !important;\n}\n\n.ml-1 {\n  margin-left: 0.25rem !important;\n}\n\n.ml-2 {\n  margin-left: 0.5rem !important;\n}\n\n.ml-3 {\n  margin-left: 1rem !important;\n}\n\n.ml-4 {\n  margin-left: 1.5rem !important;\n}\n\n.ml-5 {\n  margin-left: 3rem !important;\n}\n\n.ml-auto {\n  margin-left: auto !important;\n}\n\n.p-0 {\n  padding: 0 !important;\n}\n\n.p-1 {\n  padding: 0.25rem !important;\n}\n\n.p-2 {\n  padding: 0.5rem !important;\n}\n\n.p-3 {\n  padding: 1rem !important;\n}\n\n.p-4 {\n  padding: 1.5rem !important;\n}\n\n.p-5 {\n  padding: 3rem !important;\n}\n\n.px-0 {\n  padding-right: 0 !important;\n  padding-left: 0 !important;\n}\n\n.px-1 {\n  padding-right: 0.25rem !important;\n  padding-left: 0.25rem !important;\n}\n\n.px-2 {\n  padding-right: 0.5rem !important;\n  padding-left: 0.5rem !important;\n}\n\n.px-3 {\n  padding-right: 1rem !important;\n  padding-left: 1rem !important;\n}\n\n.px-4 {\n  padding-right: 1.5rem !important;\n  padding-left: 1.5rem !important;\n}\n\n.px-5 {\n  padding-right: 3rem !important;\n  padding-left: 3rem !important;\n}\n\n.py-0 {\n  padding-top: 0 !important;\n  padding-bottom: 0 !important;\n}\n\n.py-1 {\n  padding-top: 0.25rem !important;\n  padding-bottom: 0.25rem !important;\n}\n\n.py-2 {\n  padding-top: 0.5rem !important;\n  padding-bottom: 0.5rem !important;\n}\n\n.py-3 {\n  padding-top: 1rem !important;\n  padding-bottom: 1rem !important;\n}\n\n.py-4 {\n  padding-top: 1.5rem !important;\n  padding-bottom: 1.5rem !important;\n}\n\n.py-5 {\n  padding-top: 3rem !important;\n  padding-bottom: 3rem !important;\n}\n\n.pt-0 {\n  padding-top: 0 !important;\n}\n\n.pt-1 {\n  padding-top: 0.25rem !important;\n}\n\n.pt-2 {\n  padding-top: 0.5rem !important;\n}\n\n.pt-3 {\n  padding-top: 1rem !important;\n}\n\n.pt-4 {\n  padding-top: 1.5rem !important;\n}\n\n.pt-5 {\n  padding-top: 3rem !important;\n}\n\n.pr-0 {\n  padding-right: 0 !important;\n}\n\n.pr-1 {\n  padding-right: 0.25rem !important;\n}\n\n.pr-2 {\n  padding-right: 0.5rem !important;\n}\n\n.pr-3 {\n  padding-right: 1rem !important;\n}\n\n.pr-4 {\n  padding-right: 1.5rem !important;\n}\n\n.pr-5 {\n  padding-right: 3rem !important;\n}\n\n.pb-0 {\n  padding-bottom: 0 !important;\n}\n\n.pb-1 {\n  padding-bottom: 0.25rem !important;\n}\n\n.pb-2 {\n  padding-bottom: 0.5rem !important;\n}\n\n.pb-3 {\n  padding-bottom: 1rem !important;\n}\n\n.pb-4 {\n  padding-bottom: 1.5rem !important;\n}\n\n.pb-5 {\n  padding-bottom: 3rem !important;\n}\n\n.pl-0 {\n  padding-left: 0 !important;\n}\n\n.pl-1 {\n  padding-left: 0.25rem !important;\n}\n\n.pl-2 {\n  padding-left: 0.5rem !important;\n}\n\n.pl-3 {\n  padding-left: 1rem !important;\n}\n\n.pl-4 {\n  padding-left: 1.5rem !important;\n}\n\n.pl-5 {\n  padding-left: 3rem !important;\n}\n\n@media (min-width: 576px) {\n  .d-sm-inline {\n    display: inline !important;\n  }\n\n  .d-sm-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-sm-block {\n    display: block !important;\n  }\n\n  .d-sm-grid {\n    display: grid !important;\n  }\n\n  .d-sm-table {\n    display: table !important;\n  }\n\n  .d-sm-table-row {\n    display: table-row !important;\n  }\n\n  .d-sm-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-sm-flex {\n    display: flex !important;\n  }\n\n  .d-sm-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-sm-none {\n    display: none !important;\n  }\n\n  .flex-sm-fill {\n    flex: 1 1 auto !important;\n  }\n\n  .flex-sm-row {\n    flex-direction: row !important;\n  }\n\n  .flex-sm-column {\n    flex-direction: column !important;\n  }\n\n  .flex-sm-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n\n  .flex-sm-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n\n  .flex-sm-grow-0 {\n    flex-grow: 0 !important;\n  }\n\n  .flex-sm-grow-1 {\n    flex-grow: 1 !important;\n  }\n\n  .flex-sm-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n\n  .flex-sm-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n\n  .flex-sm-wrap {\n    flex-wrap: wrap !important;\n  }\n\n  .flex-sm-nowrap {\n    flex-wrap: nowrap !important;\n  }\n\n  .flex-sm-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n\n  .justify-content-sm-start {\n    justify-content: flex-start !important;\n  }\n\n  .justify-content-sm-end {\n    justify-content: flex-end !important;\n  }\n\n  .justify-content-sm-center {\n    justify-content: center !important;\n  }\n\n  .justify-content-sm-between {\n    justify-content: space-between !important;\n  }\n\n  .justify-content-sm-around {\n    justify-content: space-around !important;\n  }\n\n  .justify-content-sm-evenly {\n    justify-content: space-evenly !important;\n  }\n\n  .align-items-sm-start {\n    align-items: flex-start !important;\n  }\n\n  .align-items-sm-end {\n    align-items: flex-end !important;\n  }\n\n  .align-items-sm-center {\n    align-items: center !important;\n  }\n\n  .align-items-sm-baseline {\n    align-items: baseline !important;\n  }\n\n  .align-items-sm-stretch {\n    align-items: stretch !important;\n  }\n\n  .align-content-sm-start {\n    align-content: flex-start !important;\n  }\n\n  .align-content-sm-end {\n    align-content: flex-end !important;\n  }\n\n  .align-content-sm-center {\n    align-content: center !important;\n  }\n\n  .align-content-sm-between {\n    align-content: space-between !important;\n  }\n\n  .align-content-sm-around {\n    align-content: space-around !important;\n  }\n\n  .align-content-sm-stretch {\n    align-content: stretch !important;\n  }\n\n  .align-self-sm-auto {\n    align-self: auto !important;\n  }\n\n  .align-self-sm-start {\n    align-self: flex-start !important;\n  }\n\n  .align-self-sm-end {\n    align-self: flex-end !important;\n  }\n\n  .align-self-sm-center {\n    align-self: center !important;\n  }\n\n  .align-self-sm-baseline {\n    align-self: baseline !important;\n  }\n\n  .align-self-sm-stretch {\n    align-self: stretch !important;\n  }\n\n  .order-sm-first {\n    order: -1 !important;\n  }\n\n  .order-sm-0 {\n    order: 0 !important;\n  }\n\n  .order-sm-1 {\n    order: 1 !important;\n  }\n\n  .order-sm-2 {\n    order: 2 !important;\n  }\n\n  .order-sm-3 {\n    order: 3 !important;\n  }\n\n  .order-sm-4 {\n    order: 4 !important;\n  }\n\n  .order-sm-5 {\n    order: 5 !important;\n  }\n\n  .order-sm-last {\n    order: 6 !important;\n  }\n\n  .m-sm-0 {\n    margin: 0 !important;\n  }\n\n  .m-sm-1 {\n    margin: 0.25rem !important;\n  }\n\n  .m-sm-2 {\n    margin: 0.5rem !important;\n  }\n\n  .m-sm-3 {\n    margin: 1rem !important;\n  }\n\n  .m-sm-4 {\n    margin: 1.5rem !important;\n  }\n\n  .m-sm-5 {\n    margin: 3rem !important;\n  }\n\n  .m-sm-auto {\n    margin: auto !important;\n  }\n\n  .mx-sm-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n\n  .mx-sm-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n\n  .mx-sm-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n\n  .mx-sm-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n\n  .mx-sm-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n\n  .mx-sm-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n\n  .mx-sm-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n\n  .my-sm-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n\n  .my-sm-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n\n  .my-sm-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .my-sm-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .my-sm-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .my-sm-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n\n  .my-sm-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n\n  .mt-sm-0 {\n    margin-top: 0 !important;\n  }\n\n  .mt-sm-1 {\n    margin-top: 0.25rem !important;\n  }\n\n  .mt-sm-2 {\n    margin-top: 0.5rem !important;\n  }\n\n  .mt-sm-3 {\n    margin-top: 1rem !important;\n  }\n\n  .mt-sm-4 {\n    margin-top: 1.5rem !important;\n  }\n\n  .mt-sm-5 {\n    margin-top: 3rem !important;\n  }\n\n  .mt-sm-auto {\n    margin-top: auto !important;\n  }\n\n  .mr-sm-0 {\n    margin-right: 0 !important;\n  }\n\n  .mr-sm-1 {\n    margin-right: 0.25rem !important;\n  }\n\n  .mr-sm-2 {\n    margin-right: 0.5rem !important;\n  }\n\n  .mr-sm-3 {\n    margin-right: 1rem !important;\n  }\n\n  .mr-sm-4 {\n    margin-right: 1.5rem !important;\n  }\n\n  .mr-sm-5 {\n    margin-right: 3rem !important;\n  }\n\n  .mr-sm-auto {\n    margin-right: auto !important;\n  }\n\n  .mb-sm-0 {\n    margin-bottom: 0 !important;\n  }\n\n  .mb-sm-1 {\n    margin-bottom: 0.25rem !important;\n  }\n\n  .mb-sm-2 {\n    margin-bottom: 0.5rem !important;\n  }\n\n  .mb-sm-3 {\n    margin-bottom: 1rem !important;\n  }\n\n  .mb-sm-4 {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .mb-sm-5 {\n    margin-bottom: 3rem !important;\n  }\n\n  .mb-sm-auto {\n    margin-bottom: auto !important;\n  }\n\n  .ml-sm-0 {\n    margin-left: 0 !important;\n  }\n\n  .ml-sm-1 {\n    margin-left: 0.25rem !important;\n  }\n\n  .ml-sm-2 {\n    margin-left: 0.5rem !important;\n  }\n\n  .ml-sm-3 {\n    margin-left: 1rem !important;\n  }\n\n  .ml-sm-4 {\n    margin-left: 1.5rem !important;\n  }\n\n  .ml-sm-5 {\n    margin-left: 3rem !important;\n  }\n\n  .ml-sm-auto {\n    margin-left: auto !important;\n  }\n\n  .p-sm-0 {\n    padding: 0 !important;\n  }\n\n  .p-sm-1 {\n    padding: 0.25rem !important;\n  }\n\n  .p-sm-2 {\n    padding: 0.5rem !important;\n  }\n\n  .p-sm-3 {\n    padding: 1rem !important;\n  }\n\n  .p-sm-4 {\n    padding: 1.5rem !important;\n  }\n\n  .p-sm-5 {\n    padding: 3rem !important;\n  }\n\n  .px-sm-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n\n  .px-sm-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n\n  .px-sm-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n\n  .px-sm-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n\n  .px-sm-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n\n  .px-sm-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n\n  .py-sm-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n\n  .py-sm-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n\n  .py-sm-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n\n  .py-sm-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n\n  .py-sm-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n\n  .py-sm-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n\n  .pt-sm-0 {\n    padding-top: 0 !important;\n  }\n\n  .pt-sm-1 {\n    padding-top: 0.25rem !important;\n  }\n\n  .pt-sm-2 {\n    padding-top: 0.5rem !important;\n  }\n\n  .pt-sm-3 {\n    padding-top: 1rem !important;\n  }\n\n  .pt-sm-4 {\n    padding-top: 1.5rem !important;\n  }\n\n  .pt-sm-5 {\n    padding-top: 3rem !important;\n  }\n\n  .pr-sm-0 {\n    padding-right: 0 !important;\n  }\n\n  .pr-sm-1 {\n    padding-right: 0.25rem !important;\n  }\n\n  .pr-sm-2 {\n    padding-right: 0.5rem !important;\n  }\n\n  .pr-sm-3 {\n    padding-right: 1rem !important;\n  }\n\n  .pr-sm-4 {\n    padding-right: 1.5rem !important;\n  }\n\n  .pr-sm-5 {\n    padding-right: 3rem !important;\n  }\n\n  .pb-sm-0 {\n    padding-bottom: 0 !important;\n  }\n\n  .pb-sm-1 {\n    padding-bottom: 0.25rem !important;\n  }\n\n  .pb-sm-2 {\n    padding-bottom: 0.5rem !important;\n  }\n\n  .pb-sm-3 {\n    padding-bottom: 1rem !important;\n  }\n\n  .pb-sm-4 {\n    padding-bottom: 1.5rem !important;\n  }\n\n  .pb-sm-5 {\n    padding-bottom: 3rem !important;\n  }\n\n  .pl-sm-0 {\n    padding-left: 0 !important;\n  }\n\n  .pl-sm-1 {\n    padding-left: 0.25rem !important;\n  }\n\n  .pl-sm-2 {\n    padding-left: 0.5rem !important;\n  }\n\n  .pl-sm-3 {\n    padding-left: 1rem !important;\n  }\n\n  .pl-sm-4 {\n    padding-left: 1.5rem !important;\n  }\n\n  .pl-sm-5 {\n    padding-left: 3rem !important;\n  }\n}\n@media (min-width: 768px) {\n  .d-md-inline {\n    display: inline !important;\n  }\n\n  .d-md-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-md-block {\n    display: block !important;\n  }\n\n  .d-md-grid {\n    display: grid !important;\n  }\n\n  .d-md-table {\n    display: table !important;\n  }\n\n  .d-md-table-row {\n    display: table-row !important;\n  }\n\n  .d-md-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-md-flex {\n    display: flex !important;\n  }\n\n  .d-md-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-md-none {\n    display: none !important;\n  }\n\n  .flex-md-fill {\n    flex: 1 1 auto !important;\n  }\n\n  .flex-md-row {\n    flex-direction: row !important;\n  }\n\n  .flex-md-column {\n    flex-direction: column !important;\n  }\n\n  .flex-md-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n\n  .flex-md-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n\n  .flex-md-grow-0 {\n    flex-grow: 0 !important;\n  }\n\n  .flex-md-grow-1 {\n    flex-grow: 1 !important;\n  }\n\n  .flex-md-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n\n  .flex-md-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n\n  .flex-md-wrap {\n    flex-wrap: wrap !important;\n  }\n\n  .flex-md-nowrap {\n    flex-wrap: nowrap !important;\n  }\n\n  .flex-md-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n\n  .justify-content-md-start {\n    justify-content: flex-start !important;\n  }\n\n  .justify-content-md-end {\n    justify-content: flex-end !important;\n  }\n\n  .justify-content-md-center {\n    justify-content: center !important;\n  }\n\n  .justify-content-md-between {\n    justify-content: space-between !important;\n  }\n\n  .justify-content-md-around {\n    justify-content: space-around !important;\n  }\n\n  .justify-content-md-evenly {\n    justify-content: space-evenly !important;\n  }\n\n  .align-items-md-start {\n    align-items: flex-start !important;\n  }\n\n  .align-items-md-end {\n    align-items: flex-end !important;\n  }\n\n  .align-items-md-center {\n    align-items: center !important;\n  }\n\n  .align-items-md-baseline {\n    align-items: baseline !important;\n  }\n\n  .align-items-md-stretch {\n    align-items: stretch !important;\n  }\n\n  .align-content-md-start {\n    align-content: flex-start !important;\n  }\n\n  .align-content-md-end {\n    align-content: flex-end !important;\n  }\n\n  .align-content-md-center {\n    align-content: center !important;\n  }\n\n  .align-content-md-between {\n    align-content: space-between !important;\n  }\n\n  .align-content-md-around {\n    align-content: space-around !important;\n  }\n\n  .align-content-md-stretch {\n    align-content: stretch !important;\n  }\n\n  .align-self-md-auto {\n    align-self: auto !important;\n  }\n\n  .align-self-md-start {\n    align-self: flex-start !important;\n  }\n\n  .align-self-md-end {\n    align-self: flex-end !important;\n  }\n\n  .align-self-md-center {\n    align-self: center !important;\n  }\n\n  .align-self-md-baseline {\n    align-self: baseline !important;\n  }\n\n  .align-self-md-stretch {\n    align-self: stretch !important;\n  }\n\n  .order-md-first {\n    order: -1 !important;\n  }\n\n  .order-md-0 {\n    order: 0 !important;\n  }\n\n  .order-md-1 {\n    order: 1 !important;\n  }\n\n  .order-md-2 {\n    order: 2 !important;\n  }\n\n  .order-md-3 {\n    order: 3 !important;\n  }\n\n  .order-md-4 {\n    order: 4 !important;\n  }\n\n  .order-md-5 {\n    order: 5 !important;\n  }\n\n  .order-md-last {\n    order: 6 !important;\n  }\n\n  .m-md-0 {\n    margin: 0 !important;\n  }\n\n  .m-md-1 {\n    margin: 0.25rem !important;\n  }\n\n  .m-md-2 {\n    margin: 0.5rem !important;\n  }\n\n  .m-md-3 {\n    margin: 1rem !important;\n  }\n\n  .m-md-4 {\n    margin: 1.5rem !important;\n  }\n\n  .m-md-5 {\n    margin: 3rem !important;\n  }\n\n  .m-md-auto {\n    margin: auto !important;\n  }\n\n  .mx-md-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n\n  .mx-md-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n\n  .mx-md-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n\n  .mx-md-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n\n  .mx-md-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n\n  .mx-md-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n\n  .mx-md-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n\n  .my-md-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n\n  .my-md-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n\n  .my-md-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .my-md-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .my-md-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .my-md-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n\n  .my-md-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n\n  .mt-md-0 {\n    margin-top: 0 !important;\n  }\n\n  .mt-md-1 {\n    margin-top: 0.25rem !important;\n  }\n\n  .mt-md-2 {\n    margin-top: 0.5rem !important;\n  }\n\n  .mt-md-3 {\n    margin-top: 1rem !important;\n  }\n\n  .mt-md-4 {\n    margin-top: 1.5rem !important;\n  }\n\n  .mt-md-5 {\n    margin-top: 3rem !important;\n  }\n\n  .mt-md-auto {\n    margin-top: auto !important;\n  }\n\n  .mr-md-0 {\n    margin-right: 0 !important;\n  }\n\n  .mr-md-1 {\n    margin-right: 0.25rem !important;\n  }\n\n  .mr-md-2 {\n    margin-right: 0.5rem !important;\n  }\n\n  .mr-md-3 {\n    margin-right: 1rem !important;\n  }\n\n  .mr-md-4 {\n    margin-right: 1.5rem !important;\n  }\n\n  .mr-md-5 {\n    margin-right: 3rem !important;\n  }\n\n  .mr-md-auto {\n    margin-right: auto !important;\n  }\n\n  .mb-md-0 {\n    margin-bottom: 0 !important;\n  }\n\n  .mb-md-1 {\n    margin-bottom: 0.25rem !important;\n  }\n\n  .mb-md-2 {\n    margin-bottom: 0.5rem !important;\n  }\n\n  .mb-md-3 {\n    margin-bottom: 1rem !important;\n  }\n\n  .mb-md-4 {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .mb-md-5 {\n    margin-bottom: 3rem !important;\n  }\n\n  .mb-md-auto {\n    margin-bottom: auto !important;\n  }\n\n  .ml-md-0 {\n    margin-left: 0 !important;\n  }\n\n  .ml-md-1 {\n    margin-left: 0.25rem !important;\n  }\n\n  .ml-md-2 {\n    margin-left: 0.5rem !important;\n  }\n\n  .ml-md-3 {\n    margin-left: 1rem !important;\n  }\n\n  .ml-md-4 {\n    margin-left: 1.5rem !important;\n  }\n\n  .ml-md-5 {\n    margin-left: 3rem !important;\n  }\n\n  .ml-md-auto {\n    margin-left: auto !important;\n  }\n\n  .p-md-0 {\n    padding: 0 !important;\n  }\n\n  .p-md-1 {\n    padding: 0.25rem !important;\n  }\n\n  .p-md-2 {\n    padding: 0.5rem !important;\n  }\n\n  .p-md-3 {\n    padding: 1rem !important;\n  }\n\n  .p-md-4 {\n    padding: 1.5rem !important;\n  }\n\n  .p-md-5 {\n    padding: 3rem !important;\n  }\n\n  .px-md-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n\n  .px-md-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n\n  .px-md-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n\n  .px-md-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n\n  .px-md-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n\n  .px-md-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n\n  .py-md-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n\n  .py-md-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n\n  .py-md-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n\n  .py-md-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n\n  .py-md-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n\n  .py-md-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n\n  .pt-md-0 {\n    padding-top: 0 !important;\n  }\n\n  .pt-md-1 {\n    padding-top: 0.25rem !important;\n  }\n\n  .pt-md-2 {\n    padding-top: 0.5rem !important;\n  }\n\n  .pt-md-3 {\n    padding-top: 1rem !important;\n  }\n\n  .pt-md-4 {\n    padding-top: 1.5rem !important;\n  }\n\n  .pt-md-5 {\n    padding-top: 3rem !important;\n  }\n\n  .pr-md-0 {\n    padding-right: 0 !important;\n  }\n\n  .pr-md-1 {\n    padding-right: 0.25rem !important;\n  }\n\n  .pr-md-2 {\n    padding-right: 0.5rem !important;\n  }\n\n  .pr-md-3 {\n    padding-right: 1rem !important;\n  }\n\n  .pr-md-4 {\n    padding-right: 1.5rem !important;\n  }\n\n  .pr-md-5 {\n    padding-right: 3rem !important;\n  }\n\n  .pb-md-0 {\n    padding-bottom: 0 !important;\n  }\n\n  .pb-md-1 {\n    padding-bottom: 0.25rem !important;\n  }\n\n  .pb-md-2 {\n    padding-bottom: 0.5rem !important;\n  }\n\n  .pb-md-3 {\n    padding-bottom: 1rem !important;\n  }\n\n  .pb-md-4 {\n    padding-bottom: 1.5rem !important;\n  }\n\n  .pb-md-5 {\n    padding-bottom: 3rem !important;\n  }\n\n  .pl-md-0 {\n    padding-left: 0 !important;\n  }\n\n  .pl-md-1 {\n    padding-left: 0.25rem !important;\n  }\n\n  .pl-md-2 {\n    padding-left: 0.5rem !important;\n  }\n\n  .pl-md-3 {\n    padding-left: 1rem !important;\n  }\n\n  .pl-md-4 {\n    padding-left: 1.5rem !important;\n  }\n\n  .pl-md-5 {\n    padding-left: 3rem !important;\n  }\n}\n@media (min-width: 992px) {\n  .d-lg-inline {\n    display: inline !important;\n  }\n\n  .d-lg-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-lg-block {\n    display: block !important;\n  }\n\n  .d-lg-grid {\n    display: grid !important;\n  }\n\n  .d-lg-table {\n    display: table !important;\n  }\n\n  .d-lg-table-row {\n    display: table-row !important;\n  }\n\n  .d-lg-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-lg-flex {\n    display: flex !important;\n  }\n\n  .d-lg-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-lg-none {\n    display: none !important;\n  }\n\n  .flex-lg-fill {\n    flex: 1 1 auto !important;\n  }\n\n  .flex-lg-row {\n    flex-direction: row !important;\n  }\n\n  .flex-lg-column {\n    flex-direction: column !important;\n  }\n\n  .flex-lg-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n\n  .flex-lg-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n\n  .flex-lg-grow-0 {\n    flex-grow: 0 !important;\n  }\n\n  .flex-lg-grow-1 {\n    flex-grow: 1 !important;\n  }\n\n  .flex-lg-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n\n  .flex-lg-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n\n  .flex-lg-wrap {\n    flex-wrap: wrap !important;\n  }\n\n  .flex-lg-nowrap {\n    flex-wrap: nowrap !important;\n  }\n\n  .flex-lg-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n\n  .justify-content-lg-start {\n    justify-content: flex-start !important;\n  }\n\n  .justify-content-lg-end {\n    justify-content: flex-end !important;\n  }\n\n  .justify-content-lg-center {\n    justify-content: center !important;\n  }\n\n  .justify-content-lg-between {\n    justify-content: space-between !important;\n  }\n\n  .justify-content-lg-around {\n    justify-content: space-around !important;\n  }\n\n  .justify-content-lg-evenly {\n    justify-content: space-evenly !important;\n  }\n\n  .align-items-lg-start {\n    align-items: flex-start !important;\n  }\n\n  .align-items-lg-end {\n    align-items: flex-end !important;\n  }\n\n  .align-items-lg-center {\n    align-items: center !important;\n  }\n\n  .align-items-lg-baseline {\n    align-items: baseline !important;\n  }\n\n  .align-items-lg-stretch {\n    align-items: stretch !important;\n  }\n\n  .align-content-lg-start {\n    align-content: flex-start !important;\n  }\n\n  .align-content-lg-end {\n    align-content: flex-end !important;\n  }\n\n  .align-content-lg-center {\n    align-content: center !important;\n  }\n\n  .align-content-lg-between {\n    align-content: space-between !important;\n  }\n\n  .align-content-lg-around {\n    align-content: space-around !important;\n  }\n\n  .align-content-lg-stretch {\n    align-content: stretch !important;\n  }\n\n  .align-self-lg-auto {\n    align-self: auto !important;\n  }\n\n  .align-self-lg-start {\n    align-self: flex-start !important;\n  }\n\n  .align-self-lg-end {\n    align-self: flex-end !important;\n  }\n\n  .align-self-lg-center {\n    align-self: center !important;\n  }\n\n  .align-self-lg-baseline {\n    align-self: baseline !important;\n  }\n\n  .align-self-lg-stretch {\n    align-self: stretch !important;\n  }\n\n  .order-lg-first {\n    order: -1 !important;\n  }\n\n  .order-lg-0 {\n    order: 0 !important;\n  }\n\n  .order-lg-1 {\n    order: 1 !important;\n  }\n\n  .order-lg-2 {\n    order: 2 !important;\n  }\n\n  .order-lg-3 {\n    order: 3 !important;\n  }\n\n  .order-lg-4 {\n    order: 4 !important;\n  }\n\n  .order-lg-5 {\n    order: 5 !important;\n  }\n\n  .order-lg-last {\n    order: 6 !important;\n  }\n\n  .m-lg-0 {\n    margin: 0 !important;\n  }\n\n  .m-lg-1 {\n    margin: 0.25rem !important;\n  }\n\n  .m-lg-2 {\n    margin: 0.5rem !important;\n  }\n\n  .m-lg-3 {\n    margin: 1rem !important;\n  }\n\n  .m-lg-4 {\n    margin: 1.5rem !important;\n  }\n\n  .m-lg-5 {\n    margin: 3rem !important;\n  }\n\n  .m-lg-auto {\n    margin: auto !important;\n  }\n\n  .mx-lg-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n\n  .mx-lg-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n\n  .mx-lg-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n\n  .mx-lg-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n\n  .mx-lg-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n\n  .mx-lg-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n\n  .mx-lg-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n\n  .my-lg-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n\n  .my-lg-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n\n  .my-lg-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .my-lg-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .my-lg-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .my-lg-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n\n  .my-lg-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n\n  .mt-lg-0 {\n    margin-top: 0 !important;\n  }\n\n  .mt-lg-1 {\n    margin-top: 0.25rem !important;\n  }\n\n  .mt-lg-2 {\n    margin-top: 0.5rem !important;\n  }\n\n  .mt-lg-3 {\n    margin-top: 1rem !important;\n  }\n\n  .mt-lg-4 {\n    margin-top: 1.5rem !important;\n  }\n\n  .mt-lg-5 {\n    margin-top: 3rem !important;\n  }\n\n  .mt-lg-auto {\n    margin-top: auto !important;\n  }\n\n  .mr-lg-0 {\n    margin-right: 0 !important;\n  }\n\n  .mr-lg-1 {\n    margin-right: 0.25rem !important;\n  }\n\n  .mr-lg-2 {\n    margin-right: 0.5rem !important;\n  }\n\n  .mr-lg-3 {\n    margin-right: 1rem !important;\n  }\n\n  .mr-lg-4 {\n    margin-right: 1.5rem !important;\n  }\n\n  .mr-lg-5 {\n    margin-right: 3rem !important;\n  }\n\n  .mr-lg-auto {\n    margin-right: auto !important;\n  }\n\n  .mb-lg-0 {\n    margin-bottom: 0 !important;\n  }\n\n  .mb-lg-1 {\n    margin-bottom: 0.25rem !important;\n  }\n\n  .mb-lg-2 {\n    margin-bottom: 0.5rem !important;\n  }\n\n  .mb-lg-3 {\n    margin-bottom: 1rem !important;\n  }\n\n  .mb-lg-4 {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .mb-lg-5 {\n    margin-bottom: 3rem !important;\n  }\n\n  .mb-lg-auto {\n    margin-bottom: auto !important;\n  }\n\n  .ml-lg-0 {\n    margin-left: 0 !important;\n  }\n\n  .ml-lg-1 {\n    margin-left: 0.25rem !important;\n  }\n\n  .ml-lg-2 {\n    margin-left: 0.5rem !important;\n  }\n\n  .ml-lg-3 {\n    margin-left: 1rem !important;\n  }\n\n  .ml-lg-4 {\n    margin-left: 1.5rem !important;\n  }\n\n  .ml-lg-5 {\n    margin-left: 3rem !important;\n  }\n\n  .ml-lg-auto {\n    margin-left: auto !important;\n  }\n\n  .p-lg-0 {\n    padding: 0 !important;\n  }\n\n  .p-lg-1 {\n    padding: 0.25rem !important;\n  }\n\n  .p-lg-2 {\n    padding: 0.5rem !important;\n  }\n\n  .p-lg-3 {\n    padding: 1rem !important;\n  }\n\n  .p-lg-4 {\n    padding: 1.5rem !important;\n  }\n\n  .p-lg-5 {\n    padding: 3rem !important;\n  }\n\n  .px-lg-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n\n  .px-lg-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n\n  .px-lg-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n\n  .px-lg-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n\n  .px-lg-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n\n  .px-lg-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n\n  .py-lg-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n\n  .py-lg-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n\n  .py-lg-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n\n  .py-lg-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n\n  .py-lg-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n\n  .py-lg-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n\n  .pt-lg-0 {\n    padding-top: 0 !important;\n  }\n\n  .pt-lg-1 {\n    padding-top: 0.25rem !important;\n  }\n\n  .pt-lg-2 {\n    padding-top: 0.5rem !important;\n  }\n\n  .pt-lg-3 {\n    padding-top: 1rem !important;\n  }\n\n  .pt-lg-4 {\n    padding-top: 1.5rem !important;\n  }\n\n  .pt-lg-5 {\n    padding-top: 3rem !important;\n  }\n\n  .pr-lg-0 {\n    padding-right: 0 !important;\n  }\n\n  .pr-lg-1 {\n    padding-right: 0.25rem !important;\n  }\n\n  .pr-lg-2 {\n    padding-right: 0.5rem !important;\n  }\n\n  .pr-lg-3 {\n    padding-right: 1rem !important;\n  }\n\n  .pr-lg-4 {\n    padding-right: 1.5rem !important;\n  }\n\n  .pr-lg-5 {\n    padding-right: 3rem !important;\n  }\n\n  .pb-lg-0 {\n    padding-bottom: 0 !important;\n  }\n\n  .pb-lg-1 {\n    padding-bottom: 0.25rem !important;\n  }\n\n  .pb-lg-2 {\n    padding-bottom: 0.5rem !important;\n  }\n\n  .pb-lg-3 {\n    padding-bottom: 1rem !important;\n  }\n\n  .pb-lg-4 {\n    padding-bottom: 1.5rem !important;\n  }\n\n  .pb-lg-5 {\n    padding-bottom: 3rem !important;\n  }\n\n  .pl-lg-0 {\n    padding-left: 0 !important;\n  }\n\n  .pl-lg-1 {\n    padding-left: 0.25rem !important;\n  }\n\n  .pl-lg-2 {\n    padding-left: 0.5rem !important;\n  }\n\n  .pl-lg-3 {\n    padding-left: 1rem !important;\n  }\n\n  .pl-lg-4 {\n    padding-left: 1.5rem !important;\n  }\n\n  .pl-lg-5 {\n    padding-left: 3rem !important;\n  }\n}\n@media (min-width: 1200px) {\n  .d-xl-inline {\n    display: inline !important;\n  }\n\n  .d-xl-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-xl-block {\n    display: block !important;\n  }\n\n  .d-xl-grid {\n    display: grid !important;\n  }\n\n  .d-xl-table {\n    display: table !important;\n  }\n\n  .d-xl-table-row {\n    display: table-row !important;\n  }\n\n  .d-xl-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-xl-flex {\n    display: flex !important;\n  }\n\n  .d-xl-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-xl-none {\n    display: none !important;\n  }\n\n  .flex-xl-fill {\n    flex: 1 1 auto !important;\n  }\n\n  .flex-xl-row {\n    flex-direction: row !important;\n  }\n\n  .flex-xl-column {\n    flex-direction: column !important;\n  }\n\n  .flex-xl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n\n  .flex-xl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n\n  .flex-xl-grow-0 {\n    flex-grow: 0 !important;\n  }\n\n  .flex-xl-grow-1 {\n    flex-grow: 1 !important;\n  }\n\n  .flex-xl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n\n  .flex-xl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n\n  .flex-xl-wrap {\n    flex-wrap: wrap !important;\n  }\n\n  .flex-xl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n\n  .flex-xl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n\n  .justify-content-xl-start {\n    justify-content: flex-start !important;\n  }\n\n  .justify-content-xl-end {\n    justify-content: flex-end !important;\n  }\n\n  .justify-content-xl-center {\n    justify-content: center !important;\n  }\n\n  .justify-content-xl-between {\n    justify-content: space-between !important;\n  }\n\n  .justify-content-xl-around {\n    justify-content: space-around !important;\n  }\n\n  .justify-content-xl-evenly {\n    justify-content: space-evenly !important;\n  }\n\n  .align-items-xl-start {\n    align-items: flex-start !important;\n  }\n\n  .align-items-xl-end {\n    align-items: flex-end !important;\n  }\n\n  .align-items-xl-center {\n    align-items: center !important;\n  }\n\n  .align-items-xl-baseline {\n    align-items: baseline !important;\n  }\n\n  .align-items-xl-stretch {\n    align-items: stretch !important;\n  }\n\n  .align-content-xl-start {\n    align-content: flex-start !important;\n  }\n\n  .align-content-xl-end {\n    align-content: flex-end !important;\n  }\n\n  .align-content-xl-center {\n    align-content: center !important;\n  }\n\n  .align-content-xl-between {\n    align-content: space-between !important;\n  }\n\n  .align-content-xl-around {\n    align-content: space-around !important;\n  }\n\n  .align-content-xl-stretch {\n    align-content: stretch !important;\n  }\n\n  .align-self-xl-auto {\n    align-self: auto !important;\n  }\n\n  .align-self-xl-start {\n    align-self: flex-start !important;\n  }\n\n  .align-self-xl-end {\n    align-self: flex-end !important;\n  }\n\n  .align-self-xl-center {\n    align-self: center !important;\n  }\n\n  .align-self-xl-baseline {\n    align-self: baseline !important;\n  }\n\n  .align-self-xl-stretch {\n    align-self: stretch !important;\n  }\n\n  .order-xl-first {\n    order: -1 !important;\n  }\n\n  .order-xl-0 {\n    order: 0 !important;\n  }\n\n  .order-xl-1 {\n    order: 1 !important;\n  }\n\n  .order-xl-2 {\n    order: 2 !important;\n  }\n\n  .order-xl-3 {\n    order: 3 !important;\n  }\n\n  .order-xl-4 {\n    order: 4 !important;\n  }\n\n  .order-xl-5 {\n    order: 5 !important;\n  }\n\n  .order-xl-last {\n    order: 6 !important;\n  }\n\n  .m-xl-0 {\n    margin: 0 !important;\n  }\n\n  .m-xl-1 {\n    margin: 0.25rem !important;\n  }\n\n  .m-xl-2 {\n    margin: 0.5rem !important;\n  }\n\n  .m-xl-3 {\n    margin: 1rem !important;\n  }\n\n  .m-xl-4 {\n    margin: 1.5rem !important;\n  }\n\n  .m-xl-5 {\n    margin: 3rem !important;\n  }\n\n  .m-xl-auto {\n    margin: auto !important;\n  }\n\n  .mx-xl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n\n  .mx-xl-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n\n  .mx-xl-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n\n  .mx-xl-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n\n  .mx-xl-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n\n  .mx-xl-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n\n  .mx-xl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n\n  .my-xl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n\n  .my-xl-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n\n  .my-xl-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .my-xl-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .my-xl-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .my-xl-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n\n  .my-xl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n\n  .mt-xl-0 {\n    margin-top: 0 !important;\n  }\n\n  .mt-xl-1 {\n    margin-top: 0.25rem !important;\n  }\n\n  .mt-xl-2 {\n    margin-top: 0.5rem !important;\n  }\n\n  .mt-xl-3 {\n    margin-top: 1rem !important;\n  }\n\n  .mt-xl-4 {\n    margin-top: 1.5rem !important;\n  }\n\n  .mt-xl-5 {\n    margin-top: 3rem !important;\n  }\n\n  .mt-xl-auto {\n    margin-top: auto !important;\n  }\n\n  .mr-xl-0 {\n    margin-right: 0 !important;\n  }\n\n  .mr-xl-1 {\n    margin-right: 0.25rem !important;\n  }\n\n  .mr-xl-2 {\n    margin-right: 0.5rem !important;\n  }\n\n  .mr-xl-3 {\n    margin-right: 1rem !important;\n  }\n\n  .mr-xl-4 {\n    margin-right: 1.5rem !important;\n  }\n\n  .mr-xl-5 {\n    margin-right: 3rem !important;\n  }\n\n  .mr-xl-auto {\n    margin-right: auto !important;\n  }\n\n  .mb-xl-0 {\n    margin-bottom: 0 !important;\n  }\n\n  .mb-xl-1 {\n    margin-bottom: 0.25rem !important;\n  }\n\n  .mb-xl-2 {\n    margin-bottom: 0.5rem !important;\n  }\n\n  .mb-xl-3 {\n    margin-bottom: 1rem !important;\n  }\n\n  .mb-xl-4 {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .mb-xl-5 {\n    margin-bottom: 3rem !important;\n  }\n\n  .mb-xl-auto {\n    margin-bottom: auto !important;\n  }\n\n  .ml-xl-0 {\n    margin-left: 0 !important;\n  }\n\n  .ml-xl-1 {\n    margin-left: 0.25rem !important;\n  }\n\n  .ml-xl-2 {\n    margin-left: 0.5rem !important;\n  }\n\n  .ml-xl-3 {\n    margin-left: 1rem !important;\n  }\n\n  .ml-xl-4 {\n    margin-left: 1.5rem !important;\n  }\n\n  .ml-xl-5 {\n    margin-left: 3rem !important;\n  }\n\n  .ml-xl-auto {\n    margin-left: auto !important;\n  }\n\n  .p-xl-0 {\n    padding: 0 !important;\n  }\n\n  .p-xl-1 {\n    padding: 0.25rem !important;\n  }\n\n  .p-xl-2 {\n    padding: 0.5rem !important;\n  }\n\n  .p-xl-3 {\n    padding: 1rem !important;\n  }\n\n  .p-xl-4 {\n    padding: 1.5rem !important;\n  }\n\n  .p-xl-5 {\n    padding: 3rem !important;\n  }\n\n  .px-xl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n\n  .px-xl-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n\n  .px-xl-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n\n  .px-xl-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n\n  .px-xl-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n\n  .px-xl-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n\n  .py-xl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n\n  .py-xl-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n\n  .py-xl-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n\n  .py-xl-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n\n  .py-xl-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n\n  .py-xl-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n\n  .pt-xl-0 {\n    padding-top: 0 !important;\n  }\n\n  .pt-xl-1 {\n    padding-top: 0.25rem !important;\n  }\n\n  .pt-xl-2 {\n    padding-top: 0.5rem !important;\n  }\n\n  .pt-xl-3 {\n    padding-top: 1rem !important;\n  }\n\n  .pt-xl-4 {\n    padding-top: 1.5rem !important;\n  }\n\n  .pt-xl-5 {\n    padding-top: 3rem !important;\n  }\n\n  .pr-xl-0 {\n    padding-right: 0 !important;\n  }\n\n  .pr-xl-1 {\n    padding-right: 0.25rem !important;\n  }\n\n  .pr-xl-2 {\n    padding-right: 0.5rem !important;\n  }\n\n  .pr-xl-3 {\n    padding-right: 1rem !important;\n  }\n\n  .pr-xl-4 {\n    padding-right: 1.5rem !important;\n  }\n\n  .pr-xl-5 {\n    padding-right: 3rem !important;\n  }\n\n  .pb-xl-0 {\n    padding-bottom: 0 !important;\n  }\n\n  .pb-xl-1 {\n    padding-bottom: 0.25rem !important;\n  }\n\n  .pb-xl-2 {\n    padding-bottom: 0.5rem !important;\n  }\n\n  .pb-xl-3 {\n    padding-bottom: 1rem !important;\n  }\n\n  .pb-xl-4 {\n    padding-bottom: 1.5rem !important;\n  }\n\n  .pb-xl-5 {\n    padding-bottom: 3rem !important;\n  }\n\n  .pl-xl-0 {\n    padding-left: 0 !important;\n  }\n\n  .pl-xl-1 {\n    padding-left: 0.25rem !important;\n  }\n\n  .pl-xl-2 {\n    padding-left: 0.5rem !important;\n  }\n\n  .pl-xl-3 {\n    padding-left: 1rem !important;\n  }\n\n  .pl-xl-4 {\n    padding-left: 1.5rem !important;\n  }\n\n  .pl-xl-5 {\n    padding-left: 3rem !important;\n  }\n}\n@media (min-width: 1400px) {\n  .d-xxl-inline {\n    display: inline !important;\n  }\n\n  .d-xxl-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-xxl-block {\n    display: block !important;\n  }\n\n  .d-xxl-grid {\n    display: grid !important;\n  }\n\n  .d-xxl-table {\n    display: table !important;\n  }\n\n  .d-xxl-table-row {\n    display: table-row !important;\n  }\n\n  .d-xxl-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-xxl-flex {\n    display: flex !important;\n  }\n\n  .d-xxl-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-xxl-none {\n    display: none !important;\n  }\n\n  .flex-xxl-fill {\n    flex: 1 1 auto !important;\n  }\n\n  .flex-xxl-row {\n    flex-direction: row !important;\n  }\n\n  .flex-xxl-column {\n    flex-direction: column !important;\n  }\n\n  .flex-xxl-row-reverse {\n    flex-direction: row-reverse !important;\n  }\n\n  .flex-xxl-column-reverse {\n    flex-direction: column-reverse !important;\n  }\n\n  .flex-xxl-grow-0 {\n    flex-grow: 0 !important;\n  }\n\n  .flex-xxl-grow-1 {\n    flex-grow: 1 !important;\n  }\n\n  .flex-xxl-shrink-0 {\n    flex-shrink: 0 !important;\n  }\n\n  .flex-xxl-shrink-1 {\n    flex-shrink: 1 !important;\n  }\n\n  .flex-xxl-wrap {\n    flex-wrap: wrap !important;\n  }\n\n  .flex-xxl-nowrap {\n    flex-wrap: nowrap !important;\n  }\n\n  .flex-xxl-wrap-reverse {\n    flex-wrap: wrap-reverse !important;\n  }\n\n  .justify-content-xxl-start {\n    justify-content: flex-start !important;\n  }\n\n  .justify-content-xxl-end {\n    justify-content: flex-end !important;\n  }\n\n  .justify-content-xxl-center {\n    justify-content: center !important;\n  }\n\n  .justify-content-xxl-between {\n    justify-content: space-between !important;\n  }\n\n  .justify-content-xxl-around {\n    justify-content: space-around !important;\n  }\n\n  .justify-content-xxl-evenly {\n    justify-content: space-evenly !important;\n  }\n\n  .align-items-xxl-start {\n    align-items: flex-start !important;\n  }\n\n  .align-items-xxl-end {\n    align-items: flex-end !important;\n  }\n\n  .align-items-xxl-center {\n    align-items: center !important;\n  }\n\n  .align-items-xxl-baseline {\n    align-items: baseline !important;\n  }\n\n  .align-items-xxl-stretch {\n    align-items: stretch !important;\n  }\n\n  .align-content-xxl-start {\n    align-content: flex-start !important;\n  }\n\n  .align-content-xxl-end {\n    align-content: flex-end !important;\n  }\n\n  .align-content-xxl-center {\n    align-content: center !important;\n  }\n\n  .align-content-xxl-between {\n    align-content: space-between !important;\n  }\n\n  .align-content-xxl-around {\n    align-content: space-around !important;\n  }\n\n  .align-content-xxl-stretch {\n    align-content: stretch !important;\n  }\n\n  .align-self-xxl-auto {\n    align-self: auto !important;\n  }\n\n  .align-self-xxl-start {\n    align-self: flex-start !important;\n  }\n\n  .align-self-xxl-end {\n    align-self: flex-end !important;\n  }\n\n  .align-self-xxl-center {\n    align-self: center !important;\n  }\n\n  .align-self-xxl-baseline {\n    align-self: baseline !important;\n  }\n\n  .align-self-xxl-stretch {\n    align-self: stretch !important;\n  }\n\n  .order-xxl-first {\n    order: -1 !important;\n  }\n\n  .order-xxl-0 {\n    order: 0 !important;\n  }\n\n  .order-xxl-1 {\n    order: 1 !important;\n  }\n\n  .order-xxl-2 {\n    order: 2 !important;\n  }\n\n  .order-xxl-3 {\n    order: 3 !important;\n  }\n\n  .order-xxl-4 {\n    order: 4 !important;\n  }\n\n  .order-xxl-5 {\n    order: 5 !important;\n  }\n\n  .order-xxl-last {\n    order: 6 !important;\n  }\n\n  .m-xxl-0 {\n    margin: 0 !important;\n  }\n\n  .m-xxl-1 {\n    margin: 0.25rem !important;\n  }\n\n  .m-xxl-2 {\n    margin: 0.5rem !important;\n  }\n\n  .m-xxl-3 {\n    margin: 1rem !important;\n  }\n\n  .m-xxl-4 {\n    margin: 1.5rem !important;\n  }\n\n  .m-xxl-5 {\n    margin: 3rem !important;\n  }\n\n  .m-xxl-auto {\n    margin: auto !important;\n  }\n\n  .mx-xxl-0 {\n    margin-right: 0 !important;\n    margin-left: 0 !important;\n  }\n\n  .mx-xxl-1 {\n    margin-right: 0.25rem !important;\n    margin-left: 0.25rem !important;\n  }\n\n  .mx-xxl-2 {\n    margin-right: 0.5rem !important;\n    margin-left: 0.5rem !important;\n  }\n\n  .mx-xxl-3 {\n    margin-right: 1rem !important;\n    margin-left: 1rem !important;\n  }\n\n  .mx-xxl-4 {\n    margin-right: 1.5rem !important;\n    margin-left: 1.5rem !important;\n  }\n\n  .mx-xxl-5 {\n    margin-right: 3rem !important;\n    margin-left: 3rem !important;\n  }\n\n  .mx-xxl-auto {\n    margin-right: auto !important;\n    margin-left: auto !important;\n  }\n\n  .my-xxl-0 {\n    margin-top: 0 !important;\n    margin-bottom: 0 !important;\n  }\n\n  .my-xxl-1 {\n    margin-top: 0.25rem !important;\n    margin-bottom: 0.25rem !important;\n  }\n\n  .my-xxl-2 {\n    margin-top: 0.5rem !important;\n    margin-bottom: 0.5rem !important;\n  }\n\n  .my-xxl-3 {\n    margin-top: 1rem !important;\n    margin-bottom: 1rem !important;\n  }\n\n  .my-xxl-4 {\n    margin-top: 1.5rem !important;\n    margin-bottom: 1.5rem !important;\n  }\n\n  .my-xxl-5 {\n    margin-top: 3rem !important;\n    margin-bottom: 3rem !important;\n  }\n\n  .my-xxl-auto {\n    margin-top: auto !important;\n    margin-bottom: auto !important;\n  }\n\n  .mt-xxl-0 {\n    margin-top: 0 !important;\n  }\n\n  .mt-xxl-1 {\n    margin-top: 0.25rem !important;\n  }\n\n  .mt-xxl-2 {\n    margin-top: 0.5rem !important;\n  }\n\n  .mt-xxl-3 {\n    margin-top: 1rem !important;\n  }\n\n  .mt-xxl-4 {\n    margin-top: 1.5rem !important;\n  }\n\n  .mt-xxl-5 {\n    margin-top: 3rem !important;\n  }\n\n  .mt-xxl-auto {\n    margin-top: auto !important;\n  }\n\n  .mr-xxl-0 {\n    margin-right: 0 !important;\n  }\n\n  .mr-xxl-1 {\n    margin-right: 0.25rem !important;\n  }\n\n  .mr-xxl-2 {\n    margin-right: 0.5rem !important;\n  }\n\n  .mr-xxl-3 {\n    margin-right: 1rem !important;\n  }\n\n  .mr-xxl-4 {\n    margin-right: 1.5rem !important;\n  }\n\n  .mr-xxl-5 {\n    margin-right: 3rem !important;\n  }\n\n  .mr-xxl-auto {\n    margin-right: auto !important;\n  }\n\n  .mb-xxl-0 {\n    margin-bottom: 0 !important;\n  }\n\n  .mb-xxl-1 {\n    margin-bottom: 0.25rem !important;\n  }\n\n  .mb-xxl-2 {\n    margin-bottom: 0.5rem !important;\n  }\n\n  .mb-xxl-3 {\n    margin-bottom: 1rem !important;\n  }\n\n  .mb-xxl-4 {\n    margin-bottom: 1.5rem !important;\n  }\n\n  .mb-xxl-5 {\n    margin-bottom: 3rem !important;\n  }\n\n  .mb-xxl-auto {\n    margin-bottom: auto !important;\n  }\n\n  .ml-xxl-0 {\n    margin-left: 0 !important;\n  }\n\n  .ml-xxl-1 {\n    margin-left: 0.25rem !important;\n  }\n\n  .ml-xxl-2 {\n    margin-left: 0.5rem !important;\n  }\n\n  .ml-xxl-3 {\n    margin-left: 1rem !important;\n  }\n\n  .ml-xxl-4 {\n    margin-left: 1.5rem !important;\n  }\n\n  .ml-xxl-5 {\n    margin-left: 3rem !important;\n  }\n\n  .ml-xxl-auto {\n    margin-left: auto !important;\n  }\n\n  .p-xxl-0 {\n    padding: 0 !important;\n  }\n\n  .p-xxl-1 {\n    padding: 0.25rem !important;\n  }\n\n  .p-xxl-2 {\n    padding: 0.5rem !important;\n  }\n\n  .p-xxl-3 {\n    padding: 1rem !important;\n  }\n\n  .p-xxl-4 {\n    padding: 1.5rem !important;\n  }\n\n  .p-xxl-5 {\n    padding: 3rem !important;\n  }\n\n  .px-xxl-0 {\n    padding-right: 0 !important;\n    padding-left: 0 !important;\n  }\n\n  .px-xxl-1 {\n    padding-right: 0.25rem !important;\n    padding-left: 0.25rem !important;\n  }\n\n  .px-xxl-2 {\n    padding-right: 0.5rem !important;\n    padding-left: 0.5rem !important;\n  }\n\n  .px-xxl-3 {\n    padding-right: 1rem !important;\n    padding-left: 1rem !important;\n  }\n\n  .px-xxl-4 {\n    padding-right: 1.5rem !important;\n    padding-left: 1.5rem !important;\n  }\n\n  .px-xxl-5 {\n    padding-right: 3rem !important;\n    padding-left: 3rem !important;\n  }\n\n  .py-xxl-0 {\n    padding-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n\n  .py-xxl-1 {\n    padding-top: 0.25rem !important;\n    padding-bottom: 0.25rem !important;\n  }\n\n  .py-xxl-2 {\n    padding-top: 0.5rem !important;\n    padding-bottom: 0.5rem !important;\n  }\n\n  .py-xxl-3 {\n    padding-top: 1rem !important;\n    padding-bottom: 1rem !important;\n  }\n\n  .py-xxl-4 {\n    padding-top: 1.5rem !important;\n    padding-bottom: 1.5rem !important;\n  }\n\n  .py-xxl-5 {\n    padding-top: 3rem !important;\n    padding-bottom: 3rem !important;\n  }\n\n  .pt-xxl-0 {\n    padding-top: 0 !important;\n  }\n\n  .pt-xxl-1 {\n    padding-top: 0.25rem !important;\n  }\n\n  .pt-xxl-2 {\n    padding-top: 0.5rem !important;\n  }\n\n  .pt-xxl-3 {\n    padding-top: 1rem !important;\n  }\n\n  .pt-xxl-4 {\n    padding-top: 1.5rem !important;\n  }\n\n  .pt-xxl-5 {\n    padding-top: 3rem !important;\n  }\n\n  .pr-xxl-0 {\n    padding-right: 0 !important;\n  }\n\n  .pr-xxl-1 {\n    padding-right: 0.25rem !important;\n  }\n\n  .pr-xxl-2 {\n    padding-right: 0.5rem !important;\n  }\n\n  .pr-xxl-3 {\n    padding-right: 1rem !important;\n  }\n\n  .pr-xxl-4 {\n    padding-right: 1.5rem !important;\n  }\n\n  .pr-xxl-5 {\n    padding-right: 3rem !important;\n  }\n\n  .pb-xxl-0 {\n    padding-bottom: 0 !important;\n  }\n\n  .pb-xxl-1 {\n    padding-bottom: 0.25rem !important;\n  }\n\n  .pb-xxl-2 {\n    padding-bottom: 0.5rem !important;\n  }\n\n  .pb-xxl-3 {\n    padding-bottom: 1rem !important;\n  }\n\n  .pb-xxl-4 {\n    padding-bottom: 1.5rem !important;\n  }\n\n  .pb-xxl-5 {\n    padding-bottom: 3rem !important;\n  }\n\n  .pl-xxl-0 {\n    padding-left: 0 !important;\n  }\n\n  .pl-xxl-1 {\n    padding-left: 0.25rem !important;\n  }\n\n  .pl-xxl-2 {\n    padding-left: 0.5rem !important;\n  }\n\n  .pl-xxl-3 {\n    padding-left: 1rem !important;\n  }\n\n  .pl-xxl-4 {\n    padding-left: 1.5rem !important;\n  }\n\n  .pl-xxl-5 {\n    padding-left: 3rem !important;\n  }\n}\n@media print {\n  .d-print-inline {\n    display: inline !important;\n  }\n\n  .d-print-inline-block {\n    display: inline-block !important;\n  }\n\n  .d-print-block {\n    display: block !important;\n  }\n\n  .d-print-grid {\n    display: grid !important;\n  }\n\n  .d-print-table {\n    display: table !important;\n  }\n\n  .d-print-table-row {\n    display: table-row !important;\n  }\n\n  .d-print-table-cell {\n    display: table-cell !important;\n  }\n\n  .d-print-flex {\n    display: flex !important;\n  }\n\n  .d-print-inline-flex {\n    display: inline-flex !important;\n  }\n\n  .d-print-none {\n    display: none !important;\n  }\n}\n\n/*# sourceMappingURL=bootstrap-grid.css.map */\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n// Color system\n\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n// fusv-disable\n$grays: (\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900\n) !default;\n// fusv-enable\n\n$blue:    #0d6efd !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #d63384 !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #198754 !default;\n$teal:    #20c997 !default;\n$cyan:    #0dcaf0 !default;\n\n// scss-docs-start colors-map\n$colors: (\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n) !default;\n// scss-docs-end colors-map\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-900 !default;\n\n// scss-docs-start theme-colors-map\n$theme-colors: (\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n) !default;\n// scss-docs-end theme-colors-map\n\n// The contrast ratio to reach against white, to determine if color changes from \"light\" to \"dark\". Acceptable values for WCAG 2.0 are 3, 4.5 and 7.\n// See https://www.w3.org/TR/WCAG20/#visual-audio-contrast-contrast\n$min-contrast-ratio:   4.5 !default;\n\n// Customize the light and dark text colors for use in our color contrast function.\n$color-contrast-dark:      $black !default;\n$color-contrast-light:     $white !default;\n\n// fusv-disable\n$blue-100: tint-color($blue, 80%) !default;\n$blue-200: tint-color($blue, 60%) !default;\n$blue-300: tint-color($blue, 40%) !default;\n$blue-400: tint-color($blue, 20%) !default;\n$blue-500: $blue !default;\n$blue-600: shade-color($blue, 20%) !default;\n$blue-700: shade-color($blue, 40%) !default;\n$blue-800: shade-color($blue, 60%) !default;\n$blue-900: shade-color($blue, 80%) !default;\n\n$indigo-100: tint-color($indigo, 80%) !default;\n$indigo-200: tint-color($indigo, 60%) !default;\n$indigo-300: tint-color($indigo, 40%) !default;\n$indigo-400: tint-color($indigo, 20%) !default;\n$indigo-500: $indigo !default;\n$indigo-600: shade-color($indigo, 20%) !default;\n$indigo-700: shade-color($indigo, 40%) !default;\n$indigo-800: shade-color($indigo, 60%) !default;\n$indigo-900: shade-color($indigo, 80%) !default;\n\n$purple-100: tint-color($purple, 80%) !default;\n$purple-200: tint-color($purple, 60%) !default;\n$purple-300: tint-color($purple, 40%) !default;\n$purple-400: tint-color($purple, 20%) !default;\n$purple-500: $purple !default;\n$purple-600: shade-color($purple, 20%) !default;\n$purple-700: shade-color($purple, 40%) !default;\n$purple-800: shade-color($purple, 60%) !default;\n$purple-900: shade-color($purple, 80%) !default;\n\n$pink-100: tint-color($pink, 80%) !default;\n$pink-200: tint-color($pink, 60%) !default;\n$pink-300: tint-color($pink, 40%) !default;\n$pink-400: tint-color($pink, 20%) !default;\n$pink-500: $pink !default;\n$pink-600: shade-color($pink, 20%) !default;\n$pink-700: shade-color($pink, 40%) !default;\n$pink-800: shade-color($pink, 60%) !default;\n$pink-900: shade-color($pink, 80%) !default;\n\n$red-100: tint-color($red, 80%) !default;\n$red-200: tint-color($red, 60%) !default;\n$red-300: tint-color($red, 40%) !default;\n$red-400: tint-color($red, 20%) !default;\n$red-500: $red !default;\n$red-600: shade-color($red, 20%) !default;\n$red-700: shade-color($red, 40%) !default;\n$red-800: shade-color($red, 60%) !default;\n$red-900: shade-color($red, 80%) !default;\n\n$orange-100: tint-color($orange, 80%) !default;\n$orange-200: tint-color($orange, 60%) !default;\n$orange-300: tint-color($orange, 40%) !default;\n$orange-400: tint-color($orange, 20%) !default;\n$orange-500: $orange !default;\n$orange-600: shade-color($orange, 20%) !default;\n$orange-700: shade-color($orange, 40%) !default;\n$orange-800: shade-color($orange, 60%) !default;\n$orange-900: shade-color($orange, 80%) !default;\n\n$yellow-100: tint-color($yellow, 80%) !default;\n$yellow-200: tint-color($yellow, 60%) !default;\n$yellow-300: tint-color($yellow, 40%) !default;\n$yellow-400: tint-color($yellow, 20%) !default;\n$yellow-500: $yellow !default;\n$yellow-600: shade-color($yellow, 20%) !default;\n$yellow-700: shade-color($yellow, 40%) !default;\n$yellow-800: shade-color($yellow, 60%) !default;\n$yellow-900: shade-color($yellow, 80%) !default;\n\n$green-100: tint-color($green, 80%) !default;\n$green-200: tint-color($green, 60%) !default;\n$green-300: tint-color($green, 40%) !default;\n$green-400: tint-color($green, 20%) !default;\n$green-500: $green !default;\n$green-600: shade-color($green, 20%) !default;\n$green-700: shade-color($green, 40%) !default;\n$green-800: shade-color($green, 60%) !default;\n$green-900: shade-color($green, 80%) !default;\n\n$teal-100: tint-color($teal, 80%) !default;\n$teal-200: tint-color($teal, 60%) !default;\n$teal-300: tint-color($teal, 40%) !default;\n$teal-400: tint-color($teal, 20%) !default;\n$teal-500: $teal !default;\n$teal-600: shade-color($teal, 20%) !default;\n$teal-700: shade-color($teal, 40%) !default;\n$teal-800: shade-color($teal, 60%) !default;\n$teal-900: shade-color($teal, 80%) !default;\n\n$cyan-100: tint-color($cyan, 80%) !default;\n$cyan-200: tint-color($cyan, 60%) !default;\n$cyan-300: tint-color($cyan, 40%) !default;\n$cyan-400: tint-color($cyan, 20%) !default;\n$cyan-500: $cyan !default;\n$cyan-600: shade-color($cyan, 20%) !default;\n$cyan-700: shade-color($cyan, 40%) !default;\n$cyan-800: shade-color($cyan, 60%) !default;\n$cyan-900: shade-color($cyan, 80%) !default;\n// fusv-enable\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                true !default;\n$enable-rounded:              true !default;\n$enable-shadows:              false !default;\n$enable-gradients:            false !default;\n$enable-transitions:          true !default;\n$enable-reduced-motion:       true !default;\n$enable-smooth-scroll:        true !default;\n$enable-grid-classes:         true !default;\n$enable-button-pointers:      true !default;\n$enable-rfs:                  true !default;\n$enable-validation-icons:     true !default;\n$enable-negative-margins:     false !default;\n$enable-deprecation-messages: true !default;\n$enable-important-utilities:  true !default;\n\n// Prefix for :root CSS variables\n\n$variable-prefix:             bs- !default;\n\n// Gradient\n//\n// The gradient which is added to components if `$enable-gradients` is `true`\n// This gradient is also added to elements with `.bg-gradient`\n$gradient: linear-gradient(180deg, rgba($white, .15), rgba($white, 0)) !default;\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n$spacer: 1rem !default;\n$spacers: (\n  0: 0,\n  1: $spacer / 4,\n  2: $spacer / 2,\n  3: $spacer,\n  4: $spacer * 1.5,\n  5: $spacer * 3,\n) !default;\n\n$negative-spacers: if($enable-negative-margins, negativify-map($spacers), null) !default;\n\n// Position\n//\n// Define the edge positioning anchors of the position utilities.\n\n$position-values: (\n  0: 0,\n  50: 50%,\n  100: 100%\n) !default;\n\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n$body-text-align:           null !default;\n\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                              $primary !default;\n$link-decoration:                         underline !default;\n$link-shade-percentage:                   20% !default;\n$link-hover-color:                        scale-color($link-color, $link-shade-percentage) !default;\n$link-hover-decoration:                   null !default;\n\n$stretched-link-pseudo-element:           after !default;\n$stretched-link-z-index:                  1 !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n// scss-docs-start grid-breakpoints\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px,\n  xxl: 1400px\n) !default;\n// scss-docs-end grid-breakpoints\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints, \"$grid-breakpoints\");\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n// scss-docs-start container-max-widths\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px,\n  xxl: 1320px\n) !default;\n// scss-docs-end container-max-widths\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           1.5rem !default;\n$grid-row-columns:            6 !default;\n\n$gutters: $spacers !default;\n\n// Container padding\n\n$container-padding-x: $grid-gutter-width !default;\n\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$border-width:                1px !default;\n$border-widths: (\n  0: 0,\n  1: 1px,\n  2: 2px,\n  3: 3px,\n  4: 4px,\n  5: 5px\n) !default;\n\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-sm:            .2rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-pill:          50rem !default;\n\n$box-shadow:                  0 .5rem 1rem rgba($black, .15) !default;\n$box-shadow-sm:               0 .125rem .25rem rgba($black, .075) !default;\n$box-shadow-lg:               0 1rem 3rem rgba($black, .175) !default;\n$box-shadow-inset:            inset 0 1px 2px rgba($black, .075) !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         $primary !default;\n\n$caret-width:                 .3em !default;\n$caret-vertical-align:        $caret-width * .85 !default;\n$caret-spacing:               $caret-width * .85 !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n// stylelint-disable function-disallowed-list\n// scss-docs-start aspect-ratios\n$aspect-ratios: (\n  \"1x1\": 100%,\n  \"4x3\": calc(3 / 4 * 100%),\n  \"16x9\": calc(9 / 16 * 100%),\n  \"21x9\": calc(9 / 21 * 100%)\n) !default;\n// scss-docs-end aspect-ratios\n// stylelint-enable function-disallowed-list\n\n// Typography\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", \"Liberation Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n// stylelint-enable value-keyword-case\n$font-family-base:            var(--#{$variable-prefix}font-sans-serif) !default;\n$font-family-code:            var(--#{$variable-prefix}font-monospace) !default;\n\n// $font-size-root effects the value of `rem`, which is used for as well font sizes, paddings and margins\n// $font-size-base effects the font size of the body text\n$font-size-root:              null !default;\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-sm:                $font-size-base * .875 !default;\n$font-size-lg:                $font-size-base * 1.25 !default;\n\n$font-weight-lighter:         lighter !default;\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n$font-weight-bolder:          bolder !default;\n\n$font-weight-base:            $font-weight-normal !default;\n\n$line-height-base:            1.5 !default;\n$line-height-sm:              1.25 !default;\n$line-height-lg:              2 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n// scss-docs-start font-sizes\n$font-sizes: (\n  1: $h1-font-size,\n  2: $h2-font-size,\n  3: $h3-font-size,\n  4: $h4-font-size,\n  5: $h5-font-size,\n  6: $h6-font-size\n) !default;\n// scss-docs-end font-sizes\n\n$headings-margin-bottom:      $spacer / 2 !default;\n$headings-font-family:        null !default;\n$headings-font-style:         null !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              null !default;\n\n// scss-docs-start display-headings\n$display-font-sizes: (\n  1: 5rem,\n  2: 4.5rem,\n  3: 4rem,\n  4: 3.5rem,\n  5: 3rem,\n  6: 2.5rem\n) !default;\n\n$display-font-weight: 300 !default;\n$display-line-height: $headings-line-height !default;\n// scss-docs-end display-headings\n\n$lead-font-size:              $font-size-base * 1.25 !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             .875em !default;\n\n$sub-sup-font-size:           .75em !default;\n\n$text-muted:                  $gray-600 !default;\n\n$initialism-font-size:        $small-font-size !default;\n\n$blockquote-margin-y:         $spacer !default;\n$blockquote-font-size:        $font-size-base * 1.25 !default;\n$blockquote-footer-color:     $gray-600 !default;\n$blockquote-footer-font-size: $small-font-size !default;\n\n$hr-margin-y:                 $spacer !default;\n$hr-color:                    inherit !default;\n$hr-height:                   $border-width !default;\n$hr-opacity:                  .25 !default;\n\n$legend-margin-bottom:        .5rem !default;\n$legend-font-size:            1.5rem !default;\n$legend-font-weight:          null !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n// scss-docs-start table-variables\n$table-cell-padding-y:        .5rem !default;\n$table-cell-padding-x:        .5rem !default;\n$table-cell-padding-y-sm:     .25rem !default;\n$table-cell-padding-x-sm:     .25rem !default;\n\n$table-cell-vertical-align:   top !default;\n\n$table-color:                 $body-color !default;\n$table-bg:                    transparent !default;\n\n$table-th-font-weight:        null !default;\n\n$table-striped-color:         $table-color !default;\n$table-striped-bg-factor:     .05 !default;\n$table-striped-bg:            rgba($black, $table-striped-bg-factor) !default;\n\n$table-active-color:          $table-color !default;\n$table-active-bg-factor:      .1 !default;\n$table-active-bg:             rgba($black, $table-active-bg-factor) !default;\n\n$table-hover-color:           $table-color !default;\n$table-hover-bg-factor:       .075 !default;\n$table-hover-bg:              rgba($black, $table-hover-bg-factor) !default;\n\n$table-border-factor:         .1 !default;\n$table-border-width:          $border-width !default;\n$table-border-color:          $border-color !default;\n\n$table-striped-order:         odd !default;\n\n$table-group-separator-color: currentColor !default;\n\n$table-caption-color:         $text-muted !default;\n\n$table-bg-scale:              -80% !default;\n\n$table-variants: (\n  \"primary\":    scale-color($primary, $table-bg-scale),\n  \"secondary\":  scale-color($secondary, $table-bg-scale),\n  \"success\":    scale-color($success, $table-bg-scale),\n  \"info\":       scale-color($info, $table-bg-scale),\n  \"warning\":    scale-color($warning, $table-bg-scale),\n  \"danger\":     scale-color($danger, $table-bg-scale),\n  \"light\":      $light,\n  \"dark\":       $dark,\n) !default;\n// scss-docs-end table-variables\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-font-family:       null !default;\n$input-btn-font-size:         $font-size-base !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:         .25rem !default;\n$input-btn-focus-color-opacity: .25 !default;\n$input-btn-focus-color:         rgba($component-active-bg, $input-btn-focus-color-opacity) !default;\n$input-btn-focus-box-shadow:    0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-font-size-sm:      $font-size-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-font-size-lg:      $font-size-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-font-family:             $input-btn-font-family !default;\n$btn-font-size:               $input-btn-font-size !default;\n$btn-line-height:             $input-btn-line-height !default;\n$btn-white-space:             null !default; // Set to `nowrap` to prevent text wrapping\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-font-size-sm:            $input-btn-font-size-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-font-size-lg:            $input-btn-font-size-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              inset 0 1px 0 rgba($white, .15), 0 1px 1px rgba($black, .075) !default;\n$btn-focus-width:             $input-btn-focus-width !default;\n$btn-focus-box-shadow:        $input-btn-focus-box-shadow !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       inset 0 3px 5px rgba($black, .125) !default;\n\n$btn-link-color:              $link-color !default;\n$btn-link-hover-color:        $link-hover-color !default;\n$btn-link-disabled-color:     $gray-600 !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$form-text-margin-top:                  .25rem !default;\n$form-text-font-size:                   $small-font-size !default;\n$form-text-font-style:                  null !default;\n$form-text-font-weight:                 null !default;\n$form-text-color:                       $text-muted !default;\n\n$form-label-margin-bottom:              .5rem !default;\n$form-label-font-size:                  null !default;\n$form-label-font-style:                 null !default;\n$form-label-font-weight:                null !default;\n$form-label-color:                      null !default;\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-font-family:                     $input-btn-font-family !default;\n$input-font-size:                       $input-btn-font-size !default;\n$input-font-weight:                     $font-weight-base !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-font-size-sm:                    $input-btn-font-size-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-font-size-lg:                    $input-btn-font-size-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n$input-disabled-border-color:           null !default;\n\n$input-color:                           $body-color !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      $box-shadow-inset !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              tint-color($component-active-bg, 50%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     $input-btn-focus-width !default;\n$input-focus-box-shadow:                $input-btn-focus-box-shadow !default;\n\n$input-placeholder-color:               $gray-600 !default;\n$input-plaintext-color:                 $body-color !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    add($input-line-height * 1em, $input-padding-y * 2) !default;\n$input-height-inner-half:               add($input-line-height * .5em, $input-padding-y) !default;\n$input-height-inner-quarter:            add($input-line-height * .25em, $input-padding-y / 2) !default;\n\n$input-height:                          add($input-line-height * 1em, add($input-padding-y * 2, $input-height-border, false)) !default;\n$input-height-sm:                       add($input-line-height * 1em, add($input-padding-y-sm * 2, $input-height-border, false)) !default;\n$input-height-lg:                       add($input-line-height * 1em, add($input-padding-y-lg * 2, $input-height-border, false)) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n$form-check-input-width:                  1em !default;\n$form-check-min-height:                   $font-size-base * $line-height-base !default;\n$form-check-padding-left:                 $form-check-input-width + .5em !default;\n$form-check-margin-bottom:                .125rem !default;\n$form-check-label-color:                  null !default;\n$form-check-label-cursor:                 null !default;\n$form-check-transition:                   background-color .15s ease-in-out, background-position .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-check-input-active-filter:          brightness(90%) !default;\n\n$form-check-input-bg:                     $body-bg !default;\n$form-check-input-border:                 1px solid rgba(0, 0, 0, .25) !default;\n$form-check-input-border-radius:          .25em !default;\n$form-check-radio-border-radius:          50% !default;\n$form-check-input-focus-border:           $input-focus-border-color !default;\n$form-check-input-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n\n$form-check-input-checked-color:          $component-active-color !default;\n$form-check-input-checked-bg-color:       $component-active-bg !default;\n$form-check-input-checked-border-color:   $form-check-input-checked-bg-color !default;\n$form-check-input-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-checked-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/></svg>\") !default;\n$form-check-radio-checked-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='2' fill='#{$form-check-input-checked-color}'/></svg>\") !default;\n\n$form-check-input-indeterminate-color:          $component-active-color !default;\n$form-check-input-indeterminate-bg-color:       $component-active-bg !default;\n$form-check-input-indeterminate-border-color:   $form-check-input-indeterminate-bg-color !default;\n$form-check-input-indeterminate-bg-image:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$form-check-input-indeterminate-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/></svg>\") !default;\n\n$form-check-input-disabled-opacity:        .5 !default;\n$form-check-label-disabled-opacity:        $form-check-input-disabled-opacity !default;\n$form-check-btn-check-disabled-opacity:    $btn-disabled-opacity !default;\n\n$form-switch-color:               rgba(0, 0, 0, .25) !default;\n$form-switch-width:               2em !default;\n$form-switch-padding-left:        $form-switch-width + .5em !default;\n$form-switch-bg-image:            url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-color}'/></svg>\") !default;\n$form-switch-border-radius:       $form-switch-width !default;\n\n$form-switch-focus-color:         $input-focus-border-color !default;\n$form-switch-focus-bg-image:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-focus-color}'/></svg>\") !default;\n\n$form-switch-checked-color:       $component-active-color !default;\n$form-switch-checked-bg-image:    url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$form-switch-checked-color}'/></svg>\") !default;\n$form-switch-checked-bg-position: right center !default;\n\n$form-check-inline-margin-right:        1rem !default;\n\n$input-group-addon-padding-y:           $input-padding-y !default;\n$input-group-addon-padding-x:           $input-padding-x !default;\n$input-group-addon-font-weight:         $input-font-weight !default;\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$form-select-padding-y:             $input-padding-y !default;\n$form-select-padding-x:             $input-padding-x !default;\n$form-select-font-family:           $input-font-family !default;\n$form-select-font-size:             $input-font-size !default;\n$form-select-indicator-padding:     1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$form-select-font-weight:           $input-font-weight !default;\n$form-select-line-height:           $input-line-height !default;\n$form-select-color:                 $input-color !default;\n$form-select-disabled-color:        $gray-600 !default;\n$form-select-bg:                    $input-bg !default;\n$form-select-disabled-bg:           $gray-200 !default;\n$form-select-disabled-border-color: $input-disabled-border-color !default;\n$form-select-bg-position:           right $form-select-padding-x center !default;\n$form-select-bg-size:               16px 12px !default; // In pixels because image dimensions\n$form-select-indicator-color:       $gray-800 !default;\n$form-select-indicator:             url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='#{$form-select-indicator-color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/></svg>\") !default;\n\n$form-select-feedback-icon-padding-right: add(1em * .75, (2 * $form-select-padding-y * .75) + $form-select-padding-x + $form-select-indicator-padding) !default;\n$form-select-feedback-icon-position:      center right ($form-select-padding-x + $form-select-indicator-padding) !default;\n$form-select-feedback-icon-size:          $input-height-inner-half $input-height-inner-half !default;\n\n$form-select-border-width:        $input-border-width !default;\n$form-select-border-color:        $input-border-color !default;\n$form-select-border-radius:       $border-radius !default;\n$form-select-box-shadow:          $box-shadow-inset !default;\n\n$form-select-focus-border-color:  $input-focus-border-color !default;\n$form-select-focus-width:         $input-focus-width !default;\n$form-select-focus-box-shadow:    0 0 0 $form-select-focus-width $input-btn-focus-color !default;\n\n$form-select-padding-y-sm:        $input-padding-y-sm !default;\n$form-select-padding-x-sm:        $input-padding-x-sm !default;\n$form-select-font-size-sm:        $input-font-size-sm !default;\n\n$form-select-padding-y-lg:        $input-padding-y-lg !default;\n$form-select-padding-x-lg:        $input-padding-x-lg !default;\n$form-select-font-size-lg:        $input-font-size-lg !default;\n\n$form-range-track-width:          100% !default;\n$form-range-track-height:         .5rem !default;\n$form-range-track-cursor:         pointer !default;\n$form-range-track-bg:             $gray-300 !default;\n$form-range-track-border-radius:  1rem !default;\n$form-range-track-box-shadow:     $box-shadow-inset !default;\n\n$form-range-thumb-width:                   1rem !default;\n$form-range-thumb-height:                  $form-range-thumb-width !default;\n$form-range-thumb-bg:                      $component-active-bg !default;\n$form-range-thumb-border:                  0 !default;\n$form-range-thumb-border-radius:           1rem !default;\n$form-range-thumb-box-shadow:              0 .1rem .25rem rgba($black, .1) !default;\n$form-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-focus-box-shadow !default;\n$form-range-thumb-focus-box-shadow-width:  $input-focus-width !default; // For focus box shadow issue in Edge\n$form-range-thumb-active-bg:               tint-color($component-active-bg, 70%) !default;\n$form-range-thumb-disabled-bg:             $gray-500 !default;\n$form-range-thumb-transition:              background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-file-button-color:          $input-color !default;\n$form-file-button-bg:             $input-group-addon-bg !default;\n$form-file-button-hover-bg:       shade-color($form-file-button-bg, 5%) !default;\n\n$form-floating-height:            add(3.5rem, $input-height-border) !default;\n$form-floating-padding-x:         $input-padding-x !default;\n$form-floating-padding-y:         1rem !default;\n$form-floating-input-padding-t:   1.625rem !default;\n$form-floating-input-padding-b:   .625rem !default;\n$form-floating-label-opacity:     .65 !default;\n$form-floating-label-transform:   scale(.85) translateY(-.5rem) translateX(.15rem) !default;\n$form-floating-transition:        opacity .1s ease-in-out, transform .1s ease-in-out !default;\n\n// Form validation\n\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $form-text-font-size !default;\n$form-feedback-font-style:          $form-text-font-style !default;\n$form-feedback-valid-color:         $success !default;\n$form-feedback-invalid-color:       $danger !default;\n\n$form-feedback-icon-valid-color:    $form-feedback-valid-color !default;\n$form-feedback-icon-valid:          url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path fill='#{$form-feedback-icon-valid-color}' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/></svg>\") !default;\n$form-feedback-icon-invalid-color:  $form-feedback-invalid-color !default;\n$form-feedback-icon-invalid:        url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='#{$form-feedback-icon-invalid-color}'><circle cx='6' cy='6' r='4.5'/><path stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/><circle cx='6' cy='8.2' r='.6' fill='#{$form-feedback-icon-invalid-color}' stroke='none'/></svg>\") !default;\n\n// scss-docs-start form-validation-states\n$form-validation-states: (\n  \"valid\": (\n    \"color\": $form-feedback-valid-color,\n    \"icon\": $form-feedback-icon-valid\n  ),\n  \"invalid\": (\n    \"color\": $form-feedback-invalid-color,\n    \"icon\": $form-feedback-icon-invalid\n  )\n) !default;\n// scss-docs-end form-validation-states\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n// scss-docs-start zindex-stack\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n// scss-docs-end zindex-stack\n\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-font-size:                null !default;\n$nav-link-font-weight:              null !default;\n$nav-link-color:                    null !default;\n$nav-link-hover-color:              null !default;\n$nav-link-transition:               color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n\n// Navbar\n\n$navbar-padding-y:                  $spacer / 2 !default;\n$navbar-padding-x:                  null !default;\n\n$navbar-nav-link-padding-x:         .5rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   $font-size-base * $line-height-base + $nav-link-padding-y * 2 !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) / 2 !default;\n$navbar-brand-margin-right:         1rem !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n$navbar-toggler-focus-width:        $btn-focus-width !default;\n$navbar-toggler-transition:         box-shadow .15s ease-in-out !default;\n\n$navbar-dark-color:                 rgba($white, .55) !default;\n$navbar-dark-hover-color:           rgba($white, .75) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-dark-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .55) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path stroke='#{$navbar-light-color}' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/></svg>\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n$navbar-light-brand-color:                $navbar-light-active-color !default;\n$navbar-light-brand-hover-color:          $navbar-light-active-color !default;\n$navbar-dark-brand-color:                 $navbar-dark-active-color !default;\n$navbar-dark-brand-hover-color:           $navbar-dark-active-color !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-x:                0 !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-font-size:                $font-size-base !default;\n$dropdown-color:                    $body-color !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-inner-border-radius:      subtract($dropdown-border-radius, $dropdown-border-width) !default;\n$dropdown-divider-bg:               $dropdown-border-color !default;\n$dropdown-divider-margin-y:         $spacer / 2 !default;\n$dropdown-box-shadow:               $box-shadow !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         shade-color($gray-900, 10%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           $spacer / 4 !default;\n$dropdown-item-padding-x:           $spacer !default;\n\n$dropdown-header-color:             $gray-600 !default;\n$dropdown-header-padding:           $dropdown-padding-y $dropdown-item-padding-x !default;\n\n$dropdown-dark-color:               $gray-300 !default;\n$dropdown-dark-bg:                  $gray-800 !default;\n$dropdown-dark-border-color:        $dropdown-border-color !default;\n$dropdown-dark-divider-bg:          $dropdown-divider-bg !default;\n$dropdown-dark-box-shadow:          null !default;\n$dropdown-dark-link-color:          $dropdown-dark-color !default;\n$dropdown-dark-link-hover-color:    $white !default;\n$dropdown-dark-link-hover-bg:       rgba($white, .15) !default;\n$dropdown-dark-link-active-color:   $dropdown-link-active-color !default;\n$dropdown-dark-link-active-bg:      $dropdown-link-active-bg !default;\n$dropdown-dark-link-disabled-color: $gray-500 !default;\n$dropdown-dark-header-color:        $gray-500 !default;\n\n\n// Pagination\n\n$pagination-padding-y:              .375rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-radius:          $border-radius !default;\n$pagination-margin-left:            -$pagination-border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-color:            $link-hover-color !default;\n$pagination-focus-bg:               $gray-200 !default;\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n$pagination-focus-outline:          0 !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n$pagination-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n// Cards\n\n$card-spacer-y:                     $spacer !default;\n$card-spacer-x:                     $spacer !default;\n$card-title-spacer-y:               $spacer / 2 !default;\n$card-border-width:                 $border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          subtract($card-border-radius, $card-border-width) !default;\n$card-cap-padding-y:                $card-spacer-y / 2 !default;\n$card-cap-padding-x:                $card-spacer-x !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-cap-color:                    null !default;\n$card-height:                       null !default;\n$card-color:                        null !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          $spacer !default;\n\n$card-group-margin:                 $grid-gutter-width / 2 !default;\n\n// Accordion\n$accordion-padding-y:                     1rem !default;\n$accordion-padding-x:                     1.25rem !default;\n$accordion-color:                         $body-color !default;\n$accordion-bg:                            transparent !default;\n$accordion-border-width:                  $border-width !default;\n$accordion-border-color:                  rgba($black, .125) !default;\n$accordion-border-radius:                 $border-radius !default;\n\n$accordion-body-padding-y:                $accordion-padding-y !default;\n$accordion-body-padding-x:                $accordion-padding-x !default;\n\n$accordion-button-padding-y:              $accordion-padding-y !default;\n$accordion-button-padding-x:              $accordion-padding-x !default;\n$accordion-button-color:                  $accordion-color !default;\n$accordion-button-bg:                     $accordion-bg !default;\n$accordion-transition:                    $btn-transition, border-radius .15s ease !default;\n$accordion-button-active-bg:              tint-color($component-active-bg, 90%) !default;\n$accordion-button-active-color:           shade-color($primary, 10%) !default;\n\n$accordion-button-focus-border-color:     $input-focus-border-color !default;\n$accordion-button-focus-box-shadow:       $btn-focus-box-shadow !default;\n\n$accordion-icon-width:                    1.25rem !default;\n$accordion-icon-color:                    $accordion-color !default;\n$accordion-icon-active-color:             $accordion-button-active-color !default;\n$accordion-icon-transition:               transform .2s ease-in-out !default;\n$accordion-icon-transform:                rotate(180deg) !default;\n\n$accordion-button-icon:         url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n$accordion-button-active-icon:  url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$accordion-icon-active-color}'><path fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n\n// Tooltips\n\n$tooltip-font-size:                 $font-size-sm !default;\n$tooltip-max-width:                 200px !default;\n$tooltip-color:                     $white !default;\n$tooltip-bg:                        $black !default;\n$tooltip-border-radius:             $border-radius !default;\n$tooltip-opacity:                   .9 !default;\n$tooltip-padding-y:                 $spacer / 4 !default;\n$tooltip-padding-x:                 $spacer / 2 !default;\n$tooltip-margin:                    0 !default;\n\n$tooltip-arrow-width:               .8rem !default;\n$tooltip-arrow-height:              .4rem !default;\n$tooltip-arrow-color:               $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   null !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-inner-border-radius:       subtract($popover-border-radius, $popover-border-width) !default;\n$popover-box-shadow:                $box-shadow !default;\n\n$popover-header-bg:                 shade-color($popover-bg, 6%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          $spacer !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $spacer !default;\n$popover-body-padding-x:            $spacer !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Toasts\n\n$toast-max-width:                   350px !default;\n$toast-padding-x:                   .75rem !default;\n$toast-padding-y:                   .5rem !default;\n$toast-font-size:                   .875rem !default;\n$toast-color:                       null !default;\n$toast-background-color:            rgba($white, .85) !default;\n$toast-border-width:                1px !default;\n$toast-border-color:                rgba(0, 0, 0, .1) !default;\n$toast-border-radius:               $border-radius !default;\n$toast-box-shadow:                  $box-shadow !default;\n\n$toast-header-color:                $gray-600 !default;\n$toast-header-background-color:     rgba($white, .85) !default;\n$toast-header-border-color:         rgba(0, 0, 0, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   .75em !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-color:                       $white !default;\n$badge-padding-y:                   .35em !default;\n$badge-padding-x:                   .65em !default;\n$badge-border-radius:               $border-radius !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:               $spacer !default;\n\n// Margin between elements in footer, must be lower than or equal to 2 * $modal-inner-padding\n$modal-footer-margin-between:       .5rem !default;\n\n$modal-dialog-margin:               .5rem !default;\n$modal-dialog-margin-y-sm-up:       1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-color:               null !default;\n$modal-content-bg:                  $white !default;\n$modal-content-border-color:        rgba($black, .2) !default;\n$modal-content-border-width:        $border-width !default;\n$modal-content-border-radius:       $border-radius-lg !default;\n$modal-content-inner-border-radius: subtract($modal-content-border-radius, $modal-content-border-width) !default;\n$modal-content-box-shadow-xs:       $box-shadow-sm !default;\n$modal-content-box-shadow-sm-up:    $box-shadow !default;\n\n$modal-backdrop-bg:                 $black !default;\n$modal-backdrop-opacity:            .5 !default;\n$modal-header-border-color:         $border-color !default;\n$modal-footer-border-color:         $modal-header-border-color !default;\n$modal-header-border-width:         $modal-content-border-width !default;\n$modal-footer-border-width:         $modal-header-border-width !default;\n$modal-header-padding-y:            $modal-inner-padding !default;\n$modal-header-padding-x:            $modal-inner-padding !default;\n$modal-header-padding:              $modal-header-padding-y $modal-header-padding-x !default; // Keep this for backwards compatibility\n\n$modal-sm:                          300px !default;\n$modal-md:                          500px !default;\n$modal-lg:                          800px !default;\n$modal-xl:                          1140px !default;\n\n$modal-fade-transform:              translate(0, -50px) !default;\n$modal-show-transform:              none !default;\n$modal-transition:                  transform .3s ease-out !default;\n$modal-scale-transform:             scale(1.02) !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   $spacer !default;\n$alert-padding-x:                   $spacer !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-scale:                    -80% !default;\n$alert-border-scale:                -70% !default;\n$alert-color-scale:                 40% !default;\n\n$alert-dismissible-padding-r:       $alert-padding-x * 3 !default; // 3x covers width of x plus default padding on either side\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                $font-size-base * .75 !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               $box-shadow-inset !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   $primary !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n\n// List group\n\n$list-group-color:                  null !default;\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         $spacer / 2 !default;\n$list-group-item-padding-x:         $spacer !default;\n$list-group-item-bg-scale:          -80% !default;\n$list-group-item-color-scale:       40% !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              $box-shadow-sm !default;\n\n\n// Figures\n\n$figure-caption-font-size:          $small-font-size !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-font-size:              null !default;\n$breadcrumb-padding-y:              $spacer / 2 !default;\n$breadcrumb-padding-x:              $spacer !default;\n$breadcrumb-item-padding-x:         .5rem !default;\n$breadcrumb-margin-bottom:          1rem !default;\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                quote(\"/\") !default;\n$breadcrumb-border-radius:          $border-radius !default;\n\n// Carousel\n\n$carousel-control-color:             $white !default;\n$carousel-control-width:             15% !default;\n$carousel-control-opacity:           .5 !default;\n$carousel-control-hover-opacity:     .9 !default;\n$carousel-control-transition:        opacity .15s ease !default;\n\n$carousel-indicator-width:           30px !default;\n$carousel-indicator-height:          3px !default;\n$carousel-indicator-hit-area-height: 10px !default;\n$carousel-indicator-spacer:          3px !default;\n$carousel-indicator-opacity:         .5 !default;\n$carousel-indicator-active-bg:       $white !default;\n$carousel-indicator-active-opacity:  1 !default;\n$carousel-indicator-transition:      opacity .6s ease !default;\n\n$carousel-caption-width:             70% !default;\n$carousel-caption-color:             $white !default;\n$carousel-caption-padding-y:         1.25rem !default;\n$carousel-caption-spacer:            1.25rem !default;\n\n$carousel-control-icon-width:        2rem !default;\n\n$carousel-control-prev-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>\") !default;\n$carousel-control-next-icon-bg:      url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$carousel-control-color}'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>\") !default;\n\n$carousel-transition-duration:       .6s !default;\n$carousel-transition:                transform $carousel-transition-duration ease-in-out !default; // Define transform transition first if using multiple transitions (e.g., `transform 2s ease, opacity .5s ease-out`)\n\n$carousel-dark-indicator-active-bg:  $black !default;\n$carousel-dark-caption-color:        $black !default;\n$carousel-dark-control-icon-filter:  invert(1) grayscale(100) !default;\n\n\n// Spinners\n\n$spinner-width:           2rem !default;\n$spinner-height:          $spinner-width !default;\n$spinner-border-width:    .25em !default;\n$spinner-animation-speed: .75s !default;\n\n$spinner-width-sm:        1rem !default;\n$spinner-height-sm:       $spinner-width-sm !default;\n$spinner-border-width-sm: .2em !default;\n\n\n// Close\n\n$btn-close-width:            1em !default;\n$btn-close-height:           $btn-close-width !default;\n$btn-close-padding-x:        .25em !default;\n$btn-close-padding-y:        $btn-close-padding-x !default;\n$btn-close-color:            $black !default;\n$btn-close-bg:               url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#{$btn-close-color}'><path d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/></svg>\") !default;\n$btn-close-focus-shadow:     $input-btn-focus-box-shadow !default;\n$btn-close-opacity:          .5 !default;\n$btn-close-hover-opacity:    .75 !default;\n$btn-close-focus-opacity:    1 !default;\n$btn-close-disabled-opacity: .25 !default;\n$btn-close-white-filter:     invert(1) grayscale(100%) brightness(200%) !default;\n\n// Code\n\n$code-font-size:                    $small-font-size !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         null !default;\n", "// Row\n//\n// Rows contain your columns.\n\n@if $enable-grid-classes {\n  .row {\n    @include make-row();\n\n    > * {\n      @include make-col-ready();\n    }\n  }\n}\n\n\n// Columns\n//\n// Common styles for small and large grid columns\n\n@if $enable-grid-classes {\n  @include make-grid-columns();\n}\n", "/// Grid system\n//\n// Generate semantic grid columns with these mixins.\n\n@mixin make-row($gutter: $grid-gutter-width) {\n  --#{$variable-prefix}gutter-x: #{$gutter};\n  --#{$variable-prefix}gutter-y: 0;\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: calc(var(--#{$variable-prefix}gutter-y) * -1); // stylelint-disable-line function-disallowed-list\n  margin-right: calc(var(--#{$variable-prefix}gutter-x) / -2); // stylelint-disable-line function-disallowed-list\n  margin-left: calc(var(--#{$variable-prefix}gutter-x) / -2); // stylelint-disable-line function-disallowed-list\n}\n\n@mixin make-col-ready($gutter: $grid-gutter-width) {\n  // Add box sizing if only the grid is loaded\n  box-sizing: if(variable-exists(include-column-box-sizing) and $include-column-box-sizing, border-box, null);\n  // Prevent columns from becoming too narrow when at smaller grid tiers by\n  // always setting `width: 100%;`. This works because we set the width\n  // later on to override this initial width.\n  flex-shrink: 0;\n  width: 100%;\n  max-width: 100%; // Prevent `.col-auto`, `.col` (& responsive variants) from breaking out the grid\n  padding-right: calc(var(--#{$variable-prefix}gutter-x) / 2); // stylelint-disable-line function-disallowed-list\n  padding-left: calc(var(--#{$variable-prefix}gutter-x) / 2); // stylelint-disable-line function-disallowed-list\n  margin-top: var(--#{$variable-prefix}gutter-y);\n}\n\n@mixin make-col($size, $columns: $grid-columns) {\n  flex: 0 0 auto;\n  width: percentage($size / $columns);\n}\n\n@mixin make-col-auto() {\n  flex: 0 0 auto;\n  width: auto;\n}\n\n@mixin make-col-offset($size, $columns: $grid-columns) {\n  $num: $size / $columns;\n  margin-left: if($num == 0, 0, percentage($num));\n}\n\n// Row columns\n//\n// Specify on a parent element(e.g., .row) to force immediate children into NN\n// numberof columns. Supports wrapping to new lines, but does not do a Masonry\n// style grid.\n@mixin row-cols($count) {\n  > * {\n    flex: 0 0 auto;\n    width: 100% / $count;\n  }\n}\n\n// Framework grid generation\n//\n// Used only by Bootstrap to generate the correct number of grid classes given\n// any value of `$grid-columns`.\n\n@mixin make-grid-columns($columns: $grid-columns, $gutter: $grid-gutter-width, $breakpoints: $grid-breakpoints) {\n  @each $breakpoint in map-keys($breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $breakpoints);\n\n    @include media-breakpoint-up($breakpoint, $breakpoints) {\n      // Provide basic `.col-{bp}` classes for equal-width flexbox columns\n      .col#{$infix} {\n        flex: 1 0 0%; // Flexbugs #4: https://github.com/philipwalton/flexbugs#flexbug-4\n      }\n\n      .row-cols#{$infix}-auto > * {\n        @include make-col-auto();\n      }\n\n      @if $grid-row-columns > 0 {\n        @for $i from 1 through $grid-row-columns {\n          .row-cols#{$infix}-#{$i} {\n            @include row-cols($i);\n          }\n        }\n      }\n\n      .col#{$infix}-auto {\n        @include make-col-auto();\n      }\n\n      @if $columns > 0 {\n        @for $i from 1 through $columns {\n          .col#{$infix}-#{$i} {\n            @include make-col($i, $columns);\n          }\n        }\n\n        // `$columns - 1` because offsetting by the width of an entire row isn't possible\n        @for $i from 0 through ($columns - 1) {\n          @if not ($infix == \"\" and $i == 0) { // Avoid emitting useless .offset-0\n            .offset#{$infix}-#{$i} {\n              @include make-col-offset($i, $columns);\n            }\n          }\n        }\n      }\n\n      // Gutters\n      //\n      // Make use of `.g-*`, `.gx-*` or `.gy-*` utilities to change spacing between the columns.\n      @each $key, $value in $gutters {\n        .g#{$infix}-#{$key},\n        .gx#{$infix}-#{$key} {\n          --#{$variable-prefix}gutter-x: #{$value};\n        }\n\n        .g#{$infix}-#{$key},\n        .gy#{$infix}-#{$key} {\n          --#{$variable-prefix}gutter-y: #{$value};\n        }\n      }\n    }\n  }\n}\n", "// Utility generator\n// Used to generate utilities & print utilities\n@mixin generate-utility($utility, $infix, $is-rfs-media-query: false) {\n  $values: map-get($utility, values);\n\n  // If the values are a list or string, convert it into a map\n  @if type-of($values) == \"string\" or type-of(nth($values, 1)) != \"list\" {\n    $values: zip($values, $values);\n  }\n\n  @each $key, $value in $values {\n    $properties: map-get($utility, property);\n\n    // Multiple properties are possible, for example with vertical or horizontal margins or paddings\n    @if type-of($properties) == \"string\" {\n      $properties: append((), $properties);\n    }\n\n    // Use custom class if present\n    $property-class: if(map-has-key($utility, class), map-get($utility, class), nth($properties, 1));\n    $property-class: if($property-class == null, \"\", $property-class);\n\n    $infix: if($property-class == \"\" and str-slice($infix, 1, 1) == \"-\", str-slice($infix, 2), $infix);\n\n    // Don't prefix if value key is null (eg. with shadow class)\n    $property-class-modifier: if($key, if($property-class == \"\" and $infix == \"\", \"\", \"-\") + $key, \"\");\n\n    @if map-get($utility, rfs) {\n      // Inside the media query\n      @if $is-rfs-media-query {\n        $val: rfs-value($value);\n\n        // Do not render anything if fluid and non fluid values are the same\n        $value: if($val == rfs-fluid-value($value), null, $val);\n      }\n      @else {\n        $value: rfs-fluid-value($value);\n      }\n    }\n\n    @if $value != null {\n      .#{$property-class + $infix + $property-class-modifier} {\n        @each $property in $properties {\n          #{$property}: $value if($enable-important-utilities, !important, null);\n        }\n      }\n    }\n  }\n}\n", "// Loop over each breakpoint\n@each $breakpoint in map-keys($grid-breakpoints) {\n\n  // Generate media query if needed\n  @include media-breakpoint-up($breakpoint) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    // Loop over each utility property\n    @each $key, $utility in $utilities {\n      // The utility can be disabled with `false`, thus check if the utility is a map first\n      // Only proceed if responsive media queries are enabled or if it's the base media query\n      @if type-of($utility) == \"map\" and (map-get($utility, responsive) or $infix == \"\") {\n        @include generate-utility($utility, $infix);\n      }\n    }\n  }\n}\n\n// RFS rescaling\n@media (min-width: $rfs-mq-value) {\n  @each $breakpoint in map-keys($grid-breakpoints) {\n    $infix: breakpoint-infix($breakpoint, $grid-breakpoints);\n\n    @if (map-get($grid-breakpoints, $breakpoint) < $rfs-breakpoint) {\n      // Loop over each utility property\n      @each $key, $utility in $utilities {\n        // The utility can be disabled with `false`, thus check if the utility is a map first\n        // Only proceed if responsive media queries are enabled or if it's the base media query\n        @if type-of($utility) == \"map\" and map-get($utility, rfs) {\n          @include generate-utility($utility, $infix, true);\n        }\n      }\n    }\n  }\n}\n\n\n// Print utilities\n@media print {\n  @each $key, $utility in $utilities {\n    // The utility can be disabled with `false`, thus check if the utility is a map first\n    // Then check if the utility needs print styles\n    @if type-of($utility) == \"map\" and map-get($utility, print) == true {\n      @include generate-utility($utility, \"-print\");\n    }\n  }\n}\n"]}