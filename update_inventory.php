<?php
// Include database configuration
require_once 'app/config/config.php';

try {
    // Check if inventory table exists
    $result = $conn->query("SHOW TABLES LIKE 'inventory'");
    $tableExists = $result->rowCount() > 0;
    
    if ($tableExists) {
        // Update all empty prod_name values to "Season"
        $stmt = $conn->prepare("UPDATE inventory SET prod_name = 'Season' WHERE prod_name IS NULL OR prod_name = ''");
        $stmt->execute();
        $updatedRows = $stmt->rowCount();
        
        echo "Updated $updatedRows inventory records with empty product names to 'Season'.<br>";
        
        // Get all inventory records
        $stmt = $conn->query("SELECT id, prod_name FROM inventory");
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Current Inventory Records:</h3>";
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Product Name</th></tr>";
        
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($record['id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['prod_name'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "Inventory table does not exist.";
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
