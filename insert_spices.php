<?php
// Include database configuration
require_once 'app/config/config.php';

try {
    // Check if inventory table exists
    $result = $conn->query("SHOW TABLES LIKE 'inventory'");
    $tableExists = $result->rowCount() > 0;
    
    if ($tableExists) {
        // Insert a new record with product name "Spices"
        $stmt = $conn->prepare("INSERT INTO inventory 
            (prod_name, brand_name, price, prod_measure, pack_type, expiry_date, delivered_date, country, batch_code, stocks, status) 
            VALUES 
            ('Spices', 'VISKASE', 1.00, 'Grams (g)', 'Jar', '2025-09-09 00:00:00', NOW(), 'Philippines', 'SFGE65', 1.00, 'approved')");
        $stmt->execute();
        $insertedId = $conn->lastInsertId();
        
        echo "Inserted new inventory record with ID $insertedId and product name 'Spices'.<br>";
        
        // Get all inventory records
        $stmt = $conn->query("SELECT id, prod_name, brand_name, price, stocks FROM inventory ORDER BY id DESC LIMIT 10");
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Recent Inventory Records:</h3>";
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Product Name</th><th>Brand</th><th>Price</th><th>Stocks</th></tr>";
        
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($record['id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['prod_name'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($record['brand_name'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($record['price'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($record['stocks'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        echo "<p><a href='app/modules/inventory/inventory.php'>Go to Inventory Page</a></p>";
    } else {
        echo "Inventory table does not exist.";
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
