-- Euro Spice E-commerce Database Tables
-- This SQL file creates all necessary tables for the Euro Spice e-commerce system
-- Import this file into your existing 'finance' database

-- Create categories table
CREATE TABLE IF NOT EXISTS `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create products table
CREATE TABLE IF NOT EXISTS `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_id` int(11) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `stock_quantity` int(11) NOT NULL DEFAULT 0,
  `image_url` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create orders table
CREATE TABLE IF NOT EXISTS `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `status` enum('pending','processing','completed','cancelled') NOT NULL DEFAULT 'pending',
  `payment_status` enum('unpaid','paid') NOT NULL DEFAULT 'unpaid',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create order_items table
CREATE TABLE IF NOT EXISTS `order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `order_id` (`order_id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create raw_materials table
CREATE TABLE IF NOT EXISTS `raw_materials` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `quantity` int(11) NOT NULL DEFAULT 0,
  `unit` varchar(20) NOT NULL,
  `cost_per_unit` decimal(10,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create product_materials table (for recipe/ingredients)
CREATE TABLE IF NOT EXISTS `product_materials` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `material_id` int(11) NOT NULL,
  `quantity` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `product_id` (`product_id`),
  KEY `material_id` (`material_id`),
  CONSTRAINT `product_materials_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  CONSTRAINT `product_materials_ibfk_2` FOREIGN KEY (`material_id`) REFERENCES `raw_materials` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default categories
INSERT INTO `categories` (`name`, `description`) VALUES
('Food', 'All food items'),
('Drink', 'All beverage items');

-- Insert sample products for Food category
INSERT INTO `products` (`category_id`, `name`, `description`, `price`, `stock_quantity`, `image_url`) VALUES
((SELECT id FROM categories WHERE name = 'Food'), 'Chicken Curry', 'Delicious chicken curry with aromatic spices', 150.00, 50, NULL),
((SELECT id FROM categories WHERE name = 'Food'), 'Beef Steak', 'Juicy beef steak with herbs and spices', 250.00, 30, NULL),
((SELECT id FROM categories WHERE name = 'Food'), 'Vegetable Biryani', 'Fragrant rice dish with mixed vegetables', 120.00, 40, NULL),
((SELECT id FROM categories WHERE name = 'Food'), 'Fish Fillet', 'Crispy fish fillet with tartar sauce', 180.00, 25, NULL),
((SELECT id FROM categories WHERE name = 'Food'), 'Lamb Kebab', 'Grilled lamb kebab with mint sauce', 200.00, 20, NULL),
((SELECT id FROM categories WHERE name = 'Food'), 'Chicken Tikka', 'Spicy chicken tikka with yogurt marinade', 160.00, 35, NULL);

-- Insert sample products for Drink category
INSERT INTO `products` (`category_id`, `name`, `description`, `price`, `stock_quantity`, `image_url`) VALUES
((SELECT id FROM categories WHERE name = 'Drink'), 'Mango Lassi', 'Refreshing yogurt drink with mango', 80.00, 60, NULL),
((SELECT id FROM categories WHERE name = 'Drink'), 'Masala Chai', 'Spiced tea with milk', 50.00, 100, NULL),
((SELECT id FROM categories WHERE name = 'Drink'), 'Fresh Lime Soda', 'Refreshing lime soda with mint', 60.00, 80, NULL),
((SELECT id FROM categories WHERE name = 'Drink'), 'Watermelon Juice', 'Fresh watermelon juice', 70.00, 50, NULL),
((SELECT id FROM categories WHERE name = 'Drink'), 'Coconut Water', 'Natural coconut water', 65.00, 40, NULL),
((SELECT id FROM categories WHERE name = 'Drink'), 'Strawberry Smoothie', 'Creamy strawberry smoothie', 90.00, 30, NULL);

-- Insert sample raw materials
INSERT INTO `raw_materials` (`name`, `description`, `quantity`, `unit`, `cost_per_unit`) VALUES
('Chicken', 'Fresh chicken meat', 100, 'kg', 150.00),
('Rice', 'Basmati rice', 200, 'kg', 80.00),
('Beef', 'Premium beef cuts', 80, 'kg', 300.00),
('Vegetables', 'Mixed vegetables', 150, 'kg', 60.00),
('Spices', 'Mixed spices', 50, 'kg', 200.00),
('Yogurt', 'Plain yogurt', 100, 'liter', 70.00),
('Mango', 'Fresh mangoes', 80, 'kg', 120.00),
('Tea Leaves', 'Premium tea leaves', 30, 'kg', 400.00),
('Milk', 'Fresh milk', 200, 'liter', 60.00),
('Sugar', 'White sugar', 150, 'kg', 50.00);

-- We'll use the existing users table structure with role_id, phone, and address

-- We'll use existing admin users from the users table

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status, payment_status);
CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product ON order_items(product_id);
