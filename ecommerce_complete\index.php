<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'config/ecommerce_db.php';
require_once 'config/session.php';

// Check if required tables exist
function tableExists($conn, $table)
{
  try {
    $conn->query("SELECT 1 FROM $table LIMIT 1");
    return true;
  } catch (PDOException $e) {
    return false;
  }
}

$required_tables = ['categories', 'products', 'orders', 'order_items'];
$tables_exist = true;

foreach ($required_tables as $table) {
  if (!tableExists($conn, $table)) {
    $tables_exist = false;
    break;
  }
}

// Initialize variables
$food_products = [];
$drink_products = [];
$cart_items = [];
$cart_count = 0;
$cart_total = 0;
$unpaid_orders = [];
$recent_transactions = [];
$search_results = [];

// Display setup message if tables don't exist
if (!$tables_exist) {
  // We'll handle this in the HTML part
} else {
  // Only query the database if tables exist
  try {
    // Get all food products
    $food_sql = "SELECT * FROM products WHERE category_id = (SELECT id FROM categories WHERE name = 'Food') LIMIT 6";
    $food_products = executeQuery($food_sql)->fetchAll();

    // Get all drink products
    $drink_sql = "SELECT * FROM products WHERE category_id = (SELECT id FROM categories WHERE name = 'Drink') LIMIT 6";
    $drink_products = executeQuery($drink_sql)->fetchAll();
  } catch (PDOException $e) {
    error_log("Error fetching products: " . $e->getMessage());
  }

  // Get cart items if user is logged in
  if (isLoggedIn()) {
    try {
      $cart_sql = "SELECT oi.*, p.name, p.price, p.image_url
                  FROM order_items oi
                  JOIN products p ON oi.product_id = p.id
                  JOIN orders o ON oi.order_id = o.id
                  WHERE o.user_id = :user_id AND o.status = 'pending' AND o.payment_status = 'unpaid'";

      $cart_items = executeQuery($cart_sql, [':user_id' => $_SESSION['user_id']])->fetchAll();
      $cart_count = count($cart_items);

      foreach ($cart_items as $item) {
        $cart_total += $item['price'] * $item['quantity'];
      }
    } catch (PDOException $e) {
      error_log("Error fetching cart items: " . $e->getMessage());
    }
  }

  // Get unpaid orders for notification
  if (isAdmin() || isStaff()) {
    try {
      $unpaid_sql = "SELECT o.*, CONCAT(u.first_name, ' ', u.last_name) as customer_name
                    FROM orders o
                    JOIN users u ON o.user_id = u.id
                    WHERE o.payment_status = 'unpaid' AND o.status != 'cancelled'
                    ORDER BY o.created_at DESC LIMIT 3";

      $unpaid_orders = executeQuery($unpaid_sql)->fetchAll();
    } catch (PDOException $e) {
      error_log("Error fetching unpaid orders: " . $e->getMessage());
    }
  }

  // Get recent transactions for history
  if (isAdmin() || isStaff()) {
    try {
      $transactions_sql = "SELECT o.*, CONCAT(u.first_name, ' ', u.last_name) as customer_name
                          FROM orders o
                          JOIN users u ON o.user_id = u.id
                          WHERE o.status = 'completed'
                          ORDER BY o.created_at DESC LIMIT 3";

      $recent_transactions = executeQuery($transactions_sql)->fetchAll();
    } catch (PDOException $e) {
      error_log("Error fetching recent transactions: " . $e->getMessage());
    }
  }

  // Handle search
  if (isset($_GET['search']) && !empty($_GET['search'])) {
    try {
      $search = $_GET['search'];
      $search_sql = "SELECT * FROM products WHERE name LIKE :search OR description LIKE :search";
      $search_results = executeQuery($search_sql, [':search' => "%$search%"])->fetchAll();
    } catch (PDOException $e) {
      error_log("Error searching products: " . $e->getMessage());
    }
  }

  // Handle add to cart
  if (isset($_POST['add_to_cart']) && isLoggedIn()) {
    try {
      $product_id = $_POST['product_id'];
      $quantity = $_POST['quantity'];

      // Check if user has an open order
      $order_sql = "SELECT id FROM orders WHERE user_id = :user_id AND status = 'pending' AND payment_status = 'unpaid' LIMIT 1";
      $order = executeQuery($order_sql, [':user_id' => $_SESSION['user_id']])->fetch();

      if (!$order) {
        // Create new order
        $new_order_sql = "INSERT INTO orders (user_id, total_amount, status, payment_status) VALUES (:user_id, 0, 'pending', 'unpaid')";
        executeQuery($new_order_sql, [':user_id' => $_SESSION['user_id']]);

        // Get the new order ID
        $order_id = $conn->lastInsertId();
      } else {
        $order_id = $order['id'];
      }

      // Check if product already in cart
      $check_sql = "SELECT * FROM order_items WHERE order_id = :order_id AND product_id = :product_id";
      $existing_item = executeQuery($check_sql, [
        ':order_id' => $order_id,
        ':product_id' => $product_id
      ])->fetch();

      // Get product price
      $price_sql = "SELECT price FROM products WHERE id = :product_id";
      $product = executeQuery($price_sql, [':product_id' => $product_id])->fetch();
      $price = $product['price'];

      if ($existing_item) {
        // Update quantity
        $update_sql = "UPDATE order_items SET quantity = quantity + :quantity WHERE id = :id";
        executeQuery($update_sql, [
          ':quantity' => $quantity,
          ':id' => $existing_item['id']
        ]);
      } else {
        // Add new item
        $insert_sql = "INSERT INTO order_items (order_id, product_id, quantity, price)
                      VALUES (:order_id, :product_id, :quantity, :price)";
        executeQuery($insert_sql, [
          ':order_id' => $order_id,
          ':product_id' => $product_id,
          ':quantity' => $quantity,
          ':price' => $price
        ]);
      }

      // Update order total
      $update_total_sql = "UPDATE orders SET total_amount = (
                          SELECT SUM(price * quantity) FROM order_items WHERE order_id = :order_id
                          ) WHERE id = :order_id";
      executeQuery($update_total_sql, [':order_id' => $order_id]);

      // Redirect to prevent form resubmission
      header("Location: " . $_SERVER['PHP_SELF']);
      exit();
    } catch (PDOException $e) {
      error_log("Error adding to cart: " . $e->getMessage());
      // Continue to page display
    }
  }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <!-- Bootstrap CSS -->
  <link rel="stylesheet" href="plugins/bootstrap/css/bootstrap.min.css">
  <!--Fontawesome -->
  <script defer src="plugins/fontawesome/js/all.js"></script>
  <!-- Custom CSS -->
  <link href="dist/style.css" rel="stylesheet">

  <title>Euro Spice</title>
</head>

<body>
  <!-- Navbar Menu -->
  <nav class="navbar navbar-expand-lg navbar-light border-bottom site-header sticky-top py-1"
    style="background-color: #f15b31;">
    <div class="container-fluid">
      <a class="navbar-brand fw-bold fs-4" href="#" style="color: white;"><b>Euro Spice</b></a>
      <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
        aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarSupportedContent">
        <form class="d-flex mr-auto ml-5 w-100" method="GET">
          <input class="form-control form-control-sm" type="text" name="search" placeholder="Search product name"
            aria-label=".form-control-sm example" value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
          <button class="btn btn-outline-dark btn-sm mr-5" type="submit"><i class="fas fa-search"></i></button>
        </form>
        <ul class="navbar-nav">
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown"
              aria-expanded="false">
              <i class="fas fa-shopping-cart"></i>
              <?php if ($cart_count > 0): ?>
                <span class="badge bg-danger"><?php echo $cart_count; ?></span>
              <?php endif; ?>
            </a>
            <ul class="dropdown-menu dropdown-menu-right" style="width:20rem;">
              <div class="border-bottom pb-1 mb-2 d-flex justify-content-between fw-bold">
                <span class="ml-3 text-secondary">
                  Total (<?php echo $cart_count; ?>)
                </span>
                <span class="mr-3">
                  <a href="pages/cashier/cart.php" class="link-success">Cart</a>
                </span>
              </div>

              <?php if (empty($cart_items)): ?>
                <div class="ml-3 mr-3 text-center py-3">
                  <p>Your cart is empty</p>
                </div>
              <?php else: ?>
                <?php foreach ($cart_items as $item): ?>
                  <div class="ml-3 mr-3 d-flex mb-2 justify-content-between border-bottom">
                    <span>
                      <?php echo htmlspecialchars($item['name']); ?> <br>
                      <small class="fst-italic"><?php echo $item['quantity']; ?> Unit</small>
                    </span>
                    <span>PHP <?php echo number_format($item['price'] * $item['quantity'], 2); ?></span>
                  </div>
                <?php endforeach; ?>
              <?php endif; ?>
            </ul>
          </li>

          <?php if (isAdmin() || isStaff()): ?>
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown"
                aria-expanded="false">
                <i class="fas fa-bell"></i>
                <?php if (count($unpaid_orders) > 0): ?>
                  <span class="badge bg-danger"><?php echo count($unpaid_orders); ?></span>
                <?php endif; ?>
              </a>
              <ul class="dropdown-menu dropdown-menu-right" style="width:20rem;">
                <div class="border-bottom pb-1 mb-2 d-flex justify-content-between fw-bold">
                  <span class="ml-3 text-secondary">
                    Unpaid (<?php echo count($unpaid_orders); ?>)
                  </span>
                  <span class="mr-3">
                    <a href="pages/cashier/unpaid.php" class="link-success">See more</a>
                  </span>
                </div>

                <?php if (empty($unpaid_orders)): ?>
                  <div class="ml-3 mr-3 text-center py-3">
                    <p>No unpaid orders</p>
                  </div>
                <?php else: ?>
                  <?php foreach ($unpaid_orders as $order): ?>
                    <div class="ml-3 mr-3 d-flex mb-2 justify-content-between border-bottom">
                      <span>
                        <?php echo htmlspecialchars($order['customer_name']); ?> <br>
                        <small class="fst-italic">Order #<?php echo $order['id']; ?> ~ PHP <?php echo number_format($order['total_amount'], 2); ?></small>
                      </span>
                      <span>
                        <a href="pages/cashier/process_payment.php?order_id=<?php echo $order['id']; ?>" class="mt-1 btn btn-outline-success btn-sm">Pay Now</a>
                      </span>
                    </div>
                  <?php endforeach; ?>
                <?php endif; ?>
              </ul>
            </li>

            <li class="nav-item dropdown mr-4">
              <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown"
                aria-expanded="false">
                <i class="fas fa-copy"></i>
              </a>
              <ul class="dropdown-menu dropdown-menu-right" style="width:20rem;">
                <div class="border-bottom pb-1 mb-2 d-flex justify-content-between fw-bold">
                  <span class="ml-3 text-secondary">
                    History Transaction
                  </span>
                  <span class="mr-3">
                    <a href="pages/cashier/history.php" class="link-success">See more</a>
                  </span>
                </div>

                <?php if (empty($recent_transactions)): ?>
                  <div class="ml-3 mr-3 text-center py-3">
                    <p>No recent transactions</p>
                  </div>
                <?php else: ?>
                  <?php foreach ($recent_transactions as $transaction): ?>
                    <div class="ml-3 mr-3 d-flex mb-2 justify-content-between border-bottom">
                      <span>
                        <?php echo htmlspecialchars($transaction['customer_name']); ?> <br>
                        <small class="fst-italic">Order #<?php echo $transaction['id']; ?></small>
                      </span>
                      <span>PHP <?php echo number_format($transaction['total_amount'], 2); ?></span>
                    </div>
                  <?php endforeach; ?>
                <?php endif; ?>
              </ul>
            </li>
          <?php endif; ?>

          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown"
              aria-expanded="false">
              <i class="fas fa-bars"></i> Menu
            </a>
            <ul class="dropdown-menu dropdown-menu-right">
              <?php if (isAdmin() || isStaff()): ?>
                <li><a href="pages/cashier/index.php" class="dropdown-item">Cashier</a></li>
                <li><a href="pages/raw-material/index.php" class="dropdown-item">Raw Material</a></li>
                <li><a href="pages/product-list/index.php" class="dropdown-item">Product List</a></li>
                <?php if (isAdmin()): ?>
                  <li><a href="pages/user-list/index.php" class="dropdown-item">User List</a></li>
                <?php endif; ?>
                <li><a href="pages/sellbuy-report/index.php" class="dropdown-item">Sell/Buy Report</a></li>
              <?php endif; ?>
            </ul>
          </li>

          <li class="nav-item dropdown">
            <?php if (isLoggedIn()): ?>
              <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown"
                aria-expanded="false">
                <i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($_SESSION['first_name']); ?>
              </a>
              <ul class="dropdown-menu dropdown-menu-right">
                <li><a href="pages/account/settings.php" class="dropdown-item">Settings</a></li>
                <li><a href="pages/account/help.php" class="dropdown-item">Help</a></li>
                <li><a href="pages/account/logout.php" class="dropdown-item">Logout</a></li>
              </ul>
            <?php else: ?>
              <a class="nav-link" href="pages/account/login.php">
                <i class="fas fa-sign-in-alt"></i> Login
              </a>
            <?php endif; ?>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <div class="container mt-4 mb-5">
    <main>
      <?php if (!$tables_exist): ?>
        <!-- Database Setup Required Message -->
        <div class="alert alert-warning">
          <h4 class="alert-heading">Database Setup Required</h4>
          <p>The Euro Spice e-commerce system requires database tables to be set up before it can be used.</p>
          <hr>
          <p>Please import the SQL file to create the necessary tables:</p>
          <pre>ecommerce_complete/eurospice.sql</pre>

          <h5 class="mt-3">SQL Import Instructions</h5>
          <ol>
            <li>Open phpMyAdmin (usually at <a href="http://localhost/phpmyadmin" target="_blank">http://localhost/phpmyadmin</a>)</li>
            <li>Select your 'finance' database from the left sidebar</li>
            <li>Click on the 'Import' tab at the top</li>
            <li>Click 'Choose File' and select the eurospice.sql file</li>
            <li>Click 'Go' at the bottom to import the file</li>
          </ol>

          <p>After importing the SQL file, refresh this page to start using the Euro Spice e-commerce system.</p>
        </div>
      <?php elseif (isset($_GET['search']) && !empty($_GET['search'])): ?>
        <!-- Search Results -->
        <div class="album">
          <div class="container">
            <h4 class="text-secondary border-bottom mb-3 pb-2 pl-3">Search Results for "<?php echo htmlspecialchars($_GET['search']); ?>"</h4>

            <?php if (empty($search_results)): ?>
              <div class="alert alert-info">No products found matching your search.</div>
            <?php else: ?>
              <div class="position-relative row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3">
                <?php foreach ($search_results as $product): ?>
                  <div class="col">
                    <div class="card shadow-sm">
                      <?php if (!empty($product['image_url'])): ?>
                        <img src="<?php echo htmlspecialchars($product['image_url']); ?>" class="card-img-top" alt="<?php echo htmlspecialchars($product['name']); ?>" style="height: 200px; object-fit: cover;">
                      <?php else: ?>
                        <svg class="bd-placeholder-img card-img-top" width="100%" height="200" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="Placeholder: Thumbnail" preserveAspectRatio="xMidYMid slice" focusable="false">
                          <title><?php echo htmlspecialchars($product['name']); ?></title>
                          <rect width="100%" height="100%" fill="#55595c" /><text x="50%" y="50%" fill="#eceeef" dy=".3em"><?php echo htmlspecialchars($product['name']); ?></text>
                        </svg>
                      <?php endif; ?>

                      <div class="ml-3 mr-3 mt-2 text-center text-secondary">
                        <p title="<?php echo htmlspecialchars($product['description']); ?>" class="fw-bold border-bottom"><?php echo htmlspecialchars($product['name']); ?></p>
                      </div>
                      <div class="ml-3 mr-3 d-flex justify-content-between">
                        <div>
                          <p class="fw-bold">PHP <?php echo number_format($product['price'], 2); ?></p>
                        </div>
                        <div class="text-right">
                          <form action="" method="POST">
                            <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                            <input type="number" name="quantity" value="1" min="1" style="width: 3rem;">
                            <input type="submit" name="add_to_cart" class="btn btn-success btn-sm mb-1" value="+ cart">
                          </form>
                        </div>
                      </div>
                    </div>
                  </div>
                <?php endforeach; ?>
              </div>
            <?php endif; ?>
          </div>
        </div>
      <?php else: ?>
        <!-- ALl Product Food -->
        <div class="album">
          <div class="container">
            <h4 class="text-secondary border-bottom mb-3 pb-2 pl-3">Food</h4>
            <div class="position-relative row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3">
              <?php if (empty($food_products)): ?>
                <div class="col-12">
                  <div class="alert alert-info">No food products available.</div>
                </div>
              <?php else: ?>
                <?php foreach ($food_products as $product): ?>
                  <div class="col">
                    <div class="card shadow-sm">
                      <?php if (!empty($product['image_url'])): ?>
                        <img src="<?php echo htmlspecialchars($product['image_url']); ?>" class="card-img-top" alt="<?php echo htmlspecialchars($product['name']); ?>" style="height: 200px; object-fit: cover;">
                      <?php else: ?>
                        <svg class="bd-placeholder-img card-img-top" width="100%" height="200" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="Placeholder: Thumbnail" preserveAspectRatio="xMidYMid slice" focusable="false">
                          <title><?php echo htmlspecialchars($product['name']); ?></title>
                          <rect width="100%" height="100%" fill="#55595c" /><text x="50%" y="50%" fill="#eceeef" dy=".3em"><?php echo htmlspecialchars($product['name']); ?></text>
                        </svg>
                      <?php endif; ?>

                      <div class="ml-3 mr-3 mt-2 text-center text-secondary">
                        <p title="<?php echo htmlspecialchars($product['description']); ?>" class="fw-bold border-bottom"><?php echo htmlspecialchars($product['name']); ?></p>
                      </div>
                      <div class="ml-3 mr-3 d-flex justify-content-between">
                        <div>
                          <p class="fw-bold">PHP <?php echo number_format($product['price'], 2); ?></p>
                        </div>
                        <div class="text-right">
                          <form action="" method="POST">
                            <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                            <input type="number" name="quantity" value="1" min="1" style="width: 3rem;">
                            <input type="submit" name="add_to_cart" class="btn btn-success btn-sm mb-1" value="+ cart">
                          </form>
                        </div>
                      </div>
                    </div>
                  </div>
                <?php endforeach; ?>
              <?php endif; ?>
            </div>
          </div>
        </div>

        <!-- All Product Drink -->
        <div class="album">
          <div class="container">
            <h4 class="text-secondary border-bottom mb-3 mt-4 pb-2 pl-3">Drink</h4>
            <div class="position-relative row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3">
              <?php if (empty($drink_products)): ?>
                <div class="col-12">
                  <div class="alert alert-info">No drink products available.</div>
                </div>
              <?php else: ?>
                <?php foreach ($drink_products as $product): ?>
                  <div class="col">
                    <div class="card shadow-sm">
                      <?php if (!empty($product['image_url'])): ?>
                        <img src="<?php echo htmlspecialchars($product['image_url']); ?>" class="card-img-top" alt="<?php echo htmlspecialchars($product['name']); ?>" style="height: 200px; object-fit: cover;">
                      <?php else: ?>
                        <svg class="bd-placeholder-img card-img-top" width="100%" height="200" xmlns="http://www.w3.org/2000/svg" role="img" aria-label="Placeholder: Thumbnail" preserveAspectRatio="xMidYMid slice" focusable="false">
                          <title><?php echo htmlspecialchars($product['name']); ?></title>
                          <rect width="100%" height="100%" fill="#55595c" /><text x="50%" y="50%" fill="#eceeef" dy=".3em"><?php echo htmlspecialchars($product['name']); ?></text>
                        </svg>
                      <?php endif; ?>

                      <div class="ml-3 mr-3 mt-2 text-center text-secondary">
                        <p title="<?php echo htmlspecialchars($product['description']); ?>" class="fw-bold border-bottom"><?php echo htmlspecialchars($product['name']); ?></p>
                      </div>
                      <div class="ml-3 mr-3 d-flex justify-content-between">
                        <div>
                          <p class="fw-bold">PHP <?php echo number_format($product['price'], 2); ?></p>
                        </div>
                        <div class="text-right">
                          <form action="" method="POST">
                            <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                            <input type="number" name="quantity" value="1" min="1" style="width: 3rem;">
                            <input type="submit" name="add_to_cart" class="btn btn-success btn-sm mb-1" value="+ cart">
                          </form>
                        </div>
                      </div>
                    </div>
                  </div>
                <?php endforeach; ?>
              <?php endif; ?>
            </div>
          </div>
        </div>
      <?php endif; ?>
    </main>
  </div>

  <script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-alpha3/dist/js/bootstrap.min.js"></script>

  <script>
    // Enable Bootstrap dropdowns
    document.addEventListener('DOMContentLoaded', function() {
      var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
      var dropdownList = dropdownElementList.map(function(dropdownToggleEl) {
        return new bootstrap.Dropdown(dropdownToggleEl);
      });
    });
  </script>
</body>

</html>