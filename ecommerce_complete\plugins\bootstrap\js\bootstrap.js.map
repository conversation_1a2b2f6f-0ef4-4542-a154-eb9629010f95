{"version": 3, "file": "bootstrap.js", "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-target')\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href')\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let {\n    transitionDuration,\n    transitionDelay\n  } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = parseFloat(transitionDuration)\n  const floatTransitionDelay = parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (parseFloat(transitionDuration) + parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ?\n      'element' :\n      toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new Error(\n        `${componentName.toUpperCase()}: ` +\n        `Option \"${property}\" provided type \"${valueType}\" ` +\n        `but expected type \"${expectedTypes}\".`)\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nexport {\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst mapData = (() => {\n  const storeData = {}\n  let id = 1\n  return {\n    set(element, key, data) {\n      if (typeof element.bsKey === 'undefined') {\n        element.bsKey = {\n          key,\n          id\n        }\n        id++\n      }\n\n      storeData[element.bsKey.id] = data\n    },\n    get(element, key) {\n      if (!element || typeof element.bsKey === 'undefined') {\n        return null\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        return storeData[keyProperties.id]\n      }\n\n      return null\n    },\n    delete(element, key) {\n      if (typeof element.bsKey === 'undefined') {\n        return\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        delete storeData[keyProperties.id]\n        delete element.bsKey\n      }\n    }\n  }\n})()\n\nconst Data = {\n  setData(instance, key, data) {\n    mapData.set(instance, key, data)\n  },\n  getData(instance, key) {\n    return mapData.get(instance, key)\n  },\n  removeData(instance, key) {\n    mapData.delete(instance, key)\n  }\n}\n\nexport default Data\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = [\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n]\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.indexOf(namespace) > -1) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.charAt(0) === '.'\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.indexOf(handlerKey) > -1) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASSNAME_ALERT = 'alert'\nconst CLASSNAME_FADE = 'fade'\nconst CLASSNAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n\n    if (this._element) {\n      Data.setData(element, DATA_KEY, this)\n    }\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASSNAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASSNAME_SHOW)\n\n    if (!element.classList.contains(CLASSNAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler.one(element, TRANSITION_END, () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Alert.jQueryInterface\n    $.fn[NAME].Constructor = Alert\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Alert.jQueryInterface\n    }\n  }\n})\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.getData(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Button.jQueryInterface\n    $.fn[NAME].Constructor = Button\n\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Button.jQueryInterface\n    }\n  }\n})\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {\n      ...element.dataset\n    }\n\n    Object.keys(attributes).forEach(key => {\n      attributes[key] = normalizeData(attributes[key])\n    })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  matches(element, selector) {\n    return element.matches(selector)\n  },\n\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    const children = [].concat(...element.children)\n\n    return children.filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (this.matches(ancestor, selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (this.matches(next, selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_RIGHT = 'carousel-item-right'\nconst CLASS_NAME_LEFT = 'carousel-item-left'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-slide], [data-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-ride=\"carousel\"]'\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel {\n  constructor(element, config) {\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._element = element\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._items = null\n    this._config = null\n    this._element = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      if (event.touches && event.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.key) {\n      case ARROW_LEFT_KEY:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEY:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrevDirection && activeIndex === 0) ||\n                            (isNextDirection && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = SelectorEngine.find(SELECTOR_ACTIVE, this._indicatorsElement)\n      for (let i = 0; i < indicators.length; i++) {\n        indicators[i].classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        nextIndicator.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = parseInt(element.getAttribute('data-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || (activeElement &&\n      this._getItemByDirection(direction, activeElement))\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_LEFT\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_RIGHT\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler.one(activeElement, TRANSITION_END, () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(() => {\n          EventHandler.trigger(this._element, EVENT_SLID, {\n            relatedTarget: nextElement,\n            direction: eventDirectionName,\n            from: activeElementIndex,\n            to: nextElementIndex\n          })\n        }, 0)\n      })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.getData(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.getData(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Carousel.jQueryInterface\n    $.fn[NAME].Constructor = Carousel\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Carousel.jQueryInterface\n    }\n  }\n})\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element = element\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-target=\"#${element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.filter(elem => container !== elem)\n      activesData = tempActiveData[0] ? Data.getData(tempActiveData[0], DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.setData(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._parent = null\n    this._element = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.getData(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Collapse.jQueryInterface\n    $.fn[NAME].Constructor = Collapse\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Collapse.jQueryInterface\n    }\n  }\n})\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPRIGHT = 'dropright'\nconst CLASS_NAME_DROPLEFT = 'dropleft'\nconst CLASS_NAME_MENURIGHT = 'dropdown-menu-right'\nconst CLASS_NAME_NAVBAR = 'navbar'\nconst CLASS_NAME_POSITION_STATIC = 'position-static'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = 'top-start'\nconst PLACEMENT_TOPEND = 'top-end'\nconst PLACEMENT_BOTTOM = 'bottom-start'\nconst PLACEMENT_BOTTOMEND = 'bottom-end'\nconst PLACEMENT_RIGHT = 'right-start'\nconst PLACEMENT_LEFT = 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'scrollParent',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element = element\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Disable totally Popper.js for Dropdown in Navbar\n    if (!this._inNavbar) {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper.js (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        parent.classList.add(CLASS_NAME_POSITION_STATIC)\n      }\n\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._element, EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n    let placement = PLACEMENT_BOTTOM\n\n    // Handle dropup\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      placement = this._menu.classList.contains(CLASS_NAME_MENURIGHT) ?\n        PLACEMENT_TOPEND :\n        PLACEMENT_TOP\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPRIGHT)) {\n      placement = PLACEMENT_RIGHT\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPLEFT)) {\n      placement = PLACEMENT_LEFT\n    } else if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n      placement = PLACEMENT_BOTTOMEND\n    }\n\n    return placement\n  }\n\n  _detectNavbar() {\n    return Boolean(this._element.closest(`.${CLASS_NAME_NAVBAR}`))\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...(this._config.offset(data.offsets, this._element) || {})\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper.js if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON ||\n      (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown.getParentFromElement(toggles[i])\n      const context = Data.getData(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && ((event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName)) ||\n          (event.type === 'keyup' && event.key === TAB_KEY)) &&\n          dropdownMenu.contains(event.target)) {\n        continue\n      }\n\n      const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.key === ARROW_UP_KEY && index > 0) { // Up\n      index--\n    }\n\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) { // Down\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  event.stopPropagation()\n  Dropdown.dropdownInterface(this, 'toggle')\n})\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => e.stopPropagation())\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Dropdown.jQueryInterface\n    $.fn[NAME].Constructor = Dropdown\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Dropdown.jQueryInterface\n    }\n  }\n})\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true,\n  show: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean',\n  show: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config = this._getConfig(config)\n    this._element = element\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      event => this.hide(event)\n    )\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._element = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (transition) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, TRANSITION_END, transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const animate = this._element.classList.contains(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE :\n      ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        this._triggerBackdropTransition()\n      })\n\n      if (animate) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, TRANSITION_END, callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (this._element.classList.contains(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, TRANSITION_END, callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _triggerBackdropTransition() {\n    if (this._config.backdrop === 'static') {\n      const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n      if (hideEvent.defaultPrevented) {\n        return\n      }\n\n      const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n      if (!isModalOverflowing) {\n        this._element.style.overflowY = 'hidden'\n      }\n\n      this._element.classList.add(CLASS_NAME_STATIC)\n      const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n      EventHandler.off(this._element, TRANSITION_END)\n      EventHandler.one(this._element, TRANSITION_END, () => {\n        this._element.classList.remove(CLASS_NAME_STATIC)\n        if (!isModalOverflowing) {\n          EventHandler.one(this._element, TRANSITION_END, () => {\n            this._element.style.overflowY = ''\n          })\n          emulateTransitionEnd(this._element, modalTransitionDuration)\n        }\n      })\n      emulateTransitionEnd(this._element, modalTransitionDuration)\n      this._element.focus()\n    } else {\n      this.hide()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n      // Adjust fixed content padding\n      SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n        .forEach(element => {\n          const actualPadding = element.style.paddingRight\n          const calculatedPadding = window.getComputedStyle(element)['padding-right']\n          Manipulator.setDataAttribute(element, 'padding-right', actualPadding)\n          element.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n        })\n\n      // Adjust sticky content margin\n      SelectorEngine.find(SELECTOR_STICKY_CONTENT)\n        .forEach(element => {\n          const actualMargin = element.style.marginRight\n          const calculatedMargin = window.getComputedStyle(element)['margin-right']\n          Manipulator.setDataAttribute(element, 'margin-right', actualMargin)\n          element.style.marginRight = `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`\n        })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = window.getComputedStyle(document.body)['padding-right']\n\n      Manipulator.setDataAttribute(document.body, 'padding-right', actualPadding)\n      document.body.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n      .forEach(element => {\n        const padding = Manipulator.getDataAttribute(element, 'padding-right')\n        if (typeof padding !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'padding-right')\n          element.style.paddingRight = padding\n        }\n      })\n\n    // Restore sticky content and navbar-toggler margin\n    SelectorEngine.find(`${SELECTOR_STICKY_CONTENT}`)\n      .forEach(element => {\n        const margin = Manipulator.getDataAttribute(element, 'margin-right')\n        if (typeof margin !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'margin-right')\n          element.style.marginRight = margin\n        }\n      })\n\n    // Restore body padding\n    const padding = Manipulator.getDataAttribute(document.body, 'padding-right')\n    if (typeof padding === 'undefined') {\n      document.body.style.paddingRight = ''\n    } else {\n      Manipulator.removeDataAttribute(document.body, 'padding-right')\n      document.body.style.paddingRight = padding\n    }\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.getData(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.show(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Modal.jQueryInterface\n    $.fn[NAME].Constructor = Modal\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Modal.jQueryInterface\n    }\n  }\n})\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (allowlistKeys.indexOf(elName) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'allowList', 'sanitizeFn']\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(number|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacement: '(string|array)',\n  boundary: '(string|element)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: 'right',\n  BOTTOM: 'bottom',\n  LEFT: 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"tooltip-arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: 0,\n  container: false,\n  fallbackPlacement: 'flip',\n  boundary: 'scrollParent',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper.js (https://popper.js.org)')\n    }\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.element = element\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n    Data.setData(element, this.constructor.DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = Data.getData(event.delegateTarget, dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.delegateTarget,\n          this._getDelegateConfig()\n        )\n        Data.setData(event.delegateTarget, dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    Data.removeData(this.element, this.constructor.DATA_KEY)\n\n    EventHandler.off(this.element, this.constructor.EVENT_KEY)\n    EventHandler.off(this.element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config = null\n    this.tip = null\n  }\n\n  show() {\n    if (this.element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (this.isWithContent() && this._isEnabled) {\n      const showEvent = EventHandler.trigger(this.element, this.constructor.Event.SHOW)\n      const shadowRoot = findShadowRoot(this.element)\n      const isInTheDom = shadowRoot === null ?\n        this.element.ownerDocument.documentElement.contains(this.element) :\n        shadowRoot.contains(this.element)\n\n      if (showEvent.defaultPrevented || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        tip.classList.add(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this.element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this._addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      Data.setData(tip, this.constructor.DATA_KEY, this)\n\n      if (!this.element.ownerDocument.documentElement.contains(this.tip)) {\n        container.appendChild(tip)\n      }\n\n      EventHandler.trigger(this.element, this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, this._getPopperConfig(attachment))\n\n      tip.classList.add(CLASS_NAME_SHOW)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children).forEach(element => {\n          EventHandler.on(element, 'mouseover', noop())\n        })\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n\n        const prevHoverState = this._hoverState\n        this._hoverState = null\n\n        EventHandler.trigger(this.element, this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n        const transitionDuration = getTransitionDurationFromElement(this.tip)\n        EventHandler.one(this.tip, TRANSITION_END, complete)\n        emulateTransitionEnd(this.tip, transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this.element, this.constructor.Event.HIDDEN)\n      this._popper.destroy()\n    }\n\n    const hideEvent = EventHandler.trigger(this.element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, TRANSITION_END, complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.allowList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this.element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          behavior: this.config.fallbackPlacement\n        },\n        arrow: {\n          element: `.${this.constructor.NAME}-arrow`\n        },\n        preventOverflow: {\n          boundariesElement: this.config.boundary\n        }\n      },\n      onCreate: data => {\n        if (data.originalPlacement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      },\n      onUpdate: data => this._handlePopperPlacementChange(data)\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...(this.config.offset(data.offsets, this.element) || {})\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this.element,\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this.element,\n          eventIn,\n          this.config.selector,\n          event => this._enter(event)\n        )\n        EventHandler.on(this.element,\n          eventOut,\n          this.config.selector,\n          event => this._leave(event)\n        )\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this.element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this.element.closest(`.${CLASS_NAME_MODAL}`),\n      'hide.bs.modal',\n      this._hideModalHandler\n    )\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) ||\n        context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this.element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    this.tip = popperData.instance.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tooltip.jQueryInterface\n    $.fn[NAME].Constructor = Tooltip\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tooltip.jQueryInterface\n    }\n  }\n})\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.setData(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Popover.jQueryInterface\n    $.fn[NAME].Constructor = Popover\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Popover.jQueryInterface\n    }\n  }\n})\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._element = null\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset +\n      scrollHeight -\n      this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = ScrollSpy.jQueryInterface\n    $.fn[NAME].Constructor = ScrollSpy\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return ScrollSpy.jQueryInterface\n    }\n  }\n})\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab {\n  constructor(element) {\n    this._element = element\n\n    Data.setData(this._element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    let hideEvent = null\n\n    if (previous) {\n      hideEvent = EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      })\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented ||\n      (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(\n      this._element,\n      listElement\n    )\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback &&\n      (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(\n      element,\n      active,\n      callback\n    )\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, TRANSITION_END, complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.getData(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.getData(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tab.jQueryInterface\n    $.fn[NAME].Constructor = Tab\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tab.jQueryInterface\n    }\n  }\n})\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._element = null\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Toast.jQueryInterface\n    $.fn[NAME].Constructor = Toast\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Toast.jQueryInterface\n    }\n  }\n})\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"], "names": ["MAX_UID", "MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "window", "getComputedStyle", "transitionDuration", "transitionDelay", "floatTransitionDuration", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "nodeType", "emulateTransitionEnd", "duration", "called", "durationPadding", "emulatedDuration", "listener", "removeEventListener", "addEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "Error", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "hasAttribute", "onDOMContentLoaded", "callback", "readyState", "mapData", "storeData", "id", "set", "key", "data", "bs<PERSON><PERSON>", "get", "keyProperties", "delete", "Data", "setData", "instance", "getData", "removeData", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "getUidEvent", "uid", "getEvent", "bootstrapHandler", "fn", "handler", "event", "<PERSON><PERSON><PERSON><PERSON>", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "len", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "replace", "custom", "isNative", "indexOf", "add<PERSON><PERSON><PERSON>", "handlers", "previousFn", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "on", "one", "inNamespace", "isNamespace", "char<PERSON>t", "elementEvent", "slice", "keyHandlers", "trigger", "args", "$", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "SELECTOR_DISMISS", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "CLASSNAME_ALERT", "CLASSNAME_FADE", "CLASSNAME_SHOW", "<PERSON><PERSON>", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "dispose", "closest", "classList", "remove", "contains", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "jQueryInterface", "each", "handle<PERSON><PERSON><PERSON>", "alertInstance", "getInstance", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "setAttribute", "button", "normalizeData", "val", "Number", "normalizeDataKey", "chr", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "NODE_TEXT", "SelectorEngine", "matches", "find", "concat", "Element", "prototype", "findOne", "children", "filter", "child", "parents", "ancestor", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "DIRECTION_NEXT", "DIRECTION_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_SLIDE", "CLASS_NAME_RIGHT", "CLASS_NAME_LEFT", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "PointerType", "TOUCH", "PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "direction", "_handleSwipe", "absDeltax", "abs", "_keydown", "_addTouchEventListeners", "start", "pointerType", "clientX", "touches", "move", "end", "clearTimeout", "itemImg", "e", "add", "tagName", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "isGoingToWrap", "delta", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "from", "_setActiveIndicatorElement", "indicators", "nextIndicator", "elementInterval", "parseInt", "defaultInterval", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "slideEvent", "carouselInterface", "action", "TypeError", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "startEvent", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "complete", "capitalizedDimension", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "selectorElements", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_CLICK", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DISABLED", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPRIGHT", "CLASS_NAME_DROPLEFT", "CLASS_NAME_MENURIGHT", "CLASS_NAME_NAVBAR", "CLASS_NAME_POSITION_STATIC", "SELECTOR_FORM_CHILD", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "flip", "boundary", "reference", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "isActive", "clearMenus", "getParentFromElement", "showEvent", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "focus", "hideEvent", "destroy", "update", "scheduleUpdate", "stopPropagation", "constructor", "_getPlacement", "parentDropdown", "placement", "_getOffset", "offsets", "modifiers", "enabled", "preventOverflow", "boundariesElement", "applyStyle", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "dataApiKeydownHandler", "items", "backdrop", "EVENT_HIDE_PREVENTED", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "CLASS_NAME_SCROLLBAR_MEASURER", "CLASS_NAME_BACKDROP", "CLASS_NAME_OPEN", "CLASS_NAME_FADE", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_DISMISS", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "transition", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_triggerBackdropTransition", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "animate", "createElement", "className", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "scrollHeight", "clientHeight", "overflowY", "modalTransitionDuration", "paddingLeft", "paddingRight", "round", "right", "innerWidth", "_getScrollbarWidth", "actualPadding", "calculatedPadding", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "nodeValue", "regExp", "attrRegex", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "el", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacement", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_MODAL", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "_fixTransition", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "defaultBsConfig", "behavior", "arrow", "onCreate", "originalPlacement", "_handlePopperPlacementChange", "onUpdate", "triggers", "eventIn", "eventOut", "_fixTitle", "titleType", "dataAttributes", "dataAttr", "tabClass", "map", "token", "tClass", "popperData", "popper", "initConfigAnimation", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "method", "EVENT_ACTIVATE", "EVENT_SCROLL", "CLASS_NAME_DROPDOWN_ITEM", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "isActiveTarget", "queries", "link", "join", "listGroup", "navItem", "node", "spy", "CLASS_NAME_DROPDOWN_MENU", "SELECTOR_ACTIVE_UL", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "listElement", "itemSelector", "activeElements", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "CLASS_NAME_HIDE", "CLASS_NAME_SHOWING", "autohide", "Toast", "_clearTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAEA,IAAMA,OAAO,GAAG,OAAhB;EACA,IAAMC,uBAAuB,GAAG,IAAhC;EACA,IAAMC,cAAc,GAAG,eAAvB;;EAGA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAAC,GAAG,EAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,gBAAUD,GAAV;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;EAQA;EACA;EACA;EACA;EACA;;;EAEA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAAC,MAAM,EAAI;EACvB,KAAG;EACDA,IAAAA,MAAM,IAAIC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBd,OAA3B,CAAV;EACD,GAFD,QAESe,QAAQ,CAACC,cAAT,CAAwBL,MAAxB,CAFT;;EAIA,SAAOA,MAAP;EACD,CAND;;EAQA,IAAMM,WAAW,GAAG,SAAdA,WAAc,CAAAC,OAAO,EAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,aAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAME,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAjB;EAEAD,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACC,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAOH,QAAP;EACD,CAVD;;EAYA,IAAMI,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAAL,OAAO,EAAI;EACxC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;;EAEA,MAAIC,QAAJ,EAAc;EACZ,WAAOJ,QAAQ,CAACS,aAAT,CAAuBL,QAAvB,IAAmCA,QAAnC,GAA8C,IAArD;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,IAAMM,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAAP,OAAO,EAAI;EACxC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;EAEA,SAAOC,QAAQ,GAAGJ,QAAQ,CAACS,aAAT,CAAuBL,QAAvB,CAAH,GAAsC,IAArD;EACD,CAJD;;EAMA,IAAMO,gCAAgC,GAAG,SAAnCA,gCAAmC,CAAAR,OAAO,EAAI;EAClD,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,CAAP;EACD,GAHiD;;;EAAA,8BAS9CS,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,CAT8C;EAAA,MAOhDW,kBAPgD,yBAOhDA,kBAPgD;EAAA,MAQhDC,eARgD,yBAQhDA,eARgD;;EAWlD,MAAMC,uBAAuB,GAAGC,UAAU,CAACH,kBAAD,CAA1C;EACA,MAAMI,oBAAoB,GAAGD,UAAU,CAACF,eAAD,CAAvC,CAZkD;;EAelD,MAAI,CAACC,uBAAD,IAA4B,CAACE,oBAAjC,EAAuD;EACrD,WAAO,CAAP;EACD,GAjBiD;;;EAoBlDJ,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACK,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;EACAJ,EAAAA,eAAe,GAAGA,eAAe,CAACI,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAlB;EAEA,SAAO,CAACF,UAAU,CAACH,kBAAD,CAAV,GAAiCG,UAAU,CAACF,eAAD,CAA5C,IAAiE7B,uBAAxE;EACD,CAxBD;;EA0BA,IAAMkC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAAAjB,OAAO,EAAI;EACtCA,EAAAA,OAAO,CAACkB,aAAR,CAAsB,IAAIC,KAAJ,CAAUnC,cAAV,CAAtB;EACD,CAFD;;EAIA,IAAMoC,SAAS,GAAG,SAAZA,SAAY,CAAAlC,GAAG;EAAA,SAAI,CAACA,GAAG,CAAC,CAAD,CAAH,IAAUA,GAAX,EAAgBmC,QAApB;EAAA,CAArB;;EAEA,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAACtB,OAAD,EAAUuB,QAAV,EAAuB;EAClD,MAAIC,MAAM,GAAG,KAAb;EACA,MAAMC,eAAe,GAAG,CAAxB;EACA,MAAMC,gBAAgB,GAAGH,QAAQ,GAAGE,eAApC;;EACA,WAASE,QAAT,GAAoB;EAClBH,IAAAA,MAAM,GAAG,IAAT;EACAxB,IAAAA,OAAO,CAAC4B,mBAAR,CAA4B5C,cAA5B,EAA4C2C,QAA5C;EACD;;EAED3B,EAAAA,OAAO,CAAC6B,gBAAR,CAAyB7C,cAAzB,EAAyC2C,QAAzC;EACAG,EAAAA,UAAU,CAAC,YAAM;EACf,QAAI,CAACN,MAAL,EAAa;EACXP,MAAAA,oBAAoB,CAACjB,OAAD,CAApB;EACD;EACF,GAJS,EAIP0B,gBAJO,CAAV;EAKD,CAfD;;EAiBA,IAAMK,eAAe,GAAG,SAAlBA,eAAkB,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,EAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiC,UAAAC,QAAQ,EAAI;EAC3C,QAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,QAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,QAAMG,SAAS,GAAGD,KAAK,IAAIpB,SAAS,CAACoB,KAAD,CAAlB,GAChB,SADgB,GAEhBvD,MAAM,CAACuD,KAAD,CAFR;;EAIA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,KAAJ,CACDZ,aAAa,CAACa,WAAd,EAAH,yBACWP,QADX,2BACuCG,SADvC,sCAEsBF,aAFtB,SADI,CAAN;EAID;EACF,GAbD;EAcD,CAfD;;EAiBA,IAAMO,SAAS,GAAG,SAAZA,SAAY,CAAA9C,OAAO,EAAI;EAC3B,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,KAAP;EACD;;EAED,MAAIA,OAAO,CAAC+C,KAAR,IAAiB/C,OAAO,CAACgD,UAAzB,IAAuChD,OAAO,CAACgD,UAAR,CAAmBD,KAA9D,EAAqE;EACnE,QAAME,YAAY,GAAGvC,gBAAgB,CAACV,OAAD,CAArC;EACA,QAAMkD,eAAe,GAAGxC,gBAAgB,CAACV,OAAO,CAACgD,UAAT,CAAxC;EAEA,WAAOC,YAAY,CAACE,OAAb,KAAyB,MAAzB,IACLD,eAAe,CAACC,OAAhB,KAA4B,MADvB,IAELF,YAAY,CAACG,UAAb,KAA4B,QAF9B;EAGD;;EAED,SAAO,KAAP;EACD,CAfD;;EAiBA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAAArD,OAAO,EAAI;EAChC,MAAI,CAACH,QAAQ,CAACyD,eAAT,CAAyBC,YAA9B,EAA4C;EAC1C,WAAO,IAAP;EACD,GAH+B;;;EAMhC,MAAI,OAAOvD,OAAO,CAACwD,WAAf,KAA+B,UAAnC,EAA+C;EAC7C,QAAMC,IAAI,GAAGzD,OAAO,CAACwD,WAAR,EAAb;EACA,WAAOC,IAAI,YAAYC,UAAhB,GAA6BD,IAA7B,GAAoC,IAA3C;EACD;;EAED,MAAIzD,OAAO,YAAY0D,UAAvB,EAAmC;EACjC,WAAO1D,OAAP;EACD,GAb+B;;;EAgBhC,MAAI,CAACA,OAAO,CAACgD,UAAb,EAAyB;EACvB,WAAO,IAAP;EACD;;EAED,SAAOK,cAAc,CAACrD,OAAO,CAACgD,UAAT,CAArB;EACD,CArBD;;EAuBA,IAAMW,IAAI,GAAG,SAAPA,IAAO;EAAA,SAAM,YAAY,EAAlB;EAAA,CAAb;;EAEA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAA5D,OAAO;EAAA,SAAIA,OAAO,CAAC6D,YAAZ;EAAA,CAAtB;;EAEA,IAAMC,SAAS,GAAG,SAAZA,SAAY,GAAM;EAAA,gBACHrD,MADG;EAAA,MACdsD,MADc,WACdA,MADc;;EAGtB,MAAIA,MAAM,IAAI,CAAClE,QAAQ,CAACmE,IAAT,CAAcC,YAAd,CAA2B,gBAA3B,CAAf,EAA6D;EAC3D,WAAOF,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,IAAMG,kBAAkB,GAAG,SAArBA,kBAAqB,CAAAC,QAAQ,EAAI;EACrC,MAAItE,QAAQ,CAACuE,UAAT,KAAwB,SAA5B,EAAuC;EACrCvE,IAAAA,QAAQ,CAACgC,gBAAT,CAA0B,kBAA1B,EAA8CsC,QAA9C;EACD,GAFD,MAEO;EACLA,IAAAA,QAAQ;EACT;EACF,CAND;;ECtLA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EAEA,IAAME,OAAO,GAAI,YAAM;EACrB,MAAMC,SAAS,GAAG,EAAlB;EACA,MAAIC,EAAE,GAAG,CAAT;EACA,SAAO;EACLC,IAAAA,GADK,eACDxE,OADC,EACQyE,GADR,EACaC,IADb,EACmB;EACtB,UAAI,OAAO1E,OAAO,CAAC2E,KAAf,KAAyB,WAA7B,EAA0C;EACxC3E,QAAAA,OAAO,CAAC2E,KAAR,GAAgB;EACdF,UAAAA,GAAG,EAAHA,GADc;EAEdF,UAAAA,EAAE,EAAFA;EAFc,SAAhB;EAIAA,QAAAA,EAAE;EACH;;EAEDD,MAAAA,SAAS,CAACtE,OAAO,CAAC2E,KAAR,CAAcJ,EAAf,CAAT,GAA8BG,IAA9B;EACD,KAXI;EAYLE,IAAAA,GAZK,eAYD5E,OAZC,EAYQyE,GAZR,EAYa;EAChB,UAAI,CAACzE,OAAD,IAAY,OAAOA,OAAO,CAAC2E,KAAf,KAAyB,WAAzC,EAAsD;EACpD,eAAO,IAAP;EACD;;EAED,UAAME,aAAa,GAAG7E,OAAO,CAAC2E,KAA9B;;EACA,UAAIE,aAAa,CAACJ,GAAd,KAAsBA,GAA1B,EAA+B;EAC7B,eAAOH,SAAS,CAACO,aAAa,CAACN,EAAf,CAAhB;EACD;;EAED,aAAO,IAAP;EACD,KAvBI;EAwBLO,IAAAA,MAxBK,mBAwBE9E,OAxBF,EAwBWyE,GAxBX,EAwBgB;EACnB,UAAI,OAAOzE,OAAO,CAAC2E,KAAf,KAAyB,WAA7B,EAA0C;EACxC;EACD;;EAED,UAAME,aAAa,GAAG7E,OAAO,CAAC2E,KAA9B;;EACA,UAAIE,aAAa,CAACJ,GAAd,KAAsBA,GAA1B,EAA+B;EAC7B,eAAOH,SAAS,CAACO,aAAa,CAACN,EAAf,CAAhB;EACA,eAAOvE,OAAO,CAAC2E,KAAf;EACD;EACF;EAlCI,GAAP;EAoCD,CAvCe,EAAhB;;EAyCA,IAAMI,IAAI,GAAG;EACXC,EAAAA,OADW,mBACHC,QADG,EACOR,GADP,EACYC,IADZ,EACkB;EAC3BL,IAAAA,OAAO,CAACG,GAAR,CAAYS,QAAZ,EAAsBR,GAAtB,EAA2BC,IAA3B;EACD,GAHU;EAIXQ,EAAAA,OAJW,mBAIHD,QAJG,EAIOR,GAJP,EAIY;EACrB,WAAOJ,OAAO,CAACO,GAAR,CAAYK,QAAZ,EAAsBR,GAAtB,CAAP;EACD,GANU;EAOXU,EAAAA,UAPW,sBAOAF,QAPA,EAOUR,GAPV,EAOe;EACxBJ,IAAAA,OAAO,CAACS,MAAR,CAAeG,QAAf,EAAyBR,GAAzB;EACD;EATU,CAAb;;ECtDA;EACA;EACA;EACA;EACA;EACA;EAIA;EACA;EACA;EACA;EACA;;EAEA,IAAMW,cAAc,GAAG,oBAAvB;EACA,IAAMC,cAAc,GAAG,MAAvB;EACA,IAAMC,aAAa,GAAG,QAAtB;EACA,IAAMC,aAAa,GAAG,EAAtB;;EACA,IAAIC,QAAQ,GAAG,CAAf;EACA,IAAMC,YAAY,GAAG;EACnBC,EAAAA,UAAU,EAAE,WADO;EAEnBC,EAAAA,UAAU,EAAE;EAFO,CAArB;EAIA,IAAMC,YAAY,GAAG,CACnB,OADmB,EAEnB,UAFmB,EAGnB,SAHmB,EAInB,WAJmB,EAKnB,aALmB,EAMnB,YANmB,EAOnB,gBAPmB,EAQnB,WARmB,EASnB,UATmB,EAUnB,WAVmB,EAWnB,aAXmB,EAYnB,WAZmB,EAanB,SAbmB,EAcnB,UAdmB,EAenB,OAfmB,EAgBnB,mBAhBmB,EAiBnB,YAjBmB,EAkBnB,WAlBmB,EAmBnB,UAnBmB,EAoBnB,aApBmB,EAqBnB,aArBmB,EAsBnB,aAtBmB,EAuBnB,WAvBmB,EAwBnB,cAxBmB,EAyBnB,eAzBmB,EA0BnB,cA1BmB,EA2BnB,eA3BmB,EA4BnB,YA5BmB,EA6BnB,OA7BmB,EA8BnB,MA9BmB,EA+BnB,QA/BmB,EAgCnB,OAhCmB,EAiCnB,QAjCmB,EAkCnB,QAlCmB,EAmCnB,SAnCmB,EAoCnB,UApCmB,EAqCnB,MArCmB,EAsCnB,QAtCmB,EAuCnB,cAvCmB,EAwCnB,QAxCmB,EAyCnB,MAzCmB,EA0CnB,kBA1CmB,EA2CnB,kBA3CmB,EA4CnB,OA5CmB,EA6CnB,OA7CmB,EA8CnB,QA9CmB,CAArB;EAiDA;EACA;EACA;EACA;EACA;;EAEA,SAASC,WAAT,CAAqB7F,OAArB,EAA8B8F,GAA9B,EAAmC;EACjC,SAAQA,GAAG,IAAOA,GAAP,UAAeN,QAAQ,EAA3B,IAAoCxF,OAAO,CAACwF,QAA5C,IAAwDA,QAAQ,EAAvE;EACD;;EAED,SAASO,QAAT,CAAkB/F,OAAlB,EAA2B;EACzB,MAAM8F,GAAG,GAAGD,WAAW,CAAC7F,OAAD,CAAvB;EAEAA,EAAAA,OAAO,CAACwF,QAAR,GAAmBM,GAAnB;EACAP,EAAAA,aAAa,CAACO,GAAD,CAAb,GAAqBP,aAAa,CAACO,GAAD,CAAb,IAAsB,EAA3C;EAEA,SAAOP,aAAa,CAACO,GAAD,CAApB;EACD;;EAED,SAASE,gBAAT,CAA0BhG,OAA1B,EAAmCiG,EAAnC,EAAuC;EACrC,SAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;EAC7BA,IAAAA,KAAK,CAACC,cAAN,GAAuBpG,OAAvB;;EAEA,QAAIkG,OAAO,CAACG,MAAZ,EAAoB;EAClBC,MAAAA,YAAY,CAACC,GAAb,CAAiBvG,OAAjB,EAA0BmG,KAAK,CAACK,IAAhC,EAAsCP,EAAtC;EACD;;EAED,WAAOA,EAAE,CAACQ,KAAH,CAASzG,OAAT,EAAkB,CAACmG,KAAD,CAAlB,CAAP;EACD,GARD;EASD;;EAED,SAASO,0BAAT,CAAoC1G,OAApC,EAA6CC,QAA7C,EAAuDgG,EAAvD,EAA2D;EACzD,SAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;EAC7B,QAAMQ,WAAW,GAAG3G,OAAO,CAAC4G,gBAAR,CAAyB3G,QAAzB,CAApB;;EAEA,aAAW4G,MAAX,GAAsBV,KAAtB,CAAWU,MAAX,EAA6BA,MAAM,IAAIA,MAAM,KAAK,IAAlD,EAAwDA,MAAM,GAAGA,MAAM,CAAC7D,UAAxE,EAAoF;EAClF,WAAK,IAAI8D,CAAC,GAAGH,WAAW,CAACI,MAAzB,EAAiCD,CAAC,EAAlC,GAAuC;EACrC,YAAIH,WAAW,CAACG,CAAD,CAAX,KAAmBD,MAAvB,EAA+B;EAC7BV,UAAAA,KAAK,CAACC,cAAN,GAAuBS,MAAvB;;EAEA,cAAIX,OAAO,CAACG,MAAZ,EAAoB;EAClBC,YAAAA,YAAY,CAACC,GAAb,CAAiBvG,OAAjB,EAA0BmG,KAAK,CAACK,IAAhC,EAAsCP,EAAtC;EACD;;EAED,iBAAOA,EAAE,CAACQ,KAAH,CAASI,MAAT,EAAiB,CAACV,KAAD,CAAjB,CAAP;EACD;EACF;EACF,KAf4B;;;EAkB7B,WAAO,IAAP;EACD,GAnBD;EAoBD;;EAED,SAASa,WAAT,CAAqBC,MAArB,EAA6Bf,OAA7B,EAAsCgB,kBAAtC,EAAiE;EAAA,MAA3BA,kBAA2B;EAA3BA,IAAAA,kBAA2B,GAAN,IAAM;EAAA;;EAC/D,MAAMC,YAAY,GAAGhF,MAAM,CAACC,IAAP,CAAY6E,MAAZ,CAArB;;EAEA,OAAK,IAAIH,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGD,YAAY,CAACJ,MAAnC,EAA2CD,CAAC,GAAGM,GAA/C,EAAoDN,CAAC,EAArD,EAAyD;EACvD,QAAMX,KAAK,GAAGc,MAAM,CAACE,YAAY,CAACL,CAAD,CAAb,CAApB;;EAEA,QAAIX,KAAK,CAACkB,eAAN,KAA0BnB,OAA1B,IAAqCC,KAAK,CAACe,kBAAN,KAA6BA,kBAAtE,EAA0F;EACxF,aAAOf,KAAP;EACD;EACF;;EAED,SAAO,IAAP;EACD;;EAED,SAASmB,eAAT,CAAyBC,iBAAzB,EAA4CrB,OAA5C,EAAqDsB,YAArD,EAAmE;EACjE,MAAMC,UAAU,GAAG,OAAOvB,OAAP,KAAmB,QAAtC;EACA,MAAMmB,eAAe,GAAGI,UAAU,GAAGD,YAAH,GAAkBtB,OAApD,CAFiE;;EAKjE,MAAIwB,SAAS,GAAGH,iBAAiB,CAACI,OAAlB,CAA0BtC,cAA1B,EAA0C,EAA1C,CAAhB;EACA,MAAMuC,MAAM,GAAGnC,YAAY,CAACiC,SAAD,CAA3B;;EAEA,MAAIE,MAAJ,EAAY;EACVF,IAAAA,SAAS,GAAGE,MAAZ;EACD;;EAED,MAAMC,QAAQ,GAAGjC,YAAY,CAACkC,OAAb,CAAqBJ,SAArB,IAAkC,CAAC,CAApD;;EAEA,MAAI,CAACG,QAAL,EAAe;EACbH,IAAAA,SAAS,GAAGH,iBAAZ;EACD;;EAED,SAAO,CAACE,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,CAAP;EACD;;EAED,SAASK,UAAT,CAAoB/H,OAApB,EAA6BuH,iBAA7B,EAAgDrB,OAAhD,EAAyDsB,YAAzD,EAAuEnB,MAAvE,EAA+E;EAC7E,MAAI,OAAOkB,iBAAP,KAA6B,QAA7B,IAAyC,CAACvH,OAA9C,EAAuD;EACrD;EACD;;EAED,MAAI,CAACkG,OAAL,EAAc;EACZA,IAAAA,OAAO,GAAGsB,YAAV;EACAA,IAAAA,YAAY,GAAG,IAAf;EACD;;EAR4E,yBAU5BF,eAAe,CAACC,iBAAD,EAAoBrB,OAApB,EAA6BsB,YAA7B,CAVa;EAAA,MAUtEC,UAVsE;EAAA,MAU1DJ,eAV0D;EAAA,MAUzCK,SAVyC;;EAW7E,MAAMT,MAAM,GAAGlB,QAAQ,CAAC/F,OAAD,CAAvB;EACA,MAAMgI,QAAQ,GAAGf,MAAM,CAACS,SAAD,CAAN,KAAsBT,MAAM,CAACS,SAAD,CAAN,GAAoB,EAA1C,CAAjB;EACA,MAAMO,UAAU,GAAGjB,WAAW,CAACgB,QAAD,EAAWX,eAAX,EAA4BI,UAAU,GAAGvB,OAAH,GAAa,IAAnD,CAA9B;;EAEA,MAAI+B,UAAJ,EAAgB;EACdA,IAAAA,UAAU,CAAC5B,MAAX,GAAoB4B,UAAU,CAAC5B,MAAX,IAAqBA,MAAzC;EAEA;EACD;;EAED,MAAMP,GAAG,GAAGD,WAAW,CAACwB,eAAD,EAAkBE,iBAAiB,CAACI,OAAlB,CAA0BvC,cAA1B,EAA0C,EAA1C,CAAlB,CAAvB;EACA,MAAMa,EAAE,GAAGwB,UAAU,GACnBf,0BAA0B,CAAC1G,OAAD,EAAUkG,OAAV,EAAmBsB,YAAnB,CADP,GAEnBxB,gBAAgB,CAAChG,OAAD,EAAUkG,OAAV,CAFlB;EAIAD,EAAAA,EAAE,CAACiB,kBAAH,GAAwBO,UAAU,GAAGvB,OAAH,GAAa,IAA/C;EACAD,EAAAA,EAAE,CAACoB,eAAH,GAAqBA,eAArB;EACApB,EAAAA,EAAE,CAACI,MAAH,GAAYA,MAAZ;EACAJ,EAAAA,EAAE,CAACT,QAAH,GAAcM,GAAd;EACAkC,EAAAA,QAAQ,CAAClC,GAAD,CAAR,GAAgBG,EAAhB;EAEAjG,EAAAA,OAAO,CAAC6B,gBAAR,CAAyB6F,SAAzB,EAAoCzB,EAApC,EAAwCwB,UAAxC;EACD;;EAED,SAASS,aAAT,CAAuBlI,OAAvB,EAAgCiH,MAAhC,EAAwCS,SAAxC,EAAmDxB,OAAnD,EAA4DgB,kBAA5D,EAAgF;EAC9E,MAAMjB,EAAE,GAAGe,WAAW,CAACC,MAAM,CAACS,SAAD,CAAP,EAAoBxB,OAApB,EAA6BgB,kBAA7B,CAAtB;;EAEA,MAAI,CAACjB,EAAL,EAAS;EACP;EACD;;EAEDjG,EAAAA,OAAO,CAAC4B,mBAAR,CAA4B8F,SAA5B,EAAuCzB,EAAvC,EAA2CkC,OAAO,CAACjB,kBAAD,CAAlD;EACA,SAAOD,MAAM,CAACS,SAAD,CAAN,CAAkBzB,EAAE,CAACT,QAArB,CAAP;EACD;;EAED,SAAS4C,wBAAT,CAAkCpI,OAAlC,EAA2CiH,MAA3C,EAAmDS,SAAnD,EAA8DW,SAA9D,EAAyE;EACvE,MAAMC,iBAAiB,GAAGrB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;EAEAvF,EAAAA,MAAM,CAACC,IAAP,CAAYkG,iBAAZ,EAA+BjG,OAA/B,CAAuC,UAAAkG,UAAU,EAAI;EACnD,QAAIA,UAAU,CAACT,OAAX,CAAmBO,SAAnB,IAAgC,CAAC,CAArC,EAAwC;EACtC,UAAMlC,KAAK,GAAGmC,iBAAiB,CAACC,UAAD,CAA/B;EAEAL,MAAAA,aAAa,CAAClI,OAAD,EAAUiH,MAAV,EAAkBS,SAAlB,EAA6BvB,KAAK,CAACkB,eAAnC,EAAoDlB,KAAK,CAACe,kBAA1D,CAAb;EACD;EACF,GAND;EAOD;;EAED,IAAMZ,YAAY,GAAG;EACnBkC,EAAAA,EADmB,cAChBxI,OADgB,EACPmG,KADO,EACAD,OADA,EACSsB,YADT,EACuB;EACxCO,IAAAA,UAAU,CAAC/H,OAAD,EAAUmG,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC,KAAxC,CAAV;EACD,GAHkB;EAKnBiB,EAAAA,GALmB,eAKfzI,OALe,EAKNmG,KALM,EAKCD,OALD,EAKUsB,YALV,EAKwB;EACzCO,IAAAA,UAAU,CAAC/H,OAAD,EAAUmG,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC,IAAxC,CAAV;EACD,GAPkB;EASnBjB,EAAAA,GATmB,eASfvG,OATe,EASNuH,iBATM,EASarB,OATb,EASsBsB,YATtB,EASoC;EACrD,QAAI,OAAOD,iBAAP,KAA6B,QAA7B,IAAyC,CAACvH,OAA9C,EAAuD;EACrD;EACD;;EAHoD,4BAKJsH,eAAe,CAACC,iBAAD,EAAoBrB,OAApB,EAA6BsB,YAA7B,CALX;EAAA,QAK9CC,UAL8C;EAAA,QAKlCJ,eALkC;EAAA,QAKjBK,SALiB;;EAMrD,QAAMgB,WAAW,GAAGhB,SAAS,KAAKH,iBAAlC;EACA,QAAMN,MAAM,GAAGlB,QAAQ,CAAC/F,OAAD,CAAvB;EACA,QAAM2I,WAAW,GAAGpB,iBAAiB,CAACqB,MAAlB,CAAyB,CAAzB,MAAgC,GAApD;;EAEA,QAAI,OAAOvB,eAAP,KAA2B,WAA/B,EAA4C;EAC1C;EACA,UAAI,CAACJ,MAAD,IAAW,CAACA,MAAM,CAACS,SAAD,CAAtB,EAAmC;EACjC;EACD;;EAEDQ,MAAAA,aAAa,CAAClI,OAAD,EAAUiH,MAAV,EAAkBS,SAAlB,EAA6BL,eAA7B,EAA8CI,UAAU,GAAGvB,OAAH,GAAa,IAArE,CAAb;EACA;EACD;;EAED,QAAIyC,WAAJ,EAAiB;EACfxG,MAAAA,MAAM,CAACC,IAAP,CAAY6E,MAAZ,EAAoB5E,OAApB,CAA4B,UAAAwG,YAAY,EAAI;EAC1CT,QAAAA,wBAAwB,CAACpI,OAAD,EAAUiH,MAAV,EAAkB4B,YAAlB,EAAgCtB,iBAAiB,CAACuB,KAAlB,CAAwB,CAAxB,CAAhC,CAAxB;EACD,OAFD;EAGD;;EAED,QAAMR,iBAAiB,GAAGrB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;EACAvF,IAAAA,MAAM,CAACC,IAAP,CAAYkG,iBAAZ,EAA+BjG,OAA/B,CAAuC,UAAA0G,WAAW,EAAI;EACpD,UAAMR,UAAU,GAAGQ,WAAW,CAACpB,OAAZ,CAAoBrC,aAApB,EAAmC,EAAnC,CAAnB;;EAEA,UAAI,CAACoD,WAAD,IAAgBnB,iBAAiB,CAACO,OAAlB,CAA0BS,UAA1B,IAAwC,CAAC,CAA7D,EAAgE;EAC9D,YAAMpC,KAAK,GAAGmC,iBAAiB,CAACS,WAAD,CAA/B;EAEAb,QAAAA,aAAa,CAAClI,OAAD,EAAUiH,MAAV,EAAkBS,SAAlB,EAA6BvB,KAAK,CAACkB,eAAnC,EAAoDlB,KAAK,CAACe,kBAA1D,CAAb;EACD;EACF,KARD;EASD,GA7CkB;EA+CnB8B,EAAAA,OA/CmB,mBA+CXhJ,OA/CW,EA+CFmG,KA/CE,EA+CK8C,IA/CL,EA+CW;EAC5B,QAAI,OAAO9C,KAAP,KAAiB,QAAjB,IAA6B,CAACnG,OAAlC,EAA2C;EACzC,aAAO,IAAP;EACD;;EAED,QAAMkJ,CAAC,GAAGpF,SAAS,EAAnB;EACA,QAAM4D,SAAS,GAAGvB,KAAK,CAACwB,OAAN,CAActC,cAAd,EAA8B,EAA9B,CAAlB;EACA,QAAMqD,WAAW,GAAGvC,KAAK,KAAKuB,SAA9B;EACA,QAAMG,QAAQ,GAAGjC,YAAY,CAACkC,OAAb,CAAqBJ,SAArB,IAAkC,CAAC,CAApD;EAEA,QAAIyB,WAAJ;EACA,QAAIC,OAAO,GAAG,IAAd;EACA,QAAIC,cAAc,GAAG,IAArB;EACA,QAAIC,gBAAgB,GAAG,KAAvB;EACA,QAAIC,GAAG,GAAG,IAAV;;EAEA,QAAIb,WAAW,IAAIQ,CAAnB,EAAsB;EACpBC,MAAAA,WAAW,GAAGD,CAAC,CAAC/H,KAAF,CAAQgF,KAAR,EAAe8C,IAAf,CAAd;EAEAC,MAAAA,CAAC,CAAClJ,OAAD,CAAD,CAAWgJ,OAAX,CAAmBG,WAAnB;EACAC,MAAAA,OAAO,GAAG,CAACD,WAAW,CAACK,oBAAZ,EAAX;EACAH,MAAAA,cAAc,GAAG,CAACF,WAAW,CAACM,6BAAZ,EAAlB;EACAH,MAAAA,gBAAgB,GAAGH,WAAW,CAACO,kBAAZ,EAAnB;EACD;;EAED,QAAI7B,QAAJ,EAAc;EACZ0B,MAAAA,GAAG,GAAG1J,QAAQ,CAAC8J,WAAT,CAAqB,YAArB,CAAN;EACAJ,MAAAA,GAAG,CAACK,SAAJ,CAAclC,SAAd,EAAyB0B,OAAzB,EAAkC,IAAlC;EACD,KAHD,MAGO;EACLG,MAAAA,GAAG,GAAG,IAAIM,WAAJ,CAAgB1D,KAAhB,EAAuB;EAC3BiD,QAAAA,OAAO,EAAPA,OAD2B;EAE3BU,QAAAA,UAAU,EAAE;EAFe,OAAvB,CAAN;EAID,KAjC2B;;;EAoC5B,QAAI,OAAOb,IAAP,KAAgB,WAApB,EAAiC;EAC/B9G,MAAAA,MAAM,CAACC,IAAP,CAAY6G,IAAZ,EAAkB5G,OAAlB,CAA0B,UAAAoC,GAAG,EAAI;EAC/BtC,QAAAA,MAAM,CAAC4H,cAAP,CAAsBR,GAAtB,EAA2B9E,GAA3B,EAAgC;EAC9BG,UAAAA,GAD8B,iBACxB;EACJ,mBAAOqE,IAAI,CAACxE,GAAD,CAAX;EACD;EAH6B,SAAhC;EAKD,OAND;EAOD;;EAED,QAAI6E,gBAAJ,EAAsB;EACpBC,MAAAA,GAAG,CAACS,cAAJ;EACD;;EAED,QAAIX,cAAJ,EAAoB;EAClBrJ,MAAAA,OAAO,CAACkB,aAAR,CAAsBqI,GAAtB;EACD;;EAED,QAAIA,GAAG,CAACD,gBAAJ,IAAwB,OAAOH,WAAP,KAAuB,WAAnD,EAAgE;EAC9DA,MAAAA,WAAW,CAACa,cAAZ;EACD;;EAED,WAAOT,GAAP;EACD;EA1GkB,CAArB;;EC1MA;EACA;EACA;EACA;EACA;;EAEA,IAAMU,IAAI,GAAG,OAAb;EACA,IAAMC,OAAO,GAAG,cAAhB;EACA,IAAMC,QAAQ,GAAG,UAAjB;EACA,IAAMC,SAAS,SAAOD,QAAtB;EACA,IAAME,YAAY,GAAG,WAArB;EAEA,IAAMC,gBAAgB,GAAG,wBAAzB;EAEA,IAAMC,WAAW,aAAWH,SAA5B;EACA,IAAMI,YAAY,cAAYJ,SAA9B;EACA,IAAMK,oBAAoB,aAAWL,SAAX,GAAuBC,YAAjD;EAEA,IAAMK,eAAe,GAAG,OAAxB;EACA,IAAMC,cAAc,GAAG,MAAvB;EACA,IAAMC,cAAc,GAAG,MAAvB;EAEA;EACA;EACA;EACA;EACA;;MAEMC;EACJ,iBAAY7K,OAAZ,EAAqB;EACnB,SAAK8K,QAAL,GAAgB9K,OAAhB;;EAEA,QAAI,KAAK8K,QAAT,EAAmB;EACjB/F,MAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,QAAtB,EAAgC,IAAhC;EACD;EACF;;;;;EAQD;WAEAY,QAAA,eAAM/K,OAAN,EAAe;EACb,QAAMgL,WAAW,GAAGhL,OAAO,GAAG,KAAKiL,eAAL,CAAqBjL,OAArB,CAAH,GAAmC,KAAK8K,QAAnE;;EACA,QAAMI,WAAW,GAAG,KAAKC,kBAAL,CAAwBH,WAAxB,CAApB;;EAEA,QAAIE,WAAW,KAAK,IAAhB,IAAwBA,WAAW,CAAC5B,gBAAxC,EAA0D;EACxD;EACD;;EAED,SAAK8B,cAAL,CAAoBJ,WAApB;EACD;;WAEDK,UAAA,mBAAU;EACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,QAA/B;EACA,SAAKW,QAAL,GAAgB,IAAhB;EACD;;;WAIDG,kBAAA,yBAAgBjL,OAAhB,EAAyB;EACvB,WAAOO,sBAAsB,CAACP,OAAD,CAAtB,IAAmCA,OAAO,CAACsL,OAAR,OAAoBZ,eAApB,CAA1C;EACD;;WAEDS,qBAAA,4BAAmBnL,OAAnB,EAA4B;EAC1B,WAAOsG,YAAY,CAAC0C,OAAb,CAAqBhJ,OAArB,EAA8BuK,WAA9B,CAAP;EACD;;WAEDa,iBAAA,wBAAepL,OAAf,EAAwB;EAAA;;EACtBA,IAAAA,OAAO,CAACuL,SAAR,CAAkBC,MAAlB,CAAyBZ,cAAzB;;EAEA,QAAI,CAAC5K,OAAO,CAACuL,SAAR,CAAkBE,QAAlB,CAA2Bd,cAA3B,CAAL,EAAiD;EAC/C,WAAKe,eAAL,CAAqB1L,OAArB;;EACA;EACD;;EAED,QAAMW,kBAAkB,GAAGH,gCAAgC,CAACR,OAAD,CAA3D;EAEAsG,IAAAA,YAAY,CAACmC,GAAb,CAAiBzI,OAAjB,EAA0BhB,cAA1B,EAA0C;EAAA,aAAM,KAAI,CAAC0M,eAAL,CAAqB1L,OAArB,CAAN;EAAA,KAA1C;EACAsB,IAAAA,oBAAoB,CAACtB,OAAD,EAAUW,kBAAV,CAApB;EACD;;WAED+K,kBAAA,yBAAgB1L,OAAhB,EAAyB;EACvB,QAAIA,OAAO,CAACgD,UAAZ,EAAwB;EACtBhD,MAAAA,OAAO,CAACgD,UAAR,CAAmB2I,WAAnB,CAA+B3L,OAA/B;EACD;;EAEDsG,IAAAA,YAAY,CAAC0C,OAAb,CAAqBhJ,OAArB,EAA8BwK,YAA9B;EACD;;;UAIMoB,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,QAAnB,CAAX;;EAEA,UAAI,CAACzF,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAImG,KAAJ,CAAU,IAAV,CAAP;EACD;;EAED,UAAI5I,MAAM,KAAK,OAAf,EAAwB;EACtByC,QAAAA,IAAI,CAACzC,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAVM,CAAP;EAWD;;UAEM6J,gBAAP,uBAAqBC,aAArB,EAAoC;EAClC,WAAO,UAAU5F,KAAV,EAAiB;EACtB,UAAIA,KAAJ,EAAW;EACTA,QAAAA,KAAK,CAAC6D,cAAN;EACD;;EAED+B,MAAAA,aAAa,CAAChB,KAAd,CAAoB,IAApB;EACD,KAND;EAOD;;UAEMiB,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,QAAtB,CAAP;EACD;;;;0BAlFoB;EACnB,aAAOD,OAAP;EACD;;;;;EAmFH;EACA;EACA;EACA;EACA;;;EACA5D,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,oBAA1B,EAAgDH,gBAAhD,EAAkEO,KAAK,CAACiB,aAAN,CAAoB,IAAIjB,KAAJ,EAApB,CAAlE;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA3G,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,IAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,IAAL,IAAaY,KAAK,CAACe,eAAnB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,IAAL,EAAWiC,WAAX,GAAyBrB,KAAzB;;EACA3B,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,IAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,IAAL,IAAagC,kBAAb;EACA,aAAOpB,KAAK,CAACe,eAAb;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;ECjJA;EACA;EACA;EACA;EACA;;EAEA,IAAM3B,MAAI,GAAG,QAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,WAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAM+B,iBAAiB,GAAG,QAA1B;EAEA,IAAMC,oBAAoB,GAAG,wBAA7B;EAEA,IAAM5B,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EAEA;EACA;EACA;EACA;EACA;;MAEMiC;EACJ,kBAAYtM,OAAZ,EAAqB;EACnB,SAAK8K,QAAL,GAAgB9K,OAAhB;EACA+E,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;EACD;;;;;EAQD;WAEAoC,SAAA,kBAAS;EACP;EACA,SAAKzB,QAAL,CAAc0B,YAAd,CAA2B,cAA3B,EAA2C,KAAK1B,QAAL,CAAcS,SAAd,CAAwBgB,MAAxB,CAA+BH,iBAA/B,CAA3C;EACD;;WAEDf,UAAA,mBAAU;EACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EACA,SAAKW,QAAL,GAAgB,IAAhB;EACD;;;WAIMc,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;EAEA,UAAI,CAACzF,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI4H,MAAJ,CAAW,IAAX,CAAP;EACD;;EAED,UAAIrK,MAAM,KAAK,QAAf,EAAyB;EACvByC,QAAAA,IAAI,CAACzC,MAAD,CAAJ;EACD;EACF,KAVM,CAAP;EAWD;;WAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BAlCoB;EACnB,aAAOD,SAAP;EACD;;;;;EAmCH;EACA;EACA;EACA;EACA;;;EAEA5D,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4B,oBAAhD,EAAsE,UAAAlG,KAAK,EAAI;EAC7EA,EAAAA,KAAK,CAAC6D,cAAN;EAEA,MAAMyC,MAAM,GAAGtG,KAAK,CAACU,MAAN,CAAayE,OAAb,CAAqBe,oBAArB,CAAf;EAEA,MAAI3H,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAauH,MAAb,EAAqBtC,UAArB,CAAX;;EACA,MAAI,CAACzF,IAAL,EAAW;EACTA,IAAAA,IAAI,GAAG,IAAI4H,MAAJ,CAAWG,MAAX,CAAP;EACD;;EAED/H,EAAAA,IAAI,CAAC6H,MAAL;EACD,CAXD;EAaA;EACA;EACA;EACA;EACA;EACA;;EAEArI,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAaqC,MAAM,CAACV,eAApB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBI,MAAzB;;EAEApD,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOK,MAAM,CAACV,eAAd;EACD,KAHD;EAID;EACF,CAbiB,CAAlB;;EC1GA;EACA;EACA;EACA;EACA;EACA;EAEA,SAASc,aAAT,CAAuBC,GAAvB,EAA4B;EAC1B,MAAIA,GAAG,KAAK,MAAZ,EAAoB;EAClB,WAAO,IAAP;EACD;;EAED,MAAIA,GAAG,KAAK,OAAZ,EAAqB;EACnB,WAAO,KAAP;EACD;;EAED,MAAIA,GAAG,KAAKC,MAAM,CAACD,GAAD,CAAN,CAAYvN,QAAZ,EAAZ,EAAoC;EAClC,WAAOwN,MAAM,CAACD,GAAD,CAAb;EACD;;EAED,MAAIA,GAAG,KAAK,EAAR,IAAcA,GAAG,KAAK,MAA1B,EAAkC;EAChC,WAAO,IAAP;EACD;;EAED,SAAOA,GAAP;EACD;;EAED,SAASE,gBAAT,CAA0BpI,GAA1B,EAA+B;EAC7B,SAAOA,GAAG,CAACkD,OAAJ,CAAY,QAAZ,EAAsB,UAAAmF,GAAG;EAAA,iBAAQA,GAAG,CAACvN,WAAJ,EAAR;EAAA,GAAzB,CAAP;EACD;;EAED,IAAMwN,WAAW,GAAG;EAClBC,EAAAA,gBADkB,4BACDhN,OADC,EACQyE,GADR,EACajC,KADb,EACoB;EACpCxC,IAAAA,OAAO,CAACwM,YAAR,WAA6BK,gBAAgB,CAACpI,GAAD,CAA7C,EAAsDjC,KAAtD;EACD,GAHiB;EAKlByK,EAAAA,mBALkB,+BAKEjN,OALF,EAKWyE,GALX,EAKgB;EAChCzE,IAAAA,OAAO,CAACkN,eAAR,WAAgCL,gBAAgB,CAACpI,GAAD,CAAhD;EACD,GAPiB;EASlB0I,EAAAA,iBATkB,6BASAnN,OATA,EASS;EACzB,QAAI,CAACA,OAAL,EAAc;EACZ,aAAO,EAAP;EACD;;EAED,QAAMoN,UAAU,gBACXpN,OAAO,CAACqN,OADG,CAAhB;;EAIAlL,IAAAA,MAAM,CAACC,IAAP,CAAYgL,UAAZ,EAAwB/K,OAAxB,CAAgC,UAAAoC,GAAG,EAAI;EACrC2I,MAAAA,UAAU,CAAC3I,GAAD,CAAV,GAAkBiI,aAAa,CAACU,UAAU,CAAC3I,GAAD,CAAX,CAA/B;EACD,KAFD;EAIA,WAAO2I,UAAP;EACD,GAvBiB;EAyBlBE,EAAAA,gBAzBkB,4BAyBDtN,OAzBC,EAyBQyE,GAzBR,EAyBa;EAC7B,WAAOiI,aAAa,CAAC1M,OAAO,CAACE,YAAR,WAA6B2M,gBAAgB,CAACpI,GAAD,CAA7C,CAAD,CAApB;EACD,GA3BiB;EA6BlB8I,EAAAA,MA7BkB,kBA6BXvN,OA7BW,EA6BF;EACd,QAAMwN,IAAI,GAAGxN,OAAO,CAACyN,qBAAR,EAAb;EAEA,WAAO;EACLC,MAAAA,GAAG,EAAEF,IAAI,CAACE,GAAL,GAAW7N,QAAQ,CAACmE,IAAT,CAAc2J,SADzB;EAELC,MAAAA,IAAI,EAAEJ,IAAI,CAACI,IAAL,GAAY/N,QAAQ,CAACmE,IAAT,CAAc6J;EAF3B,KAAP;EAID,GApCiB;EAsClBC,EAAAA,QAtCkB,oBAsCT9N,OAtCS,EAsCA;EAChB,WAAO;EACL0N,MAAAA,GAAG,EAAE1N,OAAO,CAAC+N,SADR;EAELH,MAAAA,IAAI,EAAE5N,OAAO,CAACgO;EAFT,KAAP;EAID;EA3CiB,CAApB;;EC/BA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EAEA,IAAMC,SAAS,GAAG,CAAlB;EAEA,IAAMC,cAAc,GAAG;EACrBC,EAAAA,OADqB,mBACbnO,OADa,EACJC,QADI,EACM;EACzB,WAAOD,OAAO,CAACmO,OAAR,CAAgBlO,QAAhB,CAAP;EACD,GAHoB;EAKrBmO,EAAAA,IALqB,gBAKhBnO,QALgB,EAKND,OALM,EAK8B;EAAA;;EAAA,QAApCA,OAAoC;EAApCA,MAAAA,OAAoC,GAA1BH,QAAQ,CAACyD,eAAiB;EAAA;;EACjD,WAAO,YAAG+K,MAAH,aAAaC,OAAO,CAACC,SAAR,CAAkB3H,gBAAlB,CAAmCvH,IAAnC,CAAwCW,OAAxC,EAAiDC,QAAjD,CAAb,CAAP;EACD,GAPoB;EASrBuO,EAAAA,OATqB,mBASbvO,QATa,EASHD,OATG,EASiC;EAAA,QAApCA,OAAoC;EAApCA,MAAAA,OAAoC,GAA1BH,QAAQ,CAACyD,eAAiB;EAAA;;EACpD,WAAOgL,OAAO,CAACC,SAAR,CAAkBjO,aAAlB,CAAgCjB,IAAhC,CAAqCW,OAArC,EAA8CC,QAA9C,CAAP;EACD,GAXoB;EAarBwO,EAAAA,QAbqB,oBAaZzO,OAbY,EAaHC,QAbG,EAaO;EAAA;;EAC1B,QAAMwO,QAAQ,GAAG,aAAGJ,MAAH,cAAarO,OAAO,CAACyO,QAArB,CAAjB;;EAEA,WAAOA,QAAQ,CAACC,MAAT,CAAgB,UAAAC,KAAK;EAAA,aAAIA,KAAK,CAACR,OAAN,CAAclO,QAAd,CAAJ;EAAA,KAArB,CAAP;EACD,GAjBoB;EAmBrB2O,EAAAA,OAnBqB,mBAmBb5O,OAnBa,EAmBJC,QAnBI,EAmBM;EACzB,QAAM2O,OAAO,GAAG,EAAhB;EAEA,QAAIC,QAAQ,GAAG7O,OAAO,CAACgD,UAAvB;;EAEA,WAAO6L,QAAQ,IAAIA,QAAQ,CAACxN,QAAT,KAAsByN,IAAI,CAACC,YAAvC,IAAuDF,QAAQ,CAACxN,QAAT,KAAsB4M,SAApF,EAA+F;EAC7F,UAAI,KAAKE,OAAL,CAAaU,QAAb,EAAuB5O,QAAvB,CAAJ,EAAsC;EACpC2O,QAAAA,OAAO,CAACI,IAAR,CAAaH,QAAb;EACD;;EAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAAC7L,UAApB;EACD;;EAED,WAAO4L,OAAP;EACD,GAjCoB;EAmCrBK,EAAAA,IAnCqB,gBAmChBjP,OAnCgB,EAmCPC,QAnCO,EAmCG;EACtB,QAAIiP,QAAQ,GAAGlP,OAAO,CAACmP,sBAAvB;;EAEA,WAAOD,QAAP,EAAiB;EACf,UAAIA,QAAQ,CAACf,OAAT,CAAiBlO,QAAjB,CAAJ,EAAgC;EAC9B,eAAO,CAACiP,QAAD,CAAP;EACD;;EAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACC,sBAApB;EACD;;EAED,WAAO,EAAP;EACD,GA/CoB;EAiDrBC,EAAAA,IAjDqB,gBAiDhBpP,OAjDgB,EAiDPC,QAjDO,EAiDG;EACtB,QAAImP,IAAI,GAAGpP,OAAO,CAACqP,kBAAnB;;EAEA,WAAOD,IAAP,EAAa;EACX,UAAI,KAAKjB,OAAL,CAAaiB,IAAb,EAAmBnP,QAAnB,CAAJ,EAAkC;EAChC,eAAO,CAACmP,IAAD,CAAP;EACD;;EAEDA,MAAAA,IAAI,GAAGA,IAAI,CAACC,kBAAZ;EACD;;EAED,WAAO,EAAP;EACD;EA7DoB,CAAvB;;ECSA;EACA;EACA;EACA;EACA;;EAEA,IAAMpF,MAAI,GAAG,UAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,aAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAMiF,cAAc,GAAG,WAAvB;EACA,IAAMC,eAAe,GAAG,YAAxB;EACA,IAAMC,sBAAsB,GAAG,GAA/B;;EACA,IAAMC,eAAe,GAAG,EAAxB;EAEA,IAAMC,OAAO,GAAG;EACdC,EAAAA,QAAQ,EAAE,IADI;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE,KAHO;EAIdC,EAAAA,KAAK,EAAE,OAJO;EAKdC,EAAAA,IAAI,EAAE,IALQ;EAMdC,EAAAA,KAAK,EAAE;EANO,CAAhB;EASA,IAAMC,WAAW,GAAG;EAClBN,EAAAA,QAAQ,EAAE,kBADQ;EAElBC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE,kBAHW;EAIlBC,EAAAA,KAAK,EAAE,kBAJW;EAKlBC,EAAAA,IAAI,EAAE,SALY;EAMlBC,EAAAA,KAAK,EAAE;EANW,CAApB;EASA,IAAME,cAAc,GAAG,MAAvB;EACA,IAAMC,cAAc,GAAG,MAAvB;EACA,IAAMC,cAAc,GAAG,MAAvB;EACA,IAAMC,eAAe,GAAG,OAAxB;EAEA,IAAMC,WAAW,aAAWlG,WAA5B;EACA,IAAMmG,UAAU,YAAUnG,WAA1B;EACA,IAAMoG,aAAa,eAAapG,WAAhC;EACA,IAAMqG,gBAAgB,kBAAgBrG,WAAtC;EACA,IAAMsG,gBAAgB,kBAAgBtG,WAAtC;EACA,IAAMuG,gBAAgB,kBAAgBvG,WAAtC;EACA,IAAMwG,eAAe,iBAAexG,WAApC;EACA,IAAMyG,cAAc,gBAAczG,WAAlC;EACA,IAAM0G,iBAAiB,mBAAiB1G,WAAxC;EACA,IAAM2G,eAAe,iBAAe3G,WAApC;EACA,IAAM4G,gBAAgB,iBAAe5G,WAArC;EACA,IAAM6G,mBAAmB,YAAU7G,WAAV,GAAsBC,cAA/C;EACA,IAAMI,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EAEA,IAAM6G,mBAAmB,GAAG,UAA5B;EACA,IAAM9E,mBAAiB,GAAG,QAA1B;EACA,IAAM+E,gBAAgB,GAAG,OAAzB;EACA,IAAMC,gBAAgB,GAAG,qBAAzB;EACA,IAAMC,eAAe,GAAG,oBAAxB;EACA,IAAMC,eAAe,GAAG,oBAAxB;EACA,IAAMC,eAAe,GAAG,oBAAxB;EACA,IAAMC,wBAAwB,GAAG,eAAjC;EAEA,IAAMC,eAAe,GAAG,SAAxB;EACA,IAAMC,oBAAoB,GAAG,uBAA7B;EACA,IAAMC,aAAa,GAAG,gBAAtB;EACA,IAAMC,iBAAiB,GAAG,oBAA1B;EACA,IAAMC,kBAAkB,GAAG,0CAA3B;EACA,IAAMC,mBAAmB,GAAG,sBAA5B;EACA,IAAMC,mBAAmB,GAAG,+BAA5B;EACA,IAAMC,kBAAkB,GAAG,wBAA3B;EAEA,IAAMC,WAAW,GAAG;EAClBC,EAAAA,KAAK,EAAE,OADW;EAElBC,EAAAA,GAAG,EAAE;EAFa,CAApB;EAKA;EACA;EACA;EACA;EACA;;MACMC;EACJ,oBAAYpS,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,SAAKoQ,MAAL,GAAc,IAAd;EACA,SAAKC,SAAL,GAAiB,IAAjB;EACA,SAAKC,cAAL,GAAsB,IAAtB;EACA,SAAKC,SAAL,GAAiB,KAAjB;EACA,SAAKC,UAAL,GAAkB,KAAlB;EACA,SAAKC,YAAL,GAAoB,IAApB;EACA,SAAKC,WAAL,GAAmB,CAAnB;EACA,SAAKC,WAAL,GAAmB,CAAnB;EAEA,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;EACA,SAAK6I,QAAL,GAAgB9K,OAAhB;EACA,SAAK+S,kBAAL,GAA0B7E,cAAc,CAACM,OAAf,CAAuBsD,mBAAvB,EAA4C,KAAKhH,QAAjD,CAA1B;EACA,SAAKkI,eAAL,GAAuB,kBAAkBnT,QAAQ,CAACyD,eAA3B,IAA8C2P,SAAS,CAACC,cAAV,GAA2B,CAAhG;EACA,SAAKC,aAAL,GAAqBhL,OAAO,CAAC1H,MAAM,CAAC2S,YAAR,CAA5B;;EAEA,SAAKC,kBAAL;;EACAtO,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;EACD;;;;;EAYD;WAEAiF,OAAA,gBAAO;EACL,QAAI,CAAC,KAAKqD,UAAV,EAAsB;EACpB,WAAKa,MAAL,CAAYpD,cAAZ;EACD;EACF;;WAEDqD,kBAAA,2BAAkB;EAChB;EACA;EACA,QAAI,CAAC1T,QAAQ,CAAC2T,MAAV,IAAoB1Q,SAAS,CAAC,KAAKgI,QAAN,CAAjC,EAAkD;EAChD,WAAKsE,IAAL;EACD;EACF;;WAEDH,OAAA,gBAAO;EACL,QAAI,CAAC,KAAKwD,UAAV,EAAsB;EACpB,WAAKa,MAAL,CAAYnD,cAAZ;EACD;EACF;;WAEDL,QAAA,eAAM3J,KAAN,EAAa;EACX,QAAI,CAACA,KAAL,EAAY;EACV,WAAKqM,SAAL,GAAiB,IAAjB;EACD;;EAED,QAAItE,cAAc,CAACM,OAAf,CAAuBqD,kBAAvB,EAA2C,KAAK/G,QAAhD,CAAJ,EAA+D;EAC7D7J,MAAAA,oBAAoB,CAAC,KAAK6J,QAAN,CAApB;EACA,WAAK2I,KAAL,CAAW,IAAX;EACD;;EAEDC,IAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;EACA,SAAKA,SAAL,GAAiB,IAAjB;EACD;;WAEDmB,QAAA,eAAMtN,KAAN,EAAa;EACX,QAAI,CAACA,KAAL,EAAY;EACV,WAAKqM,SAAL,GAAiB,KAAjB;EACD;;EAED,QAAI,KAAKF,SAAT,EAAoB;EAClBoB,MAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;EACA,WAAKA,SAAL,GAAiB,IAAjB;EACD;;EAED,QAAI,KAAKO,OAAL,IAAgB,KAAKA,OAAL,CAAalD,QAA7B,IAAyC,CAAC,KAAK6C,SAAnD,EAA8D;EAC5D,WAAKmB,eAAL;;EAEA,WAAKrB,SAAL,GAAiBsB,WAAW,CAC1B,CAAC/T,QAAQ,CAACgU,eAAT,GAA2B,KAAKN,eAAhC,GAAkD,KAAKnE,IAAxD,EAA8D0E,IAA9D,CAAmE,IAAnE,CAD0B,EAE1B,KAAKjB,OAAL,CAAalD,QAFa,CAA5B;EAID;EACF;;WAEDoE,KAAA,YAAGC,KAAH,EAAU;EAAA;;EACR,SAAKzB,cAAL,GAAsBrE,cAAc,CAACM,OAAf,CAAuBkD,oBAAvB,EAA6C,KAAK5G,QAAlD,CAAtB;;EACA,QAAMmJ,WAAW,GAAG,KAAKC,aAAL,CAAmB,KAAK3B,cAAxB,CAApB;;EAEA,QAAIyB,KAAK,GAAG,KAAK3B,MAAL,CAAYtL,MAAZ,GAAqB,CAA7B,IAAkCiN,KAAK,GAAG,CAA9C,EAAiD;EAC/C;EACD;;EAED,QAAI,KAAKvB,UAAT,EAAqB;EACnBnM,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgCyF,UAAhC,EAA4C;EAAA,eAAM,KAAI,CAACwD,EAAL,CAAQC,KAAR,CAAN;EAAA,OAA5C;EACA;EACD;;EAED,QAAIC,WAAW,KAAKD,KAApB,EAA2B;EACzB,WAAKlE,KAAL;EACA,WAAK2D,KAAL;EACA;EACD;;EAED,QAAMU,SAAS,GAAGH,KAAK,GAAGC,WAAR,GAChB/D,cADgB,GAEhBC,cAFF;;EAIA,SAAKmD,MAAL,CAAYa,SAAZ,EAAuB,KAAK9B,MAAL,CAAY2B,KAAZ,CAAvB;EACD;;WAED3I,UAAA,mBAAU;EACR/E,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgCV,WAAhC;EACArF,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EAEA,SAAKkI,MAAL,GAAc,IAAd;EACA,SAAKQ,OAAL,GAAe,IAAf;EACA,SAAK/H,QAAL,GAAgB,IAAhB;EACA,SAAKwH,SAAL,GAAiB,IAAjB;EACA,SAAKE,SAAL,GAAiB,IAAjB;EACA,SAAKC,UAAL,GAAkB,IAAlB;EACA,SAAKF,cAAL,GAAsB,IAAtB;EACA,SAAKQ,kBAAL,GAA0B,IAA1B;EACD;;;WAIDD,aAAA,oBAAW7Q,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACDyN,OADC,EAEDzN,MAFC,CAAN;EAIAF,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAegO,WAAf,CAAf;EACA,WAAOhO,MAAP;EACD;;WAEDmS,eAAA,wBAAe;EACb,QAAMC,SAAS,GAAG3U,IAAI,CAAC4U,GAAL,CAAS,KAAK1B,WAAd,CAAlB;;EAEA,QAAIyB,SAAS,IAAI5E,eAAjB,EAAkC;EAChC;EACD;;EAED,QAAM0E,SAAS,GAAGE,SAAS,GAAG,KAAKzB,WAAnC;EAEA,SAAKA,WAAL,GAAmB,CAAnB,CATa;;EAYb,QAAIuB,SAAS,GAAG,CAAhB,EAAmB;EACjB,WAAKlF,IAAL;EACD,KAdY;;;EAiBb,QAAIkF,SAAS,GAAG,CAAhB,EAAmB;EACjB,WAAK/E,IAAL;EACD;EACF;;WAEDiE,qBAAA,8BAAqB;EAAA;;EACnB,QAAI,KAAKR,OAAL,CAAajD,QAAjB,EAA2B;EACzBtJ,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B0F,aAA/B,EAA8C,UAAArK,KAAK;EAAA,eAAI,MAAI,CAACoO,QAAL,CAAcpO,KAAd,CAAJ;EAAA,OAAnD;EACD;;EAED,QAAI,KAAK0M,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;EAClCxJ,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B2F,gBAA/B,EAAiD,UAAAtK,KAAK;EAAA,eAAI,MAAI,CAAC2J,KAAL,CAAW3J,KAAX,CAAJ;EAAA,OAAtD;EACAG,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B4F,gBAA/B,EAAiD,UAAAvK,KAAK;EAAA,eAAI,MAAI,CAACsN,KAAL,CAAWtN,KAAX,CAAJ;EAAA,OAAtD;EACD;;EAED,QAAI,KAAK0M,OAAL,CAAa7C,KAAb,IAAsB,KAAKgD,eAA/B,EAAgD;EAC9C,WAAKwB,uBAAL;EACD;EACF;;WAEDA,0BAAA,mCAA0B;EAAA;;EACxB,QAAMC,KAAK,GAAG,SAARA,KAAQ,CAAAtO,KAAK,EAAI;EACrB,UAAI,MAAI,CAACgN,aAAL,IAAsBlB,WAAW,CAAC9L,KAAK,CAACuO,WAAN,CAAkB7R,WAAlB,EAAD,CAArC,EAAwE;EACtE,QAAA,MAAI,CAAC8P,WAAL,GAAmBxM,KAAK,CAACwO,OAAzB;EACD,OAFD,MAEO,IAAI,CAAC,MAAI,CAACxB,aAAV,EAAyB;EAC9B,QAAA,MAAI,CAACR,WAAL,GAAmBxM,KAAK,CAACyO,OAAN,CAAc,CAAd,EAAiBD,OAApC;EACD;EACF,KAND;;EAQA,QAAME,IAAI,GAAG,SAAPA,IAAO,CAAA1O,KAAK,EAAI;EACpB;EACA,UAAIA,KAAK,CAACyO,OAAN,IAAiBzO,KAAK,CAACyO,OAAN,CAAc7N,MAAd,GAAuB,CAA5C,EAA+C;EAC7C,QAAA,MAAI,CAAC6L,WAAL,GAAmB,CAAnB;EACD,OAFD,MAEO;EACL,QAAA,MAAI,CAACA,WAAL,GAAmBzM,KAAK,CAACyO,OAAN,CAAc,CAAd,EAAiBD,OAAjB,GAA2B,MAAI,CAAChC,WAAnD;EACD;EACF,KAPD;;EASA,QAAMmC,GAAG,GAAG,SAANA,GAAM,CAAA3O,KAAK,EAAI;EACnB,UAAI,MAAI,CAACgN,aAAL,IAAsBlB,WAAW,CAAC9L,KAAK,CAACuO,WAAN,CAAkB7R,WAAlB,EAAD,CAArC,EAAwE;EACtE,QAAA,MAAI,CAAC+P,WAAL,GAAmBzM,KAAK,CAACwO,OAAN,GAAgB,MAAI,CAAChC,WAAxC;EACD;;EAED,MAAA,MAAI,CAACyB,YAAL;;EACA,UAAI,MAAI,CAACvB,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EAEA,QAAA,MAAI,CAACA,KAAL;;EACA,YAAI,MAAI,CAAC4C,YAAT,EAAuB;EACrBqC,UAAAA,YAAY,CAAC,MAAI,CAACrC,YAAN,CAAZ;EACD;;EAED,QAAA,MAAI,CAACA,YAAL,GAAoB5Q,UAAU,CAAC,UAAAqE,KAAK;EAAA,iBAAI,MAAI,CAACsN,KAAL,CAAWtN,KAAX,CAAJ;EAAA,SAAN,EAA6BqJ,sBAAsB,GAAG,MAAI,CAACqD,OAAL,CAAalD,QAAnE,CAA9B;EACD;EACF,KAtBD;;EAwBAzB,IAAAA,cAAc,CAACE,IAAf,CAAoBwD,iBAApB,EAAuC,KAAK9G,QAA5C,EAAsDzI,OAAtD,CAA8D,UAAA2S,OAAO,EAAI;EACvE1O,MAAAA,YAAY,CAACkC,EAAb,CAAgBwM,OAAhB,EAAyBhE,gBAAzB,EAA2C,UAAAiE,CAAC;EAAA,eAAIA,CAAC,CAACjL,cAAF,EAAJ;EAAA,OAA5C;EACD,KAFD;;EAIA,QAAI,KAAKmJ,aAAT,EAAwB;EACtB7M,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+BgG,iBAA/B,EAAkD,UAAA3K,KAAK;EAAA,eAAIsO,KAAK,CAACtO,KAAD,CAAT;EAAA,OAAvD;EACAG,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+BiG,eAA/B,EAAgD,UAAA5K,KAAK;EAAA,eAAI2O,GAAG,CAAC3O,KAAD,CAAP;EAAA,OAArD;;EAEA,WAAK2E,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4B1D,wBAA5B;EACD,KALD,MAKO;EACLlL,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B6F,gBAA/B,EAAiD,UAAAxK,KAAK;EAAA,eAAIsO,KAAK,CAACtO,KAAD,CAAT;EAAA,OAAtD;EACAG,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B8F,eAA/B,EAAgD,UAAAzK,KAAK;EAAA,eAAI0O,IAAI,CAAC1O,KAAD,CAAR;EAAA,OAArD;EACAG,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B+F,cAA/B,EAA+C,UAAA1K,KAAK;EAAA,eAAI2O,GAAG,CAAC3O,KAAD,CAAP;EAAA,OAApD;EACD;EACF;;WAEDoO,WAAA,kBAASpO,KAAT,EAAgB;EACd,QAAI,kBAAkBxD,IAAlB,CAAuBwD,KAAK,CAACU,MAAN,CAAasO,OAApC,CAAJ,EAAkD;EAChD;EACD;;EAED,YAAQhP,KAAK,CAAC1B,GAAd;EACE,WAAK6K,cAAL;EACEnJ,QAAAA,KAAK,CAAC6D,cAAN;EACA,aAAKiF,IAAL;EACA;;EACF,WAAKM,eAAL;EACEpJ,QAAAA,KAAK,CAAC6D,cAAN;EACA,aAAKoF,IAAL;EACA;EARJ;EAWD;;WAED8E,gBAAA,uBAAclU,OAAd,EAAuB;EACrB,SAAKqS,MAAL,GAAcrS,OAAO,IAAIA,OAAO,CAACgD,UAAnB,GACZkL,cAAc,CAACE,IAAf,CAAoBuD,aAApB,EAAmC3R,OAAO,CAACgD,UAA3C,CADY,GAEZ,EAFF;EAIA,WAAO,KAAKqP,MAAL,CAAYvK,OAAZ,CAAoB9H,OAApB,CAAP;EACD;;WAEDoV,sBAAA,6BAAoBjB,SAApB,EAA+BkB,aAA/B,EAA8C;EAC5C,QAAMC,eAAe,GAAGnB,SAAS,KAAKjE,cAAtC;EACA,QAAMqF,eAAe,GAAGpB,SAAS,KAAKhE,cAAtC;;EACA,QAAM8D,WAAW,GAAG,KAAKC,aAAL,CAAmBmB,aAAnB,CAApB;;EACA,QAAMG,aAAa,GAAG,KAAKnD,MAAL,CAAYtL,MAAZ,GAAqB,CAA3C;EACA,QAAM0O,aAAa,GAAIF,eAAe,IAAItB,WAAW,KAAK,CAApC,IACGqB,eAAe,IAAIrB,WAAW,KAAKuB,aAD5D;;EAGA,QAAIC,aAAa,IAAI,CAAC,KAAK5C,OAAL,CAAa9C,IAAnC,EAAyC;EACvC,aAAOsF,aAAP;EACD;;EAED,QAAMK,KAAK,GAAGvB,SAAS,KAAKhE,cAAd,GAA+B,CAAC,CAAhC,GAAoC,CAAlD;EACA,QAAMwF,SAAS,GAAG,CAAC1B,WAAW,GAAGyB,KAAf,IAAwB,KAAKrD,MAAL,CAAYtL,MAAtD;EAEA,WAAO4O,SAAS,KAAK,CAAC,CAAf,GACL,KAAKtD,MAAL,CAAY,KAAKA,MAAL,CAAYtL,MAAZ,GAAqB,CAAjC,CADK,GAEL,KAAKsL,MAAL,CAAYsD,SAAZ,CAFF;EAGD;;WAEDC,qBAAA,4BAAmBC,aAAnB,EAAkCC,kBAAlC,EAAsD;EACpD,QAAMC,WAAW,GAAG,KAAK7B,aAAL,CAAmB2B,aAAnB,CAApB;;EACA,QAAMG,SAAS,GAAG,KAAK9B,aAAL,CAAmBhG,cAAc,CAACM,OAAf,CAAuBkD,oBAAvB,EAA6C,KAAK5G,QAAlD,CAAnB,CAAlB;;EAEA,WAAOxE,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCwF,WAApC,EAAiD;EACtDuF,MAAAA,aAAa,EAAbA,aADsD;EAEtD1B,MAAAA,SAAS,EAAE2B,kBAF2C;EAGtDG,MAAAA,IAAI,EAAED,SAHgD;EAItDjC,MAAAA,EAAE,EAAEgC;EAJkD,KAAjD,CAAP;EAMD;;WAEDG,6BAAA,oCAA2BlW,OAA3B,EAAoC;EAClC,QAAI,KAAK+S,kBAAT,EAA6B;EAC3B,UAAMoD,UAAU,GAAGjI,cAAc,CAACE,IAAf,CAAoBqD,eAApB,EAAqC,KAAKsB,kBAA1C,CAAnB;;EACA,WAAK,IAAIjM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqP,UAAU,CAACpP,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;EAC1CqP,QAAAA,UAAU,CAACrP,CAAD,CAAV,CAAcyE,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B;EACD;;EAED,UAAMgK,aAAa,GAAG,KAAKrD,kBAAL,CAAwBtE,QAAxB,CACpB,KAAKyF,aAAL,CAAmBlU,OAAnB,CADoB,CAAtB;;EAIA,UAAIoW,aAAJ,EAAmB;EACjBA,QAAAA,aAAa,CAAC7K,SAAd,CAAwB2J,GAAxB,CAA4B9I,mBAA5B;EACD;EACF;EACF;;WAEDuH,kBAAA,2BAAkB;EAChB,QAAM3T,OAAO,GAAG,KAAKuS,cAAL,IAAuBrE,cAAc,CAACM,OAAf,CAAuBkD,oBAAvB,EAA6C,KAAK5G,QAAlD,CAAvC;;EAEA,QAAI,CAAC9K,OAAL,EAAc;EACZ;EACD;;EAED,QAAMqW,eAAe,GAAGC,QAAQ,CAACtW,OAAO,CAACE,YAAR,CAAqB,eAArB,CAAD,EAAwC,EAAxC,CAAhC;;EAEA,QAAImW,eAAJ,EAAqB;EACnB,WAAKxD,OAAL,CAAa0D,eAAb,GAA+B,KAAK1D,OAAL,CAAa0D,eAAb,IAAgC,KAAK1D,OAAL,CAAalD,QAA5E;EACA,WAAKkD,OAAL,CAAalD,QAAb,GAAwB0G,eAAxB;EACD,KAHD,MAGO;EACL,WAAKxD,OAAL,CAAalD,QAAb,GAAwB,KAAKkD,OAAL,CAAa0D,eAAb,IAAgC,KAAK1D,OAAL,CAAalD,QAArE;EACD;EACF;;WAED2D,SAAA,gBAAOa,SAAP,EAAkBnU,OAAlB,EAA2B;EAAA;;EACzB,QAAMqV,aAAa,GAAGnH,cAAc,CAACM,OAAf,CAAuBkD,oBAAvB,EAA6C,KAAK5G,QAAlD,CAAtB;;EACA,QAAM0L,kBAAkB,GAAG,KAAKtC,aAAL,CAAmBmB,aAAnB,CAA3B;;EACA,QAAMoB,WAAW,GAAGzW,OAAO,IAAKqV,aAAa,IAC3C,KAAKD,mBAAL,CAAyBjB,SAAzB,EAAoCkB,aAApC,CADF;;EAGA,QAAMqB,gBAAgB,GAAG,KAAKxC,aAAL,CAAmBuC,WAAnB,CAAzB;;EACA,QAAME,SAAS,GAAGxO,OAAO,CAAC,KAAKmK,SAAN,CAAzB;EAEA,QAAIsE,oBAAJ;EACA,QAAIC,cAAJ;EACA,QAAIf,kBAAJ;;EAEA,QAAI3B,SAAS,KAAKjE,cAAlB,EAAkC;EAChC0G,MAAAA,oBAAoB,GAAGvF,eAAvB;EACAwF,MAAAA,cAAc,GAAGvF,eAAjB;EACAwE,MAAAA,kBAAkB,GAAG1F,cAArB;EACD,KAJD,MAIO;EACLwG,MAAAA,oBAAoB,GAAGxF,gBAAvB;EACAyF,MAAAA,cAAc,GAAGtF,eAAjB;EACAuE,MAAAA,kBAAkB,GAAGzF,eAArB;EACD;;EAED,QAAIoG,WAAW,IAAIA,WAAW,CAAClL,SAAZ,CAAsBE,QAAtB,CAA+BW,mBAA/B,CAAnB,EAAsE;EACpE,WAAKqG,UAAL,GAAkB,KAAlB;EACA;EACD;;EAED,QAAMqE,UAAU,GAAG,KAAKlB,kBAAL,CAAwBa,WAAxB,EAAqCX,kBAArC,CAAnB;;EACA,QAAIgB,UAAU,CAACxN,gBAAf,EAAiC;EAC/B;EACD;;EAED,QAAI,CAAC+L,aAAD,IAAkB,CAACoB,WAAvB,EAAoC;EAClC;EACA;EACD;;EAED,SAAKhE,UAAL,GAAkB,IAAlB;;EAEA,QAAIkE,SAAJ,EAAe;EACb,WAAK7G,KAAL;EACD;;EAED,SAAKoG,0BAAL,CAAgCO,WAAhC;;EACA,SAAKlE,cAAL,GAAsBkE,WAAtB;;EAEA,QAAI,KAAK3L,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC0F,gBAAjC,CAAJ,EAAwD;EACtDsF,MAAAA,WAAW,CAAClL,SAAZ,CAAsB2J,GAAtB,CAA0B2B,cAA1B;EAEAjT,MAAAA,MAAM,CAAC6S,WAAD,CAAN;EAEApB,MAAAA,aAAa,CAAC9J,SAAd,CAAwB2J,GAAxB,CAA4B0B,oBAA5B;EACAH,MAAAA,WAAW,CAAClL,SAAZ,CAAsB2J,GAAtB,CAA0B0B,oBAA1B;EAEA,UAAMjW,kBAAkB,GAAGH,gCAAgC,CAAC6U,aAAD,CAA3D;EAEA/O,MAAAA,YAAY,CAACmC,GAAb,CAAiB4M,aAAjB,EAAgCrW,cAAhC,EAAgD,YAAM;EACpDyX,QAAAA,WAAW,CAAClL,SAAZ,CAAsBC,MAAtB,CAA6BoL,oBAA7B,EAAmDC,cAAnD;EACAJ,QAAAA,WAAW,CAAClL,SAAZ,CAAsB2J,GAAtB,CAA0B9I,mBAA1B;EAEAiJ,QAAAA,aAAa,CAAC9J,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B,EAAkDyK,cAAlD,EAAkED,oBAAlE;EAEA,QAAA,MAAI,CAACnE,UAAL,GAAkB,KAAlB;EAEA3Q,QAAAA,UAAU,CAAC,YAAM;EACfwE,UAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAC8B,QAA1B,EAAoCyF,UAApC,EAAgD;EAC9CsF,YAAAA,aAAa,EAAEY,WAD+B;EAE9CtC,YAAAA,SAAS,EAAE2B,kBAFmC;EAG9CG,YAAAA,IAAI,EAAEO,kBAHwC;EAI9CzC,YAAAA,EAAE,EAAE2C;EAJ0C,WAAhD;EAMD,SAPS,EAOP,CAPO,CAAV;EAQD,OAhBD;EAkBApV,MAAAA,oBAAoB,CAAC+T,aAAD,EAAgB1U,kBAAhB,CAApB;EACD,KA7BD,MA6BO;EACL0U,MAAAA,aAAa,CAAC9J,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B;EACAqK,MAAAA,WAAW,CAAClL,SAAZ,CAAsB2J,GAAtB,CAA0B9I,mBAA1B;EAEA,WAAKqG,UAAL,GAAkB,KAAlB;EACAnM,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyF,UAApC,EAAgD;EAC9CsF,QAAAA,aAAa,EAAEY,WAD+B;EAE9CtC,QAAAA,SAAS,EAAE2B,kBAFmC;EAG9CG,QAAAA,IAAI,EAAEO,kBAHwC;EAI9CzC,QAAAA,EAAE,EAAE2C;EAJ0C,OAAhD;EAMD;;EAED,QAAIC,SAAJ,EAAe;EACb,WAAKlD,KAAL;EACD;EACF;;;aAIMsD,oBAAP,2BAAyB/W,OAAzB,EAAkCiC,MAAlC,EAA0C;EACxC,QAAIyC,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAX;;EACA,QAAI0I,OAAO,gBACNnD,OADM,EAEN3C,WAAW,CAACI,iBAAZ,CAA8BnN,OAA9B,CAFM,CAAX;;EAKA,QAAI,OAAOiC,MAAP,KAAkB,QAAtB,EAAgC;EAC9B4Q,MAAAA,OAAO,gBACFA,OADE,EAEF5Q,MAFE,CAAP;EAID;;EAED,QAAM+U,MAAM,GAAG,OAAO/U,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC4Q,OAAO,CAAChD,KAA7D;;EAEA,QAAI,CAACnL,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAI0N,QAAJ,CAAapS,OAAb,EAAsB6S,OAAtB,CAAP;EACD;;EAED,QAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;EAC9ByC,MAAAA,IAAI,CAACqP,EAAL,CAAQ9R,MAAR;EACD,KAFD,MAEO,IAAI,OAAO+U,MAAP,KAAkB,QAAtB,EAAgC;EACrC,UAAI,OAAOtS,IAAI,CAACsS,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIC,SAAJ,wBAAkCD,MAAlC,QAAN;EACD;;EAEDtS,MAAAA,IAAI,CAACsS,MAAD,CAAJ;EACD,KANM,MAMA,IAAInE,OAAO,CAAClD,QAAR,IAAoBkD,OAAO,CAACqE,IAAhC,EAAsC;EAC3CxS,MAAAA,IAAI,CAACoL,KAAL;EACApL,MAAAA,IAAI,CAAC+O,KAAL;EACD;EACF;;aAEM7H,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3BuG,MAAAA,QAAQ,CAAC2E,iBAAT,CAA2B,IAA3B,EAAiC9U,MAAjC;EACD,KAFM,CAAP;EAGD;;aAEMkV,sBAAP,6BAA2BhR,KAA3B,EAAkC;EAChC,QAAMU,MAAM,GAAGtG,sBAAsB,CAAC,IAAD,CAArC;;EAEA,QAAI,CAACsG,MAAD,IAAW,CAACA,MAAM,CAAC0E,SAAP,CAAiBE,QAAjB,CAA0ByF,mBAA1B,CAAhB,EAAgE;EAC9D;EACD;;EAED,QAAMjP,MAAM,gBACP8K,WAAW,CAACI,iBAAZ,CAA8BtG,MAA9B,CADO,EAEPkG,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAFO,CAAZ;;EAIA,QAAMiK,UAAU,GAAG,KAAKlX,YAAL,CAAkB,eAAlB,CAAnB;;EAEA,QAAIkX,UAAJ,EAAgB;EACdnV,MAAAA,MAAM,CAAC0N,QAAP,GAAkB,KAAlB;EACD;;EAEDyC,IAAAA,QAAQ,CAAC2E,iBAAT,CAA2BlQ,MAA3B,EAAmC5E,MAAnC;;EAEA,QAAImV,UAAJ,EAAgB;EACdrS,MAAAA,IAAI,CAACG,OAAL,CAAa2B,MAAb,EAAqBsD,UAArB,EAA+B4J,EAA/B,CAAkCqD,UAAlC;EACD;;EAEDjR,IAAAA,KAAK,CAAC6D,cAAN;EACD;;aAEMgC,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BAldoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOwF,OAAP;EACD;;;;;EA+cH;EACA;EACA;EACA;EACA;;;EAEApJ,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgDsH,mBAAhD,EAAqEK,QAAQ,CAAC+E,mBAA9E;EAEA7Q,YAAY,CAACkC,EAAb,CAAgB/H,MAAhB,EAAwBwQ,mBAAxB,EAA6C,YAAM;EACjD,MAAMoG,SAAS,GAAGnJ,cAAc,CAACE,IAAf,CAAoB4D,kBAApB,CAAlB;;EAEA,OAAK,IAAIlL,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGiQ,SAAS,CAACtQ,MAAhC,EAAwCD,CAAC,GAAGM,GAA5C,EAAiDN,CAAC,EAAlD,EAAsD;EACpDsL,IAAAA,QAAQ,CAAC2E,iBAAT,CAA2BM,SAAS,CAACvQ,CAAD,CAApC,EAAyC/B,IAAI,CAACG,OAAL,CAAamS,SAAS,CAACvQ,CAAD,CAAtB,EAA2BqD,UAA3B,CAAzC;EACD;EACF,CAND;EAQA;EACA;EACA;EACA;EACA;EACA;;EAEAjG,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAamI,QAAQ,CAACxG,eAAtB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBkG,QAAzB;;EACAlJ,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOmG,QAAQ,CAACxG,eAAhB;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;ECrlBA;EACA;EACA;EACA;EACA;;EAEA,IAAM3B,MAAI,GAAG,UAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,aAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAMqF,SAAO,GAAG;EACdnD,EAAAA,MAAM,EAAE,IADM;EAEd+K,EAAAA,MAAM,EAAE;EAFM,CAAhB;EAKA,IAAMrH,aAAW,GAAG;EAClB1D,EAAAA,MAAM,EAAE,SADU;EAElB+K,EAAAA,MAAM,EAAE;EAFU,CAApB;EAKA,IAAMC,UAAU,YAAUnN,WAA1B;EACA,IAAMoN,WAAW,aAAWpN,WAA5B;EACA,IAAMqN,UAAU,YAAUrN,WAA1B;EACA,IAAMsN,YAAY,cAAYtN,WAA9B;EACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EAEA,IAAMsN,eAAe,GAAG,MAAxB;EACA,IAAMC,mBAAmB,GAAG,UAA5B;EACA,IAAMC,qBAAqB,GAAG,YAA9B;EACA,IAAMC,oBAAoB,GAAG,WAA7B;EAEA,IAAMC,KAAK,GAAG,OAAd;EACA,IAAMC,MAAM,GAAG,QAAf;EAEA,IAAMC,gBAAgB,GAAG,oBAAzB;EACA,IAAM5L,sBAAoB,GAAG,0BAA7B;EAEA;EACA;EACA;EACA;EACA;;MAEM6L;EACJ,oBAAYlY,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,SAAKkW,gBAAL,GAAwB,KAAxB;EACA,SAAKrN,QAAL,GAAgB9K,OAAhB;EACA,SAAK6S,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;EACA,SAAKmW,aAAL,GAAqBlK,cAAc,CAACE,IAAf,CAChB/B,sBAAH,iBAAkCrM,OAAO,CAACuE,EAA1C,aACG8H,sBADH,wBACyCrM,OAAO,CAACuE,EADjD,SADmB,CAArB;EAKA,QAAM8T,UAAU,GAAGnK,cAAc,CAACE,IAAf,CAAoB/B,sBAApB,CAAnB;;EAEA,SAAK,IAAIvF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGiR,UAAU,CAACtR,MAAjC,EAAyCD,CAAC,GAAGM,GAA7C,EAAkDN,CAAC,EAAnD,EAAuD;EACrD,UAAMwR,IAAI,GAAGD,UAAU,CAACvR,CAAD,CAAvB;EACA,UAAM7G,QAAQ,GAAGI,sBAAsB,CAACiY,IAAD,CAAvC;EACA,UAAMC,aAAa,GAAGrK,cAAc,CAACE,IAAf,CAAoBnO,QAApB,EACnByO,MADmB,CACZ,UAAA8J,SAAS;EAAA,eAAIA,SAAS,KAAKxY,OAAlB;EAAA,OADG,CAAtB;;EAGA,UAAIC,QAAQ,KAAK,IAAb,IAAqBsY,aAAa,CAACxR,MAAvC,EAA+C;EAC7C,aAAK0R,SAAL,GAAiBxY,QAAjB;;EACA,aAAKmY,aAAL,CAAmBpJ,IAAnB,CAAwBsJ,IAAxB;EACD;EACF;;EAED,SAAKI,OAAL,GAAe,KAAK7F,OAAL,CAAayE,MAAb,GAAsB,KAAKqB,UAAL,EAAtB,GAA0C,IAAzD;;EAEA,QAAI,CAAC,KAAK9F,OAAL,CAAayE,MAAlB,EAA0B;EACxB,WAAKsB,yBAAL,CAA+B,KAAK9N,QAApC,EAA8C,KAAKsN,aAAnD;EACD;;EAED,QAAI,KAAKvF,OAAL,CAAatG,MAAjB,EAAyB;EACvB,WAAKA,MAAL;EACD;;EAEDxH,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;EACD;;;;;EAYD;WAEAoC,SAAA,kBAAS;EACP,QAAI,KAAKzB,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,eAAjC,CAAJ,EAAuD;EACrD,WAAKkB,IAAL;EACD,KAFD,MAEO;EACL,WAAKC,IAAL;EACD;EACF;;WAEDA,OAAA,gBAAO;EAAA;;EACL,QAAI,KAAKX,gBAAL,IACF,KAAKrN,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,eAAjC,CADF,EACqD;EACnD;EACD;;EAED,QAAIoB,OAAJ;EACA,QAAIC,WAAJ;;EAEA,QAAI,KAAKN,OAAT,EAAkB;EAChBK,MAAAA,OAAO,GAAG7K,cAAc,CAACE,IAAf,CAAoB6J,gBAApB,EAAsC,KAAKS,OAA3C,EACPhK,MADO,CACA,UAAA4J,IAAI,EAAI;EACd,YAAI,OAAO,KAAI,CAACzF,OAAL,CAAayE,MAApB,KAA+B,QAAnC,EAA6C;EAC3C,iBAAOgB,IAAI,CAACpY,YAAL,CAAkB,aAAlB,MAAqC,KAAI,CAAC2S,OAAL,CAAayE,MAAzD;EACD;;EAED,eAAOgB,IAAI,CAAC/M,SAAL,CAAeE,QAAf,CAAwBmM,mBAAxB,CAAP;EACD,OAPO,CAAV;;EASA,UAAImB,OAAO,CAAChS,MAAR,KAAmB,CAAvB,EAA0B;EACxBgS,QAAAA,OAAO,GAAG,IAAV;EACD;EACF;;EAED,QAAME,SAAS,GAAG/K,cAAc,CAACM,OAAf,CAAuB,KAAKiK,SAA5B,CAAlB;;EACA,QAAIM,OAAJ,EAAa;EACX,UAAMG,cAAc,GAAGH,OAAO,CAACrK,MAAR,CAAe,UAAA4J,IAAI;EAAA,eAAIW,SAAS,KAAKX,IAAlB;EAAA,OAAnB,CAAvB;EACAU,MAAAA,WAAW,GAAGE,cAAc,CAAC,CAAD,CAAd,GAAoBnU,IAAI,CAACG,OAAL,CAAagU,cAAc,CAAC,CAAD,CAA3B,EAAgC/O,UAAhC,CAApB,GAAgE,IAA9E;;EAEA,UAAI6O,WAAW,IAAIA,WAAW,CAACb,gBAA/B,EAAiD;EAC/C;EACD;EACF;;EAED,QAAMgB,UAAU,GAAG7S,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyM,UAApC,CAAnB;;EACA,QAAI4B,UAAU,CAAC7P,gBAAf,EAAiC;EAC/B;EACD;;EAED,QAAIyP,OAAJ,EAAa;EACXA,MAAAA,OAAO,CAAC1W,OAAR,CAAgB,UAAA+W,UAAU,EAAI;EAC5B,YAAIH,SAAS,KAAKG,UAAlB,EAA8B;EAC5BlB,UAAAA,QAAQ,CAACmB,iBAAT,CAA2BD,UAA3B,EAAuC,MAAvC;EACD;;EAED,YAAI,CAACJ,WAAL,EAAkB;EAChBjU,UAAAA,IAAI,CAACC,OAAL,CAAaoU,UAAb,EAAyBjP,UAAzB,EAAmC,IAAnC;EACD;EACF,OARD;EASD;;EAED,QAAMmP,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAKzO,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BoM,mBAA/B;;EACA,SAAK9M,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4B2C,qBAA5B;;EAEA,SAAK/M,QAAL,CAAc/H,KAAd,CAAoBuW,SAApB,IAAiC,CAAjC;;EAEA,QAAI,KAAKlB,aAAL,CAAmBrR,MAAvB,EAA+B;EAC7B,WAAKqR,aAAL,CAAmB/V,OAAnB,CAA2B,UAAArC,OAAO,EAAI;EACpCA,QAAAA,OAAO,CAACuL,SAAR,CAAkBC,MAAlB,CAAyBsM,oBAAzB;EACA9X,QAAAA,OAAO,CAACwM,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD,OAHD;EAID;;EAED,SAAKgN,gBAAL,CAAsB,IAAtB;;EAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,KAAI,CAAC3O,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BqM,qBAA/B;;EACA,MAAA,KAAI,CAAC/M,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4B0C,mBAA5B,EAAiDD,eAAjD;;EAEA,MAAA,KAAI,CAAC7M,QAAL,CAAc/H,KAAd,CAAoBuW,SAApB,IAAiC,EAAjC;;EAEA,MAAA,KAAI,CAACE,gBAAL,CAAsB,KAAtB;;EAEAlT,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAI,CAAC8B,QAA1B,EAAoC0M,WAApC;EACD,KATD;;EAWA,QAAMkC,oBAAoB,GAAGJ,SAAS,CAAC,CAAD,CAAT,CAAazW,WAAb,KAA6ByW,SAAS,CAACxQ,KAAV,CAAgB,CAAhB,CAA1D;EACA,QAAM6Q,UAAU,cAAYD,oBAA5B;EACA,QAAM/Y,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKsK,QAAN,CAA3D;EAEAxE,IAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgDya,QAAhD;EAEAnY,IAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBnK,kBAAhB,CAApB;EACA,SAAKmK,QAAL,CAAc/H,KAAd,CAAoBuW,SAApB,IAAoC,KAAKxO,QAAL,CAAc6O,UAAd,CAApC;EACD;;WAEDd,OAAA,gBAAO;EAAA;;EACL,QAAI,KAAKV,gBAAL,IACF,CAAC,KAAKrN,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,eAAjC,CADH,EACsD;EACpD;EACD;;EAED,QAAMwB,UAAU,GAAG7S,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoC2M,UAApC,CAAnB;;EACA,QAAI0B,UAAU,CAAC7P,gBAAf,EAAiC;EAC/B;EACD;;EAED,QAAMgQ,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAKzO,QAAL,CAAc/H,KAAd,CAAoBuW,SAApB,IAAoC,KAAKxO,QAAL,CAAc2C,qBAAd,GAAsC6L,SAAtC,CAApC;EAEA1V,IAAAA,MAAM,CAAC,KAAKkH,QAAN,CAAN;;EAEA,SAAKA,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4B2C,qBAA5B;;EACA,SAAK/M,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BoM,mBAA/B,EAAoDD,eAApD;;EAEA,QAAMiC,kBAAkB,GAAG,KAAKxB,aAAL,CAAmBrR,MAA9C;;EACA,QAAI6S,kBAAkB,GAAG,CAAzB,EAA4B;EAC1B,WAAK,IAAI9S,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8S,kBAApB,EAAwC9S,CAAC,EAAzC,EAA6C;EAC3C,YAAMkC,OAAO,GAAG,KAAKoP,aAAL,CAAmBtR,CAAnB,CAAhB;EACA,YAAMwR,IAAI,GAAG/X,sBAAsB,CAACyI,OAAD,CAAnC;;EAEA,YAAIsP,IAAI,IAAI,CAACA,IAAI,CAAC/M,SAAL,CAAeE,QAAf,CAAwBkM,eAAxB,CAAb,EAAuD;EACrD3O,UAAAA,OAAO,CAACuC,SAAR,CAAkB2J,GAAlB,CAAsB4C,oBAAtB;EACA9O,UAAAA,OAAO,CAACwD,YAAR,CAAqB,eAArB,EAAsC,KAAtC;EACD;EACF;EACF;;EAED,SAAKgN,gBAAL,CAAsB,IAAtB;;EAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,MAAI,CAACD,gBAAL,CAAsB,KAAtB;;EACA,MAAA,MAAI,CAAC1O,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BqM,qBAA/B;;EACA,MAAA,MAAI,CAAC/M,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4B0C,mBAA5B;;EACAtR,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAC8B,QAA1B,EAAoC4M,YAApC;EACD,KALD;;EAOA,SAAK5M,QAAL,CAAc/H,KAAd,CAAoBuW,SAApB,IAAiC,EAAjC;EACA,QAAM3Y,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKsK,QAAN,CAA3D;EAEAxE,IAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgDya,QAAhD;EACAnY,IAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBnK,kBAAhB,CAApB;EACD;;WAED6Y,mBAAA,0BAAiBK,eAAjB,EAAkC;EAChC,SAAK1B,gBAAL,GAAwB0B,eAAxB;EACD;;WAEDxO,UAAA,mBAAU;EACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EAEA,SAAK0I,OAAL,GAAe,IAAf;EACA,SAAK6F,OAAL,GAAe,IAAf;EACA,SAAK5N,QAAL,GAAgB,IAAhB;EACA,SAAKsN,aAAL,GAAqB,IAArB;EACA,SAAKD,gBAAL,GAAwB,IAAxB;EACD;;;WAIDrF,aAAA,oBAAW7Q,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACDyN,SADC,EAEDzN,MAFC,CAAN;EAIAA,IAAAA,MAAM,CAACsK,MAAP,GAAgBpE,OAAO,CAAClG,MAAM,CAACsK,MAAR,CAAvB,CALiB;;EAMjBxK,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAegO,aAAf,CAAf;EACA,WAAOhO,MAAP;EACD;;WAEDsX,gBAAA,yBAAgB;EACd,WAAO,KAAKzO,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCsM,KAAjC,IAA0CA,KAA1C,GAAkDC,MAAzD;EACD;;WAEDW,aAAA,sBAAa;EAAA;;EAAA,QACLrB,MADK,GACM,KAAKzE,OADX,CACLyE,MADK;;EAGX,QAAIlW,SAAS,CAACkW,MAAD,CAAb,EAAuB;EACrB;EACA,UAAI,OAAOA,MAAM,CAACwC,MAAd,KAAyB,WAAzB,IAAwC,OAAOxC,MAAM,CAAC,CAAD,CAAb,KAAqB,WAAjE,EAA8E;EAC5EA,QAAAA,MAAM,GAAGA,MAAM,CAAC,CAAD,CAAf;EACD;EACF,KALD,MAKO;EACLA,MAAAA,MAAM,GAAGpJ,cAAc,CAACM,OAAf,CAAuB8I,MAAvB,CAAT;EACD;;EAED,QAAMrX,QAAQ,GAAMoM,sBAAN,uBAA2CiL,MAA3C,QAAd;EAEApJ,IAAAA,cAAc,CAACE,IAAf,CAAoBnO,QAApB,EAA8BqX,MAA9B,EACGjV,OADH,CACW,UAAArC,OAAO,EAAI;EAClB,UAAM+Z,QAAQ,GAAGxZ,sBAAsB,CAACP,OAAD,CAAvC;;EAEA,MAAA,MAAI,CAAC4Y,yBAAL,CACEmB,QADF,EAEE,CAAC/Z,OAAD,CAFF;EAID,KARH;EAUA,WAAOsX,MAAP;EACD;;WAEDsB,4BAAA,mCAA0B5Y,OAA1B,EAAmCga,YAAnC,EAAiD;EAC/C,QAAI,CAACha,OAAD,IAAY,CAACga,YAAY,CAACjT,MAA9B,EAAsC;EACpC;EACD;;EAED,QAAMkT,MAAM,GAAGja,OAAO,CAACuL,SAAR,CAAkBE,QAAlB,CAA2BkM,eAA3B,CAAf;EAEAqC,IAAAA,YAAY,CAAC3X,OAAb,CAAqB,UAAAiW,IAAI,EAAI;EAC3B,UAAI2B,MAAJ,EAAY;EACV3B,QAAAA,IAAI,CAAC/M,SAAL,CAAeC,MAAf,CAAsBsM,oBAAtB;EACD,OAFD,MAEO;EACLQ,QAAAA,IAAI,CAAC/M,SAAL,CAAe2J,GAAf,CAAmB4C,oBAAnB;EACD;;EAEDQ,MAAAA,IAAI,CAAC9L,YAAL,CAAkB,eAAlB,EAAmCyN,MAAnC;EACD,KARD;EASD;;;aAIMZ,oBAAP,2BAAyBrZ,OAAzB,EAAkCiC,MAAlC,EAA0C;EACxC,QAAIyC,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAX;;EACA,QAAM0I,OAAO,gBACRnD,SADQ,EAER3C,WAAW,CAACI,iBAAZ,CAA8BnN,OAA9B,CAFQ,EAGP,OAAOiC,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHzC,CAAb;;EAMA,QAAI,CAACyC,IAAD,IAASmO,OAAO,CAACtG,MAAjB,IAA2B,OAAOtK,MAAP,KAAkB,QAA7C,IAAyD,YAAYU,IAAZ,CAAiBV,MAAjB,CAA7D,EAAuF;EACrF4Q,MAAAA,OAAO,CAACtG,MAAR,GAAiB,KAAjB;EACD;;EAED,QAAI,CAAC7H,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAIwT,QAAJ,CAAalY,OAAb,EAAsB6S,OAAtB,CAAP;EACD;;EAED,QAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,UAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,MAAAA,IAAI,CAACzC,MAAD,CAAJ;EACD;EACF;;aAEM2J,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3BqM,MAAAA,QAAQ,CAACmB,iBAAT,CAA2B,IAA3B,EAAiCpX,MAAjC;EACD,KAFM,CAAP;EAGD;;aAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BAzQoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOwF,SAAP;EACD;;;;;EAsQH;EACA;EACA;EACA;EACA;;;EAEApJ,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUlG,KAAV,EAAiB;EACrF;EACA,MAAIA,KAAK,CAACU,MAAN,CAAasO,OAAb,KAAyB,GAA7B,EAAkC;EAChChP,IAAAA,KAAK,CAAC6D,cAAN;EACD;;EAED,MAAMkQ,WAAW,GAAGnN,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAApB;EACA,MAAMlN,QAAQ,GAAGI,sBAAsB,CAAC,IAAD,CAAvC;EACA,MAAM8Z,gBAAgB,GAAGjM,cAAc,CAACE,IAAf,CAAoBnO,QAApB,CAAzB;EAEAka,EAAAA,gBAAgB,CAAC9X,OAAjB,CAAyB,UAAArC,OAAO,EAAI;EAClC,QAAM0E,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAb;EACA,QAAIlI,MAAJ;;EACA,QAAIyC,IAAJ,EAAU;EACR;EACA,UAAIA,IAAI,CAACgU,OAAL,KAAiB,IAAjB,IAAyB,OAAOwB,WAAW,CAAC5C,MAAnB,KAA8B,QAA3D,EAAqE;EACnE5S,QAAAA,IAAI,CAACmO,OAAL,CAAayE,MAAb,GAAsB4C,WAAW,CAAC5C,MAAlC;EACA5S,QAAAA,IAAI,CAACgU,OAAL,GAAehU,IAAI,CAACiU,UAAL,EAAf;EACD;;EAED1W,MAAAA,MAAM,GAAG,QAAT;EACD,KARD,MAQO;EACLA,MAAAA,MAAM,GAAGiY,WAAT;EACD;;EAEDhC,IAAAA,QAAQ,CAACmB,iBAAT,CAA2BrZ,OAA3B,EAAoCiC,MAApC;EACD,GAhBD;EAiBD,CA3BD;EA6BA;EACA;EACA;EACA;EACA;EACA;;EAEAiC,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAaiO,QAAQ,CAACtM,eAAtB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBgM,QAAzB;;EACAhP,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOiM,QAAQ,CAACtM,eAAhB;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;EC5YA;EACA;EACA;EACA;EACA;;EAEA,IAAM3B,MAAI,GAAG,UAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,aAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAM+P,UAAU,GAAG,QAAnB;EACA,IAAMC,SAAS,GAAG,OAAlB;EACA,IAAMC,OAAO,GAAG,KAAhB;EACA,IAAMC,YAAY,GAAG,SAArB;EACA,IAAMC,cAAc,GAAG,WAAvB;EACA,IAAMC,kBAAkB,GAAG,CAA3B;;EAEA,IAAMC,cAAc,GAAG,IAAIhY,MAAJ,CAAc6X,YAAd,SAA8BC,cAA9B,SAAgDJ,UAAhD,CAAvB;EAEA,IAAM3C,YAAU,YAAUrN,WAA1B;EACA,IAAMsN,cAAY,cAAYtN,WAA9B;EACA,IAAMmN,YAAU,YAAUnN,WAA1B;EACA,IAAMoN,aAAW,aAAWpN,WAA5B;EACA,IAAMuQ,WAAW,aAAWvQ,WAA5B;EACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EACA,IAAMuQ,sBAAsB,eAAaxQ,WAAb,GAAyBC,cAArD;EACA,IAAMwQ,oBAAoB,aAAWzQ,WAAX,GAAuBC,cAAjD;EAEA,IAAMyQ,mBAAmB,GAAG,UAA5B;EACA,IAAMnD,iBAAe,GAAG,MAAxB;EACA,IAAMoD,iBAAiB,GAAG,QAA1B;EACA,IAAMC,oBAAoB,GAAG,WAA7B;EACA,IAAMC,mBAAmB,GAAG,UAA5B;EACA,IAAMC,oBAAoB,GAAG,qBAA7B;EACA,IAAMC,iBAAiB,GAAG,QAA1B;EACA,IAAMC,0BAA0B,GAAG,iBAAnC;EAEA,IAAM/O,sBAAoB,GAAG,0BAA7B;EACA,IAAMgP,mBAAmB,GAAG,gBAA5B;EACA,IAAMC,aAAa,GAAG,gBAAtB;EACA,IAAMC,mBAAmB,GAAG,aAA5B;EACA,IAAMC,sBAAsB,GAAG,6DAA/B;EAEA,IAAMC,aAAa,GAAG,WAAtB;EACA,IAAMC,gBAAgB,GAAG,SAAzB;EACA,IAAMC,gBAAgB,GAAG,cAAzB;EACA,IAAMC,mBAAmB,GAAG,YAA5B;EACA,IAAMC,eAAe,GAAG,aAAxB;EACA,IAAMC,cAAc,GAAG,YAAvB;EAEA,IAAMpM,SAAO,GAAG;EACdnC,EAAAA,MAAM,EAAE,CADM;EAEdwO,EAAAA,IAAI,EAAE,IAFQ;EAGdC,EAAAA,QAAQ,EAAE,cAHI;EAIdC,EAAAA,SAAS,EAAE,QAJG;EAKd9Y,EAAAA,OAAO,EAAE,SALK;EAMd+Y,EAAAA,YAAY,EAAE;EANA,CAAhB;EASA,IAAMjM,aAAW,GAAG;EAClB1C,EAAAA,MAAM,EAAE,0BADU;EAElBwO,EAAAA,IAAI,EAAE,SAFY;EAGlBC,EAAAA,QAAQ,EAAE,kBAHQ;EAIlBC,EAAAA,SAAS,EAAE,kBAJO;EAKlB9Y,EAAAA,OAAO,EAAE,QALS;EAMlB+Y,EAAAA,YAAY,EAAE;EANI,CAApB;EASA;EACA;EACA;EACA;EACA;;MAEMC;EACJ,oBAAYnc,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,SAAK6I,QAAL,GAAgB9K,OAAhB;EACA,SAAKoc,OAAL,GAAe,IAAf;EACA,SAAKvJ,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;EACA,SAAKoa,KAAL,GAAa,KAAKC,eAAL,EAAb;EACA,SAAKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EAEA,SAAKnJ,kBAAL;;EACAtO,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;EACD;;;;;EAgBD;WAEAoC,SAAA,kBAAS;EACP,QAAI,KAAKzB,QAAL,CAAc2R,QAAd,IAA0B,KAAK3R,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCqP,mBAAjC,CAA9B,EAAqF;EACnF;EACD;;EAED,QAAM4B,QAAQ,GAAG,KAAK5R,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,iBAAjC,CAAjB;;EAEAwE,IAAAA,QAAQ,CAACQ,UAAT;;EAEA,QAAID,QAAJ,EAAc;EACZ;EACD;;EAED,SAAK5D,IAAL;EACD;;WAEDA,OAAA,gBAAO;EACL,QAAI,KAAKhO,QAAL,CAAc2R,QAAd,IAA0B,KAAK3R,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCqP,mBAAjC,CAA1B,IAAmF,KAAKuB,KAAL,CAAW9Q,SAAX,CAAqBE,QAArB,CAA8BkM,iBAA9B,CAAvF,EAAuI;EACrI;EACD;;EAED,QAAML,MAAM,GAAG6E,QAAQ,CAACS,oBAAT,CAA8B,KAAK9R,QAAnC,CAAf;EACA,QAAM+K,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAK/K;EADA,KAAtB;EAIA,QAAM+R,SAAS,GAAGvW,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyM,YAApC,EAAgD1B,aAAhD,CAAlB;;EAEA,QAAIgH,SAAS,CAACvT,gBAAd,EAAgC;EAC9B;EACD,KAdI;;;EAiBL,QAAI,CAAC,KAAKiT,SAAV,EAAqB;EACnB,UAAI,OAAOO,0BAAP,KAAkB,WAAtB,EAAmC;EACjC,cAAM,IAAI7F,SAAJ,CAAc,kEAAd,CAAN;EACD;;EAED,UAAI8F,gBAAgB,GAAG,KAAKjS,QAA5B;;EAEA,UAAI,KAAK+H,OAAL,CAAaoJ,SAAb,KAA2B,QAA/B,EAAyC;EACvCc,QAAAA,gBAAgB,GAAGzF,MAAnB;EACD,OAFD,MAEO,IAAIlW,SAAS,CAAC,KAAKyR,OAAL,CAAaoJ,SAAd,CAAb,EAAuC;EAC5Cc,QAAAA,gBAAgB,GAAG,KAAKlK,OAAL,CAAaoJ,SAAhC,CAD4C;;EAI5C,YAAI,OAAO,KAAKpJ,OAAL,CAAaoJ,SAAb,CAAuBnC,MAA9B,KAAyC,WAA7C,EAA0D;EACxDiD,UAAAA,gBAAgB,GAAG,KAAKlK,OAAL,CAAaoJ,SAAb,CAAuB,CAAvB,CAAnB;EACD;EACF,OAhBkB;EAmBnB;EACA;;;EACA,UAAI,KAAKpJ,OAAL,CAAamJ,QAAb,KAA0B,cAA9B,EAA8C;EAC5C1E,QAAAA,MAAM,CAAC/L,SAAP,CAAiB2J,GAAjB,CAAqBkG,0BAArB;EACD;;EAED,WAAKgB,OAAL,GAAe,IAAIU,0BAAJ,CAAWC,gBAAX,EAA6B,KAAKV,KAAlC,EAAyC,KAAKW,gBAAL,EAAzC,CAAf;EACD,KA3CI;EA8CL;EACA;EACA;;;EACA,QAAI,kBAAkBnd,QAAQ,CAACyD,eAA3B,IACF,CAACgU,MAAM,CAAChM,OAAP,CAAeiQ,mBAAf,CADH,EACwC;EAAA;;EACtC,kBAAGlN,MAAH,aAAaxO,QAAQ,CAACmE,IAAT,CAAcyK,QAA3B,EACGpM,OADH,CACW,UAAAiW,IAAI;EAAA,eAAIhS,YAAY,CAACkC,EAAb,CAAgB8P,IAAhB,EAAsB,WAAtB,EAAmC,IAAnC,EAAyC3U,IAAI,EAA7C,CAAJ;EAAA,OADf;EAED;;EAED,SAAKmH,QAAL,CAAcmS,KAAd;;EACA,SAAKnS,QAAL,CAAc0B,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;EAEA,SAAK6P,KAAL,CAAW9Q,SAAX,CAAqBgB,MAArB,CAA4BoL,iBAA5B;;EACA,SAAK7M,QAAL,CAAcS,SAAd,CAAwBgB,MAAxB,CAA+BoL,iBAA/B;;EACArR,IAAAA,YAAY,CAAC0C,OAAb,CAAqBsO,MAArB,EAA6BE,aAA7B,EAA0C3B,aAA1C;EACD;;WAEDgD,OAAA,gBAAO;EACL,QAAI,KAAK/N,QAAL,CAAc2R,QAAd,IAA0B,KAAK3R,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCqP,mBAAjC,CAA1B,IAAmF,CAAC,KAAKuB,KAAL,CAAW9Q,SAAX,CAAqBE,QAArB,CAA8BkM,iBAA9B,CAAxF,EAAwI;EACtI;EACD;;EAED,QAAML,MAAM,GAAG6E,QAAQ,CAACS,oBAAT,CAA8B,KAAK9R,QAAnC,CAAf;EACA,QAAM+K,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAK/K;EADA,KAAtB;EAIA,QAAMoS,SAAS,GAAG5W,YAAY,CAAC0C,OAAb,CAAqBsO,MAArB,EAA6BG,YAA7B,EAAyC5B,aAAzC,CAAlB;;EAEA,QAAIqH,SAAS,CAAC5T,gBAAd,EAAgC;EAC9B;EACD;;EAED,QAAI,KAAK8S,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAae,OAAb;EACD;;EAED,SAAKd,KAAL,CAAW9Q,SAAX,CAAqBgB,MAArB,CAA4BoL,iBAA5B;;EACA,SAAK7M,QAAL,CAAcS,SAAd,CAAwBgB,MAAxB,CAA+BoL,iBAA/B;;EACArR,IAAAA,YAAY,CAAC0C,OAAb,CAAqBsO,MAArB,EAA6BI,cAA7B,EAA2C7B,aAA3C;EACD;;WAEDxK,UAAA,mBAAU;EACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EACA7D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgCV,WAAhC;EACA,SAAKU,QAAL,GAAgB,IAAhB;EACA,SAAKuR,KAAL,GAAa,IAAb;;EACA,QAAI,KAAKD,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAae,OAAb;;EACA,WAAKf,OAAL,GAAe,IAAf;EACD;EACF;;WAEDgB,SAAA,kBAAS;EACP,SAAKb,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EACA,QAAI,KAAKJ,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAaiB,cAAb;EACD;EACF;;;WAIDhK,qBAAA,8BAAqB;EAAA;;EACnB/M,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B6P,WAA/B,EAA4C,UAAAxU,KAAK,EAAI;EACnDA,MAAAA,KAAK,CAAC6D,cAAN;EACA7D,MAAAA,KAAK,CAACmX,eAAN;;EACA,MAAA,KAAI,CAAC/Q,MAAL;EACD,KAJD;EAKD;;WAEDuG,aAAA,oBAAW7Q,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACD,KAAKsb,WAAL,CAAiB7N,OADhB,EAED3C,WAAW,CAACI,iBAAZ,CAA8B,KAAKrC,QAAnC,CAFC,EAGD7I,MAHC,CAAN;EAMAF,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAe,KAAKsb,WAAL,CAAiBtN,WAAhC,CAAf;EAEA,WAAOhO,MAAP;EACD;;WAEDqa,kBAAA,2BAAkB;EAChB,WAAOpO,cAAc,CAACkB,IAAf,CAAoB,KAAKtE,QAAzB,EAAmCwQ,aAAnC,EAAkD,CAAlD,CAAP;EACD;;WAEDkC,gBAAA,yBAAgB;EACd,QAAMC,cAAc,GAAG,KAAK3S,QAAL,CAAc9H,UAArC;EACA,QAAI0a,SAAS,GAAG/B,gBAAhB,CAFc;;EAKd,QAAI8B,cAAc,CAAClS,SAAf,CAAyBE,QAAzB,CAAkCsP,iBAAlC,CAAJ,EAA0D;EACxD2C,MAAAA,SAAS,GAAG,KAAKrB,KAAL,CAAW9Q,SAAX,CAAqBE,QAArB,CAA8ByP,oBAA9B,IACVQ,gBADU,GAEVD,aAFF;EAGD,KAJD,MAIO,IAAIgC,cAAc,CAAClS,SAAf,CAAyBE,QAAzB,CAAkCuP,oBAAlC,CAAJ,EAA6D;EAClE0C,MAAAA,SAAS,GAAG7B,eAAZ;EACD,KAFM,MAEA,IAAI4B,cAAc,CAAClS,SAAf,CAAyBE,QAAzB,CAAkCwP,mBAAlC,CAAJ,EAA4D;EACjEyC,MAAAA,SAAS,GAAG5B,cAAZ;EACD,KAFM,MAEA,IAAI,KAAKO,KAAL,CAAW9Q,SAAX,CAAqBE,QAArB,CAA8ByP,oBAA9B,CAAJ,EAAyD;EAC9DwC,MAAAA,SAAS,GAAG9B,mBAAZ;EACD;;EAED,WAAO8B,SAAP;EACD;;WAEDlB,gBAAA,yBAAgB;EACd,WAAOrU,OAAO,CAAC,KAAK2C,QAAL,CAAcQ,OAAd,OAA0B6P,iBAA1B,CAAD,CAAd;EACD;;WAEDwC,aAAA,sBAAa;EAAA;;EACX,QAAMpQ,MAAM,GAAG,EAAf;;EAEA,QAAI,OAAO,KAAKsF,OAAL,CAAatF,MAApB,KAA+B,UAAnC,EAA+C;EAC7CA,MAAAA,MAAM,CAACtH,EAAP,GAAY,UAAAvB,IAAI,EAAI;EAClBA,QAAAA,IAAI,CAACkZ,OAAL,gBACKlZ,IAAI,CAACkZ,OADV,EAEM,MAAI,CAAC/K,OAAL,CAAatF,MAAb,CAAoB7I,IAAI,CAACkZ,OAAzB,EAAkC,MAAI,CAAC9S,QAAvC,KAAoD,EAF1D;EAKA,eAAOpG,IAAP;EACD,OAPD;EAQD,KATD,MASO;EACL6I,MAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKsF,OAAL,CAAatF,MAA7B;EACD;;EAED,WAAOA,MAAP;EACD;;WAEDyP,mBAAA,4BAAmB;EACjB,QAAMd,YAAY,GAAG;EACnBwB,MAAAA,SAAS,EAAE,KAAKF,aAAL,EADQ;EAEnBK,MAAAA,SAAS,EAAE;EACTtQ,QAAAA,MAAM,EAAE,KAAKoQ,UAAL,EADC;EAET5B,QAAAA,IAAI,EAAE;EACJ+B,UAAAA,OAAO,EAAE,KAAKjL,OAAL,CAAakJ;EADlB,SAFG;EAKTgC,QAAAA,eAAe,EAAE;EACfC,UAAAA,iBAAiB,EAAE,KAAKnL,OAAL,CAAamJ;EADjB;EALR;EAFQ,KAArB,CADiB;;EAejB,QAAI,KAAKnJ,OAAL,CAAa1P,OAAb,KAAyB,QAA7B,EAAuC;EACrC+Y,MAAAA,YAAY,CAAC2B,SAAb,CAAuBI,UAAvB,GAAoC;EAClCH,QAAAA,OAAO,EAAE;EADyB,OAApC;EAGD;;EAED,wBACK5B,YADL,EAEK,KAAKrJ,OAAL,CAAaqJ,YAFlB;EAID;;;aAIMgC,oBAAP,2BAAyBle,OAAzB,EAAkCiC,MAAlC,EAA0C;EACxC,QAAIyC,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAX;;EACA,QAAM0I,OAAO,GAAG,OAAO5Q,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;EAEA,QAAI,CAACyC,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAIyX,QAAJ,CAAanc,OAAb,EAAsB6S,OAAtB,CAAP;EACD;;EAED,QAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,UAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,MAAAA,IAAI,CAACzC,MAAD,CAAJ;EACD;EACF;;aAEM2J,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3BsQ,MAAAA,QAAQ,CAAC+B,iBAAT,CAA2B,IAA3B,EAAiCjc,MAAjC;EACD,KAFM,CAAP;EAGD;;aAEM0a,aAAP,oBAAkBxW,KAAlB,EAAyB;EACvB,QAAIA,KAAK,KAAKA,KAAK,CAACsG,MAAN,KAAiBgO,kBAAjB,IACXtU,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAAC1B,GAAN,KAAc6V,OADlC,CAAT,EACsD;EACpD;EACD;;EAED,QAAM6D,OAAO,GAAGjQ,cAAc,CAACE,IAAf,CAAoB/B,sBAApB,CAAhB;;EAEA,SAAK,IAAIvF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAG+W,OAAO,CAACpX,MAA9B,EAAsCD,CAAC,GAAGM,GAA1C,EAA+CN,CAAC,EAAhD,EAAoD;EAClD,UAAMwQ,MAAM,GAAG6E,QAAQ,CAACS,oBAAT,CAA8BuB,OAAO,CAACrX,CAAD,CAArC,CAAf;EACA,UAAMsX,OAAO,GAAGrZ,IAAI,CAACG,OAAL,CAAaiZ,OAAO,CAACrX,CAAD,CAApB,EAAyBqD,UAAzB,CAAhB;EACA,UAAM0L,aAAa,GAAG;EACpBA,QAAAA,aAAa,EAAEsI,OAAO,CAACrX,CAAD;EADF,OAAtB;;EAIA,UAAIX,KAAK,IAAIA,KAAK,CAACK,IAAN,KAAe,OAA5B,EAAqC;EACnCqP,QAAAA,aAAa,CAACwI,UAAd,GAA2BlY,KAA3B;EACD;;EAED,UAAI,CAACiY,OAAL,EAAc;EACZ;EACD;;EAED,UAAME,YAAY,GAAGF,OAAO,CAAC/B,KAA7B;;EACA,UAAI,CAAC8B,OAAO,CAACrX,CAAD,CAAP,CAAWyE,SAAX,CAAqBE,QAArB,CAA8BkM,iBAA9B,CAAL,EAAqD;EACnD;EACD;;EAED,UAAIxR,KAAK,KAAMA,KAAK,CAACK,IAAN,KAAe,OAAf,IACX,kBAAkB7D,IAAlB,CAAuBwD,KAAK,CAACU,MAAN,CAAasO,OAApC,CADU,IAEThP,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAAC1B,GAAN,KAAc6V,OAFpC,CAAL,IAGAgE,YAAY,CAAC7S,QAAb,CAAsBtF,KAAK,CAACU,MAA5B,CAHJ,EAGyC;EACvC;EACD;;EAED,UAAMqW,SAAS,GAAG5W,YAAY,CAAC0C,OAAb,CAAqBsO,MAArB,EAA6BG,YAA7B,EAAyC5B,aAAzC,CAAlB;;EACA,UAAIqH,SAAS,CAAC5T,gBAAd,EAAgC;EAC9B;EACD,OA9BiD;EAiClD;;;EACA,UAAI,kBAAkBzJ,QAAQ,CAACyD,eAA/B,EAAgD;EAAA;;EAC9C,qBAAG+K,MAAH,cAAaxO,QAAQ,CAACmE,IAAT,CAAcyK,QAA3B,EACGpM,OADH,CACW,UAAAiW,IAAI;EAAA,iBAAIhS,YAAY,CAACC,GAAb,CAAiB+R,IAAjB,EAAuB,WAAvB,EAAoC,IAApC,EAA0C3U,IAAI,EAA9C,CAAJ;EAAA,SADf;EAED;;EAEDwa,MAAAA,OAAO,CAACrX,CAAD,CAAP,CAAW0F,YAAX,CAAwB,eAAxB,EAAyC,OAAzC;;EAEA,UAAI4R,OAAO,CAAChC,OAAZ,EAAqB;EACnBgC,QAAAA,OAAO,CAAChC,OAAR,CAAgBe,OAAhB;EACD;;EAEDmB,MAAAA,YAAY,CAAC/S,SAAb,CAAuBC,MAAvB,CAA8BmM,iBAA9B;EACAwG,MAAAA,OAAO,CAACrX,CAAD,CAAP,CAAWyE,SAAX,CAAqBC,MAArB,CAA4BmM,iBAA5B;EACArR,MAAAA,YAAY,CAAC0C,OAAb,CAAqBsO,MAArB,EAA6BI,cAA7B,EAA2C7B,aAA3C;EACD;EACF;;aAEM+G,uBAAP,8BAA4B5c,OAA5B,EAAqC;EACnC,WAAOO,sBAAsB,CAACP,OAAD,CAAtB,IAAmCA,OAAO,CAACgD,UAAlD;EACD;;aAEMub,wBAAP,+BAA6BpY,KAA7B,EAAoC;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EACA,QAAI,kBAAkBxD,IAAlB,CAAuBwD,KAAK,CAACU,MAAN,CAAasO,OAApC,IACFhP,KAAK,CAAC1B,GAAN,KAAc4V,SAAd,IAA4BlU,KAAK,CAAC1B,GAAN,KAAc2V,UAAd,KAC1BjU,KAAK,CAAC1B,GAAN,KAAc+V,cAAd,IAAgCrU,KAAK,CAAC1B,GAAN,KAAc8V,YAA/C,IACCpU,KAAK,CAACU,MAAN,CAAayE,OAAb,CAAqBgQ,aAArB,CAF0B,CAD1B,GAIF,CAACZ,cAAc,CAAC/X,IAAf,CAAoBwD,KAAK,CAAC1B,GAA1B,CAJH,EAImC;EACjC;EACD;;EAED0B,IAAAA,KAAK,CAAC6D,cAAN;EACA7D,IAAAA,KAAK,CAACmX,eAAN;;EAEA,QAAI,KAAKb,QAAL,IAAiB,KAAKlR,SAAL,CAAeE,QAAf,CAAwBqP,mBAAxB,CAArB,EAAmE;EACjE;EACD;;EAED,QAAMxD,MAAM,GAAG6E,QAAQ,CAACS,oBAAT,CAA8B,IAA9B,CAAf;EACA,QAAMF,QAAQ,GAAG,KAAKnR,SAAL,CAAeE,QAAf,CAAwBkM,iBAAxB,CAAjB;;EAEA,QAAIxR,KAAK,CAAC1B,GAAN,KAAc2V,UAAlB,EAA8B;EAC5B,UAAM3N,MAAM,GAAG,KAAK0B,OAAL,CAAa9B,sBAAb,IAAqC,IAArC,GAA4C6B,cAAc,CAACe,IAAf,CAAoB,IAApB,EAA0B5C,sBAA1B,EAAgD,CAAhD,CAA3D;EACAI,MAAAA,MAAM,CAACwQ,KAAP;EACAd,MAAAA,QAAQ,CAACQ,UAAT;EACA;EACD;;EAED,QAAI,CAACD,QAAD,IAAavW,KAAK,CAAC1B,GAAN,KAAc4V,SAA/B,EAA0C;EACxC8B,MAAAA,QAAQ,CAACQ,UAAT;EACA;EACD;;EAED,QAAM6B,KAAK,GAAGtQ,cAAc,CAACE,IAAf,CAAoBoN,sBAApB,EAA4ClE,MAA5C,EAAoD5I,MAApD,CAA2D5L,SAA3D,CAAd;;EAEA,QAAI,CAAC0b,KAAK,CAACzX,MAAX,EAAmB;EACjB;EACD;;EAED,QAAIiN,KAAK,GAAGwK,KAAK,CAAC1W,OAAN,CAAc3B,KAAK,CAACU,MAApB,CAAZ;;EAEA,QAAIV,KAAK,CAAC1B,GAAN,KAAc8V,YAAd,IAA8BvG,KAAK,GAAG,CAA1C,EAA6C;EAAE;EAC7CA,MAAAA,KAAK;EACN;;EAED,QAAI7N,KAAK,CAAC1B,GAAN,KAAc+V,cAAd,IAAgCxG,KAAK,GAAGwK,KAAK,CAACzX,MAAN,GAAe,CAA3D,EAA8D;EAAE;EAC9DiN,MAAAA,KAAK;EACN,KApDiC;;;EAuDlCA,IAAAA,KAAK,GAAGA,KAAK,KAAK,CAAC,CAAX,GAAe,CAAf,GAAmBA,KAA3B;EAEAwK,IAAAA,KAAK,CAACxK,KAAD,CAAL,CAAaiJ,KAAb;EACD;;aAEMjR,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BA9XoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOwF,SAAP;EACD;;;0BAEwB;EACvB,aAAOO,aAAP;EACD;;;;;EAuXH;EACA;EACA;EACA;EACA;;;EAEA3J,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B+a,sBAA1B,EAAkDvO,sBAAlD,EAAwE8P,QAAQ,CAACoC,qBAAjF;EACAjY,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B+a,sBAA1B,EAAkDU,aAAlD,EAAiEa,QAAQ,CAACoC,qBAA1E;EACAjY,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD0R,QAAQ,CAACQ,UAAzD;EACArW,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0Bgb,oBAA1B,EAAgDsB,QAAQ,CAACQ,UAAzD;EACArW,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUlG,KAAV,EAAiB;EACrFA,EAAAA,KAAK,CAAC6D,cAAN;EACA7D,EAAAA,KAAK,CAACmX,eAAN;EACAnB,EAAAA,QAAQ,CAAC+B,iBAAT,CAA2B,IAA3B,EAAiC,QAAjC;EACD,CAJD;EAKA5X,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4Q,mBAAhD,EAAqE,UAAApG,CAAC;EAAA,SAAIA,CAAC,CAACqI,eAAF,EAAJ;EAAA,CAAtE;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEApZ,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAakS,QAAQ,CAACvQ,eAAtB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBiQ,QAAzB;;EACAjT,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOkQ,QAAQ,CAACvQ,eAAhB;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;EClfA;EACA;EACA;EACA;EACA;;EAEA,IAAM3B,MAAI,GAAG,OAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,UAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EACA,IAAM+P,YAAU,GAAG,QAAnB;EAEA,IAAM1K,SAAO,GAAG;EACd+O,EAAAA,QAAQ,EAAE,IADI;EAEd7O,EAAAA,QAAQ,EAAE,IAFI;EAGdqN,EAAAA,KAAK,EAAE,IAHO;EAIdnE,EAAAA,IAAI,EAAE;EAJQ,CAAhB;EAOA,IAAM7I,aAAW,GAAG;EAClBwO,EAAAA,QAAQ,EAAE,kBADQ;EAElB7O,EAAAA,QAAQ,EAAE,SAFQ;EAGlBqN,EAAAA,KAAK,EAAE,SAHW;EAIlBnE,EAAAA,IAAI,EAAE;EAJY,CAApB;EAOA,IAAMrB,YAAU,YAAUrN,WAA1B;EACA,IAAMsU,oBAAoB,qBAAmBtU,WAA7C;EACA,IAAMsN,cAAY,cAAYtN,WAA9B;EACA,IAAMmN,YAAU,YAAUnN,WAA1B;EACA,IAAMoN,aAAW,aAAWpN,WAA5B;EACA,IAAMuU,aAAa,eAAavU,WAAhC;EACA,IAAMwU,YAAY,cAAYxU,WAA9B;EACA,IAAMyU,mBAAmB,qBAAmBzU,WAA5C;EACA,IAAM0U,qBAAqB,uBAAqB1U,WAAhD;EACA,IAAM2U,qBAAqB,uBAAqB3U,WAAhD;EACA,IAAM4U,uBAAuB,yBAAuB5U,WAApD;EACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EAEA,IAAM4U,6BAA6B,GAAG,yBAAtC;EACA,IAAMC,mBAAmB,GAAG,gBAA5B;EACA,IAAMC,eAAe,GAAG,YAAxB;EACA,IAAMC,eAAe,GAAG,MAAxB;EACA,IAAMzH,iBAAe,GAAG,MAAxB;EACA,IAAM0H,iBAAiB,GAAG,cAA1B;EAEA,IAAMC,eAAe,GAAG,eAAxB;EACA,IAAMC,mBAAmB,GAAG,aAA5B;EACA,IAAMlT,sBAAoB,GAAG,uBAA7B;EACA,IAAMmT,qBAAqB,GAAG,wBAA9B;EACA,IAAMC,sBAAsB,GAAG,mDAA/B;EACA,IAAMC,uBAAuB,GAAG,aAAhC;EAEA;EACA;EACA;EACA;EACA;;MAEMC;EACJ,iBAAY3f,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,SAAK4Q,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;EACA,SAAK6I,QAAL,GAAgB9K,OAAhB;EACA,SAAK4f,OAAL,GAAe1R,cAAc,CAACM,OAAf,CAAuB8Q,eAAvB,EAAwCtf,OAAxC,CAAf;EACA,SAAK6f,SAAL,GAAiB,IAAjB;EACA,SAAKC,QAAL,GAAgB,KAAhB;EACA,SAAKC,kBAAL,GAA0B,KAA1B;EACA,SAAKC,oBAAL,GAA4B,KAA5B;EACA,SAAK7H,gBAAL,GAAwB,KAAxB;EACA,SAAK8H,eAAL,GAAuB,CAAvB;EACAlb,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;EACD;;;;;EAYD;WAEAoC,SAAA,gBAAOsJ,aAAP,EAAsB;EACpB,WAAO,KAAKiK,QAAL,GAAgB,KAAKjH,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAUjD,aAAV,CAArC;EACD;;WAEDiD,OAAA,cAAKjD,aAAL,EAAoB;EAAA;;EAClB,QAAI,KAAKiK,QAAL,IAAiB,KAAK3H,gBAA1B,EAA4C;EAC1C;EACD;;EAED,QAAI,KAAKrN,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC2T,eAAjC,CAAJ,EAAuD;EACrD,WAAKjH,gBAAL,GAAwB,IAAxB;EACD;;EAED,QAAM0E,SAAS,GAAGvW,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyM,YAApC,EAAgD;EAChE1B,MAAAA,aAAa,EAAbA;EADgE,KAAhD,CAAlB;;EAIA,QAAI,KAAKiK,QAAL,IAAiBjD,SAAS,CAACvT,gBAA/B,EAAiD;EAC/C;EACD;;EAED,SAAKwW,QAAL,GAAgB,IAAhB;;EAEA,SAAKI,eAAL;;EACA,SAAKC,aAAL;;EAEA,SAAKC,aAAL;;EAEA,SAAKC,eAAL;;EACA,SAAKC,eAAL;;EAEAha,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EACE+T,mBADF,EAEEW,qBAFF,EAGE,UAAArZ,KAAK;EAAA,aAAI,KAAI,CAAC0S,IAAL,CAAU1S,KAAV,CAAJ;EAAA,KAHP;EAMAG,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKoX,OAArB,EAA8BZ,uBAA9B,EAAuD,YAAM;EAC3D1Y,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAI,CAACqC,QAAtB,EAAgCiU,qBAAhC,EAAuD,UAAA5Y,KAAK,EAAI;EAC9D,YAAIA,KAAK,CAACU,MAAN,KAAiB,KAAI,CAACiE,QAA1B,EAAoC;EAClC,UAAA,KAAI,CAACkV,oBAAL,GAA4B,IAA5B;EACD;EACF,OAJD;EAKD,KAND;;EAQA,SAAKO,aAAL,CAAmB;EAAA,aAAM,KAAI,CAACC,YAAL,CAAkB3K,aAAlB,CAAN;EAAA,KAAnB;EACD;;WAEDgD,OAAA,cAAK1S,KAAL,EAAY;EAAA;;EACV,QAAIA,KAAJ,EAAW;EACTA,MAAAA,KAAK,CAAC6D,cAAN;EACD;;EAED,QAAI,CAAC,KAAK8V,QAAN,IAAkB,KAAK3H,gBAA3B,EAA6C;EAC3C;EACD;;EAED,QAAM+E,SAAS,GAAG5W,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoC2M,YAApC,CAAlB;;EAEA,QAAIyF,SAAS,CAAC5T,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKwW,QAAL,GAAgB,KAAhB;;EACA,QAAMW,UAAU,GAAG,KAAK3V,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC2T,eAAjC,CAAnB;;EAEA,QAAIqB,UAAJ,EAAgB;EACd,WAAKtI,gBAAL,GAAwB,IAAxB;EACD;;EAED,SAAKkI,eAAL;;EACA,SAAKC,eAAL;;EAEAha,IAAAA,YAAY,CAACC,GAAb,CAAiB1G,QAAjB,EAA2B8e,aAA3B;;EAEA,SAAK7T,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BmM,iBAA/B;;EAEArR,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgC+T,mBAAhC;EACAvY,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKqZ,OAAtB,EAA+BZ,uBAA/B;;EAEA,QAAIyB,UAAJ,EAAgB;EACd,UAAM9f,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKsK,QAAN,CAA3D;EAEAxE,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgD,UAAAmH,KAAK;EAAA,eAAI,MAAI,CAACua,UAAL,CAAgBva,KAAhB,CAAJ;EAAA,OAArD;EACA7E,MAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBnK,kBAAhB,CAApB;EACD,KALD,MAKO;EACL,WAAK+f,UAAL;EACD;EACF;;WAEDrV,UAAA,mBAAU;EACR,KAAC5K,MAAD,EAAS,KAAKqK,QAAd,EAAwB,KAAK8U,OAA7B,EACGvd,OADH,CACW,UAAAse,WAAW;EAAA,aAAIra,YAAY,CAACC,GAAb,CAAiBoa,WAAjB,EAA8BvW,WAA9B,CAAJ;EAAA,KADtB;EAGA;EACJ;EACA;EACA;EACA;;EACI9D,IAAAA,YAAY,CAACC,GAAb,CAAiB1G,QAAjB,EAA2B8e,aAA3B;EAEA5Z,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EAEA,SAAK0I,OAAL,GAAe,IAAf;EACA,SAAK/H,QAAL,GAAgB,IAAhB;EACA,SAAK8U,OAAL,GAAe,IAAf;EACA,SAAKC,SAAL,GAAiB,IAAjB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,kBAAL,GAA0B,IAA1B;EACA,SAAKC,oBAAL,GAA4B,IAA5B;EACA,SAAK7H,gBAAL,GAAwB,IAAxB;EACA,SAAK8H,eAAL,GAAuB,IAAvB;EACD;;WAEDW,eAAA,wBAAe;EACb,SAAKR,aAAL;EACD;;;WAIDtN,aAAA,oBAAW7Q,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACDyN,SADC,EAEDzN,MAFC,CAAN;EAIAF,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAegO,aAAf,CAAf;EACA,WAAOhO,MAAP;EACD;;WAEDue,eAAA,sBAAa3K,aAAb,EAA4B;EAAA;;EAC1B,QAAM4K,UAAU,GAAG,KAAK3V,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC2T,eAAjC,CAAnB;;EACA,QAAMyB,SAAS,GAAG3S,cAAc,CAACM,OAAf,CAAuB+Q,mBAAvB,EAA4C,KAAKK,OAAjD,CAAlB;;EAEA,QAAI,CAAC,KAAK9U,QAAL,CAAc9H,UAAf,IACA,KAAK8H,QAAL,CAAc9H,UAAd,CAAyB3B,QAAzB,KAAsCyN,IAAI,CAACC,YAD/C,EAC6D;EAC3D;EACAlP,MAAAA,QAAQ,CAACmE,IAAT,CAAc8c,WAAd,CAA0B,KAAKhW,QAA/B;EACD;;EAED,SAAKA,QAAL,CAAc/H,KAAd,CAAoBI,OAApB,GAA8B,OAA9B;;EACA,SAAK2H,QAAL,CAAcoC,eAAd,CAA8B,aAA9B;;EACA,SAAKpC,QAAL,CAAc0B,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;EACA,SAAK1B,QAAL,CAAc0B,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;EACA,SAAK1B,QAAL,CAAc6C,SAAd,GAA0B,CAA1B;;EAEA,QAAIkT,SAAJ,EAAe;EACbA,MAAAA,SAAS,CAAClT,SAAV,GAAsB,CAAtB;EACD;;EAED,QAAI8S,UAAJ,EAAgB;EACd7c,MAAAA,MAAM,CAAC,KAAKkH,QAAN,CAAN;EACD;;EAED,SAAKA,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4ByC,iBAA5B;;EAEA,QAAI,KAAK9E,OAAL,CAAaoK,KAAjB,EAAwB;EACtB,WAAK8D,aAAL;EACD;;EAED,QAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,GAAM;EAC/B,UAAI,MAAI,CAACnO,OAAL,CAAaoK,KAAjB,EAAwB;EACtB,QAAA,MAAI,CAACnS,QAAL,CAAcmS,KAAd;EACD;;EAED,MAAA,MAAI,CAAC9E,gBAAL,GAAwB,KAAxB;EACA7R,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAC8B,QAA1B,EAAoC0M,aAApC,EAAiD;EAC/C3B,QAAAA,aAAa,EAAbA;EAD+C,OAAjD;EAGD,KATD;;EAWA,QAAI4K,UAAJ,EAAgB;EACd,UAAM9f,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKof,OAAN,CAA3D;EAEAtZ,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKmX,OAAtB,EAA+B5gB,cAA/B,EAA+CgiB,kBAA/C;EACA1f,MAAAA,oBAAoB,CAAC,KAAKse,OAAN,EAAejf,kBAAf,CAApB;EACD,KALD,MAKO;EACLqgB,MAAAA,kBAAkB;EACnB;EACF;;WAEDD,gBAAA,yBAAgB;EAAA;;EACdza,IAAAA,YAAY,CAACC,GAAb,CAAiB1G,QAAjB,EAA2B8e,aAA3B,EADc;;EAEdrY,IAAAA,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B8e,aAA1B,EAAyC,UAAAxY,KAAK,EAAI;EAChD,UAAItG,QAAQ,KAAKsG,KAAK,CAACU,MAAnB,IACA,MAAI,CAACiE,QAAL,KAAkB3E,KAAK,CAACU,MADxB,IAEA,CAAC,MAAI,CAACiE,QAAL,CAAcW,QAAd,CAAuBtF,KAAK,CAACU,MAA7B,CAFL,EAE2C;EACzC,QAAA,MAAI,CAACiE,QAAL,CAAcmS,KAAd;EACD;EACF,KAND;EAOD;;WAEDoD,kBAAA,2BAAkB;EAAA;;EAChB,QAAI,KAAKP,QAAT,EAAmB;EACjBxZ,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+BgU,qBAA/B,EAAsD,UAAA3Y,KAAK,EAAI;EAC7D,YAAI,MAAI,CAAC0M,OAAL,CAAajD,QAAb,IAAyBzJ,KAAK,CAAC1B,GAAN,KAAc2V,YAA3C,EAAuD;EACrDjU,UAAAA,KAAK,CAAC6D,cAAN;;EACA,UAAA,MAAI,CAAC6O,IAAL;EACD,SAHD,MAGO,IAAI,CAAC,MAAI,CAAChG,OAAL,CAAajD,QAAd,IAA0BzJ,KAAK,CAAC1B,GAAN,KAAc2V,YAA5C,EAAwD;EAC7D,UAAA,MAAI,CAAC6G,0BAAL;EACD;EACF,OAPD;EAQD,KATD,MASO;EACL3a,MAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgCgU,qBAAhC;EACD;EACF;;WAEDwB,kBAAA,2BAAkB;EAAA;;EAChB,QAAI,KAAKR,QAAT,EAAmB;EACjBxZ,MAAAA,YAAY,CAACkC,EAAb,CAAgB/H,MAAhB,EAAwBme,YAAxB,EAAsC;EAAA,eAAM,MAAI,CAACwB,aAAL,EAAN;EAAA,OAAtC;EACD,KAFD,MAEO;EACL9Z,MAAAA,YAAY,CAACC,GAAb,CAAiB9F,MAAjB,EAAyBme,YAAzB;EACD;EACF;;WAED8B,aAAA,sBAAa;EAAA;;EACX,SAAK5V,QAAL,CAAc/H,KAAd,CAAoBI,OAApB,GAA8B,MAA9B;;EACA,SAAK2H,QAAL,CAAc0B,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;EACA,SAAK1B,QAAL,CAAcoC,eAAd,CAA8B,YAA9B;;EACA,SAAKpC,QAAL,CAAcoC,eAAd,CAA8B,MAA9B;;EACA,SAAKiL,gBAAL,GAAwB,KAAxB;;EACA,SAAKoI,aAAL,CAAmB,YAAM;EACvB1gB,MAAAA,QAAQ,CAACmE,IAAT,CAAcuH,SAAd,CAAwBC,MAAxB,CAA+B2T,eAA/B;;EACA,MAAA,MAAI,CAAC+B,iBAAL;;EACA,MAAA,MAAI,CAACC,eAAL;;EACA7a,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAC8B,QAA1B,EAAoC4M,cAApC;EACD,KALD;EAMD;;WAED0J,kBAAA,2BAAkB;EAChB,SAAKvB,SAAL,CAAe7c,UAAf,CAA0B2I,WAA1B,CAAsC,KAAKkU,SAA3C;;EACA,SAAKA,SAAL,GAAiB,IAAjB;EACD;;WAEDU,gBAAA,uBAAcpc,QAAd,EAAwB;EAAA;;EACtB,QAAMkd,OAAO,GAAG,KAAKvW,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC2T,eAAjC,IACdA,eADc,GAEd,EAFF;;EAIA,QAAI,KAAKU,QAAL,IAAiB,KAAKjN,OAAL,CAAa4L,QAAlC,EAA4C;EAC1C,WAAKoB,SAAL,GAAiBhgB,QAAQ,CAACyhB,aAAT,CAAuB,KAAvB,CAAjB;EACA,WAAKzB,SAAL,CAAe0B,SAAf,GAA2BrC,mBAA3B;;EAEA,UAAImC,OAAJ,EAAa;EACX,aAAKxB,SAAL,CAAetU,SAAf,CAAyB2J,GAAzB,CAA6BmM,OAA7B;EACD;;EAEDxhB,MAAAA,QAAQ,CAACmE,IAAT,CAAc8c,WAAd,CAA0B,KAAKjB,SAA/B;EAEAvZ,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B+T,mBAA/B,EAAoD,UAAA1Y,KAAK,EAAI;EAC3D,YAAI,MAAI,CAAC6Z,oBAAT,EAA+B;EAC7B,UAAA,MAAI,CAACA,oBAAL,GAA4B,KAA5B;EACA;EACD;;EAED,YAAI7Z,KAAK,CAACU,MAAN,KAAiBV,KAAK,CAACqb,aAA3B,EAA0C;EACxC;EACD;;EAED,QAAA,MAAI,CAACP,0BAAL;EACD,OAXD;;EAaA,UAAII,OAAJ,EAAa;EACXzd,QAAAA,MAAM,CAAC,KAAKic,SAAN,CAAN;EACD;;EAED,WAAKA,SAAL,CAAetU,SAAf,CAAyB2J,GAAzB,CAA6ByC,iBAA7B;;EAEA,UAAI,CAAC0J,OAAL,EAAc;EACZld,QAAAA,QAAQ;EACR;EACD;;EAED,UAAMsd,0BAA0B,GAAGjhB,gCAAgC,CAAC,KAAKqf,SAAN,CAAnE;EAEAvZ,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKoX,SAAtB,EAAiC7gB,cAAjC,EAAiDmF,QAAjD;EACA7C,MAAAA,oBAAoB,CAAC,KAAKue,SAAN,EAAiB4B,0BAAjB,CAApB;EACD,KAtCD,MAsCO,IAAI,CAAC,KAAK3B,QAAN,IAAkB,KAAKD,SAA3B,EAAsC;EAC3C,WAAKA,SAAL,CAAetU,SAAf,CAAyBC,MAAzB,CAAgCmM,iBAAhC;;EAEA,UAAM+J,cAAc,GAAG,SAAjBA,cAAiB,GAAM;EAC3B,QAAA,MAAI,CAACN,eAAL;;EACAjd,QAAAA,QAAQ;EACT,OAHD;;EAKA,UAAI,KAAK2G,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC2T,eAAjC,CAAJ,EAAuD;EACrD,YAAMqC,2BAA0B,GAAGjhB,gCAAgC,CAAC,KAAKqf,SAAN,CAAnE;;EACAvZ,QAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKoX,SAAtB,EAAiC7gB,cAAjC,EAAiD0iB,cAAjD;EACApgB,QAAAA,oBAAoB,CAAC,KAAKue,SAAN,EAAiB4B,2BAAjB,CAApB;EACD,OAJD,MAIO;EACLC,QAAAA,cAAc;EACf;EACF,KAfM,MAeA;EACLvd,MAAAA,QAAQ;EACT;EACF;;WAED8c,6BAAA,sCAA6B;EAAA;;EAC3B,QAAI,KAAKpO,OAAL,CAAa4L,QAAb,KAA0B,QAA9B,EAAwC;EACtC,UAAMvB,SAAS,GAAG5W,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoC4T,oBAApC,CAAlB;;EACA,UAAIxB,SAAS,CAAC5T,gBAAd,EAAgC;EAC9B;EACD;;EAED,UAAMqY,kBAAkB,GAAG,KAAK7W,QAAL,CAAc8W,YAAd,GAA6B/hB,QAAQ,CAACyD,eAAT,CAAyBue,YAAjF;;EAEA,UAAI,CAACF,kBAAL,EAAyB;EACvB,aAAK7W,QAAL,CAAc/H,KAAd,CAAoB+e,SAApB,GAAgC,QAAhC;EACD;;EAED,WAAKhX,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4BmK,iBAA5B;;EACA,UAAM0C,uBAAuB,GAAGvhB,gCAAgC,CAAC,KAAKof,OAAN,CAAhE;EACAtZ,MAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgC9L,cAAhC;EACAsH,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgD,YAAM;EACpD,QAAA,MAAI,CAAC8L,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B6T,iBAA/B;;EACA,YAAI,CAACsC,kBAAL,EAAyB;EACvBrb,UAAAA,YAAY,CAACmC,GAAb,CAAiB,MAAI,CAACqC,QAAtB,EAAgC9L,cAAhC,EAAgD,YAAM;EACpD,YAAA,MAAI,CAAC8L,QAAL,CAAc/H,KAAd,CAAoB+e,SAApB,GAAgC,EAAhC;EACD,WAFD;EAGAxgB,UAAAA,oBAAoB,CAAC,MAAI,CAACwJ,QAAN,EAAgBiX,uBAAhB,CAApB;EACD;EACF,OARD;EASAzgB,MAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBiX,uBAAhB,CAApB;;EACA,WAAKjX,QAAL,CAAcmS,KAAd;EACD,KA1BD,MA0BO;EACL,WAAKpE,IAAL;EACD;EACF;EAGD;EACA;;;WAEAuH,gBAAA,yBAAgB;EACd,QAAMuB,kBAAkB,GACtB,KAAK7W,QAAL,CAAc8W,YAAd,GAA6B/hB,QAAQ,CAACyD,eAAT,CAAyBue,YADxD;;EAGA,QAAI,CAAC,KAAK9B,kBAAN,IAA4B4B,kBAAhC,EAAoD;EAClD,WAAK7W,QAAL,CAAc/H,KAAd,CAAoBif,WAApB,GAAqC,KAAK/B,eAA1C;EACD;;EAED,QAAI,KAAKF,kBAAL,IAA2B,CAAC4B,kBAAhC,EAAoD;EAClD,WAAK7W,QAAL,CAAc/H,KAAd,CAAoBkf,YAApB,GAAsC,KAAKhC,eAA3C;EACD;EACF;;WAEDiB,oBAAA,6BAAoB;EAClB,SAAKpW,QAAL,CAAc/H,KAAd,CAAoBif,WAApB,GAAkC,EAAlC;EACA,SAAKlX,QAAL,CAAc/H,KAAd,CAAoBkf,YAApB,GAAmC,EAAnC;EACD;;WAED/B,kBAAA,2BAAkB;EAChB,QAAM1S,IAAI,GAAG3N,QAAQ,CAACmE,IAAT,CAAcyJ,qBAAd,EAAb;EACA,SAAKsS,kBAAL,GAA0BrgB,IAAI,CAACwiB,KAAL,CAAW1U,IAAI,CAACI,IAAL,GAAYJ,IAAI,CAAC2U,KAA5B,IAAqC1hB,MAAM,CAAC2hB,UAAtE;EACA,SAAKnC,eAAL,GAAuB,KAAKoC,kBAAL,EAAvB;EACD;;WAEDlC,gBAAA,yBAAgB;EAAA;;EACd,QAAI,KAAKJ,kBAAT,EAA6B;EAC3B;EACA;EAEA;EACA7R,MAAAA,cAAc,CAACE,IAAf,CAAoBqR,sBAApB,EACGpd,OADH,CACW,UAAArC,OAAO,EAAI;EAClB,YAAMsiB,aAAa,GAAGtiB,OAAO,CAAC+C,KAAR,CAAckf,YAApC;EACA,YAAMM,iBAAiB,GAAG9hB,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,EAAiC,eAAjC,CAA1B;EACA+M,QAAAA,WAAW,CAACC,gBAAZ,CAA6BhN,OAA7B,EAAsC,eAAtC,EAAuDsiB,aAAvD;EACAtiB,QAAAA,OAAO,CAAC+C,KAAR,CAAckf,YAAd,GAAgCnhB,UAAU,CAACyhB,iBAAD,CAAV,GAAgC,OAAI,CAACtC,eAArE;EACD,OANH,EAL2B;;EAc3B/R,MAAAA,cAAc,CAACE,IAAf,CAAoBsR,uBAApB,EACGrd,OADH,CACW,UAAArC,OAAO,EAAI;EAClB,YAAMwiB,YAAY,GAAGxiB,OAAO,CAAC+C,KAAR,CAAc0f,WAAnC;EACA,YAAMC,gBAAgB,GAAGjiB,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,EAAiC,cAAjC,CAAzB;EACA+M,QAAAA,WAAW,CAACC,gBAAZ,CAA6BhN,OAA7B,EAAsC,cAAtC,EAAsDwiB,YAAtD;EACAxiB,QAAAA,OAAO,CAAC+C,KAAR,CAAc0f,WAAd,GAA+B3hB,UAAU,CAAC4hB,gBAAD,CAAV,GAA+B,OAAI,CAACzC,eAAnE;EACD,OANH,EAd2B;;EAuB3B,UAAMqC,aAAa,GAAGziB,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBkf,YAA1C;EACA,UAAMM,iBAAiB,GAAG9hB,MAAM,CAACC,gBAAP,CAAwBb,QAAQ,CAACmE,IAAjC,EAAuC,eAAvC,CAA1B;EAEA+I,MAAAA,WAAW,CAACC,gBAAZ,CAA6BnN,QAAQ,CAACmE,IAAtC,EAA4C,eAA5C,EAA6Dse,aAA7D;EACAziB,MAAAA,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBkf,YAApB,GAAsCnhB,UAAU,CAACyhB,iBAAD,CAAV,GAAgC,KAAKtC,eAA3E;EACD;;EAEDpgB,IAAAA,QAAQ,CAACmE,IAAT,CAAcuH,SAAd,CAAwB2J,GAAxB,CAA4BiK,eAA5B;EACD;;WAEDgC,kBAAA,2BAAkB;EAChB;EACAjT,IAAAA,cAAc,CAACE,IAAf,CAAoBqR,sBAApB,EACGpd,OADH,CACW,UAAArC,OAAO,EAAI;EAClB,UAAM2iB,OAAO,GAAG5V,WAAW,CAACO,gBAAZ,CAA6BtN,OAA7B,EAAsC,eAAtC,CAAhB;;EACA,UAAI,OAAO2iB,OAAP,KAAmB,WAAvB,EAAoC;EAClC5V,QAAAA,WAAW,CAACE,mBAAZ,CAAgCjN,OAAhC,EAAyC,eAAzC;EACAA,QAAAA,OAAO,CAAC+C,KAAR,CAAckf,YAAd,GAA6BU,OAA7B;EACD;EACF,KAPH,EAFgB;;EAYhBzU,IAAAA,cAAc,CAACE,IAAf,MAAuBsR,uBAAvB,EACGrd,OADH,CACW,UAAArC,OAAO,EAAI;EAClB,UAAM4iB,MAAM,GAAG7V,WAAW,CAACO,gBAAZ,CAA6BtN,OAA7B,EAAsC,cAAtC,CAAf;;EACA,UAAI,OAAO4iB,MAAP,KAAkB,WAAtB,EAAmC;EACjC7V,QAAAA,WAAW,CAACE,mBAAZ,CAAgCjN,OAAhC,EAAyC,cAAzC;EACAA,QAAAA,OAAO,CAAC+C,KAAR,CAAc0f,WAAd,GAA4BG,MAA5B;EACD;EACF,KAPH,EAZgB;;EAsBhB,QAAMD,OAAO,GAAG5V,WAAW,CAACO,gBAAZ,CAA6BzN,QAAQ,CAACmE,IAAtC,EAA4C,eAA5C,CAAhB;;EACA,QAAI,OAAO2e,OAAP,KAAmB,WAAvB,EAAoC;EAClC9iB,MAAAA,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBkf,YAApB,GAAmC,EAAnC;EACD,KAFD,MAEO;EACLlV,MAAAA,WAAW,CAACE,mBAAZ,CAAgCpN,QAAQ,CAACmE,IAAzC,EAA+C,eAA/C;EACAnE,MAAAA,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBkf,YAApB,GAAmCU,OAAnC;EACD;EACF;;WAEDN,qBAAA,8BAAqB;EAAE;EACrB,QAAMQ,SAAS,GAAGhjB,QAAQ,CAACyhB,aAAT,CAAuB,KAAvB,CAAlB;EACAuB,IAAAA,SAAS,CAACtB,SAAV,GAAsBtC,6BAAtB;EACApf,IAAAA,QAAQ,CAACmE,IAAT,CAAc8c,WAAd,CAA0B+B,SAA1B;EACA,QAAMC,cAAc,GAAGD,SAAS,CAACpV,qBAAV,GAAkCsV,KAAlC,GAA0CF,SAAS,CAACG,WAA3E;EACAnjB,IAAAA,QAAQ,CAACmE,IAAT,CAAc2H,WAAd,CAA0BkX,SAA1B;EACA,WAAOC,cAAP;EACD;;;UAIMlX,kBAAP,yBAAuB3J,MAAvB,EAA+B4T,aAA/B,EAA8C;EAC5C,WAAO,KAAKhK,IAAL,CAAU,YAAY;EAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;EACA,UAAM0I,OAAO,gBACRnD,SADQ,EAER3C,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAFQ,EAGP,OAAOlL,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHzC,CAAb;;EAMA,UAAI,CAACyC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIib,KAAJ,CAAU,IAAV,EAAgB9M,OAAhB,CAAP;EACD;;EAED,UAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ,CAAa4T,aAAb;EACD,OAND,MAMO,IAAIhD,OAAO,CAACiG,IAAZ,EAAkB;EACvBpU,QAAAA,IAAI,CAACoU,IAAL,CAAUjD,aAAV;EACD;EACF,KArBM,CAAP;EAsBD;;UAEM7J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BArdoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOwF,SAAP;EACD;;;;;EAkdH;EACA;EACA;EACA;EACA;;;EAEApJ,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUlG,KAAV,EAAiB;EAAA;;EACrF,MAAMU,MAAM,GAAGtG,sBAAsB,CAAC,IAAD,CAArC;;EAEA,MAAI,KAAK4U,OAAL,KAAiB,GAAjB,IAAwB,KAAKA,OAAL,KAAiB,MAA7C,EAAqD;EACnDhP,IAAAA,KAAK,CAAC6D,cAAN;EACD;;EAED1D,EAAAA,YAAY,CAACmC,GAAb,CAAiB5B,MAAjB,EAAyB0Q,YAAzB,EAAqC,UAAAsF,SAAS,EAAI;EAChD,QAAIA,SAAS,CAACvT,gBAAd,EAAgC;EAC9B;EACA;EACD;;EAEDhD,IAAAA,YAAY,CAACmC,GAAb,CAAiB5B,MAAjB,EAAyB6Q,cAAzB,EAAuC,YAAM;EAC3C,UAAI5U,SAAS,CAAC,OAAD,CAAb,EAAqB;EACnB,QAAA,OAAI,CAACma,KAAL;EACD;EACF,KAJD;EAKD,GAXD;EAaA,MAAIvY,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa2B,MAAb,EAAqBsD,UAArB,CAAX;;EACA,MAAI,CAACzF,IAAL,EAAW;EACT,QAAMzC,MAAM,gBACP8K,WAAW,CAACI,iBAAZ,CAA8BtG,MAA9B,CADO,EAEPkG,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAFO,CAAZ;;EAKAzI,IAAAA,IAAI,GAAG,IAAIib,KAAJ,CAAU9Y,MAAV,EAAkB5E,MAAlB,CAAP;EACD;;EAEDyC,EAAAA,IAAI,CAACoU,IAAL,CAAU,IAAV;EACD,CA/BD;EAiCA;EACA;EACA;EACA;EACA;EACA;;EAEA5U,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAa0V,KAAK,CAAC/T,eAAnB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyByT,KAAzB;;EACAzW,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAO0T,KAAK,CAAC/T,eAAb;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;ECzmBA;EACA;EACA;EACA;EACA;EACA;EAEA,IAAMqX,QAAQ,GAAG,CACf,YADe,EAEf,MAFe,EAGf,MAHe,EAIf,UAJe,EAKf,UALe,EAMf,QANe,EAOf,KAPe,EAQf,YARe,CAAjB;EAWA,IAAMC,sBAAsB,GAAG,gBAA/B;EAEA;EACA;EACA;EACA;EACA;;EACA,IAAMC,gBAAgB,GAAG,6DAAzB;EAEA;EACA;EACA;EACA;EACA;;EACA,IAAMC,gBAAgB,GAAG,oIAAzB;;EAEA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACC,IAAD,EAAOC,oBAAP,EAAgC;EACvD,MAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAclkB,WAAd,EAAjB;;EAEA,MAAIgkB,oBAAoB,CAACzb,OAArB,CAA6B0b,QAA7B,MAA2C,CAAC,CAAhD,EAAmD;EACjD,QAAIP,QAAQ,CAACnb,OAAT,CAAiB0b,QAAjB,MAA+B,CAAC,CAApC,EAAuC;EACrC,aAAOrb,OAAO,CAACmb,IAAI,CAACI,SAAL,CAAepkB,KAAf,CAAqB6jB,gBAArB,KAA0CG,IAAI,CAACI,SAAL,CAAepkB,KAAf,CAAqB8jB,gBAArB,CAA3C,CAAd;EACD;;EAED,WAAO,IAAP;EACD;;EAED,MAAMO,MAAM,GAAGJ,oBAAoB,CAAC7U,MAArB,CAA4B,UAAAkV,SAAS;EAAA,WAAIA,SAAS,YAAYlhB,MAAzB;EAAA,GAArC,CAAf,CAXuD;;EAcvD,OAAK,IAAIoE,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGuc,MAAM,CAAC5c,MAA7B,EAAqCD,CAAC,GAAGM,GAAzC,EAA8CN,CAAC,EAA/C,EAAmD;EACjD,QAAI0c,QAAQ,CAAClkB,KAAT,CAAeqkB,MAAM,CAAC7c,CAAD,CAArB,CAAJ,EAA+B;EAC7B,aAAO,IAAP;EACD;EACF;;EAED,SAAO,KAAP;EACD,CArBD;;EAuBO,IAAM+c,gBAAgB,GAAG;EAC9B;EACA,OAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCX,sBAAvC,CAFyB;EAG9BY,EAAAA,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;EAI9BC,EAAAA,IAAI,EAAE,EAJwB;EAK9BC,EAAAA,CAAC,EAAE,EAL2B;EAM9BC,EAAAA,EAAE,EAAE,EAN0B;EAO9BC,EAAAA,GAAG,EAAE,EAPyB;EAQ9BC,EAAAA,IAAI,EAAE,EARwB;EAS9BC,EAAAA,GAAG,EAAE,EATyB;EAU9BC,EAAAA,EAAE,EAAE,EAV0B;EAW9BC,EAAAA,EAAE,EAAE,EAX0B;EAY9BC,EAAAA,EAAE,EAAE,EAZ0B;EAa9BC,EAAAA,EAAE,EAAE,EAb0B;EAc9BC,EAAAA,EAAE,EAAE,EAd0B;EAe9BC,EAAAA,EAAE,EAAE,EAf0B;EAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;EAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;EAkB9B9d,EAAAA,CAAC,EAAE,EAlB2B;EAmB9B+d,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;EAoB9BC,EAAAA,EAAE,EAAE,EApB0B;EAqB9BC,EAAAA,EAAE,EAAE,EArB0B;EAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;EAuB9BC,EAAAA,GAAG,EAAE,EAvByB;EAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;EAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;EA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;EA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;EA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;EA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;EA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;EA+B9BC,EAAAA,EAAE,EAAE;EA/B0B,CAAzB;EAkCA,SAASC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyD;EAAA;;EAC9D,MAAI,CAACF,UAAU,CAAC5e,MAAhB,EAAwB;EACtB,WAAO4e,UAAP;EACD;;EAED,MAAIE,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;EAClD,WAAOA,UAAU,CAACF,UAAD,CAAjB;EACD;;EAED,MAAMG,SAAS,GAAG,IAAIrlB,MAAM,CAACslB,SAAX,EAAlB;EACA,MAAMC,eAAe,GAAGF,SAAS,CAACG,eAAV,CAA0BN,UAA1B,EAAsC,WAAtC,CAAxB;EACA,MAAMO,aAAa,GAAG/jB,MAAM,CAACC,IAAP,CAAYwjB,SAAZ,CAAtB;;EACA,MAAMO,QAAQ,GAAG,YAAG9X,MAAH,aAAa2X,eAAe,CAAChiB,IAAhB,CAAqB4C,gBAArB,CAAsC,GAAtC,CAAb,CAAjB;;EAZ8D,6BAcrDE,CAdqD,EAc9CM,GAd8C;EAAA;;EAe5D,QAAMgf,EAAE,GAAGD,QAAQ,CAACrf,CAAD,CAAnB;EACA,QAAMuf,MAAM,GAAGD,EAAE,CAAC3C,QAAH,CAAYlkB,WAAZ,EAAf;;EAEA,QAAI2mB,aAAa,CAACpe,OAAd,CAAsBue,MAAtB,MAAkC,CAAC,CAAvC,EAA0C;EACxCD,MAAAA,EAAE,CAACpjB,UAAH,CAAc2I,WAAd,CAA0Bya,EAA1B;EAEA;EACD;;EAED,QAAME,aAAa,GAAG,aAAGjY,MAAH,cAAa+X,EAAE,CAAChZ,UAAhB,CAAtB;;EACA,QAAMmZ,iBAAiB,GAAG,GAAGlY,MAAH,CAAUuX,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACS,MAAD,CAAT,IAAqB,EAArD,CAA1B;EAEAC,IAAAA,aAAa,CAACjkB,OAAd,CAAsB,UAAAihB,IAAI,EAAI;EAC5B,UAAI,CAACD,gBAAgB,CAACC,IAAD,EAAOiD,iBAAP,CAArB,EAAgD;EAC9CH,QAAAA,EAAE,CAAClZ,eAAH,CAAmBoW,IAAI,CAACG,QAAxB;EACD;EACF,KAJD;EA3B4D;;EAc9D,OAAK,IAAI3c,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAG+e,QAAQ,CAACpf,MAA/B,EAAuCD,CAAC,GAAGM,GAA3C,EAAgDN,CAAC,EAAjD,EAAqD;EAAA,qBAA5CA,CAA4C;;EAAA,6BAOjD;EAWH;;EAED,SAAOkf,eAAe,CAAChiB,IAAhB,CAAqBwiB,SAA5B;EACD;;ECjGD;EACA;EACA;EACA;EACA;;EAEA,IAAMvc,MAAI,GAAG,SAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,YAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAMsc,YAAY,GAAG,YAArB;EACA,IAAMC,kBAAkB,GAAG,IAAIhkB,MAAJ,aAAqB+jB,YAArB,WAAyC,GAAzC,CAA3B;EACA,IAAME,qBAAqB,GAAG,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAA9B;EAEA,IAAM1W,aAAW,GAAG;EAClB2W,EAAAA,SAAS,EAAE,SADO;EAElBC,EAAAA,QAAQ,EAAE,QAFQ;EAGlBC,EAAAA,KAAK,EAAE,2BAHW;EAIlB9d,EAAAA,OAAO,EAAE,QAJS;EAKlB+d,EAAAA,KAAK,EAAE,iBALW;EAMlBC,EAAAA,IAAI,EAAE,SANY;EAOlB/mB,EAAAA,QAAQ,EAAE,kBAPQ;EAQlByd,EAAAA,SAAS,EAAE,mBARO;EASlBnQ,EAAAA,MAAM,EAAE,0BATU;EAUlB0L,EAAAA,SAAS,EAAE,0BAVO;EAWlBgO,EAAAA,iBAAiB,EAAE,gBAXD;EAYlBjL,EAAAA,QAAQ,EAAE,kBAZQ;EAalBkL,EAAAA,QAAQ,EAAE,SAbQ;EAclBrB,EAAAA,UAAU,EAAE,iBAdM;EAelBD,EAAAA,SAAS,EAAE,QAfO;EAgBlB1J,EAAAA,YAAY,EAAE;EAhBI,CAApB;EAmBA,IAAMiL,aAAa,GAAG;EACpBC,EAAAA,IAAI,EAAE,MADc;EAEpBC,EAAAA,GAAG,EAAE,KAFe;EAGpBC,EAAAA,KAAK,EAAE,OAHa;EAIpBC,EAAAA,MAAM,EAAE,QAJY;EAKpBC,EAAAA,IAAI,EAAE;EALc,CAAtB;EAQA,IAAM9X,SAAO,GAAG;EACdkX,EAAAA,SAAS,EAAE,IADG;EAEdC,EAAAA,QAAQ,EAAE,yCACQ,mCADR,GAEQ,yCAJJ;EAKd7d,EAAAA,OAAO,EAAE,aALK;EAMd8d,EAAAA,KAAK,EAAE,EANO;EAOdC,EAAAA,KAAK,EAAE,CAPO;EAQdC,EAAAA,IAAI,EAAE,KARQ;EASd/mB,EAAAA,QAAQ,EAAE,KATI;EAUdyd,EAAAA,SAAS,EAAE,KAVG;EAWdnQ,EAAAA,MAAM,EAAE,CAXM;EAYd0L,EAAAA,SAAS,EAAE,KAZG;EAadgO,EAAAA,iBAAiB,EAAE,MAbL;EAcdjL,EAAAA,QAAQ,EAAE,cAdI;EAedkL,EAAAA,QAAQ,EAAE,IAfI;EAgBdrB,EAAAA,UAAU,EAAE,IAhBE;EAiBdD,EAAAA,SAAS,EAAE/B,gBAjBG;EAkBd3H,EAAAA,YAAY,EAAE;EAlBA,CAAhB;EAqBA,IAAM/a,OAAK,GAAG;EACZsmB,EAAAA,IAAI,WAASrd,WADD;EAEZsd,EAAAA,MAAM,aAAWtd,WAFL;EAGZud,EAAAA,IAAI,WAASvd,WAHD;EAIZwd,EAAAA,KAAK,YAAUxd,WAJH;EAKZyd,EAAAA,QAAQ,eAAazd,WALT;EAMZ0d,EAAAA,KAAK,YAAU1d,WANH;EAOZ2d,EAAAA,OAAO,cAAY3d,WAPP;EAQZ4d,EAAAA,QAAQ,eAAa5d,WART;EASZ6d,EAAAA,UAAU,iBAAe7d,WATb;EAUZ8d,EAAAA,UAAU,iBAAe9d;EAVb,CAAd;EAaA,IAAMgV,iBAAe,GAAG,MAAxB;EACA,IAAM+I,gBAAgB,GAAG,OAAzB;EACA,IAAMxQ,iBAAe,GAAG,MAAxB;EAEA,IAAMyQ,gBAAgB,GAAG,MAAzB;EACA,IAAMC,eAAe,GAAG,KAAxB;EAEA,IAAMC,sBAAsB,GAAG,gBAA/B;EAEA,IAAMC,aAAa,GAAG,OAAtB;EACA,IAAMC,aAAa,GAAG,OAAtB;EACA,IAAMC,aAAa,GAAG,OAAtB;EACA,IAAMC,cAAc,GAAG,QAAvB;EAEA;EACA;EACA;EACA;EACA;;MAEMC;EACJ,mBAAY3oB,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,QAAI,OAAO6a,0BAAP,KAAkB,WAAtB,EAAmC;EACjC,YAAM,IAAI7F,SAAJ,CAAc,iEAAd,CAAN;EACD,KAH0B;;;EAM3B,SAAK2R,UAAL,GAAkB,IAAlB;EACA,SAAKC,QAAL,GAAgB,CAAhB;EACA,SAAKC,WAAL,GAAmB,EAAnB;EACA,SAAKC,cAAL,GAAsB,EAAtB;EACA,SAAK3M,OAAL,GAAe,IAAf,CAV2B;;EAa3B,SAAKpc,OAAL,GAAeA,OAAf;EACA,SAAKiC,MAAL,GAAc,KAAK6Q,UAAL,CAAgB7Q,MAAhB,CAAd;EACA,SAAK+mB,GAAL,GAAW,IAAX;;EAEA,SAAKC,aAAL;;EACAlkB,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsB,KAAKud,WAAL,CAAiBpT,QAAvC,EAAiD,IAAjD;EACD;;;;;EAgCD;WAEA+e,SAAA,kBAAS;EACP,SAAKN,UAAL,GAAkB,IAAlB;EACD;;WAEDO,UAAA,mBAAU;EACR,SAAKP,UAAL,GAAkB,KAAlB;EACD;;WAEDQ,gBAAA,yBAAgB;EACd,SAAKR,UAAL,GAAkB,CAAC,KAAKA,UAAxB;EACD;;WAEDrc,SAAA,gBAAOpG,KAAP,EAAc;EACZ,QAAI,CAAC,KAAKyiB,UAAV,EAAsB;EACpB;EACD;;EAED,QAAIziB,KAAJ,EAAW;EACT,UAAMkjB,OAAO,GAAG,KAAK9L,WAAL,CAAiBpT,QAAjC;EACA,UAAIiU,OAAO,GAAGrZ,IAAI,CAACG,OAAL,CAAaiB,KAAK,CAACC,cAAnB,EAAmCijB,OAAnC,CAAd;;EAEA,UAAI,CAACjL,OAAL,EAAc;EACZA,QAAAA,OAAO,GAAG,IAAI,KAAKb,WAAT,CACRpX,KAAK,CAACC,cADE,EAER,KAAKkjB,kBAAL,EAFQ,CAAV;EAIAvkB,QAAAA,IAAI,CAACC,OAAL,CAAamB,KAAK,CAACC,cAAnB,EAAmCijB,OAAnC,EAA4CjL,OAA5C;EACD;;EAEDA,MAAAA,OAAO,CAAC2K,cAAR,CAAuBQ,KAAvB,GAA+B,CAACnL,OAAO,CAAC2K,cAAR,CAAuBQ,KAAvD;;EAEA,UAAInL,OAAO,CAACoL,oBAAR,EAAJ,EAAoC;EAClCpL,QAAAA,OAAO,CAACqL,MAAR,CAAe,IAAf,EAAqBrL,OAArB;EACD,OAFD,MAEO;EACLA,QAAAA,OAAO,CAACsL,MAAR,CAAe,IAAf,EAAqBtL,OAArB;EACD;EACF,KAnBD,MAmBO;EACL,UAAI,KAAKuL,aAAL,GAAqBpe,SAArB,CAA+BE,QAA/B,CAAwCkM,iBAAxC,CAAJ,EAA8D;EAC5D,aAAK+R,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;EACA;EACD;;EAED,WAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;EACD;EACF;;WAEDpe,UAAA,mBAAU;EACR0J,IAAAA,YAAY,CAAC,KAAK8T,QAAN,CAAZ;EAEA9jB,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKnF,OAArB,EAA8B,KAAKud,WAAL,CAAiBpT,QAA/C;EAEA7D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKvG,OAAtB,EAA+B,KAAKud,WAAL,CAAiBnT,SAAhD;EACA9D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKvG,OAAL,CAAasL,OAAb,OAAyB6c,gBAAzB,CAAjB,EAA+D,eAA/D,EAAgF,KAAKyB,iBAArF;;EAEA,QAAI,KAAKZ,GAAT,EAAc;EACZ,WAAKA,GAAL,CAAShmB,UAAT,CAAoB2I,WAApB,CAAgC,KAAKqd,GAArC;EACD;;EAED,SAAKJ,UAAL,GAAkB,IAAlB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,WAAL,GAAmB,IAAnB;EACA,SAAKC,cAAL,GAAsB,IAAtB;;EACA,QAAI,KAAK3M,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAae,OAAb;EACD;;EAED,SAAKf,OAAL,GAAe,IAAf;EACA,SAAKpc,OAAL,GAAe,IAAf;EACA,SAAKiC,MAAL,GAAc,IAAd;EACA,SAAK+mB,GAAL,GAAW,IAAX;EACD;;WAEDlQ,OAAA,gBAAO;EAAA;;EACL,QAAI,KAAK9Y,OAAL,CAAa+C,KAAb,CAAmBI,OAAnB,KAA+B,MAAnC,EAA2C;EACzC,YAAM,IAAIP,KAAJ,CAAU,qCAAV,CAAN;EACD;;EAED,QAAI,KAAKinB,aAAL,MAAwB,KAAKjB,UAAjC,EAA6C;EAC3C,UAAM/L,SAAS,GAAGvW,YAAY,CAAC0C,OAAb,CAAqB,KAAKhJ,OAA1B,EAAmC,KAAKud,WAAL,CAAiBpc,KAAjB,CAAuBwmB,IAA1D,CAAlB;EACA,UAAMmC,UAAU,GAAGzmB,cAAc,CAAC,KAAKrD,OAAN,CAAjC;EACA,UAAM+pB,UAAU,GAAGD,UAAU,KAAK,IAAf,GACjB,KAAK9pB,OAAL,CAAagqB,aAAb,CAA2B1mB,eAA3B,CAA2CmI,QAA3C,CAAoD,KAAKzL,OAAzD,CADiB,GAEjB8pB,UAAU,CAACre,QAAX,CAAoB,KAAKzL,OAAzB,CAFF;;EAIA,UAAI6c,SAAS,CAACvT,gBAAV,IAA8B,CAACygB,UAAnC,EAA+C;EAC7C;EACD;;EAED,UAAMf,GAAG,GAAG,KAAKW,aAAL,EAAZ;EACA,UAAMM,KAAK,GAAGzqB,MAAM,CAAC,KAAK+d,WAAL,CAAiBtT,IAAlB,CAApB;EAEA+e,MAAAA,GAAG,CAACxc,YAAJ,CAAiB,IAAjB,EAAuByd,KAAvB;EACA,WAAKjqB,OAAL,CAAawM,YAAb,CAA0B,kBAA1B,EAA8Cyd,KAA9C;EAEA,WAAKC,UAAL;;EAEA,UAAI,KAAKjoB,MAAL,CAAY2kB,SAAhB,EAA2B;EACzBoC,QAAAA,GAAG,CAACzd,SAAJ,CAAc2J,GAAd,CAAkBkK,iBAAlB;EACD;;EAED,UAAM1B,SAAS,GAAG,OAAO,KAAKzb,MAAL,CAAYyb,SAAnB,KAAiC,UAAjC,GAChB,KAAKzb,MAAL,CAAYyb,SAAZ,CAAsBre,IAAtB,CAA2B,IAA3B,EAAiC2pB,GAAjC,EAAsC,KAAKhpB,OAA3C,CADgB,GAEhB,KAAKiC,MAAL,CAAYyb,SAFd;;EAIA,UAAMyM,UAAU,GAAG,KAAKC,cAAL,CAAoB1M,SAApB,CAAnB;;EACA,WAAK2M,mBAAL,CAAyBF,UAAzB;;EAEA,UAAMlR,SAAS,GAAG,KAAKqR,aAAL,EAAlB;;EACAvlB,MAAAA,IAAI,CAACC,OAAL,CAAagkB,GAAb,EAAkB,KAAKzL,WAAL,CAAiBpT,QAAnC,EAA6C,IAA7C;;EAEA,UAAI,CAAC,KAAKnK,OAAL,CAAagqB,aAAb,CAA2B1mB,eAA3B,CAA2CmI,QAA3C,CAAoD,KAAKud,GAAzD,CAAL,EAAoE;EAClE/P,QAAAA,SAAS,CAAC6H,WAAV,CAAsBkI,GAAtB;EACD;;EAED1iB,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAKhJ,OAA1B,EAAmC,KAAKud,WAAL,CAAiBpc,KAAjB,CAAuB0mB,QAA1D;EAEA,WAAKzL,OAAL,GAAe,IAAIU,0BAAJ,CAAW,KAAK9c,OAAhB,EAAyBgpB,GAAzB,EAA8B,KAAKhM,gBAAL,CAAsBmN,UAAtB,CAA9B,CAAf;EAEAnB,MAAAA,GAAG,CAACzd,SAAJ,CAAc2J,GAAd,CAAkByC,iBAAlB,EAzC2C;EA4C3C;EACA;EACA;;EACA,UAAI,kBAAkB9X,QAAQ,CAACyD,eAA/B,EAAgD;EAAA;;EAC9C,oBAAG+K,MAAH,aAAaxO,QAAQ,CAACmE,IAAT,CAAcyK,QAA3B,EAAqCpM,OAArC,CAA6C,UAAArC,OAAO,EAAI;EACtDsG,UAAAA,YAAY,CAACkC,EAAb,CAAgBxI,OAAhB,EAAyB,WAAzB,EAAsC2D,IAAI,EAA1C;EACD,SAFD;EAGD;;EAED,UAAM8V,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,YAAI,KAAI,CAACxX,MAAL,CAAY2kB,SAAhB,EAA2B;EACzB,UAAA,KAAI,CAAC2D,cAAL;EACD;;EAED,YAAMC,cAAc,GAAG,KAAI,CAAC1B,WAA5B;EACA,QAAA,KAAI,CAACA,WAAL,GAAmB,IAAnB;EAEAxiB,QAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAI,CAAChJ,OAA1B,EAAmC,KAAI,CAACud,WAAL,CAAiBpc,KAAjB,CAAuBymB,KAA1D;;EAEA,YAAI4C,cAAc,KAAKnC,eAAvB,EAAwC;EACtC,UAAA,KAAI,CAACqB,MAAL,CAAY,IAAZ,EAAkB,KAAlB;EACD;EACF,OAbD;;EAeA,UAAI,KAAKV,GAAL,CAASzd,SAAT,CAAmBE,QAAnB,CAA4B2T,iBAA5B,CAAJ,EAAkD;EAChD,YAAMze,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKwoB,GAAN,CAA3D;EACA1iB,QAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKugB,GAAtB,EAA2BhqB,cAA3B,EAA2Cya,QAA3C;EACAnY,QAAAA,oBAAoB,CAAC,KAAK0nB,GAAN,EAAWroB,kBAAX,CAApB;EACD,OAJD,MAIO;EACL8Y,QAAAA,QAAQ;EACT;EACF;EACF;;WAEDZ,OAAA,gBAAO;EAAA;;EACL,QAAI,CAAC,KAAKuD,OAAV,EAAmB;EACjB;EACD;;EAED,QAAM4M,GAAG,GAAG,KAAKW,aAAL,EAAZ;;EACA,QAAMlQ,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,UAAI,MAAI,CAACqP,WAAL,KAAqBV,gBAArB,IAAyCY,GAAG,CAAChmB,UAAjD,EAA6D;EAC3DgmB,QAAAA,GAAG,CAAChmB,UAAJ,CAAe2I,WAAf,CAA2Bqd,GAA3B;EACD;;EAED,MAAA,MAAI,CAACyB,cAAL;;EACA,MAAA,MAAI,CAACzqB,OAAL,CAAakN,eAAb,CAA6B,kBAA7B;;EACA5G,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAChJ,OAA1B,EAAmC,MAAI,CAACud,WAAL,CAAiBpc,KAAjB,CAAuBumB,MAA1D;;EACA,MAAA,MAAI,CAACtL,OAAL,CAAae,OAAb;EACD,KATD;;EAWA,QAAMD,SAAS,GAAG5W,YAAY,CAAC0C,OAAb,CAAqB,KAAKhJ,OAA1B,EAAmC,KAAKud,WAAL,CAAiBpc,KAAjB,CAAuBsmB,IAA1D,CAAlB;;EACA,QAAIvK,SAAS,CAAC5T,gBAAd,EAAgC;EAC9B;EACD;;EAED0f,IAAAA,GAAG,CAACzd,SAAJ,CAAcC,MAAd,CAAqBmM,iBAArB,EAtBK;EAyBL;;EACA,QAAI,kBAAkB9X,QAAQ,CAACyD,eAA/B,EAAgD;EAAA;;EAC9C,mBAAG+K,MAAH,cAAaxO,QAAQ,CAACmE,IAAT,CAAcyK,QAA3B,EACGpM,OADH,CACW,UAAArC,OAAO;EAAA,eAAIsG,YAAY,CAACC,GAAb,CAAiBvG,OAAjB,EAA0B,WAA1B,EAAuC2D,IAAvC,CAAJ;EAAA,OADlB;EAED;;EAED,SAAKolB,cAAL,CAAoBN,aAApB,IAAqC,KAArC;EACA,SAAKM,cAAL,CAAoBP,aAApB,IAAqC,KAArC;EACA,SAAKO,cAAL,CAAoBR,aAApB,IAAqC,KAArC;;EAEA,QAAI,KAAKS,GAAL,CAASzd,SAAT,CAAmBE,QAAnB,CAA4B2T,iBAA5B,CAAJ,EAAkD;EAChD,UAAMze,kBAAkB,GAAGH,gCAAgC,CAACwoB,GAAD,CAA3D;EAEA1iB,MAAAA,YAAY,CAACmC,GAAb,CAAiBugB,GAAjB,EAAsBhqB,cAAtB,EAAsCya,QAAtC;EACAnY,MAAAA,oBAAoB,CAAC0nB,GAAD,EAAMroB,kBAAN,CAApB;EACD,KALD,MAKO;EACL8Y,MAAAA,QAAQ;EACT;;EAED,SAAKqP,WAAL,GAAmB,EAAnB;EACD;;WAED1L,SAAA,kBAAS;EACP,QAAI,KAAKhB,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAaiB,cAAb;EACD;EACF;;;WAIDwM,gBAAA,yBAAgB;EACd,WAAO1hB,OAAO,CAAC,KAAKuiB,QAAL,EAAD,CAAd;EACD;;WAEDf,gBAAA,yBAAgB;EACd,QAAI,KAAKX,GAAT,EAAc;EACZ,aAAO,KAAKA,GAAZ;EACD;;EAED,QAAMhpB,OAAO,GAAGH,QAAQ,CAACyhB,aAAT,CAAuB,KAAvB,CAAhB;EACAthB,IAAAA,OAAO,CAACwmB,SAAR,GAAoB,KAAKvkB,MAAL,CAAY4kB,QAAhC;EAEA,SAAKmC,GAAL,GAAWhpB,OAAO,CAACyO,QAAR,CAAiB,CAAjB,CAAX;EACA,WAAO,KAAKua,GAAZ;EACD;;WAEDkB,aAAA,sBAAa;EACX,QAAMlB,GAAG,GAAG,KAAKW,aAAL,EAAZ;EACA,SAAKgB,iBAAL,CAAuBzc,cAAc,CAACM,OAAf,CAAuB8Z,sBAAvB,EAA+CU,GAA/C,CAAvB,EAA4E,KAAK0B,QAAL,EAA5E;EACA1B,IAAAA,GAAG,CAACzd,SAAJ,CAAcC,MAAd,CAAqB4T,iBAArB,EAAsCzH,iBAAtC;EACD;;WAEDgT,oBAAA,2BAAkB3qB,OAAlB,EAA2B4qB,OAA3B,EAAoC;EAClC,QAAI5qB,OAAO,KAAK,IAAhB,EAAsB;EACpB;EACD;;EAED,QAAI,OAAO4qB,OAAP,KAAmB,QAAnB,IAA+BxpB,SAAS,CAACwpB,OAAD,CAA5C,EAAuD;EACrD,UAAIA,OAAO,CAAC9Q,MAAZ,EAAoB;EAClB8Q,QAAAA,OAAO,GAAGA,OAAO,CAAC,CAAD,CAAjB;EACD,OAHoD;;;EAMrD,UAAI,KAAK3oB,MAAL,CAAY+kB,IAAhB,EAAsB;EACpB,YAAI4D,OAAO,CAAC5nB,UAAR,KAAuBhD,OAA3B,EAAoC;EAClCA,UAAAA,OAAO,CAACwmB,SAAR,GAAoB,EAApB;EACAxmB,UAAAA,OAAO,CAAC8gB,WAAR,CAAoB8J,OAApB;EACD;EACF,OALD,MAKO;EACL5qB,QAAAA,OAAO,CAAC6qB,WAAR,GAAsBD,OAAO,CAACC,WAA9B;EACD;;EAED;EACD;;EAED,QAAI,KAAK5oB,MAAL,CAAY+kB,IAAhB,EAAsB;EACpB,UAAI,KAAK/kB,MAAL,CAAYilB,QAAhB,EAA0B;EACxB0D,QAAAA,OAAO,GAAGlF,YAAY,CAACkF,OAAD,EAAU,KAAK3oB,MAAL,CAAY2jB,SAAtB,EAAiC,KAAK3jB,MAAL,CAAY4jB,UAA7C,CAAtB;EACD;;EAED7lB,MAAAA,OAAO,CAACwmB,SAAR,GAAoBoE,OAApB;EACD,KAND,MAMO;EACL5qB,MAAAA,OAAO,CAAC6qB,WAAR,GAAsBD,OAAtB;EACD;EACF;;WAEDF,WAAA,oBAAW;EACT,QAAI5D,KAAK,GAAG,KAAK9mB,OAAL,CAAaE,YAAb,CAA0B,qBAA1B,CAAZ;;EAEA,QAAI,CAAC4mB,KAAL,EAAY;EACVA,MAAAA,KAAK,GAAG,OAAO,KAAK7kB,MAAL,CAAY6kB,KAAnB,KAA6B,UAA7B,GACN,KAAK7kB,MAAL,CAAY6kB,KAAZ,CAAkBznB,IAAlB,CAAuB,KAAKW,OAA5B,CADM,GAEN,KAAKiC,MAAL,CAAY6kB,KAFd;EAGD;;EAED,WAAOA,KAAP;EACD;;;WAID9J,mBAAA,0BAAiBmN,UAAjB,EAA6B;EAAA;;EAC3B,QAAMW,eAAe,GAAG;EACtBpN,MAAAA,SAAS,EAAEyM,UADW;EAEtBtM,MAAAA,SAAS,EAAE;EACTtQ,QAAAA,MAAM,EAAE,KAAKoQ,UAAL,EADC;EAET5B,QAAAA,IAAI,EAAE;EACJgP,UAAAA,QAAQ,EAAE,KAAK9oB,MAAL,CAAYglB;EADlB,SAFG;EAKT+D,QAAAA,KAAK,EAAE;EACLhrB,UAAAA,OAAO,QAAM,KAAKud,WAAL,CAAiBtT,IAAvB;EADF,SALE;EAQT8T,QAAAA,eAAe,EAAE;EACfC,UAAAA,iBAAiB,EAAE,KAAK/b,MAAL,CAAY+Z;EADhB;EARR,OAFW;EActBiP,MAAAA,QAAQ,EAAE,kBAAAvmB,IAAI,EAAI;EAChB,YAAIA,IAAI,CAACwmB,iBAAL,KAA2BxmB,IAAI,CAACgZ,SAApC,EAA+C;EAC7C,UAAA,MAAI,CAACyN,4BAAL,CAAkCzmB,IAAlC;EACD;EACF,OAlBqB;EAmBtB0mB,MAAAA,QAAQ,EAAE,kBAAA1mB,IAAI;EAAA,eAAI,MAAI,CAACymB,4BAAL,CAAkCzmB,IAAlC,CAAJ;EAAA;EAnBQ,KAAxB;EAsBA,wBACKomB,eADL,EAEK,KAAK7oB,MAAL,CAAYia,YAFjB;EAID;;WAEDmO,sBAAA,6BAAoBF,UAApB,EAAgC;EAC9B,SAAKR,aAAL,GAAqBpe,SAArB,CAA+B2J,GAA/B,CAAsCuR,YAAtC,SAAsD0D,UAAtD;EACD;;WAEDxM,aAAA,sBAAa;EAAA;;EACX,QAAMpQ,MAAM,GAAG,EAAf;;EAEA,QAAI,OAAO,KAAKtL,MAAL,CAAYsL,MAAnB,KAA8B,UAAlC,EAA8C;EAC5CA,MAAAA,MAAM,CAACtH,EAAP,GAAY,UAAAvB,IAAI,EAAI;EAClBA,QAAAA,IAAI,CAACkZ,OAAL,gBACKlZ,IAAI,CAACkZ,OADV,EAEM,MAAI,CAAC3b,MAAL,CAAYsL,MAAZ,CAAmB7I,IAAI,CAACkZ,OAAxB,EAAiC,MAAI,CAAC5d,OAAtC,KAAkD,EAFxD;EAKA,eAAO0E,IAAP;EACD,OAPD;EAQD,KATD,MASO;EACL6I,MAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKtL,MAAL,CAAYsL,MAA5B;EACD;;EAED,WAAOA,MAAP;EACD;;WAED+c,gBAAA,yBAAgB;EACd,QAAI,KAAKroB,MAAL,CAAYgX,SAAZ,KAA0B,KAA9B,EAAqC;EACnC,aAAOpZ,QAAQ,CAACmE,IAAhB;EACD;;EAED,QAAI5C,SAAS,CAAC,KAAKa,MAAL,CAAYgX,SAAb,CAAb,EAAsC;EACpC,aAAO,KAAKhX,MAAL,CAAYgX,SAAnB;EACD;;EAED,WAAO/K,cAAc,CAACM,OAAf,CAAuB,KAAKvM,MAAL,CAAYgX,SAAnC,CAAP;EACD;;WAEDmR,iBAAA,wBAAe1M,SAAf,EAA0B;EACxB,WAAOyJ,aAAa,CAACzJ,SAAS,CAAC7a,WAAV,EAAD,CAApB;EACD;;WAEDomB,gBAAA,yBAAgB;EAAA;;EACd,QAAMoC,QAAQ,GAAG,KAAKppB,MAAL,CAAY+G,OAAZ,CAAoBhI,KAApB,CAA0B,GAA1B,CAAjB;EAEAqqB,IAAAA,QAAQ,CAAChpB,OAAT,CAAiB,UAAA2G,OAAO,EAAI;EAC1B,UAAIA,OAAO,KAAK,OAAhB,EAAyB;EACvB1C,QAAAA,YAAY,CAACkC,EAAb,CAAgB,MAAI,CAACxI,OAArB,EACE,MAAI,CAACud,WAAL,CAAiBpc,KAAjB,CAAuB2mB,KADzB,EAEE,MAAI,CAAC7lB,MAAL,CAAYhC,QAFd,EAGE,UAAAkG,KAAK;EAAA,iBAAI,MAAI,CAACoG,MAAL,CAAYpG,KAAZ,CAAJ;EAAA,SAHP;EAKD,OAND,MAMO,IAAI6C,OAAO,KAAK0f,cAAhB,EAAgC;EACrC,YAAM4C,OAAO,GAAGtiB,OAAO,KAAKuf,aAAZ,GACd,MAAI,CAAChL,WAAL,CAAiBpc,KAAjB,CAAuB8mB,UADT,GAEd,MAAI,CAAC1K,WAAL,CAAiBpc,KAAjB,CAAuB4mB,OAFzB;EAGA,YAAMwD,QAAQ,GAAGviB,OAAO,KAAKuf,aAAZ,GACf,MAAI,CAAChL,WAAL,CAAiBpc,KAAjB,CAAuB+mB,UADR,GAEf,MAAI,CAAC3K,WAAL,CAAiBpc,KAAjB,CAAuB6mB,QAFzB;EAIA1hB,QAAAA,YAAY,CAACkC,EAAb,CAAgB,MAAI,CAACxI,OAArB,EACEsrB,OADF,EAEE,MAAI,CAACrpB,MAAL,CAAYhC,QAFd,EAGE,UAAAkG,KAAK;EAAA,iBAAI,MAAI,CAACsjB,MAAL,CAAYtjB,KAAZ,CAAJ;EAAA,SAHP;EAKAG,QAAAA,YAAY,CAACkC,EAAb,CAAgB,MAAI,CAACxI,OAArB,EACEurB,QADF,EAEE,MAAI,CAACtpB,MAAL,CAAYhC,QAFd,EAGE,UAAAkG,KAAK;EAAA,iBAAI,MAAI,CAACujB,MAAL,CAAYvjB,KAAZ,CAAJ;EAAA,SAHP;EAKD;EACF,KA1BD;;EA4BA,SAAKyjB,iBAAL,GAAyB,YAAM;EAC7B,UAAI,MAAI,CAAC5pB,OAAT,EAAkB;EAChB,QAAA,MAAI,CAAC6Y,IAAL;EACD;EACF,KAJD;;EAMAvS,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKxI,OAAL,CAAasL,OAAb,OAAyB6c,gBAAzB,CAAhB,EACE,eADF,EAEE,KAAKyB,iBAFP;;EAKA,QAAI,KAAK3nB,MAAL,CAAYhC,QAAhB,EAA0B;EACxB,WAAKgC,MAAL,gBACK,KAAKA,MADV;EAEE+G,QAAAA,OAAO,EAAE,QAFX;EAGE/I,QAAAA,QAAQ,EAAE;EAHZ;EAKD,KAND,MAMO;EACL,WAAKurB,SAAL;EACD;EACF;;WAEDA,YAAA,qBAAY;EACV,QAAMC,SAAS,GAAG,OAAO,KAAKzrB,OAAL,CAAaE,YAAb,CAA0B,qBAA1B,CAAzB;;EAEA,QAAI,KAAKF,OAAL,CAAaE,YAAb,CAA0B,OAA1B,KAAsCurB,SAAS,KAAK,QAAxD,EAAkE;EAChE,WAAKzrB,OAAL,CAAawM,YAAb,CACE,qBADF,EAEE,KAAKxM,OAAL,CAAaE,YAAb,CAA0B,OAA1B,KAAsC,EAFxC;EAKA,WAAKF,OAAL,CAAawM,YAAb,CAA0B,OAA1B,EAAmC,EAAnC;EACD;EACF;;WAEDid,SAAA,gBAAOtjB,KAAP,EAAciY,OAAd,EAAuB;EACrB,QAAMiL,OAAO,GAAG,KAAK9L,WAAL,CAAiBpT,QAAjC;EACAiU,IAAAA,OAAO,GAAGA,OAAO,IAAIrZ,IAAI,CAACG,OAAL,CAAaiB,KAAK,CAACC,cAAnB,EAAmCijB,OAAnC,CAArB;;EAEA,QAAI,CAACjL,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAI,KAAKb,WAAT,CACRpX,KAAK,CAACC,cADE,EAER,KAAKkjB,kBAAL,EAFQ,CAAV;EAIAvkB,MAAAA,IAAI,CAACC,OAAL,CAAamB,KAAK,CAACC,cAAnB,EAAmCijB,OAAnC,EAA4CjL,OAA5C;EACD;;EAED,QAAIjY,KAAJ,EAAW;EACTiY,MAAAA,OAAO,CAAC2K,cAAR,CACE5iB,KAAK,CAACK,IAAN,KAAe,SAAf,GAA2BgiB,aAA3B,GAA2CD,aAD7C,IAEI,IAFJ;EAGD;;EAED,QAAInK,OAAO,CAACuL,aAAR,GAAwBpe,SAAxB,CAAkCE,QAAlC,CAA2CkM,iBAA3C,KACAyG,OAAO,CAAC0K,WAAR,KAAwBV,gBAD5B,EAC8C;EAC5ChK,MAAAA,OAAO,CAAC0K,WAAR,GAAsBV,gBAAtB;EACA;EACD;;EAEDrT,IAAAA,YAAY,CAACqJ,OAAO,CAACyK,QAAT,CAAZ;EAEAzK,IAAAA,OAAO,CAAC0K,WAAR,GAAsBV,gBAAtB;;EAEA,QAAI,CAAChK,OAAO,CAACnc,MAAR,CAAe8kB,KAAhB,IAAyB,CAAC3I,OAAO,CAACnc,MAAR,CAAe8kB,KAAf,CAAqBjO,IAAnD,EAAyD;EACvDsF,MAAAA,OAAO,CAACtF,IAAR;EACA;EACD;;EAEDsF,IAAAA,OAAO,CAACyK,QAAR,GAAmB/mB,UAAU,CAAC,YAAM;EAClC,UAAIsc,OAAO,CAAC0K,WAAR,KAAwBV,gBAA5B,EAA8C;EAC5ChK,QAAAA,OAAO,CAACtF,IAAR;EACD;EACF,KAJ4B,EAI1BsF,OAAO,CAACnc,MAAR,CAAe8kB,KAAf,CAAqBjO,IAJK,CAA7B;EAKD;;WAED4Q,SAAA,gBAAOvjB,KAAP,EAAciY,OAAd,EAAuB;EACrB,QAAMiL,OAAO,GAAG,KAAK9L,WAAL,CAAiBpT,QAAjC;EACAiU,IAAAA,OAAO,GAAGA,OAAO,IAAIrZ,IAAI,CAACG,OAAL,CAAaiB,KAAK,CAACC,cAAnB,EAAmCijB,OAAnC,CAArB;;EAEA,QAAI,CAACjL,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAI,KAAKb,WAAT,CACRpX,KAAK,CAACC,cADE,EAER,KAAKkjB,kBAAL,EAFQ,CAAV;EAIAvkB,MAAAA,IAAI,CAACC,OAAL,CAAamB,KAAK,CAACC,cAAnB,EAAmCijB,OAAnC,EAA4CjL,OAA5C;EACD;;EAED,QAAIjY,KAAJ,EAAW;EACTiY,MAAAA,OAAO,CAAC2K,cAAR,CACE5iB,KAAK,CAACK,IAAN,KAAe,UAAf,GAA4BgiB,aAA5B,GAA4CD,aAD9C,IAEI,KAFJ;EAGD;;EAED,QAAInK,OAAO,CAACoL,oBAAR,EAAJ,EAAoC;EAClC;EACD;;EAEDzU,IAAAA,YAAY,CAACqJ,OAAO,CAACyK,QAAT,CAAZ;EAEAzK,IAAAA,OAAO,CAAC0K,WAAR,GAAsBT,eAAtB;;EAEA,QAAI,CAACjK,OAAO,CAACnc,MAAR,CAAe8kB,KAAhB,IAAyB,CAAC3I,OAAO,CAACnc,MAAR,CAAe8kB,KAAf,CAAqBlO,IAAnD,EAAyD;EACvDuF,MAAAA,OAAO,CAACvF,IAAR;EACA;EACD;;EAEDuF,IAAAA,OAAO,CAACyK,QAAR,GAAmB/mB,UAAU,CAAC,YAAM;EAClC,UAAIsc,OAAO,CAAC0K,WAAR,KAAwBT,eAA5B,EAA6C;EAC3CjK,QAAAA,OAAO,CAACvF,IAAR;EACD;EACF,KAJ4B,EAI1BuF,OAAO,CAACnc,MAAR,CAAe8kB,KAAf,CAAqBlO,IAJK,CAA7B;EAKD;;WAED2Q,uBAAA,gCAAuB;EACrB,SAAK,IAAMxgB,OAAX,IAAsB,KAAK+f,cAA3B,EAA2C;EACzC,UAAI,KAAKA,cAAL,CAAoB/f,OAApB,CAAJ,EAAkC;EAChC,eAAO,IAAP;EACD;EACF;;EAED,WAAO,KAAP;EACD;;WAED8J,aAAA,oBAAW7Q,MAAX,EAAmB;EACjB,QAAMypB,cAAc,GAAG3e,WAAW,CAACI,iBAAZ,CAA8B,KAAKnN,OAAnC,CAAvB;EAEAmC,IAAAA,MAAM,CAACC,IAAP,CAAYspB,cAAZ,EAA4BrpB,OAA5B,CAAoC,UAAAspB,QAAQ,EAAI;EAC9C,UAAIhF,qBAAqB,CAAC7e,OAAtB,CAA8B6jB,QAA9B,MAA4C,CAAC,CAAjD,EAAoD;EAClD,eAAOD,cAAc,CAACC,QAAD,CAArB;EACD;EACF,KAJD;;EAMA,QAAI1pB,MAAM,IAAI,OAAOA,MAAM,CAACgX,SAAd,KAA4B,QAAtC,IAAkDhX,MAAM,CAACgX,SAAP,CAAiBa,MAAvE,EAA+E;EAC7E7X,MAAAA,MAAM,CAACgX,SAAP,GAAmBhX,MAAM,CAACgX,SAAP,CAAiB,CAAjB,CAAnB;EACD;;EAEDhX,IAAAA,MAAM,gBACD,KAAKsb,WAAL,CAAiB7N,OADhB,EAEDgc,cAFC,EAGA,OAAOzpB,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHhD,CAAN;;EAMA,QAAI,OAAOA,MAAM,CAAC8kB,KAAd,KAAwB,QAA5B,EAAsC;EACpC9kB,MAAAA,MAAM,CAAC8kB,KAAP,GAAe;EACbjO,QAAAA,IAAI,EAAE7W,MAAM,CAAC8kB,KADA;EAEblO,QAAAA,IAAI,EAAE5W,MAAM,CAAC8kB;EAFA,OAAf;EAID;;EAED,QAAI,OAAO9kB,MAAM,CAAC6kB,KAAd,KAAwB,QAA5B,EAAsC;EACpC7kB,MAAAA,MAAM,CAAC6kB,KAAP,GAAe7kB,MAAM,CAAC6kB,KAAP,CAAa1nB,QAAb,EAAf;EACD;;EAED,QAAI,OAAO6C,MAAM,CAAC2oB,OAAd,KAA0B,QAA9B,EAAwC;EACtC3oB,MAAAA,MAAM,CAAC2oB,OAAP,GAAiB3oB,MAAM,CAAC2oB,OAAP,CAAexrB,QAAf,EAAjB;EACD;;EAED2C,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAe,KAAKsb,WAAL,CAAiBtN,WAAhC,CAAf;;EAEA,QAAIhO,MAAM,CAACilB,QAAX,EAAqB;EACnBjlB,MAAAA,MAAM,CAAC4kB,QAAP,GAAkBnB,YAAY,CAACzjB,MAAM,CAAC4kB,QAAR,EAAkB5kB,MAAM,CAAC2jB,SAAzB,EAAoC3jB,MAAM,CAAC4jB,UAA3C,CAA9B;EACD;;EAED,WAAO5jB,MAAP;EACD;;WAEDqnB,qBAAA,8BAAqB;EACnB,QAAMrnB,MAAM,GAAG,EAAf;;EAEA,QAAI,KAAKA,MAAT,EAAiB;EACf,WAAK,IAAMwC,GAAX,IAAkB,KAAKxC,MAAvB,EAA+B;EAC7B,YAAI,KAAKsb,WAAL,CAAiB7N,OAAjB,CAAyBjL,GAAzB,MAAkC,KAAKxC,MAAL,CAAYwC,GAAZ,CAAtC,EAAwD;EACtDxC,UAAAA,MAAM,CAACwC,GAAD,CAAN,GAAc,KAAKxC,MAAL,CAAYwC,GAAZ,CAAd;EACD;EACF;EACF;;EAED,WAAOxC,MAAP;EACD;;WAEDwoB,iBAAA,0BAAiB;EACf,QAAMzB,GAAG,GAAG,KAAKW,aAAL,EAAZ;EACA,QAAMiC,QAAQ,GAAG5C,GAAG,CAAC9oB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgConB,kBAAhC,CAAjB;;EACA,QAAIkF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAC7kB,MAAT,GAAkB,CAA3C,EAA8C;EAC5C6kB,MAAAA,QAAQ,CAACC,GAAT,CAAa,UAAAC,KAAK;EAAA,eAAIA,KAAK,CAAC1rB,IAAN,EAAJ;EAAA,OAAlB,EACGiC,OADH,CACW,UAAA0pB,MAAM;EAAA,eAAI/C,GAAG,CAACzd,SAAJ,CAAcC,MAAd,CAAqBugB,MAArB,CAAJ;EAAA,OADjB;EAED;EACF;;WAEDZ,+BAAA,sCAA6Ba,UAA7B,EAAyC;EACvC,SAAKhD,GAAL,GAAWgD,UAAU,CAAC/mB,QAAX,CAAoBgnB,MAA/B;;EACA,SAAKxB,cAAL;;EACA,SAAKJ,mBAAL,CAAyB,KAAKD,cAAL,CAAoB4B,UAAU,CAACtO,SAA/B,CAAzB;EACD;;WAED6M,iBAAA,0BAAiB;EACf,QAAMvB,GAAG,GAAG,KAAKW,aAAL,EAAZ;EACA,QAAMuC,mBAAmB,GAAG,KAAKjqB,MAAL,CAAY2kB,SAAxC;;EACA,QAAIoC,GAAG,CAAC9oB,YAAJ,CAAiB,aAAjB,MAAoC,IAAxC,EAA8C;EAC5C;EACD;;EAED8oB,IAAAA,GAAG,CAACzd,SAAJ,CAAcC,MAAd,CAAqB4T,iBAArB;EACA,SAAKnd,MAAL,CAAY2kB,SAAZ,GAAwB,KAAxB;EACA,SAAK/N,IAAL;EACA,SAAKC,IAAL;EACA,SAAK7W,MAAL,CAAY2kB,SAAZ,GAAwBsF,mBAAxB;EACD;;;YAIMtgB,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;EACA,UAAM0I,OAAO,GAAG,OAAO5Q,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAACyC,IAAD,IAAS,eAAe/B,IAAf,CAAoBV,MAApB,CAAb,EAA0C;EACxC;EACD;;EAED,UAAI,CAACyC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIikB,OAAJ,CAAY,IAAZ,EAAkB9V,OAAlB,CAAP;EACD;;EAED,UAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ;EACD;EACF,KAnBM,CAAP;EAoBD;;YAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BAroBoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOwF,SAAP;EACD;;;0BAEiB;EAChB,aAAOzF,MAAP;EACD;;;0BAEqB;EACpB,aAAOE,UAAP;EACD;;;0BAEkB;EACjB,aAAOhJ,OAAP;EACD;;;0BAEsB;EACrB,aAAOiJ,WAAP;EACD;;;0BAEwB;EACvB,aAAO6F,aAAP;EACD;;;;;EA8mBH;EACA;EACA;EACA;EACA;EACA;;;EAEA/L,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAa0e,OAAO,CAAC/c,eAArB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyByc,OAAzB;;EACAzf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAO0c,OAAO,CAAC/c,eAAf;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;ECvxBA;EACA;EACA;EACA;EACA;;EAEA,IAAM3B,MAAI,GAAG,SAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,YAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAMsc,cAAY,GAAG,YAArB;EACA,IAAMC,oBAAkB,GAAG,IAAIhkB,MAAJ,aAAqB+jB,cAArB,WAAyC,GAAzC,CAA3B;;EAEA,IAAM/W,SAAO,gBACRiZ,OAAO,CAACjZ,OADA;EAEXgO,EAAAA,SAAS,EAAE,OAFA;EAGX1U,EAAAA,OAAO,EAAE,OAHE;EAIX4hB,EAAAA,OAAO,EAAE,EAJE;EAKX/D,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEE,kCAFF,GAGE;EARD,EAAb;;EAWA,IAAM5W,aAAW,gBACZ0Y,OAAO,CAAC1Y,WADI;EAEf2a,EAAAA,OAAO,EAAE;EAFM,EAAjB;;EAKA,IAAMzpB,OAAK,GAAG;EACZsmB,EAAAA,IAAI,WAASrd,WADD;EAEZsd,EAAAA,MAAM,aAAWtd,WAFL;EAGZud,EAAAA,IAAI,WAASvd,WAHD;EAIZwd,EAAAA,KAAK,YAAUxd,WAJH;EAKZyd,EAAAA,QAAQ,eAAazd,WALT;EAMZ0d,EAAAA,KAAK,YAAU1d,WANH;EAOZ2d,EAAAA,OAAO,cAAY3d,WAPP;EAQZ4d,EAAAA,QAAQ,eAAa5d,WART;EASZ6d,EAAAA,UAAU,iBAAe7d,WATb;EAUZ8d,EAAAA,UAAU,iBAAe9d;EAVb,CAAd;EAaA,IAAMgV,iBAAe,GAAG,MAAxB;EACA,IAAMzH,iBAAe,GAAG,MAAxB;EAEA,IAAMwU,cAAc,GAAG,iBAAvB;EACA,IAAMC,gBAAgB,GAAG,eAAzB;EAEA;EACA;EACA;EACA;EACA;;MAEMC;;;;;;;;;EA+BJ;WAEAxC,gBAAA,yBAAgB;EACd,WAAO,KAAKa,QAAL,MAAmB,KAAK4B,WAAL,EAA1B;EACD;;WAEDpC,aAAA,sBAAa;EACX,QAAMlB,GAAG,GAAG,KAAKW,aAAL,EAAZ,CADW;;EAIX,SAAKgB,iBAAL,CAAuBzc,cAAc,CAACM,OAAf,CAAuB2d,cAAvB,EAAuCnD,GAAvC,CAAvB,EAAoE,KAAK0B,QAAL,EAApE;;EACA,QAAIE,OAAO,GAAG,KAAK0B,WAAL,EAAd;;EACA,QAAI,OAAO1B,OAAP,KAAmB,UAAvB,EAAmC;EACjCA,MAAAA,OAAO,GAAGA,OAAO,CAACvrB,IAAR,CAAa,KAAKW,OAAlB,CAAV;EACD;;EAED,SAAK2qB,iBAAL,CAAuBzc,cAAc,CAACM,OAAf,CAAuB4d,gBAAvB,EAAyCpD,GAAzC,CAAvB,EAAsE4B,OAAtE;EAEA5B,IAAAA,GAAG,CAACzd,SAAJ,CAAcC,MAAd,CAAqB4T,iBAArB,EAAsCzH,iBAAtC;EACD;;;WAID0S,sBAAA,6BAAoBF,UAApB,EAAgC;EAC9B,SAAKR,aAAL,GAAqBpe,SAArB,CAA+B2J,GAA/B,CAAsCuR,cAAtC,SAAsD0D,UAAtD;EACD;;WAEDmC,cAAA,uBAAc;EACZ,WAAO,KAAKtsB,OAAL,CAAaE,YAAb,CAA0B,cAA1B,KACL,KAAK+B,MAAL,CAAY2oB,OADd;EAED;;WAEDH,iBAAA,0BAAiB;EACf,QAAMzB,GAAG,GAAG,KAAKW,aAAL,EAAZ;EACA,QAAMiC,QAAQ,GAAG5C,GAAG,CAAC9oB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgConB,oBAAhC,CAAjB;;EACA,QAAIkF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAC7kB,MAAT,GAAkB,CAA3C,EAA8C;EAC5C6kB,MAAAA,QAAQ,CAACC,GAAT,CAAa,UAAAC,KAAK;EAAA,eAAIA,KAAK,CAAC1rB,IAAN,EAAJ;EAAA,OAAlB,EACGiC,OADH,CACW,UAAA0pB,MAAM;EAAA,eAAI/C,GAAG,CAACzd,SAAJ,CAAcC,MAAd,CAAqBugB,MAArB,CAAJ;EAAA,OADjB;EAED;EACF;;;YAIMngB,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;EACA,UAAM0I,OAAO,GAAG,OAAO5Q,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;EAEA,UAAI,CAACyC,IAAD,IAAS,eAAe/B,IAAf,CAAoBV,MAApB,CAAb,EAA0C;EACxC;EACD;;EAED,UAAI,CAACyC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI2nB,OAAJ,CAAY,IAAZ,EAAkBxZ,OAAlB,CAAP;EACA9N,QAAAA,IAAI,CAACC,OAAL,CAAa,IAAb,EAAmBmF,UAAnB,EAA6BzF,IAA7B;EACD;;EAED,UAAI,OAAOzC,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ;EACD;EACF,KApBM,CAAP;EAqBD;;YAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;EAnGD;0BAEqB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOwF,SAAP;EACD;;;0BAEiB;EAChB,aAAOzF,MAAP;EACD;;;0BAEqB;EACpB,aAAOE,UAAP;EACD;;;0BAEkB;EACjB,aAAOhJ,OAAP;EACD;;;0BAEsB;EACrB,aAAOiJ,WAAP;EACD;;;0BAEwB;EACvB,aAAO6F,aAAP;EACD;;;;IA7BmB0Y;EAuGtB;EACA;EACA;EACA;EACA;EACA;;;EAEAzkB,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAaoiB,OAAO,CAACzgB,eAArB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBmgB,OAAzB;;EACAnjB,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOogB,OAAO,CAACzgB,eAAf;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;EC5JA;EACA;EACA;EACA;EACA;;EAEA,IAAM3B,MAAI,GAAG,WAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,cAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAMqF,SAAO,GAAG;EACdnC,EAAAA,MAAM,EAAE,EADM;EAEdgf,EAAAA,MAAM,EAAE,MAFM;EAGd1lB,EAAAA,MAAM,EAAE;EAHM,CAAhB;EAMA,IAAMoJ,aAAW,GAAG;EAClB1C,EAAAA,MAAM,EAAE,QADU;EAElBgf,EAAAA,MAAM,EAAE,QAFU;EAGlB1lB,EAAAA,MAAM,EAAE;EAHU,CAApB;EAMA,IAAM2lB,cAAc,gBAAcpiB,WAAlC;EACA,IAAMqiB,YAAY,cAAYriB,WAA9B;EACA,IAAM6G,qBAAmB,YAAU7G,WAAV,GAAsBC,cAA/C;EAEA,IAAMqiB,wBAAwB,GAAG,eAAjC;EACA,IAAMtgB,mBAAiB,GAAG,QAA1B;EAEA,IAAMugB,iBAAiB,GAAG,qBAA1B;EACA,IAAMC,uBAAuB,GAAG,mBAAhC;EACA,IAAMC,kBAAkB,GAAG,WAA3B;EACA,IAAMC,kBAAkB,GAAG,WAA3B;EACA,IAAMC,mBAAmB,GAAG,kBAA5B;EACA,IAAMC,iBAAiB,GAAG,WAA1B;EACA,IAAMC,wBAAwB,GAAG,kBAAjC;EAEA,IAAMC,aAAa,GAAG,QAAtB;EACA,IAAMC,eAAe,GAAG,UAAxB;EAEA;EACA;EACA;EACA;EACA;;MAEMC;EACJ,qBAAYptB,OAAZ,EAAqBiC,MAArB,EAA6B;EAAA;;EAC3B,SAAK6I,QAAL,GAAgB9K,OAAhB;EACA,SAAKqtB,cAAL,GAAsBrtB,OAAO,CAACmV,OAAR,KAAoB,MAApB,GAA6B1U,MAA7B,GAAsCT,OAA5D;EACA,SAAK6S,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;EACA,SAAKwW,SAAL,GAAoB,KAAK5F,OAAL,CAAahM,MAAjC,SAA2CgmB,kBAA3C,UAAkE,KAAKha,OAAL,CAAahM,MAA/E,SAAyFkmB,mBAAzF,UAAiH,KAAKla,OAAL,CAAahM,MAA9H,UAAyI6lB,wBAAzI;EACA,SAAKY,QAAL,GAAgB,EAAhB;EACA,SAAKC,QAAL,GAAgB,EAAhB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACA,SAAKC,aAAL,GAAqB,CAArB;EAEAnnB,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAK6kB,cAArB,EAAqCZ,YAArC,EAAmD,UAAAtmB,KAAK;EAAA,aAAI,KAAI,CAACunB,QAAL,CAAcvnB,KAAd,CAAJ;EAAA,KAAxD;EAEA,SAAKwnB,OAAL;;EACA,SAAKD,QAAL;;EAEA3oB,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;EACD;;;;;EAYD;WAEAwjB,UAAA,mBAAU;EAAA;;EACR,QAAMC,UAAU,GAAG,KAAKP,cAAL,KAAwB,KAAKA,cAAL,CAAoB5sB,MAA5C,GACjBysB,aADiB,GAEjBC,eAFF;EAIA,QAAMU,YAAY,GAAG,KAAKhb,OAAL,CAAa0Z,MAAb,KAAwB,MAAxB,GACnBqB,UADmB,GAEnB,KAAK/a,OAAL,CAAa0Z,MAFf;EAIA,QAAMuB,UAAU,GAAGD,YAAY,KAAKV,eAAjB,GACjB,KAAKY,aAAL,EADiB,GAEjB,CAFF;EAIA,SAAKT,QAAL,GAAgB,EAAhB;EACA,SAAKC,QAAL,GAAgB,EAAhB;EACA,SAAKE,aAAL,GAAqB,KAAKO,gBAAL,EAArB;EAEA,QAAMC,OAAO,GAAG/f,cAAc,CAACE,IAAf,CAAoB,KAAKqK,SAAzB,CAAhB;EAEAwV,IAAAA,OAAO,CAACpC,GAAR,CAAY,UAAA7rB,OAAO,EAAI;EACrB,UAAMkuB,cAAc,GAAG7tB,sBAAsB,CAACL,OAAD,CAA7C;EACA,UAAM6G,MAAM,GAAGqnB,cAAc,GAAGhgB,cAAc,CAACM,OAAf,CAAuB0f,cAAvB,CAAH,GAA4C,IAAzE;;EAEA,UAAIrnB,MAAJ,EAAY;EACV,YAAMsnB,SAAS,GAAGtnB,MAAM,CAAC4G,qBAAP,EAAlB;;EACA,YAAI0gB,SAAS,CAACpL,KAAV,IAAmBoL,SAAS,CAACC,MAAjC,EAAyC;EACvC,iBAAO,CACLrhB,WAAW,CAAC8gB,YAAD,CAAX,CAA0BhnB,MAA1B,EAAkC6G,GAAlC,GAAwCogB,UADnC,EAELI,cAFK,CAAP;EAID;EACF;;EAED,aAAO,IAAP;EACD,KAfD,EAgBGxf,MAhBH,CAgBU,UAAA2f,IAAI;EAAA,aAAIA,IAAJ;EAAA,KAhBd,EAiBGC,IAjBH,CAiBQ,UAACxK,CAAD,EAAIE,CAAJ;EAAA,aAAUF,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAC,CAAC,CAAD,CAAlB;EAAA,KAjBR,EAkBG3hB,OAlBH,CAkBW,UAAAgsB,IAAI,EAAI;EACf,MAAA,MAAI,CAACf,QAAL,CAActe,IAAd,CAAmBqf,IAAI,CAAC,CAAD,CAAvB;;EACA,MAAA,MAAI,CAACd,QAAL,CAAcve,IAAd,CAAmBqf,IAAI,CAAC,CAAD,CAAvB;EACD,KArBH;EAsBD;;WAEDhjB,UAAA,mBAAU;EACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EACA7D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK8mB,cAAtB,EAAsCjjB,WAAtC;EAEA,SAAKU,QAAL,GAAgB,IAAhB;EACA,SAAKuiB,cAAL,GAAsB,IAAtB;EACA,SAAKxa,OAAL,GAAe,IAAf;EACA,SAAK4F,SAAL,GAAiB,IAAjB;EACA,SAAK6U,QAAL,GAAgB,IAAhB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACD;;;WAID3a,aAAA,oBAAW7Q,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACDyN,SADC,EAEA,OAAOzN,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAFhD,CAAN;;EAKA,QAAI,OAAOA,MAAM,CAAC4E,MAAd,KAAyB,QAAzB,IAAqCzF,SAAS,CAACa,MAAM,CAAC4E,MAAR,CAAlD,EAAmE;EAAA,UAC3DtC,EAD2D,GACpDtC,MAAM,CAAC4E,MAD6C,CAC3DtC,EAD2D;;EAEjE,UAAI,CAACA,EAAL,EAAS;EACPA,QAAAA,EAAE,GAAG/E,MAAM,CAACyK,MAAD,CAAX;EACAhI,QAAAA,MAAM,CAAC4E,MAAP,CAActC,EAAd,GAAmBA,EAAnB;EACD;;EAEDtC,MAAAA,MAAM,CAAC4E,MAAP,SAAoBtC,EAApB;EACD;;EAEDxC,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAegO,aAAf,CAAf;EAEA,WAAOhO,MAAP;EACD;;WAED8rB,gBAAA,yBAAgB;EACd,WAAO,KAAKV,cAAL,KAAwB5sB,MAAxB,GACL,KAAK4sB,cAAL,CAAoBkB,WADf,GAEL,KAAKlB,cAAL,CAAoB1f,SAFtB;EAGD;;WAEDqgB,mBAAA,4BAAmB;EACjB,WAAO,KAAKX,cAAL,CAAoBzL,YAApB,IAAoCliB,IAAI,CAAC8uB,GAAL,CACzC3uB,QAAQ,CAACmE,IAAT,CAAc4d,YAD2B,EAEzC/hB,QAAQ,CAACyD,eAAT,CAAyBse,YAFgB,CAA3C;EAID;;WAED6M,mBAAA,4BAAmB;EACjB,WAAO,KAAKpB,cAAL,KAAwB5sB,MAAxB,GACLA,MAAM,CAACiuB,WADF,GAEL,KAAKrB,cAAL,CAAoB5f,qBAApB,GAA4C2gB,MAF9C;EAGD;;WAEDV,WAAA,oBAAW;EACT,QAAM/f,SAAS,GAAG,KAAKogB,aAAL,KAAuB,KAAKlb,OAAL,CAAatF,MAAtD;;EACA,QAAMqU,YAAY,GAAG,KAAKoM,gBAAL,EAArB;;EACA,QAAMW,SAAS,GAAG,KAAK9b,OAAL,CAAatF,MAAb,GAChBqU,YADgB,GAEhB,KAAK6M,gBAAL,EAFF;;EAIA,QAAI,KAAKhB,aAAL,KAAuB7L,YAA3B,EAAyC;EACvC,WAAK+L,OAAL;EACD;;EAED,QAAIhgB,SAAS,IAAIghB,SAAjB,EAA4B;EAC1B,UAAM9nB,MAAM,GAAG,KAAK0mB,QAAL,CAAc,KAAKA,QAAL,CAAcxmB,MAAd,GAAuB,CAArC,CAAf;;EAEA,UAAI,KAAKymB,aAAL,KAAuB3mB,MAA3B,EAAmC;EACjC,aAAK+nB,SAAL,CAAe/nB,MAAf;EACD;;EAED;EACD;;EAED,QAAI,KAAK2mB,aAAL,IAAsB7f,SAAS,GAAG,KAAK2f,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;EAC9E,WAAKE,aAAL,GAAqB,IAArB;;EACA,WAAKqB,MAAL;;EACA;EACD;;EAED,SAAK,IAAI/nB,CAAC,GAAG,KAAKwmB,QAAL,CAAcvmB,MAA3B,EAAmCD,CAAC,EAApC,GAAyC;EACvC,UAAMgoB,cAAc,GAAG,KAAKtB,aAAL,KAAuB,KAAKD,QAAL,CAAczmB,CAAd,CAAvB,IACnB6G,SAAS,IAAI,KAAK2f,QAAL,CAAcxmB,CAAd,CADM,KAElB,OAAO,KAAKwmB,QAAL,CAAcxmB,CAAC,GAAG,CAAlB,CAAP,KAAgC,WAAhC,IACG6G,SAAS,GAAG,KAAK2f,QAAL,CAAcxmB,CAAC,GAAG,CAAlB,CAHG,CAAvB;;EAKA,UAAIgoB,cAAJ,EAAoB;EAClB,aAAKF,SAAL,CAAe,KAAKrB,QAAL,CAAczmB,CAAd,CAAf;EACD;EACF;EACF;;WAED8nB,YAAA,mBAAU/nB,MAAV,EAAkB;EAChB,SAAK2mB,aAAL,GAAqB3mB,MAArB;;EAEA,SAAKgoB,MAAL;;EAEA,QAAME,OAAO,GAAG,KAAKtW,SAAL,CAAezX,KAAf,CAAqB,GAArB,EACb6qB,GADa,CACT,UAAA5rB,QAAQ;EAAA,aAAOA,QAAP,uBAAgC4G,MAAhC,YAA4C5G,QAA5C,gBAA8D4G,MAA9D;EAAA,KADC,CAAhB;;EAGA,QAAMmoB,IAAI,GAAG9gB,cAAc,CAACM,OAAf,CAAuBugB,OAAO,CAACE,IAAR,CAAa,GAAb,CAAvB,CAAb;;EAEA,QAAID,IAAI,CAACzjB,SAAL,CAAeE,QAAf,CAAwBihB,wBAAxB,CAAJ,EAAuD;EACrDxe,MAAAA,cAAc,CAACM,OAAf,CAAuBye,wBAAvB,EAAiD+B,IAAI,CAAC1jB,OAAL,CAAa0hB,iBAAb,CAAjD,EACGzhB,SADH,CACa2J,GADb,CACiB9I,mBADjB;EAGA4iB,MAAAA,IAAI,CAACzjB,SAAL,CAAe2J,GAAf,CAAmB9I,mBAAnB;EACD,KALD,MAKO;EACL;EACA4iB,MAAAA,IAAI,CAACzjB,SAAL,CAAe2J,GAAf,CAAmB9I,mBAAnB;EAEA8B,MAAAA,cAAc,CAACU,OAAf,CAAuBogB,IAAvB,EAA6BpC,uBAA7B,EACGvqB,OADH,CACW,UAAA6sB,SAAS,EAAI;EACpB;EACA;EACAhhB,QAAAA,cAAc,CAACe,IAAf,CAAoBigB,SAApB,EAAkCrC,kBAAlC,UAAyDE,mBAAzD,EACG1qB,OADH,CACW,UAAAgsB,IAAI;EAAA,iBAAIA,IAAI,CAAC9iB,SAAL,CAAe2J,GAAf,CAAmB9I,mBAAnB,CAAJ;EAAA,SADf,EAHoB;;EAOpB8B,QAAAA,cAAc,CAACe,IAAf,CAAoBigB,SAApB,EAA+BpC,kBAA/B,EACGzqB,OADH,CACW,UAAA8sB,OAAO,EAAI;EAClBjhB,UAAAA,cAAc,CAACO,QAAf,CAAwB0gB,OAAxB,EAAiCtC,kBAAjC,EACGxqB,OADH,CACW,UAAAgsB,IAAI;EAAA,mBAAIA,IAAI,CAAC9iB,SAAL,CAAe2J,GAAf,CAAmB9I,mBAAnB,CAAJ;EAAA,WADf;EAED,SAJH;EAKD,OAbH;EAcD;;EAED9F,IAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAKqkB,cAA1B,EAA0Cb,cAA1C,EAA0D;EACxD3W,MAAAA,aAAa,EAAEhP;EADyC,KAA1D;EAGD;;WAEDgoB,SAAA,kBAAS;EACP3gB,IAAAA,cAAc,CAACE,IAAf,CAAoB,KAAKqK,SAAzB,EACG/J,MADH,CACU,UAAA0gB,IAAI;EAAA,aAAIA,IAAI,CAAC7jB,SAAL,CAAeE,QAAf,CAAwBW,mBAAxB,CAAJ;EAAA,KADd,EAEG/J,OAFH,CAEW,UAAA+sB,IAAI;EAAA,aAAIA,IAAI,CAAC7jB,SAAL,CAAeC,MAAf,CAAsBY,mBAAtB,CAAJ;EAAA,KAFf;EAGD;;;cAIMR,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;EACA,UAAM0I,OAAO,GAAG,OAAO5Q,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAACyC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI0oB,SAAJ,CAAc,IAAd,EAAoBva,OAApB,CAAP;EACD;;EAED,UAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ;EACD;EACF,KAfM,CAAP;EAgBD;;cAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BAzNoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOwF,SAAP;EACD;;;;;EAsNH;EACA;EACA;EACA;EACA;;;EAEApJ,YAAY,CAACkC,EAAb,CAAgB/H,MAAhB,EAAwBwQ,qBAAxB,EAA6C,YAAM;EACjD/C,EAAAA,cAAc,CAACE,IAAf,CAAoBue,iBAApB,EACGtqB,OADH,CACW,UAAAgtB,GAAG;EAAA,WAAI,IAAIjC,SAAJ,CAAciC,GAAd,EAAmBtiB,WAAW,CAACI,iBAAZ,CAA8BkiB,GAA9B,CAAnB,CAAJ;EAAA,GADd;EAED,CAHD;EAKA;EACA;EACA;EACA;EACA;EACA;;EAEAnrB,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAamjB,SAAS,CAACxhB,eAAvB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBkhB,SAAzB;;EACAlkB,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOmhB,SAAS,CAACxhB,eAAjB;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;ECnTA;EACA;EACA;EACA;EACA;;EAEA,IAAM3B,MAAI,GAAG,KAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,QAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAMoN,YAAU,YAAUrN,WAA1B;EACA,IAAMsN,cAAY,cAAYtN,WAA9B;EACA,IAAMmN,YAAU,YAAUnN,WAA1B;EACA,IAAMoN,aAAW,aAAWpN,WAA5B;EACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EAEA,IAAMilB,wBAAwB,GAAG,eAAjC;EACA,IAAMljB,mBAAiB,GAAG,QAA1B;EACA,IAAM0O,qBAAmB,GAAG,UAA5B;EACA,IAAMsE,iBAAe,GAAG,MAAxB;EACA,IAAMzH,iBAAe,GAAG,MAAxB;EAEA,IAAMqV,mBAAiB,GAAG,WAA1B;EACA,IAAMJ,yBAAuB,GAAG,mBAAhC;EACA,IAAMnb,iBAAe,GAAG,SAAxB;EACA,IAAM8d,kBAAkB,GAAG,uBAA3B;EACA,IAAMljB,sBAAoB,GAAG,iEAA7B;EACA,IAAM4gB,0BAAwB,GAAG,kBAAjC;EACA,IAAMuC,8BAA8B,GAAG,iCAAvC;EAEA;EACA;EACA;EACA;EACA;;MAEMC;EACJ,eAAYzvB,OAAZ,EAAqB;EACnB,SAAK8K,QAAL,GAAgB9K,OAAhB;EAEA+E,IAAAA,IAAI,CAACC,OAAL,CAAa,KAAK8F,QAAlB,EAA4BX,UAA5B,EAAsC,IAAtC;EACD;;;;;EAQD;WAEA2O,OAAA,gBAAO;EAAA;;EACL,QAAK,KAAKhO,QAAL,CAAc9H,UAAd,IACH,KAAK8H,QAAL,CAAc9H,UAAd,CAAyB3B,QAAzB,KAAsCyN,IAAI,CAACC,YADxC,IAEH,KAAKjE,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCW,mBAAjC,CAFE,IAGF,KAAKtB,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCqP,qBAAjC,CAHF,EAGyD;EACvD;EACD;;EAED,QAAI5L,QAAJ;EACA,QAAMrI,MAAM,GAAGtG,sBAAsB,CAAC,KAAKuK,QAAN,CAArC;;EACA,QAAM4kB,WAAW,GAAG,KAAK5kB,QAAL,CAAcQ,OAAd,CAAsBshB,yBAAtB,CAApB;;EAEA,QAAI8C,WAAJ,EAAiB;EACf,UAAMC,YAAY,GAAGD,WAAW,CAACjM,QAAZ,KAAyB,IAAzB,IAAiCiM,WAAW,CAACjM,QAAZ,KAAyB,IAA1D,GAAiE8L,kBAAjE,GAAsF9d,iBAA3G;EACAvC,MAAAA,QAAQ,GAAGhB,cAAc,CAACE,IAAf,CAAoBuhB,YAApB,EAAkCD,WAAlC,CAAX;EACAxgB,MAAAA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,CAACnI,MAAT,GAAkB,CAAnB,CAAnB;EACD;;EAED,QAAImW,SAAS,GAAG,IAAhB;;EAEA,QAAIhO,QAAJ,EAAc;EACZgO,MAAAA,SAAS,GAAG5W,YAAY,CAAC0C,OAAb,CAAqBkG,QAArB,EAA+BuI,YAA/B,EAA2C;EACrD5B,QAAAA,aAAa,EAAE,KAAK/K;EADiC,OAA3C,CAAZ;EAGD;;EAED,QAAM+R,SAAS,GAAGvW,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyM,YAApC,EAAgD;EAChE1B,MAAAA,aAAa,EAAE3G;EADiD,KAAhD,CAAlB;;EAIA,QAAI2N,SAAS,CAACvT,gBAAV,IACD4T,SAAS,KAAK,IAAd,IAAsBA,SAAS,CAAC5T,gBADnC,EACsD;EACpD;EACD;;EAED,SAAKslB,SAAL,CACE,KAAK9jB,QADP,EAEE4kB,WAFF;;EAKA,QAAMjW,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrBnT,MAAAA,YAAY,CAAC0C,OAAb,CAAqBkG,QAArB,EAA+BwI,cAA/B,EAA6C;EAC3C7B,QAAAA,aAAa,EAAE,KAAI,CAAC/K;EADuB,OAA7C;EAGAxE,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAI,CAAC8B,QAA1B,EAAoC0M,aAApC,EAAiD;EAC/C3B,QAAAA,aAAa,EAAE3G;EADgC,OAAjD;EAGD,KAPD;;EASA,QAAIrI,MAAJ,EAAY;EACV,WAAK+nB,SAAL,CAAe/nB,MAAf,EAAuBA,MAAM,CAAC7D,UAA9B,EAA0CyW,QAA1C;EACD,KAFD,MAEO;EACLA,MAAAA,QAAQ;EACT;EACF;;WAEDpO,UAAA,mBAAU;EACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EACA,SAAKW,QAAL,GAAgB,IAAhB;EACD;;;WAID8jB,YAAA,mBAAU5uB,OAAV,EAAmBiZ,SAAnB,EAA8B9U,QAA9B,EAAwC;EAAA;;EACtC,QAAMyrB,cAAc,GAAG3W,SAAS,KAAKA,SAAS,CAACwK,QAAV,KAAuB,IAAvB,IAA+BxK,SAAS,CAACwK,QAAV,KAAuB,IAA3D,CAAT,GACrBvV,cAAc,CAACE,IAAf,CAAoBmhB,kBAApB,EAAwCtW,SAAxC,CADqB,GAErB/K,cAAc,CAACO,QAAf,CAAwBwK,SAAxB,EAAmCxH,iBAAnC,CAFF;EAIA,QAAMoe,MAAM,GAAGD,cAAc,CAAC,CAAD,CAA7B;EACA,QAAM/V,eAAe,GAAG1V,QAAQ,IAC7B0rB,MAAM,IAAIA,MAAM,CAACtkB,SAAP,CAAiBE,QAAjB,CAA0B2T,iBAA1B,CADb;;EAGA,QAAM3F,QAAQ,GAAG,SAAXA,QAAW;EAAA,aAAM,MAAI,CAACqW,mBAAL,CACrB9vB,OADqB,EAErB6vB,MAFqB,EAGrB1rB,QAHqB,CAAN;EAAA,KAAjB;;EAMA,QAAI0rB,MAAM,IAAIhW,eAAd,EAA+B;EAC7B,UAAMlZ,kBAAkB,GAAGH,gCAAgC,CAACqvB,MAAD,CAA3D;EACAA,MAAAA,MAAM,CAACtkB,SAAP,CAAiBC,MAAjB,CAAwBmM,iBAAxB;EAEArR,MAAAA,YAAY,CAACmC,GAAb,CAAiBonB,MAAjB,EAAyB7wB,cAAzB,EAAyCya,QAAzC;EACAnY,MAAAA,oBAAoB,CAACuuB,MAAD,EAASlvB,kBAAT,CAApB;EACD,KAND,MAMO;EACL8Y,MAAAA,QAAQ;EACT;EACF;;WAEDqW,sBAAA,6BAAoB9vB,OAApB,EAA6B6vB,MAA7B,EAAqC1rB,QAArC,EAA+C;EAC7C,QAAI0rB,MAAJ,EAAY;EACVA,MAAAA,MAAM,CAACtkB,SAAP,CAAiBC,MAAjB,CAAwBY,mBAAxB;EAEA,UAAM2jB,aAAa,GAAG7hB,cAAc,CAACM,OAAf,CAAuBghB,8BAAvB,EAAuDK,MAAM,CAAC7sB,UAA9D,CAAtB;;EAEA,UAAI+sB,aAAJ,EAAmB;EACjBA,QAAAA,aAAa,CAACxkB,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B;EACD;;EAED,UAAIyjB,MAAM,CAAC3vB,YAAP,CAAoB,MAApB,MAAgC,KAApC,EAA2C;EACzC2vB,QAAAA,MAAM,CAACrjB,YAAP,CAAoB,eAApB,EAAqC,KAArC;EACD;EACF;;EAEDxM,IAAAA,OAAO,CAACuL,SAAR,CAAkB2J,GAAlB,CAAsB9I,mBAAtB;;EACA,QAAIpM,OAAO,CAACE,YAAR,CAAqB,MAArB,MAAiC,KAArC,EAA4C;EAC1CF,MAAAA,OAAO,CAACwM,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAED5I,IAAAA,MAAM,CAAC5D,OAAD,CAAN;;EAEA,QAAIA,OAAO,CAACuL,SAAR,CAAkBE,QAAlB,CAA2B2T,iBAA3B,CAAJ,EAAiD;EAC/Cpf,MAAAA,OAAO,CAACuL,SAAR,CAAkB2J,GAAlB,CAAsByC,iBAAtB;EACD;;EAED,QAAI3X,OAAO,CAACgD,UAAR,IAAsBhD,OAAO,CAACgD,UAAR,CAAmBuI,SAAnB,CAA6BE,QAA7B,CAAsC6jB,wBAAtC,CAA1B,EAA2F;EACzF,UAAMU,eAAe,GAAGhwB,OAAO,CAACsL,OAAR,CAAgB0hB,mBAAhB,CAAxB;;EAEA,UAAIgD,eAAJ,EAAqB;EACnB9hB,QAAAA,cAAc,CAACE,IAAf,CAAoB6e,0BAApB,EACG5qB,OADH,CACW,UAAA4tB,QAAQ;EAAA,iBAAIA,QAAQ,CAAC1kB,SAAT,CAAmB2J,GAAnB,CAAuB9I,mBAAvB,CAAJ;EAAA,SADnB;EAED;;EAEDpM,MAAAA,OAAO,CAACwM,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAED,QAAIrI,QAAJ,EAAc;EACZA,MAAAA,QAAQ;EACT;EACF;;;QAIMyH,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3B,UAAMnH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,KAAgC,IAAIslB,GAAJ,CAAQ,IAAR,CAA7C;;EAEA,UAAI,OAAOxtB,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ;EACD;EACF,KAVM,CAAP;EAWD;;QAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BA3JoB;EACnB,aAAOD,SAAP;EACD;;;;;EA4JH;EACA;EACA;EACA;EACA;;;EAEA5D,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUlG,KAAV,EAAiB;EACrFA,EAAAA,KAAK,CAAC6D,cAAN;EAEA,MAAMtF,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,KAAgC,IAAIslB,GAAJ,CAAQ,IAAR,CAA7C;EACA/qB,EAAAA,IAAI,CAACoU,IAAL;EACD,CALD;EAOA;EACA;EACA;EACA;EACA;EACA;;EAEA5U,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAawlB,GAAG,CAAC7jB,eAAjB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBujB,GAAzB;;EACAvmB,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOwjB,GAAG,CAAC7jB,eAAX;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;ECjOA;EACA;EACA;EACA;EACA;;EAEA,IAAM3B,MAAI,GAAG,OAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,UAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EAEA,IAAM0U,qBAAmB,qBAAmBzU,WAA5C;EACA,IAAMqN,YAAU,YAAUrN,WAA1B;EACA,IAAMsN,cAAY,cAAYtN,WAA9B;EACA,IAAMmN,YAAU,YAAUnN,WAA1B;EACA,IAAMoN,aAAW,aAAWpN,WAA5B;EAEA,IAAMgV,iBAAe,GAAG,MAAxB;EACA,IAAM8Q,eAAe,GAAG,MAAxB;EACA,IAAMvY,iBAAe,GAAG,MAAxB;EACA,IAAMwY,kBAAkB,GAAG,SAA3B;EAEA,IAAMlgB,aAAW,GAAG;EAClB2W,EAAAA,SAAS,EAAE,SADO;EAElBwJ,EAAAA,QAAQ,EAAE,SAFQ;EAGlBrJ,EAAAA,KAAK,EAAE;EAHW,CAApB;EAMA,IAAMrX,SAAO,GAAG;EACdkX,EAAAA,SAAS,EAAE,IADG;EAEdwJ,EAAAA,QAAQ,EAAE,IAFI;EAGdrJ,EAAAA,KAAK,EAAE;EAHO,CAAhB;EAMA,IAAMvH,uBAAqB,GAAG,wBAA9B;EAEA;EACA;EACA;EACA;EACA;;MAEM6Q;EACJ,iBAAYrwB,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,SAAK6I,QAAL,GAAgB9K,OAAhB;EACA,SAAK6S,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;EACA,SAAK4mB,QAAL,GAAgB,IAAhB;;EACA,SAAKI,aAAL;;EACAlkB,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;EACD;;;;;EAgBD;WAEA2O,OAAA,gBAAO;EAAA;;EACL,QAAM+D,SAAS,GAAGvW,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyM,YAApC,CAAlB;;EAEA,QAAIsF,SAAS,CAACvT,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKgnB,aAAL;;EAEA,QAAI,KAAKzd,OAAL,CAAa+T,SAAjB,EAA4B;EAC1B,WAAK9b,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4BkK,iBAA5B;EACD;;EAED,QAAM3F,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,KAAI,CAAC3O,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B2kB,kBAA/B;;EACA,MAAA,KAAI,CAACrlB,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4ByC,iBAA5B;;EAEArR,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAI,CAAC8B,QAA1B,EAAoC0M,aAApC;;EAEA,UAAI,KAAI,CAAC3E,OAAL,CAAaud,QAAjB,EAA2B;EACzB,QAAA,KAAI,CAACvH,QAAL,GAAgB/mB,UAAU,CAAC,YAAM;EAC/B,UAAA,KAAI,CAAC+W,IAAL;EACD,SAFyB,EAEvB,KAAI,CAAChG,OAAL,CAAakU,KAFU,CAA1B;EAGD;EACF,KAXD;;EAaA,SAAKjc,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B0kB,eAA/B;;EACAtsB,IAAAA,MAAM,CAAC,KAAKkH,QAAN,CAAN;;EACA,SAAKA,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4Bib,kBAA5B;;EACA,QAAI,KAAKtd,OAAL,CAAa+T,SAAjB,EAA4B;EAC1B,UAAMjmB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKsK,QAAN,CAA3D;EAEAxE,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgDya,QAAhD;EACAnY,MAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBnK,kBAAhB,CAApB;EACD,KALD,MAKO;EACL8Y,MAAAA,QAAQ;EACT;EACF;;WAEDZ,OAAA,gBAAO;EAAA;;EACL,QAAI,CAAC,KAAK/N,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,iBAAjC,CAAL,EAAwD;EACtD;EACD;;EAED,QAAMuF,SAAS,GAAG5W,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoC2M,YAApC,CAAlB;;EAEA,QAAIyF,SAAS,CAAC5T,gBAAd,EAAgC;EAC9B;EACD;;EAED,QAAMmQ,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,MAAI,CAAC3O,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4Bgb,eAA5B;;EACA5pB,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAC8B,QAA1B,EAAoC4M,cAApC;EACD,KAHD;;EAKA,SAAK5M,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BmM,iBAA/B;;EACA,QAAI,KAAK9E,OAAL,CAAa+T,SAAjB,EAA4B;EAC1B,UAAMjmB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKsK,QAAN,CAA3D;EAEAxE,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgDya,QAAhD;EACAnY,MAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBnK,kBAAhB,CAApB;EACD,KALD,MAKO;EACL8Y,MAAAA,QAAQ;EACT;EACF;;WAEDpO,UAAA,mBAAU;EACR,SAAKilB,aAAL;;EAEA,QAAI,KAAKxlB,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,iBAAjC,CAAJ,EAAuD;EACrD,WAAK7M,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BmM,iBAA/B;EACD;;EAEDrR,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgC+T,qBAAhC;EACA9Z,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EAEA,SAAKW,QAAL,GAAgB,IAAhB;EACA,SAAK+H,OAAL,GAAe,IAAf;EACD;;;WAIDC,aAAA,oBAAW7Q,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACDyN,SADC,EAED3C,WAAW,CAACI,iBAAZ,CAA8B,KAAKrC,QAAnC,CAFC,EAGA,OAAO7I,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHhD,CAAN;EAMAF,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAe,KAAKsb,WAAL,CAAiBtN,WAAhC,CAAf;EAEA,WAAOhO,MAAP;EACD;;WAEDgnB,gBAAA,yBAAgB;EAAA;;EACd3iB,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B+T,qBAA/B,EAAoDW,uBAApD,EAA2E;EAAA,aAAM,MAAI,CAAC3G,IAAL,EAAN;EAAA,KAA3E;EACD;;WAEDyX,gBAAA,yBAAgB;EACdvb,IAAAA,YAAY,CAAC,KAAK8T,QAAN,CAAZ;EACA,SAAKA,QAAL,GAAgB,IAAhB;EACD;;;UAIMjd,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;EACA,UAAM0I,OAAO,GAAG,OAAO5Q,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAACyC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI2rB,KAAJ,CAAU,IAAV,EAAgBxd,OAAhB,CAAP;EACD;;EAED,UAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAfM,CAAP;EAgBD;;UAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BA5IoB;EACnB,aAAOD,SAAP;EACD;;;0BAEwB;EACvB,aAAO+F,aAAP;EACD;;;0BAEoB;EACnB,aAAOP,SAAP;EACD;;;;;EAqIH;EACA;EACA;EACA;EACA;EACA;;;EAEAxL,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAaomB,KAAK,CAACzkB,eAAnB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBmkB,KAAzB;;EACAnnB,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOokB,KAAK,CAACzkB,eAAb;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;EC/NA;EACA;EACA;EACA;EACA;EACA;AAcA,kBAAe;EACbf,EAAAA,KAAK,EAALA,KADa;EAEbyB,EAAAA,MAAM,EAANA,MAFa;EAGb8F,EAAAA,QAAQ,EAARA,QAHa;EAIb8F,EAAAA,QAAQ,EAARA,QAJa;EAKbiE,EAAAA,QAAQ,EAARA,QALa;EAMbwD,EAAAA,KAAK,EAALA,KANa;EAOb0M,EAAAA,OAAO,EAAPA,OAPa;EAQbe,EAAAA,SAAS,EAATA,SARa;EASbqC,EAAAA,GAAG,EAAHA,GATa;EAUbY,EAAAA,KAAK,EAALA,KAVa;EAWb1H,EAAAA,OAAO,EAAPA;EAXa,CAAf;;;;;;;;"}