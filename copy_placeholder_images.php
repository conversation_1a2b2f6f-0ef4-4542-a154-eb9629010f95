<?php
// Create placeholder images for missing files without using GD library

// Function to create a simple HTML placeholder image
function createHtmlPlaceholder($filename, $text = 'Placeholder')
{
    $html = '<!DOCTYPE html>
<html>
<head>
    <title>Placeholder Image</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        .placeholder {
            border: 1px solid #ccc;
            padding: 20px;
            text-align: center;
            background-color: #fff;
        }
    </style>
</head>
<body>
    <div class="placeholder">
        ' . htmlspecialchars($text) . '
    </div>
</body>
</html>';

    file_put_contents($filename . '.html', $html);
    echo "Created HTML placeholder: $filename.html<br>";
}

// Create uploads directory if it doesn't exist
if (!file_exists('uploads')) {
    mkdir('uploads', 0777, true);
    echo "Created uploads directory<br>";
}

// Copy default image if available, otherwise create HTML placeholders
$default_image = 'app/assets/img/default-placeholder.png';
if (file_exists($default_image)) {
    // Copy the default image to the required locations
    copy($default_image, 'eurospice-favicon.png');
    copy($default_image, 'eurospice-logo.png');
    copy($default_image, 'eurospice-grid.png');
    
    echo "Copied default placeholder image to required locations<br>";
} else {
    // Create HTML placeholders instead
    createHtmlPlaceholder('eurospice-favicon', 'Favicon');
    createHtmlPlaceholder('eurospice-logo', 'Logo');
    createHtmlPlaceholder('eurospice-grid', 'Grid');
}

echo "<p>All placeholders created successfully!</p>";
echo "<p><a href='app/modules/inventory/inventory.php'>Go to Inventory Page</a></p>";
?>
