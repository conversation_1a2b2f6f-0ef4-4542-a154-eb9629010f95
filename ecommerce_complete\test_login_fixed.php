<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database configuration
require_once 'config/database.php';

echo "<h1>Database Connection Test</h1>";
echo "<p>Database connection is working.</p>";

echo "<h2>Users Table Test</h2>";
try {
    // Test query to get users
    $sql = "SELECT * FROM users LIMIT 5";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($users) > 0) {
        echo "<p>Successfully retrieved " . count($users) . " users from the database.</p>";
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Username</th><th>First Name</th><th>Last Name</th><th>Role ID</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['username'] . "</td>";
            echo "<td>" . $user['first_name'] . "</td>";
            echo "<td>" . $user['last_name'] . "</td>";
            echo "<td>" . $user['role_id'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>No users found in the database.</p>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error querying users table: " . $e->getMessage() . "</p>";
}

echo "<h2>Login Test</h2>";
echo "<p>Try logging in using the form below:</p>";
?>

<form action="pages/account/login.php" method="POST">
    <div>
        <label for="username">Username:</label>
        <input type="text" id="username" name="username" required>
    </div>
    <div>
        <label for="password">Password:</label>
        <input type="password" id="password" name="password" required>
    </div>
    <div>
        <button type="submit">Login</button>
    </div>
</form>

<p>Or click the link below to go to the login page:</p>
<a href="pages/account/login.php">Go to Login Page</a>
