-- SQL script to remove constraints from finance database tables

-- First, identify tables with constraints
SELECT 
    tc.TABLE_NAME, 
    tc.CONSTRAINT_NAME, 
    tc.CONSTRAINT_TYPE
FROM 
    information_schema.TABLE_CONSTRAINTS tc
WHERE 
    tc.TABLE_SCHEMA = DATABASE()
    AND tc.CONSTRAINT_TYPE IN ('UNIQUE', 'FOREIGN KEY');

-- Remove constraints from the approved table (which likely has the brand constraint)
ALTER TABLE approved DROP INDEX IF EXISTS brand_name;
ALTER TABLE approved DROP INDEX IF EXISTS brand;
ALTER TABLE approved DROP INDEX IF EXISTS brand_unique;
ALTER TABLE approved DROP INDEX IF EXISTS unique_brand;
ALTER TABLE approved DROP INDEX IF EXISTS idx_brand_name;
ALTER TABLE approved DROP INDEX IF EXISTS idx_brand;

-- If there's a specific constraint named in the error message
ALTER TABLE approved DROP INDEX IF EXISTS VISKASE;

-- Modify the brand_name column to allow duplicates (remove UNIQUE constraint)
ALTER TABLE approved MODIFY COLUMN brand_name VA<PERSON>HA<PERSON>(100);

-- Also check and fix the products table
ALTER TABLE products DROP INDEX IF EXISTS brand;
ALTER TABLE products DROP INDEX IF EXISTS brand_name;
ALTER TABLE products DROP INDEX IF EXISTS brand_unique;
ALTER TABLE products DROP INDEX IF EXISTS unique_brand;
ALTER TABLE products DROP INDEX IF EXISTS idx_brand;
ALTER TABLE products DROP INDEX IF EXISTS idx_brand_name;

-- Modify the brand column in products table
ALTER TABLE products MODIFY COLUMN brand VARCHAR(100);

-- Check for any other tables that might have brand constraints
-- For example, inventory table
ALTER TABLE inventory DROP INDEX IF EXISTS brand_name;
ALTER TABLE inventory DROP INDEX IF EXISTS brand;
ALTER TABLE inventory MODIFY COLUMN brand_name VARCHAR(100);

-- Check for any other tables that might have brand constraints
-- For example, order_requests table
ALTER TABLE order_requests DROP INDEX IF EXISTS brand_name;
ALTER TABLE order_requests DROP INDEX IF EXISTS brand;
ALTER TABLE order_requests MODIFY COLUMN brand_name VARCHAR(100) NULL;
