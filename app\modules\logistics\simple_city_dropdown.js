// Simple city dropdown functionality
document.addEventListener('DOMContentLoaded', function () {
    // Local province data for Philippines
    const philippineProvinces = [
        { code: '0137', name: 'Metro Manila', popular: true },
        { code: '0128', name: '<PERSON><PERSON><PERSON>', popular: true },
        { code: '0133', name: 'Laguna', popular: true },
        { code: '0174', name: 'R<PERSON>l', popular: true },
        { code: '0122', name: 'Batangas' },
        { code: '0127', name: 'Bulacan' },
        { code: '0177', name: 'Pampanga' },
        { code: '0181', name: '<PERSON><PERSON><PERSON>' },
        { code: '0183', name: '<PERSON><PERSON><PERSON><PERSON>' },
        { code: '0123', name: 'Bat<PERSON>' },
        { code: '0134', name: 'Nueva Ecija' },
        { code: '0124', name: 'Aurora' }
    ];

    // Local city data for Philippines
    const philippineCities = {
        // Cavite
        '0128': [
            '<PERSON>', '<PERSON>ade<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Cavite City', '<PERSON><PERSON><PERSON><PERSON><PERSON>',
            'General <PERSON>', 'General <PERSON>', '<PERSON>',
            '<PERSON><PERSON>', '<PERSON>dan<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>don', 'Mendez',
            'Naic', 'Noveleta', 'Rosario', 'Silang', 'Tagaytay', 'Tanza', 'Ternate', 'Trece Martires'
        ],
        // Laguna
        '0133': [
            'Alaminos', 'Bay', 'Biñan', 'Cabuyao', 'Calamba', 'Calauan',
            'Cavinti', 'Famy', 'Kalayaan', 'Liliw', 'Los Baños', 'Luisiana',
            'Lumban', 'Mabitac', 'Magdalena', 'Majayjay', 'Nagcarlan', 'Paete',
            'Pagsanjan', 'Pakil', 'Pangil', 'Pila', 'Rizal', 'San Pablo',
            'San Pedro', 'Santa Cruz', 'Santa Maria', 'Santa Rosa', 'Siniloan', 'Victoria'
        ],
        // Rizal
        '0174': [
            'Angono', 'Antipolo', 'Baras', 'Binangonan', 'Cainta', 'Cardona',
            'Jalajala', 'Morong', 'Pililla', 'Rodriguez', 'San Mateo', 'Tanay',
            'Taytay', 'Teresa'
        ],
        // Metro Manila
        '0137': [
            'Caloocan', 'Las Piñas', 'Makati', 'Malabon', 'Mandaluyong', 'Manila',
            'Marikina', 'Muntinlupa', 'Navotas', 'Parañaque', 'Pasay', 'Pasig',
            'Pateros', 'Quezon City', 'San Juan', 'Taguig', 'Valenzuela'
        ],
        // Batangas
        '0122': [
            'Agoncillo', 'Alitagtag', 'Balayan', 'Balete', 'Batangas City', 'Bauan',
            'Calaca', 'Calatagan', 'Cuenca', 'Ibaan', 'Laurel', 'Lemery', 'Lian',
            'Lipa', 'Lobo', 'Mabini', 'Malvar', 'Mataas na Kahoy', 'Nasugbu',
            'Padre Garcia', 'Rosario', 'San Jose', 'San Juan', 'San Luis',
            'San Nicolas', 'San Pascual', 'Santa Teresita', 'Santo Tomas',
            'Taal', 'Talisay', 'Tanauan', 'Taysan', 'Tingloy', 'Tuy'
        ],
        // Bulacan
        '0127': [
            'Angat', 'Balagtas', 'Baliuag', 'Bocaue', 'Bulakan', 'Bustos',
            'Calumpit', 'Doña Remedios Trinidad', 'Guiguinto', 'Hagonoy',
            'Malolos', 'Marilao', 'Meycauayan', 'Norzagaray', 'Obando',
            'Pandi', 'Paombong', 'Plaridel', 'Pulilan', 'San Ildefonso',
            'San Jose del Monte', 'San Miguel', 'San Rafael', 'Santa Maria'
        ],
        // Default cities for other provinces
        'default': [
            'City 1', 'City 2', 'City 3', 'City 4', 'City 5'
        ]
    };

    // Define popular cities for each province
    const popularCities = {
        '0128': ['Dasmariñas', 'Bacoor', 'Imus', 'Tagaytay', 'General Trias'], // Cavite
        '0133': ['Santa Rosa', 'Calamba', 'San Pedro', 'Biñan', 'Los Baños'], // Laguna
        '0174': ['Antipolo', 'Cainta', 'Taytay'], // Rizal
        '0137': ['Manila', 'Quezon City', 'Makati', 'Taguig', 'Pasig'], // Metro Manila
        '0122': ['Batangas City', 'Lipa', 'Tanauan'], // Batangas
        '0127': ['Malolos', 'San Jose del Monte', 'Meycauayan'] // Bulacan
    };

    // Initialize province dropdown
    function initializeProvinceDropdown() {
        const provinceSelect = document.getElementById('province');
        if (!provinceSelect) {
            console.error('Province dropdown element not found');
            return;
        }

        console.log('Initializing province dropdown');

        // Clear existing options
        provinceSelect.innerHTML = '<option value="">Select Province</option>';

        // Add provinces
        philippineProvinces.forEach(province => {
            const option = document.createElement('option');
            option.value = province.code;
            option.textContent = province.name;
            provinceSelect.appendChild(option);
        });

        // Set up change event
        provinceSelect.addEventListener('change', function () {
            const provinceCode = this.value;
            console.log('Province changed to:', provinceCode);
            populateCityDropdown(provinceCode);
        });

        console.log('Province dropdown initialized with', philippineProvinces.length, 'provinces');
    }

    // Populate city dropdown based on selected province
    function populateCityDropdown(provinceCode) {
        const citySelect = document.getElementById('city');
        if (!citySelect) {
            console.error('City dropdown element not found');
            return;
        }

        // Clear existing options
        citySelect.innerHTML = '<option value="">Select City</option>';

        if (!provinceCode) {
            console.log('No province code provided, city dropdown cleared');
            return;
        }

        // Get cities for this province
        const cities = philippineCities[provinceCode] || philippineCities['default'];
        console.log(`Found ${cities.length} cities for province ${provinceCode}`);

        // Sort cities alphabetically
        cities.sort();

        // Get popular cities for this province
        const popular = popularCities[provinceCode] || [];

        // Add popular cities first if available
        if (popular.length > 0) {
            // Create an optgroup for popular cities
            const popularGroup = document.createElement('optgroup');
            popularGroup.label = '★ Popular Cities';

            popular.forEach(city => {
                if (cities.includes(city)) {
                    const option = document.createElement('option');
                    option.value = city;
                    option.textContent = city;
                    popularGroup.appendChild(option);
                }
            });

            if (popularGroup.children.length > 0) {
                citySelect.appendChild(popularGroup);
            }
        }

        // Add remaining cities
        const otherCities = cities.filter(city => !popular.includes(city));

        // Create an optgroup for other cities
        const otherGroup = document.createElement('optgroup');
        otherGroup.label = 'All Cities';

        otherCities.forEach(city => {
            const option = document.createElement('option');
            option.value = city;
            option.textContent = city;
            otherGroup.appendChild(option);
        });

        if (otherGroup.children.length > 0) {
            citySelect.appendChild(otherGroup);
        }

        console.log(`Loaded ${cities.length} cities for province ${provinceCode}`);

        // Trigger a change event on the city select to update the address if a city is already selected
        if (citySelect.value) {
            // Use setTimeout to ensure the event fires after the function completes
            setTimeout(() => {
                const event = new Event('change');
                citySelect.dispatchEvent(event);
            }, 50);
        }
    }

    // Function to update address based on selected city and province
    function updateAddressField() {
        const provinceSelect = document.getElementById('province');
        const citySelect = document.getElementById('city');
        const addressField = document.getElementById('delivery_address');
        const zipCodeField = document.getElementById('zip_code');

        if (!provinceSelect || !citySelect || !addressField) {
            return;
        }

        // Get selected values
        const provinceCode = provinceSelect.value;
        const provinceName = provinceSelect.options[provinceSelect.selectedIndex]?.text || '';
        const cityName = citySelect.value;

        // Only proceed if both city and province are selected
        if (provinceCode && cityName) {
            // Get current address value
            let currentAddress = addressField.value || '';

            // If address is empty or just the loading text, create a new one
            if (currentAddress === '' || currentAddress === 'Loading address...') {
                addressField.value = cityName + ', ' + provinceName + ', Philippines';
            }

            // Set default zip code if empty
            if (zipCodeField && (!zipCodeField.value || zipCodeField.value === '')) {
                if (provinceCode === '0128') { // Cavite
                    zipCodeField.value = '4114';
                } else if (provinceCode === '0133') { // Laguna
                    zipCodeField.value = '4023';
                } else if (provinceCode === '0174') { // Rizal
                    zipCodeField.value = '1870';
                } else if (provinceCode === '0137') { // Metro Manila
                    zipCodeField.value = '1000';
                } else {
                    zipCodeField.value = '1000'; // Default
                }
            }
        }
    }

    // Add change event listeners to update address
    function setupAddressUpdates() {
        const provinceSelect = document.getElementById('province');
        const citySelect = document.getElementById('city');

        if (provinceSelect) {
            provinceSelect.addEventListener('change', function () {
                // Wait for city dropdown to be populated
                setTimeout(updateAddressField, 100);
            });
        }

        if (citySelect) {
            citySelect.addEventListener('change', updateAddressField);
        }
    }

    // Initialize the province dropdown
    initializeProvinceDropdown();

    // Setup address updates
    setupAddressUpdates();
});
