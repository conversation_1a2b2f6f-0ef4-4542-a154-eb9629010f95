<?php
session_start();
require_once '../../config/config.php';

/** @var PDO $conn */

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['username'];
    $password = $_POST['password'];

    // Password requirements
    $password_requirements = [
        'length' => strlen($password) >= 8,
        'uppercase' => preg_match('/[A-Z]/', $password),
        'lowercase' => preg_match('/[a-z]/', $password),
        'number' => preg_match('/[0-9]/', $password),
        'special' => preg_match('/[!@#$%^&*()\-_=+{};:,<.>]/', $password)
    ];

    $query = "SELECT users.*, roles.name as role FROM users LEFT JOIN roles ON users.role_id = roles.id WHERE email = ?";
    $stmt = $conn->prepare($query);
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user) {
        if (password_verify($password, $user['password'])) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['email'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['department'] = $user['department'];

            // Redirect based on role
            switch ($user['role']) {
                case 'admin':
                    header('Location: ' . BASE_URL . '/app/modules/admin/admin_dashboard.php');
                    break;
                case 'finance':
                    header('Location: ' . BASE_URL . '/app/modules/finance/PAR.php');
                    break;
                case 'inventory':
                    header('Location: ' . BASE_URL . '/app/modules/inventory/inventory.php');
                    break;
                case 'logistics':
                    header('Location: ' . BASE_URL . '/app/modules/logistics/logistics_management.php');
                    break;
                case 'warehouse':
                    header('Location: ' . BASE_URL . '/app/modules/warehouse/warehouse_management.php');
                    break;
                case 'payroll':
                    header('Location: ' . BASE_URL . '/app/modules/payroll/payroll.php');
                    break;
                case 'hr':
                    header('Location: ' . BASE_URL . '/app/modules/user/dashboard_user.php');
                    break;
                case 'procurement':
                    header('Location: ' . BASE_URL . '/app/modules/procurement/procurement.php');
                    break;
                default:
                    header('Location: ' . BASE_URL . '/app/modules/admin/admin_dashboard.php');
            }
            exit();
        } else {
            $error = "Invalid password";
        }
    } else {
        $error = "Invalid email";
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Finance Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-image: url('../uploads/images/eurospice-maze-2.png');
            background-attachment: fixed;
            background-size: cover;
            background-repeat: no-repeat;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-card {
            background: #faf2e9;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .login-header h1 {
            color: rgb(221, 131, 34);
            font-size: 1.8rem;
        }

        .password-requirements {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.5rem;
        }

        .requirement {
            margin-bottom: 0.25rem;
        }

        .requirement.met {
            color: #28a745;
        }

        .requirement.unmet {
            color: #dc3545;
        }
    </style>
</head>

<body>
    <div class="login-card">
        <div class="login-header">
            <img src="uploads/images/eurospice-logo.png" alt="Euro Spice Logo" width="100%">
            <p class="text-muted">Please login to continue</p>
        </div>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <form method="POST" action="" id="loginForm">
            <div class="mb-3">
                <label for="username" class="form-label">Email</label>
                <input type="email" class="form-control" id="username" name="username" required autocomplete="username">
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <input type="password" class="form-control" id="password" name="password" required autocomplete="current-password">
                <div class="password-requirements">
                    <div class="requirement" id="length">• At least 8 characters long</div>
                    <div class="requirement" id="uppercase">• At least one uppercase letter</div>
                    <div class="requirement" id="lowercase">• At least one lowercase letter</div>
                    <div class="requirement" id="number">• At least one number</div>
                    <div class="requirement" id="special">• At least one special character</div>
                </div>
            </div>
            <button type="submit" class="btn btn-primary w-100">Login</button>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;

            // Check each requirement
            document.getElementById('length').className = 'requirement ' + (password.length >= 8 ? 'met' : 'unmet');
            document.getElementById('uppercase').className = 'requirement ' + (/[A-Z]/.test(password) ? 'met' : 'unmet');
            document.getElementById('lowercase').className = 'requirement ' + (/[a-z]/.test(password) ? 'met' : 'unmet');
            document.getElementById('number').className = 'requirement ' + (/[0-9]/.test(password) ? 'met' : 'unmet');
            document.getElementById('special').className = 'requirement ' + (/[!@#$%^&*()\-_=+{};:,<.>]/.test(password) ? 'met' : 'unmet');
        });
    </script>
</body>

</html>