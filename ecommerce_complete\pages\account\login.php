<?php
require_once '../../config/ecommerce_db.php';
require_once '../../config/session.php';

$error = '';
$success = '';

// Check if user is already logged in and not forcing login
$force_login = isset($_GET['force']) && $_GET['force'] == 1;

if (isLoggedIn() && !$force_login) {
    // If user is admin, redirect to cashier
    if (isAdmin()) {
        header("Location: ../cashier/index.php");
    } else {
        // Otherwise redirect to main index
        header("Location: ../../index.php");
    }
    exit();
}

// Check if user was redirected from registration page
if (isset($_GET['registered']) && $_GET['registered'] == 1) {
    $success = "Registration successful! Please login with your new account.";
}

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';

    if (empty($username) || empty($password)) {
        $error = "Username and password are required.";
    } else {
        try {
            // Get user from database (without joining role table)
            $sql = "SELECT * FROM e_users
                    WHERE username = :username OR email = :email
                    LIMIT 1";
            $stmt = executeQuery($sql, [
                ':username' => $username,
                ':email' => $username // Allow login with email too
            ]);

            $user = $stmt->fetch();

            if ($user && password_verify($password, $user['password'])) {
                // Login successful
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['user_role'] = $user['role']; // Use role column
                $_SESSION['first_name'] = $user['first_name'];
                $_SESSION['last_name'] = $user['last_name'];

                // Redirect based on role
                if ($user['role'] === 'admin') { // Check for admin role
                    header("Location: ../cashier/index.php");
                } else {
                    header("Location: ../../index.php");
                }
                exit();
            } else {
                // For debugging
                if ($user) {
                    error_log("Password verification failed for user: " . $username);
                    $error = "Invalid password. Stored hash: " . $user['password'];

                    // Show debug info if in development
                    echo "<!-- Debug info:
                    User found: " . json_encode($user) . "
                    Password verification result: " . (password_verify($password, $user['password']) ? 'true' : 'false') . "
                    -->";
                } else {
                    error_log("User not found: " . $username);
                    $error = "User not found.";

                    // Show debug info if in development
                    echo "<!-- Debug info: User not found for username/email: " . $username . " -->";
                }
            }
        } catch (PDOException $e) {
            error_log("Login error: " . $e->getMessage());
            $error = "Database error. Please try again later.";
        }
    }
}

// Get error message from session if exists
if (isset($_SESSION['error'])) {
    $error = $_SESSION['error'];
    unset($_SESSION['error']);
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Euro Spice | Sign In</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-SgOJa3DmI69IUzQ2PVdRZhwQ+dy64/BUtbMJw1MZ8t5HZApcHrRKUc4W0kG879m7" crossorigin="anonymous">
    <link
        href="https://cdn.prod.website-files.com/66a833f537135b05bc1eaecb/css/maria-bettinas-dynamite-site.webflow.05b59e178.css"
        rel="stylesheet" type="text/css">
    <style>
        body {
            width: 100%;
            height: 100%;
            display: grid;
            place-items: center;
            background-color: #F15B31;
        }

        .container-me {
            width: 400px;
            height: 500px;
            background-color: white;
            padding: 40px;
            border-radius: 100px;
            box-shadow: -30px 30px 0px rgba(0, 0, 0, 0.1);
        }

        p {
            margin-top: 30px;
            margin-bottom: 10px;
        }

        nav a img {
            width: 70px;
            height: 70px;
        }

        .alert {
            margin-bottom: 20px;
            border-radius: 10px;
        }
    </style>
</head>

<body>
    <nav>
        <a href="../../index.php"><img src="../../plugins/fontawesome/svgs/solid/arrow-left.svg" alt="go back to login page" style="width: 70px; height: 70px; filter: invert(1);"></a>
    </nav>
    <div class="container-me">
        <h1 class="text-center">Sign In</h1>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>

        <form method="POST">
            <div class="mb-3">
                <label for="username" class="form-label">Username</label>
                <input type="text" class="form-control" id="username" name="username" required>
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <input type="password" class="form-control" id="password" name="password" required
                    pattern="(?=.*\d).{8,}"
                    title="Password must be at least 8 characters long and contain at least one number.">
            </div>

            <button type="submit" class="btn btn-warning">Sign In</button>
        </form>

        <p>Do not have an Account?</p>

        <a href="register.php<?php echo $force_login ? '?force=1' : ''; ?>" class="btn btn-warning">Sign Up</a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-7z6c4e8b2f"></script>
</body>

</html>