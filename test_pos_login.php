<?php
// Test script to check POS login functionality

// Function to make a POST request with cookies and follow redirects
function post_request($url, $data, $cookies = [])
{
    $options = [
        'http' => [
            'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
            'method'  => 'POST',
            'content' => http_build_query($data)
        ]
    ];

    // Add cookies if provided
    if (!empty($cookies)) {
        $cookie_string = '';
        foreach ($cookies as $key => $value) {
            $cookie_string .= "$key=$value; ";
        }
        $options['http']['header'] .= "Cookie: " . trim($cookie_string) . "\r\n";
    }

    $context  = stream_context_create($options);
    $result = file_get_contents($url, false, $context);

    // Get response headers to check for redirects
    $response_headers = $http_response_header ?? [];

    // Check for redirect
    $redirect_url = null;
    foreach ($response_headers as $header) {
        if (preg_match('/^Location: (.*)$/', $header, $matches)) {
            $redirect_url = trim($matches[1]);
            break;
        }
    }

    // Extract cookies from response
    $cookies_out = [];
    foreach ($response_headers as $header) {
        if (preg_match('/^Set-Cookie: (.*?)=(.*?);/i', $header, $matches)) {
            $cookies_out[$matches[1]] = $matches[2];
        }
    }

    return [
        'content' => $result,
        'headers' => $response_headers,
        'redirect' => $redirect_url,
        'cookies' => $cookies_out
    ];
}

// Function to make a GET request with cookies and follow redirects
function get_request($url, $cookies = [])
{
    $options = [
        'http' => [
            'method' => 'GET'
        ]
    ];

    // Add cookies if provided
    if (!empty($cookies)) {
        $cookie_string = '';
        foreach ($cookies as $key => $value) {
            $cookie_string .= "$key=$value; ";
        }
        $options['http']['header'] = "Cookie: " . trim($cookie_string) . "\r\n";
    }

    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);

    // Get response headers
    $response_headers = $http_response_header ?? [];

    // Check for redirect
    $redirect_url = null;
    foreach ($response_headers as $header) {
        if (preg_match('/^Location: (.*)$/', $header, $matches)) {
            $redirect_url = trim($matches[1]);
            break;
        }
    }

    // Extract cookies from response
    $cookies_out = [];
    foreach ($response_headers as $header) {
        if (preg_match('/^Set-Cookie: (.*?)=(.*?);/i', $header, $matches)) {
            $cookies_out[$matches[1]] = $matches[2];
        }
    }

    return [
        'content' => $result,
        'headers' => $response_headers,
        'redirect' => $redirect_url,
        'cookies' => $cookies_out
    ];
}

// Function to follow redirects
function follow_redirects($url, $base_url, $cookies = [], $max_redirects = 5)
{
    $redirects = 0;
    $final_url = $url;
    $all_cookies = $cookies;

    while ($redirects < $max_redirects) {
        // Handle relative URLs
        if (strpos($final_url, 'http') !== 0) {
            // If it starts with /, it's relative to the domain root
            if (strpos($final_url, '/') === 0) {
                $parsed_base = parse_url($base_url);
                $final_url = $parsed_base['scheme'] . '://' . $parsed_base['host'] . $final_url;
            } else {
                // It's relative to the current directory
                $dir = dirname($base_url);
                $final_url = $dir . '/' . $final_url;
            }
        }

        echo "Requesting URL: $final_url\n";
        $response = get_request($final_url, $all_cookies);

        // Merge cookies
        $all_cookies = array_merge($all_cookies, $response['cookies']);

        if ($response['redirect']) {
            $final_url = $response['redirect'];
            $redirects++;
            echo "Redirected to: $final_url\n";
            // Update base URL for the next redirect
            $base_url = $final_url;
        } else {
            break;
        }
    }

    return [
        'url' => $final_url,
        'content' => $response['content'],
        'cookies' => $all_cookies
    ];
}

// Start the test
echo "Starting POS login test...\n";

// Step 1: Access the login page to get cookies
$login_url = 'http://localhost/finance/ecommerce_complete/pages/account/login.php';
$login_response = get_request($login_url);
$cookies = $login_response['cookies'];

echo "Accessed login page, got cookies: " . json_encode($cookies) . "\n";

// Step 2: Submit the login form with admin credentials
$login_data = [
    'username' => 'admin',  // Assuming admin username
    'password' => 'admin123'  // Assuming admin password
];

$post_response = post_request($login_url, $login_data, $cookies);
$cookies = array_merge($cookies, $post_response['cookies']);

echo "Submitted login form, redirect to: " . ($post_response['redirect'] ?? 'No redirect') . "\n";

// Step 3: Follow redirects to see where we end up
if ($post_response['redirect']) {
    $final_response = follow_redirects($post_response['redirect'], $login_url, $cookies);
    echo "Final URL after redirects: " . $final_response['url'] . "\n";

    // Check if we ended up at the cashier page
    if (strpos($final_response['url'], 'cashier/index.php') !== false) {
        echo "SUCCESS: Redirected to cashier page as expected!\n";

        // Print a snippet of the page content to verify
        $content_snippet = substr($final_response['content'], 0, 500);
        echo "Page content snippet:\n" . $content_snippet . "...\n";
    } else {
        echo "FAILURE: Did not redirect to cashier page. Ended up at: " . $final_response['url'] . "\n";
    }
} else {
    echo "FAILURE: No redirect after login. This suggests login failed.\n";

    // Check for error messages in the response
    if (
        strpos($post_response['content'], 'Invalid') !== false ||
        strpos($post_response['content'], 'error') !== false
    ) {
        echo "Login error detected in response. Check credentials.\n";

        // Print a snippet of the page content to see the error
        $content_snippet = substr($post_response['content'], 0, 500);
        echo "Page content snippet:\n" . $content_snippet . "...\n";
    }
}

echo "Test completed.\n";
