<?php
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Response Checker</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
</head>

<body>
    <div class="container mt-4">
        <h1>API Response Checker</h1>

        <div class="card mb-4">
            <div class="card-header">
                <h5>Test Province API</h5>
            </div>
            <div class="card-body">
                <button id="testProvinceApi" class="btn btn-primary">Test Province API</button>
                <div id="provinceApiResult" class="mt-3"></div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <h5>Test City API</h5>
            </div>
            <div class="card-body">
                <input type="text" id="provinceCode" class="form-control mb-2" placeholder="Province Code (e.g., 0128)">
                <button id="testCityApi" class="btn btn-primary">Test City API</button>
                <button id="testLocalCityData" class="btn btn-success ms-2">Test Local City Data</button>
                <div id="cityApiResult" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Test Province API
            $("#testProvinceApi").click(function() {
                const resultDiv = $("#provinceApiResult");
                resultDiv.html('<div class="alert alert-info">Loading...</div>');

                // Use fetch with text() instead of json()
                fetch('https://psgc.gitlab.io/api/provinces/')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.text(); // Get as text first
                    })
                    .then(text => {
                        try {
                            // Try to parse the text as JSON
                            const data = JSON.parse(text);

                            // Display the first 3 provinces
                            let html = '<div class="alert alert-success">Success! First 3 provinces:</div>';
                            html += '<pre>' + JSON.stringify(data.slice(0, 3), null, 2) + '</pre>';

                            resultDiv.html(html);
                        } catch (e) {
                            // If parsing fails, show the raw response
                            resultDiv.html(`
                                <div class="alert alert-danger">Error parsing JSON: ${e.message}</div>
                                <p>Raw response (first 200 chars):</p>
                                <pre>${text.substring(0, 200)}...</pre>
                            `);
                        }
                    })
                    .catch(error => {
                        resultDiv.html(`<div class="alert alert-danger">Error: ${error.message}</div>`);
                    });
            });

            // Test City API
            $("#testCityApi").click(function() {
                const provinceCode = $("#provinceCode").val().trim();
                const resultDiv = $("#cityApiResult");

                if (!provinceCode) {
                    resultDiv.html('<div class="alert alert-warning">Please enter a province code</div>');
                    return;
                }

                resultDiv.html('<div class="alert alert-info">Loading...</div>');

                // Use fetch with text() instead of json()
                fetch(`https://psgc.gitlab.io/api/provinces/${provinceCode}/cities-municipalities/`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.text(); // Get as text first
                    })
                    .then(text => {
                        try {
                            // Try to parse the text as JSON
                            const data = JSON.parse(text);

                            // Display the first 3 cities
                            let html = '<div class="alert alert-success">Success! First 3 cities/municipalities:</div>';
                            html += '<pre>' + JSON.stringify(data.slice(0, 3), null, 2) + '</pre>';

                            resultDiv.html(html);
                        } catch (e) {
                            // If parsing fails, show the raw response
                            resultDiv.html(`
                                <div class="alert alert-danger">Error parsing JSON: ${e.message}</div>
                                <p>Raw response (first 200 chars):</p>
                                <pre>${text.substring(0, 200)}...</pre>
                            `);
                        }
                    })
                    .catch(error => {
                        resultDiv.html(`<div class="alert alert-danger">Error: ${error.message}</div>`);
                    });
            });

            // Local city data for Philippines
            const philippineCities = {
                // Cavite
                '0128': [
                    'Alfonso', 'Amadeo', 'Bacoor', 'Carmona', 'Cavite City', 'Dasmariñas',
                    'General Emilio Aguinaldo', 'General Mariano Alvarez', 'General Trias',
                    'Imus', 'Indang', 'Kawit', 'Magallanes', 'Maragondon', 'Mendez',
                    'Naic', 'Noveleta', 'Rosario', 'Silang', 'Tagaytay', 'Tanza', 'Ternate', 'Trece Martires'
                ],
                // Laguna
                '0133': [
                    'Alaminos', 'Bay', 'Biñan', 'Cabuyao', 'Calamba', 'Calauan',
                    'Cavinti', 'Famy', 'Kalayaan', 'Liliw', 'Los Baños', 'Luisiana',
                    'Lumban', 'Mabitac', 'Magdalena', 'Majayjay', 'Nagcarlan', 'Paete',
                    'Pagsanjan', 'Pakil', 'Pangil', 'Pila', 'Rizal', 'San Pablo',
                    'San Pedro', 'Santa Cruz', 'Santa Maria', 'Santa Rosa', 'Siniloan', 'Victoria'
                ],
                // Rizal
                '0174': [
                    'Angono', 'Antipolo', 'Baras', 'Binangonan', 'Cainta', 'Cardona',
                    'Jalajala', 'Morong', 'Pililla', 'Rodriguez', 'San Mateo', 'Tanay',
                    'Taytay', 'Teresa'
                ],
                // Metro Manila
                '0137': [
                    'Caloocan', 'Las Piñas', 'Makati', 'Malabon', 'Mandaluyong', 'Manila',
                    'Marikina', 'Muntinlupa', 'Navotas', 'Parañaque', 'Pasay', 'Pasig',
                    'Pateros', 'Quezon City', 'San Juan', 'Taguig', 'Valenzuela'
                ],
                // Default cities for other provinces
                'default': [
                    'City 1', 'City 2', 'City 3', 'City 4', 'City 5'
                ]
            };

            // Test Local City Data
            $("#testLocalCityData").click(function() {
                const provinceCode = $("#provinceCode").val().trim();
                const resultDiv = $("#cityApiResult");

                if (!provinceCode) {
                    resultDiv.html('<div class="alert alert-warning">Please enter a province code</div>');
                    return;
                }

                // Get cities from our local data
                const cities = philippineCities[provinceCode] || philippineCities['default'];

                if (cities && cities.length > 0) {
                    // Display the cities
                    let html = `<div class="alert alert-success">Found ${cities.length} cities in local data for province ${provinceCode}</div>`;
                    html += '<pre>' + JSON.stringify(cities.slice(0, 10), null, 2) + '</pre>';

                    if (cities.length > 10) {
                        html += `<p>...and ${cities.length - 10} more cities</p>`;
                    }

                    resultDiv.html(html);
                } else {
                    resultDiv.html('<div class="alert alert-warning">No cities found for this province code</div>');
                }
            });
        });
    </script>
</body>

</html>