<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set error handling to catch all errors
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    // For AJAX requests, return JSON error
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => "Error: $errstr in $errfile on line $errline"
        ]);
        exit;
    }
    
    // For regular requests, use default error handling
    return false;
});

// Function to remove item from cart
function removeFromCart()
{
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;

    // Make sure we have a valid product ID
    if ($product_id <= 0) {
        $_SESSION['error'] = 'Invalid product ID';
        header('Location: ../inventory/cart.php');
        exit;
    }

    // Remove product from cart
    if (isset($_SESSION['cart'][$product_id])) {
        unset($_SESSION['cart'][$product_id]);
    }

    $_SESSION['success'] = 'Product removed from cart successfully';
    header('Location: ../inventory/cart.php');
    exit;
}

// Helper function to respond with error (for AJAX requests)
function respondWithError($message)
{
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
        // AJAX request
        header('Content-Type: application/json');
        echo json_encode(['status' => 'error', 'message' => $message]);
    } else {
        // Regular form submission
        $_SESSION['error'] = $message;
        header('Location: ../inventory/cart.php');
    }
    exit;
}

// Try to include database connection
try {
    include_once "../../config/config.php";
    
    // Check if connection exists
    if (!isset($conn)) {
        throw new Exception("Database connection failed");
    }
} catch (Exception $e) {
    // For AJAX requests, return JSON error
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => "Database connection error: " . $e->getMessage()
        ]);
        exit;
    } else {
        // For regular requests, set session error and redirect
        $_SESSION['error'] = "Database connection error: " . $e->getMessage();
        header('Location: ../inventory/cart.php');
        exit;
    }
}

// Initialize cart if not exists
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

// Function to get cart item count
function getCartCount()
{
    if (!isset($_SESSION['cart'])) {
        return 0;
    }

    return array_sum($_SESSION['cart']);
}

// Check if it's an AJAX request for cart count
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['action']) && $_GET['action'] === 'count') {
    header('Content-Type: application/json');
    echo json_encode(['status' => 'success', 'cart_count' => getCartCount()]);
    exit;
}

// Handle POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';

    try {
        switch ($action) {
            case 'add':
                addToCart();
                break;

            case 'update':
                updateCart();
                break;

            case 'remove':
                removeFromCart();
                break;

            default:
                // Invalid action
                if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                    // AJAX request
                    header('Content-Type: application/json');
                    echo json_encode(['status' => 'error', 'message' => 'Invalid action']);
                } else {
                    // Regular form submission
                    $_SESSION['error'] = 'Invalid action';
                    header('Location: ../inventory/cart.php');
                }
                exit;
        }
    } catch (Exception $e) {
        // For AJAX requests, return JSON error
        if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'error',
                'message' => "Error: " . $e->getMessage()
            ]);
        } else {
            // For regular requests, set session error and redirect
            $_SESSION['error'] = "Error: " . $e->getMessage();
            header('Location: ../inventory/cart.php');
        }
        exit;
    }
}

// Function to add product to cart
function addToCart()
{
    // For demo purposes, just return success
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
        // AJAX request
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'success',
            'message' => 'Product added to cart successfully',
            'cart_count' => getCartCount() + 1
        ]);
    } else {
        // Regular form submission
        $_SESSION['success'] = 'Product added to cart successfully';
        header('Location: ../inventory/cart.php');
    }
    exit;
}

// Function to update cart quantity
function updateCart()
{
    // For demo purposes, just return success
    $_SESSION['success'] = 'Cart updated successfully';
    header('Location: ../inventory/cart.php');
    exit;
}

// Restore default error handler
restore_error_handler();
?>
