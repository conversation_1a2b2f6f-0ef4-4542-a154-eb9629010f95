{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/popper.js/dist/esm/popper.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "names": ["storeData", "id", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "_window$getComputedSt", "window", "getComputedStyle", "transitionDuration", "transitionDelay", "floatTransitionDuration", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "obj", "nodeType", "emulateTransitionEnd", "duration", "called", "emulatedDuration", "addEventListener", "listener", "removeEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "toString", "call", "match", "toLowerCase", "RegExp", "test", "Error", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "hasAttribute", "onDOMContentLoaded", "callback", "readyState", "mapData", "set", "key", "data", "bs<PERSON><PERSON>", "get", "keyProperties", "delete", "Data", "instance", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "handler", "delegationSelector", "uidEventList", "i", "len", "length", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "replace", "custom", "indexOf", "add<PERSON><PERSON><PERSON>", "oneOff", "_normalizeParams", "handlers", "previousFn", "fn", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "this", "<PERSON><PERSON><PERSON><PERSON>", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "on", "one", "_normalizeParams2", "inNamespace", "isNamespace", "char<PERSON>t", "elementEvent", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "removeNamespacedHandlers", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "$", "isNative", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "NAME", "<PERSON><PERSON>", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "dispose", "closest", "_this", "classList", "remove", "contains", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "jQueryInterface", "each", "handle<PERSON><PERSON><PERSON>", "alertInstance", "getInstance", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "<PERSON><PERSON>", "toggle", "setAttribute", "normalizeData", "val", "Number", "normalizeDataKey", "chr", "button", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "_extends", "dataset", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "SelectorEngine", "matches", "find", "_ref", "documentElement", "concat", "Element", "prototype", "findOne", "children", "_ref2", "filter", "child", "parents", "ancestor", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "EVENT_KEY", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "PointerType", "TOUCH", "PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "direction", "_handleSwipe", "absDeltax", "abs", "_this2", "_keydown", "_addTouchEventListeners", "_this3", "start", "pointerType", "clientX", "touches", "end", "clearTimeout", "itemImg", "e", "add", "move", "tagName", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "from", "_setActiveIndicatorElement", "indicators", "nextIndicator", "elementInterval", "parseInt", "defaultInterval", "directionalClassName", "orderClassName", "_this4", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "carouselInterface", "action", "TypeError", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "Collapse", "_isTransitioning", "_triggerArray", "SELECTOR_DATA_TOGGLE", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "<PERSON><PERSON><PERSON><PERSON>", "timeoutDuration", "longerTimeoutBrowsers", "userAgent", "debounce", "Promise", "resolve", "then", "scheduled", "isFunction", "functionToCheck", "getStyleComputedProperty", "css", "ownerDocument", "defaultView", "getParentNode", "nodeName", "host", "getScrollParent", "_getStyleComputedProp", "overflow", "overflowX", "overflowY", "getReferenceNode", "reference", "referenceNode", "isIE11", "MSInputMethodContext", "documentMode", "isIE10", "isIE", "version", "getOffsetParent", "noOffsetParent", "offsetParent", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "compareDocumentPosition", "DOCUMENT_POSITION_FOLLOWING", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "element1root", "getScroll", "side", "arguments", "undefined", "upperSide", "html", "scrollingElement", "includeScroll", "subtract", "modifier", "bottom", "right", "getBordersSize", "styles", "axis", "sideA", "sideB", "getSize", "computedStyle", "max", "getWindowSizes", "height", "width", "classCallCheck", "createClass", "defineProperties", "props", "descriptor", "enumerable", "configurable", "writable", "protoProps", "staticProps", "assign", "source", "hasOwnProperty", "getClientRect", "offsets", "result", "sizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "getOffsetRectRelativeToArbitraryNode", "fixedPosition", "isHTML", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "getViewportOffsetRectRelativeToArtbitraryNode", "excludeScroll", "relativeOffset", "innerWidth", "innerHeight", "isFixed", "getFixedPositionOffsetParent", "parentElement", "el", "getBoundaries", "popper", "padding", "boundariesElement", "boundaries", "boundariesNode", "_getWindowSizes", "isPaddingNumber", "getArea", "computeAutoPlacement", "placement", "refRect", "rects", "sorted<PERSON>reas", "map", "area", "sort", "a", "b", "filtered<PERSON><PERSON>s", "computedPlacement", "variation", "getReferenceOffsets", "state", "commonOffsetParent", "getOuterSizes", "x", "marginBottom", "y", "marginRight", "getOppositePlacement", "hash", "matched", "getPopperOffsets", "referenceOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "arr", "check", "Array", "runModifiers", "modifiers", "ends", "prop", "findIndex", "cur", "console", "warn", "enabled", "update", "isDestroyed", "arrowStyles", "flipped", "options", "positionFixed", "flip", "originalPlacement", "isCreated", "onUpdate", "onCreate", "isModifierEnabled", "modifierName", "some", "name", "getSupportedPropertyName", "prefixes", "upperProp", "to<PERSON><PERSON><PERSON>", "destroy", "<PERSON><PERSON><PERSON><PERSON>", "disableEventListeners", "removeOnDestroy", "getWindow", "setupEventListeners", "updateBound", "passive", "scrollElement", "attachToScrollParents", "scrollParents", "isBody", "eventsEnabled", "enableEventListeners", "scheduleUpdate", "cancelAnimationFrame", "isNumeric", "n", "isNaN", "isFinite", "setStyles", "unit", "isFirefox", "isModifierRequired", "requestingName", "requested<PERSON><PERSON>", "requesting", "isRequired", "_requesting", "requested", "placements", "validPlacements", "clockwise", "counter", "reverse", "BEHAVIORS", "parseOffset", "basePlacement", "useHeight", "fragments", "frag", "divider", "search", "splitRegex", "ops", "op", "mergeWithPrevious", "reduce", "str", "toValue", "index2", "De<PERSON>ults", "shift", "shiftvariation", "_data$offsets", "isVertical", "shiftOffsets", "preventOverflow", "transformProp", "popperStyles", "transform", "priority", "primary", "escapeWithReference", "secondary", "min", "keepTogether", "opSide", "arrow", "_data$offsets$arrow", "arrowElement", "sideCapitalized", "altSide", "arrowElementSize", "center", "popperMarginSide", "popperBorderSide", "sideValue", "round", "placementOpposite", "flipOrder", "behavior", "step", "refOffsets", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariationByRef", "flipVariations", "flippedVariationByContent", "flipVariationsByContent", "flippedVariation", "getOppositeVariation", "inner", "subtractLength", "bound", "computeStyle", "legacyGpuAccelerationOption", "gpuAcceleration", "offsetParentRect", "shouldRound", "noRound", "v", "referenceWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isVariation", "horizontalToInteger", "verticalToInteger", "getRoundedOffsets", "devicePixelRatio", "prefixedProperty", "invertTop", "invertLeft", "x-placement", "applyStyle", "onLoad", "modifierOptions", "<PERSON><PERSON>", "requestAnimationFrame", "Utils", "global", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "REGEXP_KEYDOWN", "ARROW_UP_KEY", "boundary", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "isActive", "clearMenus", "getParentFromElement", "referenceElement", "_getPopperConfig", "focus", "stopPropagation", "constructor", "_getPlacement", "parentDropdown", "_getOffset", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "dataApiKeydownHandler", "items", "backdrop", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "showEvent", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "transition", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_this5", "_triggerBackdropTransition", "_this6", "_this7", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "_this8", "animate", "createElement", "className", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "_this9", "isModalOverflowing", "scrollHeight", "modalTransitionDuration", "paddingLeft", "paddingRight", "_getScrollbarWidth", "_this10", "actualPadding", "calculatedPadding", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "margin", "scrollDiv", "scrollbarWidth", "_this11", "uriAttrs", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "DefaultAllowlist", "*", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "_loop", "el<PERSON>ame", "attributeList", "allowedAttributes", "attr", "allowedAttributeList", "attrName", "nodeValue", "regExp", "attrRegex", "allowedAttribute", "innerHTML", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "fallbackPlacement", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "DATA_KEY", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "findShadowRoot", "attachShadow", "getRootNode", "root", "ShadowRoot", "isInTheDom", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "complete", "_fixTransition", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "_handlePopperPlacementChange", "CLASS_PREFIX", "eventIn", "eventOut", "_fixTitle", "titleType", "dataAttributes", "dataAttr", "tabClass", "token", "tClass", "popperData", "initConfigAnimation", "Popover", "_getContent", "method", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "item", "pageYOffset", "_getOffsetHeight", "maxScroll", "_activate", "_clear", "queries", "link", "join", "listGroup", "SELECTOR_NAV_LINKS", "navItem", "spy", "Tab", "listElement", "itemSelector", "hideEvent", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdown", "autohide", "Toast", "_clearTimeout"], "mappings": ";;;;;wpBAOA,ICOQA,EACFC,EDWAC,EAAS,SAAAC,GACb,GACEA,GAAUC,KAAKC,MArBH,IAqBSD,KAAKE,gBACnBC,SAASC,eAAeL,IAEjC,OAAOA,GAGHM,EAAc,SAAAC,GAClB,IAAIC,EAAWD,EAAQE,aAAa,eAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAME,EAAWH,EAAQE,aAAa,QAEtCD,EAAWE,GAAyB,MAAbA,EAAmBA,EAASC,OAAS,KAG9D,OAAOH,GAGHI,EAAyB,SAAAL,GAC7B,IAAMC,EAAWF,EAAYC,GAE7B,OAAIC,GACKJ,SAASS,cAAcL,GAAYA,EAGrC,MAGHM,EAAyB,SAAAP,GAC7B,IAAMC,EAAWF,EAAYC,GAE7B,OAAOC,EAAWJ,SAASS,cAAcL,GAAY,MAGjDO,EAAmC,SAAAR,GACvC,IAAKA,EACH,OAAO,EAFyC,IAAAS,EAS9CC,OAAOC,iBAAiBX,GAF1BY,EAPgDH,EAOhDG,mBACAC,EARgDJ,EAQhDI,gBAGIC,EAA0BC,WAAWH,GACrCI,EAAuBD,WAAWF,GAGxC,OAAKC,GAA4BE,GAKjCJ,EAAqBA,EAAmBK,MAAM,KAAK,GACnDJ,EAAkBA,EAAgBI,MAAM,KAAK,GA3Ef,KA6EtBF,WAAWH,GAAsBG,WAAWF,KAP3C,GAULK,EAAuB,SAAAlB,GAC3BA,EAAQmB,cAAc,IAAIC,MAhFL,mBAmFjBC,EAAY,SAAAC,GAAG,OAAKA,EAAI,IAAMA,GAAKC,UAEnCC,EAAuB,SAACxB,EAASyB,GACrC,IAAIC,GAAS,EAEPC,EAAmBF,EADD,EAOxBzB,EAAQ4B,iBA9Fa,iBAyFrB,SAASC,IACPH,GAAS,EACT1B,EAAQ8B,oBA3FW,gBA2FyBD,MAI9CE,YAAW,WACJL,GACHR,EAAqBlB,KAEtB2B,IAGCK,EAAkB,SAACC,EAAeC,EAAQC,GAC9CC,OAAOC,KAAKF,GAAaG,SAAQ,SAAAC,GAC/B,IArGWjB,EAqGLkB,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASpB,EAAUoB,GACnC,UAvGAnB,OADSA,EAyGFmB,GAvGT,GAAUnB,EAGL,GAAGqB,SAASC,KAAKtB,GAAKuB,MAAM,eAAe,GAAGC,cAsGnD,IAAK,IAAIC,OAAOP,GAAeQ,KAAKN,GAClC,MAAM,IAAIO,MACLhB,EAAciB,cAAdjB,aACQM,EADX,oBACuCG,EADpCT,wBAEmBO,EAFtB,UAOFW,EAAY,SAAAnD,GAChB,IAAKA,EACH,OAAO,EAGT,GAAIA,EAAQoD,OAASpD,EAAQqD,YAAcrD,EAAQqD,WAAWD,MAAO,CACnE,IAAME,EAAe3C,iBAAiBX,GAChCuD,EAAkB5C,iBAAiBX,EAAQqD,YAEjD,MAAgC,SAAzBC,EAAaE,SACU,SAA5BD,EAAgBC,SACY,WAA5BF,EAAaG,WAGjB,OAAO,GA0BHC,EAAO,WAAA,OAAM,cAEbC,EAAS,SAAA3D,GAAO,OAAIA,EAAQ4D,cAE5BC,EAAY,WAAM,IACdC,EAAWpD,OAAXoD,OAER,OAAIA,IAAWjE,SAASkE,KAAKC,aAAa,kBACjCF,EAGF,MAGHG,EAAqB,SAAAC,GACG,YAAxBrE,SAASsE,WACXtE,SAAS+B,iBAAiB,mBAAoBsC,GAE9CA,KC7KEE,GACE9E,EAAY,GACdC,EAAK,EACF,CACL8E,IADK,SACDrE,EAASsE,EAAKC,QACa,IAAlBvE,EAAQwE,QACjBxE,EAAQwE,MAAQ,CACdF,IAAAA,EACA/E,GAAAA,GAEFA,KAGFD,EAAUU,EAAQwE,MAAMjF,IAAMgF,GAEhCE,IAZK,SAYDzE,EAASsE,GACX,IAAKtE,QAAoC,IAAlBA,EAAQwE,MAC7B,OAAO,KAGT,IAAME,EAAgB1E,EAAQwE,MAC9B,OAAIE,EAAcJ,MAAQA,EACjBhF,EAAUoF,EAAcnF,IAG1B,MAEToF,OAxBK,SAwBE3E,EAASsE,GACd,QAA6B,IAAlBtE,EAAQwE,MAAnB,CAIA,IAAME,EAAgB1E,EAAQwE,MAC1BE,EAAcJ,MAAQA,WACjBhF,EAAUoF,EAAcnF,WACxBS,EAAQwE,WAMjBI,EAAO,SACHC,EAAUP,EAAKC,GACrBH,EAAQC,IAAIQ,EAAUP,EAAKC,IAFzBK,EAAO,SAIHC,EAAUP,GAChB,OAAOF,EAAQK,IAAII,EAAUP,IAL3BM,EAAO,SAOAC,EAAUP,GACnBF,EAAQO,OAAOE,EAAUP,IC/CvBQ,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GAClBC,EAAW,EACTC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,EAAe,CACnB,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,UASF,SAASC,EAAYvF,EAASwF,GAC5B,OAAQA,GAAUA,EAAP,KAAeN,KAAiBlF,EAAQkF,UAAYA,IAGjE,SAASO,EAASzF,GAChB,IAAMwF,EAAMD,EAAYvF,GAKxB,OAHAA,EAAQkF,SAAWM,EACnBP,EAAcO,GAAOP,EAAcO,IAAQ,GAEpCP,EAAcO,GAsCvB,SAASE,EAAYC,EAAQC,EAASC,QAA2B,IAA3BA,IAAAA,EAAqB,MAGzD,IAFA,IAAMC,EAAe1D,OAAOC,KAAKsD,GAExBI,EAAI,EAAGC,EAAMF,EAAaG,OAAQF,EAAIC,EAAKD,IAAK,CACvD,IAAMG,EAAQP,EAAOG,EAAaC,IAElC,GAAIG,EAAMC,kBAAoBP,GAAWM,EAAML,qBAAuBA,EACpE,OAAOK,EAIX,OAAO,KAGT,SAASE,EAAgBC,EAAmBT,EAASU,GACnD,IAAMC,EAAgC,iBAAZX,EACpBO,EAAkBI,EAAaD,EAAeV,EAGhDY,EAAYH,EAAkBI,QAAQ1B,EAAgB,IACpD2B,EAASvB,EAAaqB,GAY5B,OAVIE,IACFF,EAAYE,GAGGpB,EAAaqB,QAAQH,IAAc,IAGlDA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASI,EAAW5G,EAASqG,EAAmBT,EAASU,EAAcO,GACrE,GAAiC,iBAAtBR,GAAmCrG,EAA9C,CAIK4F,IACHA,EAAUU,EACVA,EAAe,MAP4D,IAAAQ,EAU5BV,EAAgBC,EAAmBT,EAASU,GAAtFC,EAVsEO,EAAA,GAU1DX,EAV0DW,EAAA,GAUzCN,EAVyCM,EAAA,GAWvEnB,EAASF,EAASzF,GAClB+G,EAAWpB,EAAOa,KAAeb,EAAOa,GAAa,IACrDQ,EAAatB,EAAYqB,EAAUZ,EAAiBI,EAAaX,EAAU,MAEjF,GAAIoB,EACFA,EAAWH,OAASG,EAAWH,QAAUA,MAD3C,CAMA,IAAMrB,EAAMD,EAAYY,EAAiBE,EAAkBI,QAAQ3B,EAAgB,KAC7EmC,EAAKV,EAhFb,SAAoCvG,EAASC,EAAUgH,GACrD,OAAO,SAASrB,EAAQM,GAGtB,IAFA,IAAMgB,EAAclH,EAAQmH,iBAAiBlH,GAElCmH,EAAWlB,EAAXkB,OAAkBA,GAAUA,IAAWC,KAAMD,EAASA,EAAO/D,WACtE,IAAK,IAAI0C,EAAImB,EAAYjB,OAAQF,KAC/B,GAAImB,EAAYnB,KAAOqB,EAOrB,OANAlB,EAAMoB,eAAiBF,EAEnBxB,EAAQiB,QACVU,EAAaC,IAAIxH,EAASkG,EAAMuB,KAAMR,GAGjCA,EAAGS,MAAMN,EAAQ,CAAClB,IAM/B,OAAO,MA8DPyB,CAA2B3H,EAAS4F,EAASU,GA7FjD,SAA0BtG,EAASiH,GACjC,OAAO,SAASrB,EAAQM,GAOtB,OANAA,EAAMoB,eAAiBtH,EAEnB4F,EAAQiB,QACVU,EAAaC,IAAIxH,EAASkG,EAAMuB,KAAMR,GAGjCA,EAAGS,MAAM1H,EAAS,CAACkG,KAsF1B0B,CAAiB5H,EAAS4F,GAE5BqB,EAAGpB,mBAAqBU,EAAaX,EAAU,KAC/CqB,EAAGd,gBAAkBA,EACrBc,EAAGJ,OAASA,EACZI,EAAG/B,SAAWM,EACduB,EAASvB,GAAOyB,EAEhBjH,EAAQ4B,iBAAiB4E,EAAWS,EAAIV,KAG1C,SAASsB,EAAc7H,EAAS2F,EAAQa,EAAWZ,EAASC,GAC1D,IAAMoB,EAAKvB,EAAYC,EAAOa,GAAYZ,EAASC,GAE9CoB,IAILjH,EAAQ8B,oBAAoB0E,EAAWS,EAAIa,QAAQjC,WAC5CF,EAAOa,GAAWS,EAAG/B,WAe9B,IAAMqC,EAAe,CACnBQ,GADmB,SAChB/H,EAASkG,EAAON,EAASU,GAC1BM,EAAW5G,EAASkG,EAAON,EAASU,GAAc,IAGpD0B,IALmB,SAKfhI,EAASkG,EAAON,EAASU,GAC3BM,EAAW5G,EAASkG,EAAON,EAASU,GAAc,IAGpDkB,IATmB,SASfxH,EAASqG,EAAmBT,EAASU,GACvC,GAAiC,iBAAtBD,GAAmCrG,EAA9C,CADqD,IAAAiI,EAKJ7B,EAAgBC,EAAmBT,EAASU,GAAtFC,EAL8C0B,EAAA,GAKlC9B,EALkC8B,EAAA,GAKjBzB,EALiByB,EAAA,GAM/CC,EAAc1B,IAAcH,EAC5BV,EAASF,EAASzF,GAClBmI,EAA8C,MAAhC9B,EAAkB+B,OAAO,GAE7C,QAA+B,IAApBjC,EAAX,CAUIgC,GACF/F,OAAOC,KAAKsD,GAAQrD,SAAQ,SAAA+F,IA1ClC,SAAkCrI,EAAS2F,EAAQa,EAAW8B,GAC5D,IAAMC,EAAoB5C,EAAOa,IAAc,GAE/CpE,OAAOC,KAAKkG,GAAmBjG,SAAQ,SAAAkG,GACrC,GAAIA,EAAW7B,QAAQ2B,IAAc,EAAG,CACtC,IAAMpC,EAAQqC,EAAkBC,GAEhCX,EAAc7H,EAAS2F,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,wBAoCrE4C,CAAyBzI,EAAS2F,EAAQ0C,EAAchC,EAAkBqC,MAAM,OAIpF,IAAMH,EAAoB5C,EAAOa,IAAc,GAC/CpE,OAAOC,KAAKkG,GAAmBjG,SAAQ,SAAAqG,GACrC,IAAMH,EAAaG,EAAYlC,QAAQzB,EAAe,IAEtD,IAAKkD,GAAe7B,EAAkBM,QAAQ6B,IAAe,EAAG,CAC9D,IAAMtC,EAAQqC,EAAkBI,GAEhCd,EAAc7H,EAAS2F,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,4BAvB3E,CAEE,IAAKF,IAAWA,EAAOa,GACrB,OAGFqB,EAAc7H,EAAS2F,EAAQa,EAAWL,EAAiBI,EAAaX,EAAU,SAsBtFgD,QA/CmB,SA+CX5I,EAASkG,EAAO2C,GACtB,GAAqB,iBAAV3C,IAAuBlG,EAChC,OAAO,KAGT,IAKI8I,EALEC,EAAIlF,IACJ2C,EAAYN,EAAMO,QAAQ1B,EAAgB,IAC1CmD,EAAchC,IAAUM,EACxBwC,EAAW1D,EAAaqB,QAAQH,IAAc,EAGhDyC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EACnBC,EAAM,KA4CV,OA1CIlB,GAAea,IACjBD,EAAcC,EAAE3H,MAAM8E,EAAO2C,GAE7BE,EAAE/I,GAAS4I,QAAQE,GACnBG,GAAWH,EAAYO,uBACvBH,GAAkBJ,EAAYQ,gCAC9BH,EAAmBL,EAAYS,sBAG7BP,GACFI,EAAMvJ,SAAS2J,YAAY,eACvBC,UAAUjD,EAAWyC,GAAS,GAElCG,EAAM,IAAIM,YAAYxD,EAAO,CAC3B+C,QAAAA,EACAU,YAAY,SAKI,IAATd,GACTzG,OAAOC,KAAKwG,GAAMvG,SAAQ,SAAAgC,GACxBlC,OAAOwH,eAAeR,EAAK9E,EAAK,CAC9BG,IAD8B,WAE5B,OAAOoE,EAAKvE,SAMhB6E,GACFC,EAAIS,iBAGFX,GACFlJ,EAAQmB,cAAciI,GAGpBA,EAAID,uBAA2C,IAAhBL,GACjCA,EAAYe,iBAGPT,IC7SLU,EAAO,QAsBPC,EAAAA,WACJ,SAAAA,EAAY/J,GACVqH,KAAK2C,SAAWhK,EAEZqH,KAAK2C,UACPpF,EAAa5E,EAzBF,WAyBqBqH,iCAYpC4C,MAAA,SAAMjK,GACJ,IAAMkK,EAAclK,EAAUqH,KAAK8C,gBAAgBnK,GAAWqH,KAAK2C,SAC7DI,EAAc/C,KAAKgD,mBAAmBH,GAExB,OAAhBE,GAAwBA,EAAYjB,kBAIxC9B,KAAKiD,eAAeJ,MAGtBK,QAAA,WACE3F,EAAgByC,KAAK2C,SAjDR,YAkDb3C,KAAK2C,SAAW,QAKlBG,gBAAA,SAAgBnK,GACd,OAAOO,EAAuBP,IAAYA,EAAQwK,QAAR,aAG5CH,mBAAA,SAAmBrK,GACjB,OAAOuH,EAAaqB,QAAQ5I,EAtDf,qBAyDfsK,eAAA,SAAetK,GAAS,IAAAyK,EAAApD,KAGtB,GAFArH,EAAQ0K,UAAUC,OApDC,QAsDd3K,EAAQ0K,UAAUE,SAvDJ,QAuDnB,CAKA,IAAMhK,EAAqBJ,EAAiCR,GAE5DuH,EAAaS,IAAIhI,EH1FE,iBG0FuB,WAAA,OAAMyK,EAAKI,gBAAgB7K,MACrEwB,EAAqBxB,EAASY,QAP5ByG,KAAKwD,gBAAgB7K,MAUzB6K,gBAAA,SAAgB7K,GACVA,EAAQqD,YACVrD,EAAQqD,WAAWyH,YAAY9K,GAGjCuH,EAAaqB,QAAQ5I,EA3EP,sBAgFT+K,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACf,IAAIzG,EAAOK,EAAayC,KAzFb,YA2FN9C,IACHA,EAAO,IAAIwF,EAAM1C,OAGJ,UAAXnF,GACFqC,EAAKrC,GAAQmF,YAKZ4D,cAAP,SAAqBC,GACnB,OAAO,SAAUhF,GACXA,GACFA,EAAM2D,iBAGRqB,EAAcjB,MAAM5C,UAIjB8D,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAhHP,qDAgCb,MAjCY,qBAqBV+J,GAqGNxC,EAAaQ,GAAGlI,SAjHU,0BAJD,yBAqHyCkK,EAAMkB,cAAc,IAAIlB,IAS1F9F,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAG6C,GAChCf,EAAE9B,GAAG6C,GAAQC,EAAMgB,gBACnBhC,EAAE9B,GAAG6C,GAAMuB,YAActB,EACzBhB,EAAE9B,GAAG6C,GAAMwB,WAAa,WAEtB,OADAvC,EAAE9B,GAAG6C,GAAQsB,EACNrB,EAAMgB,qBCpJnB,IAkBMQ,EAAAA,WACJ,SAAAA,EAAYvL,GACVqH,KAAK2C,SAAWhK,EAChB4E,EAAa5E,EAnBA,YAmBmBqH,iCAWlCmE,OAAA,WAEEnE,KAAK2C,SAASyB,aAAa,eAAgBpE,KAAK2C,SAASU,UAAUc,OA5B7C,cA+BxBjB,QAAA,WACE3F,EAAgByC,KAAK2C,SApCR,aAqCb3C,KAAK2C,SAAW,QAKXe,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACf,IAAIzG,EAAOK,EAAayC,KA5Cb,aA8CN9C,IACHA,EAAO,IAAIgH,EAAOlE,OAGL,WAAXnF,GACFqC,EAAKrC,WAKJiJ,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAzDP,sDAyBb,MA1BY,qBAiBVuL,GC5BN,SAASG,EAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQC,OAAOD,GAAKhJ,WACfiJ,OAAOD,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASE,EAAiBvH,GACxB,OAAOA,EAAImC,QAAQ,UAAU,SAAAqF,GAAG,MAAA,IAAQA,EAAIhJ,iBD0D9CyE,EAAaQ,GAAGlI,SA3DU,2BAFG,0BA6DyC,SAAAqG,GACpEA,EAAM2D,iBAEN,IAAMkC,EAAS7F,EAAMkB,OAAOoD,QAhED,0BAkEvBjG,EAAOK,EAAamH,EAxET,aAyEVxH,IACHA,EAAO,IAAIgH,EAAOQ,IAGpBxH,EAAKiH,YAUPvH,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAF,OAC3B8B,EAAE9B,GAAF,OAAasE,EAAOR,gBACpBhC,EAAE9B,GAAF,OAAWoE,YAAcE,EAEzBxC,EAAE9B,GAAF,OAAWqE,WAAa,WAEtB,OADAvC,EAAE9B,GAAF,OAAamE,EACNG,EAAOR,qBCrFpB,IAAMiB,EAAc,CAClBC,iBADkB,SACDjM,EAASsE,EAAK7B,GAC7BzC,EAAQyL,aAAR,QAA6BI,EAAiBvH,GAAQ7B,IAGxDyJ,oBALkB,SAKElM,EAASsE,GAC3BtE,EAAQmM,gBAAR,QAAgCN,EAAiBvH,KAGnD8H,kBATkB,SASApM,GAChB,IAAKA,EACH,MAAO,GAGT,IAAMqM,EAAUC,EAAA,GACXtM,EAAQuM,SAOb,OAJAnK,OAAOC,KAAKgK,GAAY/J,SAAQ,SAAAgC,GAC9B+H,EAAW/H,GAAOoH,EAAcW,EAAW/H,OAGtC+H,GAGTG,iBAzBkB,SAyBDxM,EAASsE,GACxB,OAAOoH,EAAc1L,EAAQE,aAAR,QAA6B2L,EAAiBvH,MAGrEmI,OA7BkB,SA6BXzM,GACL,IAAM0M,EAAO1M,EAAQ2M,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAM/M,SAASkE,KAAK8I,UAC9BC,KAAMJ,EAAKI,KAAOjN,SAASkE,KAAKgJ,aAIpCC,SAtCkB,SAsCThN,GACP,MAAO,CACL4M,IAAK5M,EAAQiN,UACbH,KAAM9M,EAAQkN,cCzDdC,EAAiB,CACrBC,QADqB,SACbpN,EAASC,GACf,OAAOD,EAAQoN,QAAQnN,IAGzBoN,KALqB,SAKhBpN,EAAUD,GAAoC,IAAAsN,EACjD,YADiD,IAApCtN,IAAAA,EAAUH,SAAS0N,kBACzBD,EAAA,IAAGE,OAAH9F,MAAA4F,EAAaG,QAAQC,UAAUvG,iBAAiBvE,KAAK5C,EAASC,KAGvE0N,QATqB,SASb1N,EAAUD,GAChB,YADoD,IAApCA,IAAAA,EAAUH,SAAS0N,iBAC5BE,QAAQC,UAAUpN,cAAcsC,KAAK5C,EAASC,IAGvD2N,SAbqB,SAaZ5N,EAASC,GAAU,IAAA4N,EACpBD,GAAWC,EAAA,IAAGL,OAAH9F,MAAAmG,EAAa7N,EAAQ4N,UAEtC,OAAOA,EAASE,QAAO,SAAAC,GAAK,OAAIA,EAAMX,QAAQnN,OAGhD+N,QAnBqB,SAmBbhO,EAASC,GAKf,IAJA,IAAM+N,EAAU,GAEZC,EAAWjO,EAAQqD,WAEhB4K,GAAYA,EAAS1M,WAAa2M,KAAKC,cA1BhC,IA0BgDF,EAAS1M,UACjE8F,KAAK+F,QAAQa,EAAUhO,IACzB+N,EAAQI,KAAKH,GAGfA,EAAWA,EAAS5K,WAGtB,OAAO2K,GAGTK,KAnCqB,SAmChBrO,EAASC,GAGZ,IAFA,IAAIqO,EAAWtO,EAAQuO,uBAEhBD,GAAU,CACf,GAAIA,EAASlB,QAAQnN,GACnB,MAAO,CAACqO,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAjDqB,SAiDhBxO,EAASC,GAGZ,IAFA,IAAIuO,EAAOxO,EAAQyO,mBAEZD,GAAM,CACX,GAAInH,KAAK+F,QAAQoB,EAAMvO,GACrB,MAAO,CAACuO,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,KC7CL3E,EAAO,WAGP4E,EAAS,eAQTC,EAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,EAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAwCHE,EAAc,CAClBC,MAAO,QACPC,IAAK,OAQDC,EAAAA,WACJ,SAAAA,EAAYtP,EAASkC,GACnBmF,KAAKkI,OAAS,KACdlI,KAAKmI,UAAY,KACjBnI,KAAKoI,eAAiB,KACtBpI,KAAKqI,WAAY,EACjBrI,KAAKsI,YAAa,EAClBtI,KAAKuI,aAAe,KACpBvI,KAAKwI,YAAc,EACnBxI,KAAKyI,YAAc,EAEnBzI,KAAK0I,QAAU1I,KAAK2I,WAAW9N,GAC/BmF,KAAK2C,SAAWhK,EAChBqH,KAAK4I,mBAAqB9C,EAAeQ,QA3BjB,uBA2B8CtG,KAAK2C,UAC3E3C,KAAK6I,gBAAkB,iBAAkBrQ,SAAS0N,iBAAmB4C,UAAUC,eAAiB,EAChG/I,KAAKgJ,cAAgBvI,QAAQpH,OAAO4P,cAEpCjJ,KAAKkJ,qBACL3L,EAAa5E,EA5FA,cA4FmBqH,iCAelCmH,KAAA,WACOnH,KAAKsI,YACRtI,KAAKmJ,OAlFY,WAsFrBC,gBAAA,YAGO5Q,SAAS6Q,QAAUvN,EAAUkE,KAAK2C,WACrC3C,KAAKmH,UAITH,KAAA,WACOhH,KAAKsI,YACRtI,KAAKmJ,OA/FY,WAmGrBzB,MAAA,SAAM7I,GACCA,IACHmB,KAAKqI,WAAY,GAGfvC,EAAeQ,QAzEI,2CAyEwBtG,KAAK2C,YAClD9I,EAAqBmG,KAAK2C,UAC1B3C,KAAKsJ,OAAM,IAGbC,cAAcvJ,KAAKmI,WACnBnI,KAAKmI,UAAY,QAGnBmB,MAAA,SAAMzK,GACCA,IACHmB,KAAKqI,WAAY,GAGfrI,KAAKmI,YACPoB,cAAcvJ,KAAKmI,WACnBnI,KAAKmI,UAAY,MAGfnI,KAAK0I,SAAW1I,KAAK0I,QAAQnB,WAAavH,KAAKqI,YACjDrI,KAAKwJ,kBAELxJ,KAAKmI,UAAYsB,aACdjR,SAASkR,gBAAkB1J,KAAKoJ,gBAAkBpJ,KAAKmH,MAAMwC,KAAK3J,MACnEA,KAAK0I,QAAQnB,cAKnBqC,GAAA,SAAGC,GAAO,IAAAzG,EAAApD,KACRA,KAAKoI,eAAiBtC,EAAeQ,QA1GZ,wBA0G0CtG,KAAK2C,UACxE,IAAMmH,EAAc9J,KAAK+J,cAAc/J,KAAKoI,gBAE5C,KAAIyB,EAAQ7J,KAAKkI,OAAOtJ,OAAS,GAAKiL,EAAQ,GAI9C,GAAI7J,KAAKsI,WACPpI,EAAaS,IAAIX,KAAK2C,SAzIZ,oBAyIkC,WAAA,OAAMS,EAAKwG,GAAGC,UAD5D,CAKA,GAAIC,IAAgBD,EAGlB,OAFA7J,KAAK0H,aACL1H,KAAKsJ,QAIP,IAAMU,EAAYH,EAAQC,EAzJP,OACA,OA4JnB9J,KAAKmJ,OAAOa,EAAWhK,KAAKkI,OAAO2B,QAGrC3G,QAAA,WACEhD,EAAaC,IAAIH,KAAK2C,SAAU0E,GAChC9J,EAAgByC,KAAK2C,SA7LR,eA+Lb3C,KAAKkI,OAAS,KACdlI,KAAK0I,QAAU,KACf1I,KAAK2C,SAAW,KAChB3C,KAAKmI,UAAY,KACjBnI,KAAKqI,UAAY,KACjBrI,KAAKsI,WAAa,KAClBtI,KAAKoI,eAAiB,KACtBpI,KAAK4I,mBAAqB,QAK5BD,WAAA,SAAW9N,GAMT,OALAA,EAAMoK,EAAA,GACDqC,EACAzM,GAELF,EAAgB8H,EAAM5H,EAAQgN,GACvBhN,KAGToP,aAAA,WACE,IAAMC,EAAY7R,KAAK8R,IAAInK,KAAKyI,aAEhC,KAAIyB,GAhNgB,IAgNpB,CAIA,IAAMF,EAAYE,EAAYlK,KAAKyI,YAEnCzI,KAAKyI,YAAc,EAGfuB,EAAY,GACdhK,KAAKgH,OAIHgD,EAAY,GACdhK,KAAKmH,WAIT+B,mBAAA,WAAqB,IAAAkB,EAAApK,KACfA,KAAK0I,QAAQlB,UACftH,EAAaQ,GAAGV,KAAK2C,SA1MR,uBA0MiC,SAAA9D,GAAK,OAAIuL,EAAKC,SAASxL,MAG5C,UAAvBmB,KAAK0I,QAAQhB,QACfxH,EAAaQ,GAAGV,KAAK2C,SA7ML,0BA6MiC,SAAA9D,GAAK,OAAIuL,EAAK1C,MAAM7I,MACrEqB,EAAaQ,GAAGV,KAAK2C,SA7ML,0BA6MiC,SAAA9D,GAAK,OAAIuL,EAAKd,MAAMzK,OAGnEmB,KAAK0I,QAAQd,OAAS5H,KAAK6I,iBAC7B7I,KAAKsK,6BAITA,wBAAA,WAA0B,IAAAC,EAAAvK,KAClBwK,EAAQ,SAAA3L,GACR0L,EAAKvB,eAAiBlB,EAAYjJ,EAAM4L,YAAY5O,eACtD0O,EAAK/B,YAAc3J,EAAM6L,QACfH,EAAKvB,gBACfuB,EAAK/B,YAAc3J,EAAM8L,QAAQ,GAAGD,UAalCE,EAAM,SAAA/L,GACN0L,EAAKvB,eAAiBlB,EAAYjJ,EAAM4L,YAAY5O,iBACtD0O,EAAK9B,YAAc5J,EAAM6L,QAAUH,EAAK/B,aAG1C+B,EAAKN,eACsB,UAAvBM,EAAK7B,QAAQhB,QASf6C,EAAK7C,QACD6C,EAAKhC,cACPsC,aAAaN,EAAKhC,cAGpBgC,EAAKhC,aAAe7N,YAAW,SAAAmE,GAAK,OAAI0L,EAAKjB,MAAMzK,KAzR5B,IAyR6D0L,EAAK7B,QAAQnB,YAIrGzB,EAAeE,KAzOO,qBAyOiBhG,KAAK2C,UAAU1H,SAAQ,SAAA6P,GAC5D5K,EAAaQ,GAAGoK,EA1PA,yBA0P2B,SAAAC,GAAC,OAAIA,EAAEvI,uBAGhDxC,KAAKgJ,eACP9I,EAAaQ,GAAGV,KAAK2C,SAhQJ,2BAgQiC,SAAA9D,GAAK,OAAI2L,EAAM3L,MACjEqB,EAAaQ,GAAGV,KAAK2C,SAhQN,yBAgQiC,SAAA9D,GAAK,OAAI+L,EAAI/L,MAE7DmB,KAAK2C,SAASU,UAAU2H,IAtPG,mBAwP3B9K,EAAaQ,GAAGV,KAAK2C,SAxQL,0BAwQiC,SAAA9D,GAAK,OAAI2L,EAAM3L,MAChEqB,EAAaQ,GAAGV,KAAK2C,SAxQN,yBAwQiC,SAAA9D,GAAK,OA5C1C,SAAAA,GAEPA,EAAM8L,SAAW9L,EAAM8L,QAAQ/L,OAAS,EAC1C2L,EAAK9B,YAAc,EAEnB8B,EAAK9B,YAAc5J,EAAM8L,QAAQ,GAAGD,QAAUH,EAAK/B,YAuCIyC,CAAKpM,MAC9DqB,EAAaQ,GAAGV,KAAK2C,SAxQP,wBAwQiC,SAAA9D,GAAK,OAAI+L,EAAI/L,UAIhEwL,SAAA,SAASxL,GACP,IAAI,kBAAkBlD,KAAKkD,EAAMkB,OAAOmL,SAIxC,OAAQrM,EAAM5B,KACZ,IArTiB,YAsTf4B,EAAM2D,iBACNxC,KAAKgH,OACL,MACF,IAxTkB,aAyThBnI,EAAM2D,iBACNxC,KAAKmH,WAMX4C,cAAA,SAAcpR,GAKZ,OAJAqH,KAAKkI,OAASvP,GAAWA,EAAQqD,WAC/B8J,EAAeE,KA9QC,iBA8QmBrN,EAAQqD,YAC3C,GAEKgE,KAAKkI,OAAO5I,QAAQ3G,MAG7BwS,oBAAA,SAAoBnB,EAAWoB,GAC7B,IAAMC,EAnTa,SAmTKrB,EAClBsB,EAnTa,SAmTKtB,EAClBF,EAAc9J,KAAK+J,cAAcqB,GACjCG,EAAgBvL,KAAKkI,OAAOtJ,OAAS,EAI3C,IAHuB0M,GAAmC,IAAhBxB,GACjBuB,GAAmBvB,IAAgByB,KAEtCvL,KAAK0I,QAAQf,KACjC,OAAOyD,EAGT,IACMI,GAAa1B,GA9TA,SA6TLE,GAAgC,EAAI,IACRhK,KAAKkI,OAAOtJ,OAEtD,OAAsB,IAAf4M,EACLxL,KAAKkI,OAAOlI,KAAKkI,OAAOtJ,OAAS,GACjCoB,KAAKkI,OAAOsD,MAGhBC,mBAAA,SAAmBC,EAAeC,GAChC,IAAMC,EAAc5L,KAAK+J,cAAc2B,GACjCG,EAAY7L,KAAK+J,cAAcjE,EAAeQ,QA3S3B,wBA2SyDtG,KAAK2C,WAEvF,OAAOzC,EAAaqB,QAAQvB,KAAK2C,SArUpB,oBAqU2C,CACtD+I,cAAAA,EACA1B,UAAW2B,EACXG,KAAMD,EACNjC,GAAIgC,OAIRG,2BAAA,SAA2BpT,GACzB,GAAIqH,KAAK4I,mBAAoB,CAE3B,IADA,IAAMoD,EAAalG,EAAeE,KAxThB,UAwTsChG,KAAK4I,oBACpDlK,EAAI,EAAGA,EAAIsN,EAAWpN,OAAQF,IACrCsN,EAAWtN,GAAG2E,UAAUC,OAlUN,UAqUpB,IAAM2I,EAAgBjM,KAAK4I,mBAAmBrC,SAC5CvG,KAAK+J,cAAcpR,IAGjBsT,GACFA,EAAc5I,UAAU2H,IA1UN,cA+UxBxB,gBAAA,WACE,IAAM7Q,EAAUqH,KAAKoI,gBAAkBtC,EAAeQ,QAvU7B,wBAuU2DtG,KAAK2C,UAEzF,GAAKhK,EAAL,CAIA,IAAMuT,EAAkBC,SAASxT,EAAQE,aAAa,iBAAkB,IAEpEqT,GACFlM,KAAK0I,QAAQ0D,gBAAkBpM,KAAK0I,QAAQ0D,iBAAmBpM,KAAK0I,QAAQnB,SAC5EvH,KAAK0I,QAAQnB,SAAW2E,GAExBlM,KAAK0I,QAAQnB,SAAWvH,KAAK0I,QAAQ0D,iBAAmBpM,KAAK0I,QAAQnB,aAIzE4B,OAAA,SAAOa,EAAWrR,GAAS,IASrB0T,EACAC,EACAX,EAXqBY,EAAAvM,KACnBoL,EAAgBtF,EAAeQ,QAxVZ,wBAwV0CtG,KAAK2C,UAClE6J,EAAqBxM,KAAK+J,cAAcqB,GACxCqB,EAAc9T,GAAYyS,GAC9BpL,KAAKmL,oBAAoBnB,EAAWoB,GAEhCsB,EAAmB1M,KAAK+J,cAAc0C,GACtCE,EAAYlM,QAAQT,KAAKmI,WAgB/B,GA3YmB,SAiYf6B,GACFqC,EA3WkB,qBA4WlBC,EA3WkB,qBA4WlBX,EAlYiB,SAoYjBU,EAhXmB,sBAiXnBC,EA9WkB,qBA+WlBX,EArYkB,SAwYhBc,GAAeA,EAAYpJ,UAAUE,SAvXnB,UAwXpBvD,KAAKsI,YAAa,OAKpB,IADmBtI,KAAKyL,mBAAmBgB,EAAad,GACzC7J,kBAIVsJ,GAAkBqB,EAAvB,CAcA,GATAzM,KAAKsI,YAAa,EAEdqE,GACF3M,KAAK0H,QAGP1H,KAAK+L,2BAA2BU,GAChCzM,KAAKoI,eAAiBqE,EAElBzM,KAAK2C,SAASU,UAAUE,SA9YP,SA8YmC,CACtDkJ,EAAYpJ,UAAU2H,IAAIsB,GAE1BhQ,EAAOmQ,GAEPrB,EAAc/H,UAAU2H,IAAIqB,GAC5BI,EAAYpJ,UAAU2H,IAAIqB,GAE1B,IAAM9S,EAAqBJ,EAAiCiS,GAE5DlL,EAAaS,IAAIyK,EP/dA,iBO+d+B,WAC9CqB,EAAYpJ,UAAUC,OAAO+I,EAAsBC,GACnDG,EAAYpJ,UAAU2H,IA3ZJ,UA6ZlBI,EAAc/H,UAAUC,OA7ZN,SA6ZgCgJ,EAAgBD,GAElEE,EAAKjE,YAAa,EAElB5N,YAAW,WACTwF,EAAaqB,QAAQgL,EAAK5J,SAhbpB,mBAgb0C,CAC9C+I,cAAee,EACfzC,UAAW2B,EACXG,KAAMU,EACN5C,GAAI8C,MAEL,MAGLvS,EAAqBiR,EAAe7R,QAEpC6R,EAAc/H,UAAUC,OA7aJ,UA8apBmJ,EAAYpJ,UAAU2H,IA9aF,UAgbpBhL,KAAKsI,YAAa,EAClBpI,EAAaqB,QAAQvB,KAAK2C,SA/bhB,mBA+bsC,CAC9C+I,cAAee,EACfzC,UAAW2B,EACXG,KAAMU,EACN5C,GAAI8C,IAIJC,GACF3M,KAAKsJ,YAMFsD,kBAAP,SAAyBjU,EAASkC,GAChC,IAAIqC,EAAOK,EAAa5E,EAhfX,eAifT+P,EAAOzD,EAAA,GACNqC,EACA3C,EAAYI,kBAAkBpM,IAGb,iBAAXkC,IACT6N,EAAOzD,EAAA,GACFyD,EACA7N,IAIP,IAAMgS,EAA2B,iBAAXhS,EAAsBA,EAAS6N,EAAQjB,MAM7D,GAJKvK,IACHA,EAAO,IAAI+K,EAAStP,EAAS+P,IAGT,iBAAX7N,EACTqC,EAAK0M,GAAG/O,QACH,GAAsB,iBAAXgS,EAAqB,CACrC,QAA4B,IAAjB3P,EAAK2P,GACd,MAAM,IAAIC,UAAJ,oBAAkCD,EAAlC,KAGR3P,EAAK2P,UACInE,EAAQnB,UAAYmB,EAAQqE,OACrC7P,EAAKwK,QACLxK,EAAKoM,YAIF5F,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACfsE,EAAS2E,kBAAkB5M,KAAMnF,SAI9BmS,oBAAP,SAA2BnO,GACzB,IAAMkB,EAAS7G,EAAuB8G,MAEtC,GAAKD,GAAWA,EAAOsD,UAAUE,SA5eT,YA4exB,CAIA,IAAM1I,EAAMoK,EAAA,GACPN,EAAYI,kBAAkBhF,GAC9B4E,EAAYI,kBAAkB/E,OAE7BiN,EAAajN,KAAKnH,aAAa,iBAEjCoU,IACFpS,EAAO0M,UAAW,GAGpBU,EAAS2E,kBAAkB7M,EAAQlF,GAE/BoS,GACF1P,EAAawC,EA3iBF,eA2iBoB6J,GAAGqD,GAGpCpO,EAAM2D,qBAGDsB,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAljBP,wDAkGb,MAnGY,+CAuGZ,OAAO2O,QA5BLW,GAkfN/H,EAAaQ,GAAGlI,SAhhBU,6BAiBE,gCA+fyCyP,EAAS+E,qBAE9E9M,EAAaQ,GAAGrH,OAnhBS,6BAmhBoB,WAG3C,IAFA,IAAM6T,EAAYpH,EAAeE,KAjgBR,0BAmgBhBtH,EAAI,EAAGC,EAAMuO,EAAUtO,OAAQF,EAAIC,EAAKD,IAC/CuJ,EAAS2E,kBAAkBM,EAAUxO,GAAInB,EAAa2P,EAAUxO,GAlkBnD,mBA6kBjB9B,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAG6C,GAChCf,EAAE9B,GAAG6C,GAAQwF,EAASvE,gBACtBhC,EAAE9B,GAAG6C,GAAMuB,YAAciE,EACzBvG,EAAE9B,GAAG6C,GAAMwB,WAAa,WAEtB,OADAvC,EAAE9B,GAAG6C,GAAQsB,EACNkE,EAASvE,qBCxlBtB,IAAMjB,EAAO,WAMP6E,EAAU,CACdnD,QAAQ,EACRgJ,OAAQ,IAGJtF,EAAc,CAClB1D,OAAQ,UACRgJ,OAAQ,oBA0BJC,EAAAA,WACJ,SAAAA,EAAYzU,EAASkC,GACnBmF,KAAKqN,kBAAmB,EACxBrN,KAAK2C,SAAWhK,EAChBqH,KAAK0I,QAAU1I,KAAK2I,WAAW9N,GAC/BmF,KAAKsN,cAAgBxH,EAAeE,KAC/BuH,mCAA+B5U,EAAQT,GAAvCqV,6CACsC5U,EAAQT,GADjD,MAMF,IAFA,IAAMsV,EAAa1H,EAAeE,KAlBT,4BAoBhBtH,EAAI,EAAGC,EAAM6O,EAAW5O,OAAQF,EAAIC,EAAKD,IAAK,CACrD,IAAM+O,EAAOD,EAAW9O,GAClB9F,EAAWI,EAAuByU,GAClCC,EAAgB5H,EAAeE,KAAKpN,GACvC6N,QAAO,SAAAkH,GAAS,OAAIA,IAAchV,KAEpB,OAAbC,GAAqB8U,EAAc9O,SACrCoB,KAAK4N,UAAYhV,EACjBoH,KAAKsN,cAAcvG,KAAK0G,IAI5BzN,KAAK6N,QAAU7N,KAAK0I,QAAQyE,OAASnN,KAAK8N,aAAe,KAEpD9N,KAAK0I,QAAQyE,QAChBnN,KAAK+N,0BAA0B/N,KAAK2C,SAAU3C,KAAKsN,eAGjDtN,KAAK0I,QAAQvE,QACfnE,KAAKmE,SAGP5G,EAAa5E,EAvEA,cAuEmBqH,iCAelCmE,OAAA,WACMnE,KAAK2C,SAASU,UAAUE,SAnER,QAoElBvD,KAAKgO,OAELhO,KAAKiO,UAITA,KAAA,WAAO,IAAA7K,EAAApD,KACL,IAAIA,KAAKqN,mBACPrN,KAAK2C,SAASU,UAAUE,SA5EN,QA2EpB,CAKA,IAAI2K,EACAC,EAEAnO,KAAK6N,SAUgB,KATvBK,EAAUpI,EAAeE,KA5EN,qBA4E6BhG,KAAK6N,SAClDpH,QAAO,SAAAgH,GACN,MAAmC,iBAAxBrK,EAAKsF,QAAQyE,OACfM,EAAK5U,aAAa,iBAAmBuK,EAAKsF,QAAQyE,OAGpDM,EAAKpK,UAAUE,SAzFJ,gBA4FV3E,SACVsP,EAAU,MAId,IAAME,EAAYtI,EAAeQ,QAAQtG,KAAK4N,WAC9C,GAAIM,EAAS,CACX,IAAMG,EAAiBH,EAAQzH,QAAO,SAAAgH,GAAI,OAAIW,IAAcX,KAG5D,IAFAU,EAAcE,EAAe,GAAK9Q,EAAa8Q,EAAe,GAzHnD,eAyHmE,OAE3DF,EAAYd,iBAC7B,OAKJ,IADmBnN,EAAaqB,QAAQvB,KAAK2C,SAlHjC,oBAmHGb,iBAAf,CAIIoM,GACFA,EAAQjT,SAAQ,SAAAqT,GACVF,IAAcE,GAChBlB,EAASmB,kBAAkBD,EAAY,QAGpCH,GACH5Q,EAAa+Q,EA5IN,cA4I4B,SAKzC,IAAME,EAAYxO,KAAKyO,gBAEvBzO,KAAK2C,SAASU,UAAUC,OA9HA,YA+HxBtD,KAAK2C,SAASU,UAAU2H,IA9HE,cAgI1BhL,KAAK2C,SAAS5G,MAAMyS,GAAa,EAE7BxO,KAAKsN,cAAc1O,QACrBoB,KAAKsN,cAAcrS,SAAQ,SAAAtC,GACzBA,EAAQ0K,UAAUC,OAnIG,aAoIrB3K,EAAQyL,aAAa,iBAAiB,MAI1CpE,KAAK0O,kBAAiB,GAEtB,IAYMC,EAAU,UADaH,EAAU,GAAG3S,cAAgB2S,EAAUnN,MAAM,IAEpE9H,EAAqBJ,EAAiC6G,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,SRvMH,iBQwLF,WACfS,EAAKT,SAASU,UAAUC,OA5IA,cA6IxBF,EAAKT,SAASU,UAAU2H,IA9IF,WADJ,QAiJlB5H,EAAKT,SAAS5G,MAAMyS,GAAa,GAEjCpL,EAAKsL,kBAAiB,GAEtBxO,EAAaqB,QAAQ6B,EAAKT,SA1Jf,wBAmKbxI,EAAqB6F,KAAK2C,SAAUpJ,GACpCyG,KAAK2C,SAAS5G,MAAMyS,GAAgBxO,KAAK2C,SAASgM,GAAlD,UAGFX,KAAA,WAAO,IAAA5D,EAAApK,KACL,IAAIA,KAAKqN,kBACNrN,KAAK2C,SAASU,UAAUE,SApKP,UAwKDrD,EAAaqB,QAAQvB,KAAK2C,SA5KjC,oBA6KGb,iBAAf,CAIA,IAAM0M,EAAYxO,KAAKyO,gBAEvBzO,KAAK2C,SAAS5G,MAAMyS,GAAgBxO,KAAK2C,SAAS2C,wBAAwBkJ,GAA1E,KAEAlS,EAAO0D,KAAK2C,UAEZ3C,KAAK2C,SAASU,UAAU2H,IAjLE,cAkL1BhL,KAAK2C,SAASU,UAAUC,OAnLA,WADJ,QAsLpB,IAAMsL,EAAqB5O,KAAKsN,cAAc1O,OAC9C,GAAIgQ,EAAqB,EACvB,IAAK,IAAIlQ,EAAI,EAAGA,EAAIkQ,EAAoBlQ,IAAK,CAC3C,IAAM6C,EAAUvB,KAAKsN,cAAc5O,GAC7B+O,EAAOvU,EAAuBqI,GAEhCkM,IAASA,EAAKpK,UAAUE,SA5LZ,UA6LdhC,EAAQ8B,UAAU2H,IA1LC,aA2LnBzJ,EAAQ6C,aAAa,iBAAiB,IAK5CpE,KAAK0O,kBAAiB,GAStB1O,KAAK2C,SAAS5G,MAAMyS,GAAa,GACjC,IAAMjV,EAAqBJ,EAAiC6G,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,SR1PH,iBQgPF,WACfyH,EAAKsE,kBAAiB,GACtBtE,EAAKzH,SAASU,UAAUC,OArMA,cAsMxB8G,EAAKzH,SAASU,UAAU2H,IAvMF,YAwMtB9K,EAAaqB,QAAQ6I,EAAKzH,SA5Md,yBAmNdxI,EAAqB6F,KAAK2C,SAAUpJ,OAGtCmV,iBAAA,SAAiBG,GACf7O,KAAKqN,iBAAmBwB,KAG1B3L,QAAA,WACE3F,EAAgByC,KAAK2C,SA5OR,eA8Ob3C,KAAK0I,QAAU,KACf1I,KAAK6N,QAAU,KACf7N,KAAK2C,SAAW,KAChB3C,KAAKsN,cAAgB,KACrBtN,KAAKqN,iBAAmB,QAK1B1E,WAAA,SAAW9N,GAOT,OANAA,EAAMoK,EAAA,GACDqC,EACAzM,IAEEsJ,OAAS1D,QAAQ5F,EAAOsJ,QAC/BxJ,EAAgB8H,EAAM5H,EAAQgN,GACvBhN,KAGT4T,cAAA,WACE,OAAOzO,KAAK2C,SAASU,UAAUE,SAzOrB,SAAA,QACC,YA2ObuK,WAAA,WAAa,IAAAvD,EAAAvK,KACLmN,EAAWnN,KAAK0I,QAAhByE,OAEFnT,EAAUmT,QAEiB,IAAlBA,EAAO2B,aAA+C,IAAd3B,EAAO,KACxDA,EAASA,EAAO,IAGlBA,EAASrH,EAAeQ,QAAQ6G,GAGlC,IAAMvU,EAAc2U,yCAAqCJ,EAA3C,KAYd,OAVArH,EAAeE,KAAKpN,EAAUuU,GAC3BlS,SAAQ,SAAAtC,GACP,IAAMoW,EAAW7V,EAAuBP,GAExC4R,EAAKwD,0BACHgB,EACA,CAACpW,OAIAwU,KAGTY,0BAAA,SAA0BpV,EAASqW,GACjC,GAAKrW,GAAYqW,EAAapQ,OAA9B,CAIA,IAAMqQ,EAAStW,EAAQ0K,UAAUE,SAjRb,QAmRpByL,EAAa/T,SAAQ,SAAAwS,GACfwB,EACFxB,EAAKpK,UAAUC,OAlRM,aAoRrBmK,EAAKpK,UAAU2H,IApRM,aAuRvByC,EAAKrJ,aAAa,gBAAiB6K,UAMhCV,kBAAP,SAAyB5V,EAASkC,GAChC,IAAIqC,EAAOK,EAAa5E,EArTX,eAsTP+P,EAAOzD,EAAA,GACRqC,EACA3C,EAAYI,kBAAkBpM,GACX,iBAAXkC,GAAuBA,EAASA,EAAS,IAWtD,IARKqC,GAAQwL,EAAQvE,QAA4B,iBAAXtJ,GAAuB,YAAYc,KAAKd,KAC5E6N,EAAQvE,QAAS,GAGdjH,IACHA,EAAO,IAAIkQ,EAASzU,EAAS+P,IAGT,iBAAX7N,EAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,SAIF6I,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACfyJ,EAASmB,kBAAkBvO,KAAMnF,SAI9BiJ,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EApVP,wDA6Eb,MA9EY,+CAkFZ,OAAO2O,QA5CL8F,GAyTNlN,EAAaQ,GAAGlI,SA5UU,6BAWG,4BAiUyC,SAAUqG,GAEjD,MAAzBA,EAAMkB,OAAOmL,SACfrM,EAAM2D,iBAGR,IAAM0M,EAAcvK,EAAYI,kBAAkB/E,MAC5CpH,EAAWI,EAAuBgH,MACf8F,EAAeE,KAAKpN,GAE5BqC,SAAQ,SAAAtC,GACvB,IACIkC,EADEqC,EAAOK,EAAa5E,EAzWb,eA2WTuE,GAEmB,OAAjBA,EAAK2Q,SAAkD,iBAAvBqB,EAAY/B,SAC9CjQ,EAAKwL,QAAQyE,OAAS+B,EAAY/B,OAClCjQ,EAAK2Q,QAAU3Q,EAAK4Q,cAGtBjT,EAAS,UAETA,EAASqU,EAGX9B,EAASmB,kBAAkB5V,EAASkC,SAWxC+B,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAG6C,GAChCf,EAAE9B,GAAG6C,GAAQ2K,EAAS1J,gBACtBhC,EAAE9B,GAAG6C,GAAMuB,YAAcoJ,EACzB1L,EAAE9B,GAAG6C,GAAMwB,WAAa,WAEtB,OADAvC,EAAE9B,GAAG6C,GAAQsB,EACNqJ,EAAS1J,qBCnZtB,IAAIyL,GAA8B,oBAAX9V,QAA8C,oBAAbb,UAAiD,oBAAdsQ,UAEvFsG,GAAkB,WAEpB,IADA,IAAIC,EAAwB,CAAC,OAAQ,UAAW,WACvC3Q,EAAI,EAAGA,EAAI2Q,EAAsBzQ,OAAQF,GAAK,EACrD,GAAIyQ,IAAarG,UAAUwG,UAAUhQ,QAAQ+P,EAAsB3Q,KAAO,EACxE,OAAO,EAGX,OAAO,EAPa,GAqCtB,IAWI6Q,GAXqBJ,IAAa9V,OAAOmW,QA3B7C,SAA2B5P,GACzB,IAAIvF,GAAS,EACb,OAAO,WACDA,IAGJA,GAAS,EACThB,OAAOmW,QAAQC,UAAUC,MAAK,WAC5BrV,GAAS,EACTuF,UAKN,SAAsBA,GACpB,IAAI+P,GAAY,EAChB,OAAO,WACAA,IACHA,GAAY,EACZjV,YAAW,WACTiV,GAAY,EACZ/P,MACCwP,OAyBT,SAASQ,GAAWC,GAElB,OAAOA,GAA8D,sBADvD,GACoBvU,SAASC,KAAKsU,GAUlD,SAASC,GAAyBnX,EAASuC,GACzC,GAAyB,IAArBvC,EAAQuB,SACV,MAAO,GAGT,IACI6V,EADSpX,EAAQqX,cAAcC,YAClB3W,iBAAiBX,EAAS,MAC3C,OAAOuC,EAAW6U,EAAI7U,GAAY6U,EAUpC,SAASG,GAAcvX,GACrB,MAAyB,SAArBA,EAAQwX,SACHxX,EAEFA,EAAQqD,YAAcrD,EAAQyX,KAUvC,SAASC,GAAgB1X,GAEvB,IAAKA,EACH,OAAOH,SAASkE,KAGlB,OAAQ/D,EAAQwX,UACd,IAAK,OACL,IAAK,OACH,OAAOxX,EAAQqX,cAActT,KAC/B,IAAK,YACH,OAAO/D,EAAQ+D,KAKnB,IAAI4T,EAAwBR,GAAyBnX,GACjD4X,EAAWD,EAAsBC,SACjCC,EAAYF,EAAsBE,UAClCC,EAAYH,EAAsBG,UAEtC,MAAI,wBAAwB9U,KAAK4U,EAAWE,EAAYD,GAC/C7X,EAGF0X,GAAgBH,GAAcvX,IAUvC,SAAS+X,GAAiBC,GACxB,OAAOA,GAAaA,EAAUC,cAAgBD,EAAUC,cAAgBD,EAG1E,IAAIE,GAAS1B,OAAgB9V,OAAOyX,uBAAwBtY,SAASuY,cACjEC,GAAS7B,IAAa,UAAUxT,KAAKmN,UAAUwG,WASnD,SAAS2B,GAAKC,GACZ,OAAgB,KAAZA,EACKL,GAEO,KAAZK,EACKF,GAEFH,IAAUG,GAUnB,SAASG,GAAgBxY,GACvB,IAAKA,EACH,OAAOH,SAAS0N,gBAQlB,IALA,IAAIkL,EAAiBH,GAAK,IAAMzY,SAASkE,KAAO,KAG5C2U,EAAe1Y,EAAQ0Y,cAAgB,KAEpCA,IAAiBD,GAAkBzY,EAAQyO,oBAChDiK,GAAgB1Y,EAAUA,EAAQyO,oBAAoBiK,aAGxD,IAAIlB,EAAWkB,GAAgBA,EAAalB,SAE5C,OAAKA,GAAyB,SAAbA,GAAoC,SAAbA,GAMsB,IAA1D,CAAC,KAAM,KAAM,SAAS7Q,QAAQ+R,EAAalB,WAA2E,WAAvDL,GAAyBuB,EAAc,YACjGF,GAAgBE,GAGlBA,EATE1Y,EAAUA,EAAQqX,cAAc9J,gBAAkB1N,SAAS0N,gBA4BtE,SAASoL,GAAQC,GACf,OAAwB,OAApBA,EAAKvV,WACAsV,GAAQC,EAAKvV,YAGfuV,EAWT,SAASC,GAAuBC,EAAUC,GAExC,KAAKD,GAAaA,EAASvX,UAAawX,GAAaA,EAASxX,UAC5D,OAAO1B,SAAS0N,gBAIlB,IAAIyL,EAAQF,EAASG,wBAAwBF,GAAY7K,KAAKgL,4BAC1DrH,EAAQmH,EAAQF,EAAWC,EAC3B9G,EAAM+G,EAAQD,EAAWD,EAGzBK,EAAQtZ,SAASuZ,cACrBD,EAAME,SAASxH,EAAO,GACtBsH,EAAMG,OAAOrH,EAAK,GAClB,IA/CyBjS,EACrBwX,EA8CA+B,EAA0BJ,EAAMI,wBAIpC,GAAIT,IAAaS,GAA2BR,IAAaQ,GAA2B1H,EAAMjH,SAASqH,GACjG,MAjDe,UAFbuF,GADqBxX,EAoDDuZ,GAnDD/B,WAKH,SAAbA,GAAuBgB,GAAgBxY,EAAQwZ,qBAAuBxZ,EAkDpEwY,GAAgBe,GAHdA,EAOX,IAAIE,EAAed,GAAQG,GAC3B,OAAIW,EAAahC,KACRoB,GAAuBY,EAAahC,KAAMsB,GAE1CF,GAAuBC,EAAUH,GAAQI,GAAUtB,MAY9D,SAASiC,GAAU1Z,GACjB,IAAI2Z,EAAOC,UAAU3T,OAAS,QAAsB4T,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,MAE3EE,EAAqB,QAATH,EAAiB,YAAc,aAC3CnC,EAAWxX,EAAQwX,SAEvB,GAAiB,SAAbA,GAAoC,SAAbA,EAAqB,CAC9C,IAAIuC,EAAO/Z,EAAQqX,cAAc9J,gBAC7ByM,EAAmBha,EAAQqX,cAAc2C,kBAAoBD,EACjE,OAAOC,EAAiBF,GAG1B,OAAO9Z,EAAQ8Z,GAYjB,SAASG,GAAcvN,EAAM1M,GAC3B,IAAIka,EAAWN,UAAU3T,OAAS,QAAsB4T,IAAjBD,UAAU,IAAmBA,UAAU,GAE1E/M,EAAY6M,GAAU1Z,EAAS,OAC/B+M,EAAa2M,GAAU1Z,EAAS,QAChCma,EAAWD,GAAY,EAAI,EAK/B,OAJAxN,EAAKE,KAAOC,EAAYsN,EACxBzN,EAAK0N,QAAUvN,EAAYsN,EAC3BzN,EAAKI,MAAQC,EAAaoN,EAC1BzN,EAAK2N,OAAStN,EAAaoN,EACpBzN,EAaT,SAAS4N,GAAeC,EAAQC,GAC9B,IAAIC,EAAiB,MAATD,EAAe,OAAS,MAChCE,EAAkB,SAAVD,EAAmB,QAAU,SAEzC,OAAO1Z,WAAWwZ,EAAO,SAAWE,EAAQ,UAAY1Z,WAAWwZ,EAAO,SAAWG,EAAQ,UAG/F,SAASC,GAAQH,EAAMzW,EAAMgW,EAAMa,GACjC,OAAOlb,KAAKmb,IAAI9W,EAAK,SAAWyW,GAAOzW,EAAK,SAAWyW,GAAOT,EAAK,SAAWS,GAAOT,EAAK,SAAWS,GAAOT,EAAK,SAAWS,GAAOlC,GAAK,IAAM9E,SAASuG,EAAK,SAAWS,IAAShH,SAASoH,EAAc,UAAqB,WAATJ,EAAoB,MAAQ,UAAYhH,SAASoH,EAAc,UAAqB,WAATJ,EAAoB,SAAW,WAAa,GAG5U,SAASM,GAAejb,GACtB,IAAIkE,EAAOlE,EAASkE,KAChBgW,EAAOla,EAAS0N,gBAChBqN,EAAgBtC,GAAK,KAAO3X,iBAAiBoZ,GAEjD,MAAO,CACLgB,OAAQJ,GAAQ,SAAU5W,EAAMgW,EAAMa,GACtCI,MAAOL,GAAQ,QAAS5W,EAAMgW,EAAMa,IAIxC,IAAIK,GAAiB,SAAUpW,EAAUwG,GACvC,KAAMxG,aAAoBwG,GACxB,MAAM,IAAI8I,UAAU,sCAIpB+G,GAAc,WAChB,SAASC,EAAiB/T,EAAQgU,GAChC,IAAK,IAAIrV,EAAI,EAAGA,EAAIqV,EAAMnV,OAAQF,IAAK,CACrC,IAAIsV,EAAaD,EAAMrV,GACvBsV,EAAWC,WAAaD,EAAWC,aAAc,EACjDD,EAAWE,cAAe,EACtB,UAAWF,IAAYA,EAAWG,UAAW,GACjDpZ,OAAOwH,eAAexC,EAAQiU,EAAW/W,IAAK+W,IAIlD,OAAO,SAAUhQ,EAAaoQ,EAAYC,GAGxC,OAFID,GAAYN,EAAiB9P,EAAYqC,UAAW+N,GACpDC,GAAaP,EAAiB9P,EAAaqQ,GACxCrQ,GAdO,GAsBdzB,GAAiB,SAAUtI,EAAKgD,EAAK7B,GAYvC,OAXI6B,KAAOhD,EACTc,OAAOwH,eAAetI,EAAKgD,EAAK,CAC9B7B,MAAOA,EACP6Y,YAAY,EACZC,cAAc,EACdC,UAAU,IAGZla,EAAIgD,GAAO7B,EAGNnB,GAGLgL,GAAWlK,OAAOuZ,QAAU,SAAUvU,GACxC,IAAK,IAAIrB,EAAI,EAAGA,EAAI6T,UAAU3T,OAAQF,IAAK,CACzC,IAAI6V,EAAShC,UAAU7T,GAEvB,IAAK,IAAIzB,KAAOsX,EACVxZ,OAAOsL,UAAUmO,eAAejZ,KAAKgZ,EAAQtX,KAC/C8C,EAAO9C,GAAOsX,EAAOtX,IAK3B,OAAO8C,GAUT,SAAS0U,GAAcC,GACrB,OAAOzP,GAAS,GAAIyP,EAAS,CAC3B1B,MAAO0B,EAAQjP,KAAOiP,EAAQf,MAC9BZ,OAAQ2B,EAAQnP,IAAMmP,EAAQhB,SAWlC,SAASpO,GAAsB3M,GAC7B,IAAI0M,EAAO,GAKX,IACE,GAAI4L,GAAK,IAAK,CACZ5L,EAAO1M,EAAQ2M,wBACf,IAAIE,EAAY6M,GAAU1Z,EAAS,OAC/B+M,EAAa2M,GAAU1Z,EAAS,QACpC0M,EAAKE,KAAOC,EACZH,EAAKI,MAAQC,EACbL,EAAK0N,QAAUvN,EACfH,EAAK2N,OAAStN,OAEdL,EAAO1M,EAAQ2M,wBAEjB,MAAOyF,IAET,IAAI4J,EAAS,CACXlP,KAAMJ,EAAKI,KACXF,IAAKF,EAAKE,IACVoO,MAAOtO,EAAK2N,MAAQ3N,EAAKI,KACzBiO,OAAQrO,EAAK0N,OAAS1N,EAAKE,KAIzBqP,EAA6B,SAArBjc,EAAQwX,SAAsBsD,GAAe9a,EAAQqX,eAAiB,GAC9E2D,EAAQiB,EAAMjB,OAAShb,EAAQkc,aAAeF,EAAOhB,MACrDD,EAASkB,EAAMlB,QAAU/a,EAAQmc,cAAgBH,EAAOjB,OAExDqB,EAAiBpc,EAAQqc,YAAcrB,EACvCsB,EAAgBtc,EAAQ4D,aAAemX,EAI3C,GAAIqB,GAAkBE,EAAe,CACnC,IAAI/B,EAASpD,GAAyBnX,GACtCoc,GAAkB9B,GAAeC,EAAQ,KACzC+B,GAAiBhC,GAAeC,EAAQ,KAExCyB,EAAOhB,OAASoB,EAChBJ,EAAOjB,QAAUuB,EAGnB,OAAOR,GAAcE,GAGvB,SAASO,GAAqC3O,EAAU4G,GACtD,IAAIgI,EAAgB5C,UAAU3T,OAAS,QAAsB4T,IAAjBD,UAAU,IAAmBA,UAAU,GAE/EvB,EAASC,GAAK,IACdmE,EAA6B,SAApBjI,EAAOgD,SAChBkF,EAAe/P,GAAsBiB,GACrC+O,EAAahQ,GAAsB6H,GACnCoI,EAAelF,GAAgB9J,GAE/B2M,EAASpD,GAAyB3C,GAClCqI,EAAiB9b,WAAWwZ,EAAOsC,gBACnCC,EAAkB/b,WAAWwZ,EAAOuC,iBAGpCN,GAAiBC,IACnBE,EAAW/P,IAAMlN,KAAKmb,IAAI8B,EAAW/P,IAAK,GAC1C+P,EAAW7P,KAAOpN,KAAKmb,IAAI8B,EAAW7P,KAAM,IAE9C,IAAIiP,EAAUD,GAAc,CAC1BlP,IAAK8P,EAAa9P,IAAM+P,EAAW/P,IAAMiQ,EACzC/P,KAAM4P,EAAa5P,KAAO6P,EAAW7P,KAAOgQ,EAC5C9B,MAAO0B,EAAa1B,MACpBD,OAAQ2B,EAAa3B,SASvB,GAPAgB,EAAQgB,UAAY,EACpBhB,EAAQiB,WAAa,GAMhB3E,GAAUoE,EAAQ,CACrB,IAAIM,EAAYhc,WAAWwZ,EAAOwC,WAC9BC,EAAajc,WAAWwZ,EAAOyC,YAEnCjB,EAAQnP,KAAOiQ,EAAiBE,EAChChB,EAAQ3B,QAAUyC,EAAiBE,EACnChB,EAAQjP,MAAQgQ,EAAkBE,EAClCjB,EAAQ1B,OAASyC,EAAkBE,EAGnCjB,EAAQgB,UAAYA,EACpBhB,EAAQiB,WAAaA,EAOvB,OAJI3E,IAAWmE,EAAgBhI,EAAO5J,SAASgS,GAAgBpI,IAAWoI,GAA0C,SAA1BA,EAAapF,YACrGuE,EAAU9B,GAAc8B,EAASvH,IAG5BuH,EAGT,SAASkB,GAA8Cjd,GACrD,IAAIkd,EAAgBtD,UAAU3T,OAAS,QAAsB4T,IAAjBD,UAAU,IAAmBA,UAAU,GAE/EG,EAAO/Z,EAAQqX,cAAc9J,gBAC7B4P,EAAiBZ,GAAqCvc,EAAS+Z,GAC/DiB,EAAQtb,KAAKmb,IAAId,EAAKmC,YAAaxb,OAAO0c,YAAc,GACxDrC,EAASrb,KAAKmb,IAAId,EAAKoC,aAAczb,OAAO2c,aAAe,GAE3DxQ,EAAaqQ,EAAkC,EAAlBxD,GAAUK,GACvChN,EAAcmQ,EAA0C,EAA1BxD,GAAUK,EAAM,QAE9CtN,EAAS,CACXG,IAAKC,EAAYsQ,EAAevQ,IAAMuQ,EAAeJ,UACrDjQ,KAAMC,EAAaoQ,EAAerQ,KAAOqQ,EAAeH,WACxDhC,MAAOA,EACPD,OAAQA,GAGV,OAAOe,GAAcrP,GAWvB,SAAS6Q,GAAQtd,GACf,IAAIwX,EAAWxX,EAAQwX,SACvB,GAAiB,SAAbA,GAAoC,SAAbA,EACzB,OAAO,EAET,GAAsD,UAAlDL,GAAyBnX,EAAS,YACpC,OAAO,EAET,IAAIqD,EAAakU,GAAcvX,GAC/B,QAAKqD,GAGEia,GAAQja,GAWjB,SAASka,GAA6Bvd,GAEpC,IAAKA,IAAYA,EAAQwd,eAAiBlF,KACxC,OAAOzY,SAAS0N,gBAGlB,IADA,IAAIkQ,EAAKzd,EAAQwd,cACVC,GAAoD,SAA9CtG,GAAyBsG,EAAI,cACxCA,EAAKA,EAAGD,cAEV,OAAOC,GAAM5d,SAAS0N,gBAcxB,SAASmQ,GAAcC,EAAQ3F,EAAW4F,EAASC,GACjD,IAAIrB,EAAgB5C,UAAU3T,OAAS,QAAsB4T,IAAjBD,UAAU,IAAmBA,UAAU,GAI/EkE,EAAa,CAAElR,IAAK,EAAGE,KAAM,GAC7B4L,EAAe8D,EAAgBe,GAA6BI,GAAU9E,GAAuB8E,EAAQ5F,GAAiBC,IAG1H,GAA0B,aAAtB6F,EACFC,EAAab,GAA8CvE,EAAc8D,OACpE,CAEL,IAAIuB,OAAiB,EACK,iBAAtBF,EAE8B,UADhCE,EAAiBrG,GAAgBH,GAAcS,KAC5BR,WACjBuG,EAAiBJ,EAAOtG,cAAc9J,iBAGxCwQ,EAD+B,WAAtBF,EACQF,EAAOtG,cAAc9J,gBAErBsQ,EAGnB,IAAI9B,EAAUQ,GAAqCwB,EAAgBrF,EAAc8D,GAGjF,GAAgC,SAA5BuB,EAAevG,UAAwB8F,GAAQ5E,GAWjDoF,EAAa/B,MAXmD,CAChE,IAAIiC,EAAkBlD,GAAe6C,EAAOtG,eACxC0D,EAASiD,EAAgBjD,OACzBC,EAAQgD,EAAgBhD,MAE5B8C,EAAWlR,KAAOmP,EAAQnP,IAAMmP,EAAQgB,UACxCe,EAAW1D,OAASW,EAASgB,EAAQnP,IACrCkR,EAAWhR,MAAQiP,EAAQjP,KAAOiP,EAAQiB,WAC1Cc,EAAWzD,MAAQW,EAAQe,EAAQjP,MASvC,IAAImR,EAAqC,iBADzCL,EAAUA,GAAW,GAOrB,OALAE,EAAWhR,MAAQmR,EAAkBL,EAAUA,EAAQ9Q,MAAQ,EAC/DgR,EAAWlR,KAAOqR,EAAkBL,EAAUA,EAAQhR,KAAO,EAC7DkR,EAAWzD,OAAS4D,EAAkBL,EAAUA,EAAQvD,OAAS,EACjEyD,EAAW1D,QAAU6D,EAAkBL,EAAUA,EAAQxD,QAAU,EAE5D0D,EAGT,SAASI,GAAQ5Q,GAIf,OAHYA,EAAK0N,MACJ1N,EAAKyN,OAcpB,SAASoD,GAAqBC,EAAWC,EAASV,EAAQ3F,EAAW6F,GACnE,IAAID,EAAUhE,UAAU3T,OAAS,QAAsB4T,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,EAElF,IAAmC,IAA/BwE,EAAUzX,QAAQ,QACpB,OAAOyX,EAGT,IAAIN,EAAaJ,GAAcC,EAAQ3F,EAAW4F,EAASC,GAEvDS,EAAQ,CACV1R,IAAK,CACHoO,MAAO8C,EAAW9C,MAClBD,OAAQsD,EAAQzR,IAAMkR,EAAWlR,KAEnCyN,MAAO,CACLW,MAAO8C,EAAWzD,MAAQgE,EAAQhE,MAClCU,OAAQ+C,EAAW/C,QAErBX,OAAQ,CACNY,MAAO8C,EAAW9C,MAClBD,OAAQ+C,EAAW1D,OAASiE,EAAQjE,QAEtCtN,KAAM,CACJkO,MAAOqD,EAAQvR,KAAOgR,EAAWhR,KACjCiO,OAAQ+C,EAAW/C,SAInBwD,EAAcnc,OAAOC,KAAKic,GAAOE,KAAI,SAAUla,GACjD,OAAOgI,GAAS,CACdhI,IAAKA,GACJga,EAAMha,GAAM,CACbma,KAAMP,GAAQI,EAAMha,SAErBoa,MAAK,SAAUC,EAAGC,GACnB,OAAOA,EAAEH,KAAOE,EAAEF,QAGhBI,EAAgBN,EAAYzQ,QAAO,SAAUD,GAC/C,IAAImN,EAAQnN,EAAMmN,MACdD,EAASlN,EAAMkN,OACnB,OAAOC,GAAS2C,EAAOzB,aAAenB,GAAU4C,EAAOxB,gBAGrD2C,EAAoBD,EAAc5Y,OAAS,EAAI4Y,EAAc,GAAGva,IAAMia,EAAY,GAAGja,IAErFya,EAAYX,EAAUnd,MAAM,KAAK,GAErC,OAAO6d,GAAqBC,EAAY,IAAMA,EAAY,IAa5D,SAASC,GAAoBC,EAAOtB,EAAQ3F,GAC1C,IAAIwE,EAAgB5C,UAAU3T,OAAS,QAAsB4T,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,KAEpFsF,EAAqB1C,EAAgBe,GAA6BI,GAAU9E,GAAuB8E,EAAQ5F,GAAiBC,IAChI,OAAOuE,GAAqCvE,EAAWkH,EAAoB1C,GAU7E,SAAS2C,GAAcnf,GACrB,IACIua,EADSva,EAAQqX,cAAcC,YACf3W,iBAAiBX,GACjCof,EAAIre,WAAWwZ,EAAOwC,WAAa,GAAKhc,WAAWwZ,EAAO8E,cAAgB,GAC1EC,EAAIve,WAAWwZ,EAAOyC,YAAc,GAAKjc,WAAWwZ,EAAOgF,aAAe,GAK9E,MAJa,CACXvE,MAAOhb,EAAQqc,YAAciD,EAC7BvE,OAAQ/a,EAAQ4D,aAAewb,GAYnC,SAASI,GAAqBpB,GAC5B,IAAIqB,EAAO,CAAE3S,KAAM,QAASuN,MAAO,OAAQD,OAAQ,MAAOxN,IAAK,UAC/D,OAAOwR,EAAU3X,QAAQ,0BAA0B,SAAUiZ,GAC3D,OAAOD,EAAKC,MAchB,SAASC,GAAiBhC,EAAQiC,EAAkBxB,GAClDA,EAAYA,EAAUnd,MAAM,KAAK,GAGjC,IAAI4e,EAAaV,GAAcxB,GAG3BmC,EAAgB,CAClB9E,MAAO6E,EAAW7E,MAClBD,OAAQ8E,EAAW9E,QAIjBgF,GAAoD,IAA1C,CAAC,QAAS,QAAQpZ,QAAQyX,GACpC4B,EAAWD,EAAU,MAAQ,OAC7BE,EAAgBF,EAAU,OAAS,MACnCG,EAAcH,EAAU,SAAW,QACnCI,EAAwBJ,EAAqB,QAAX,SAStC,OAPAD,EAAcE,GAAYJ,EAAiBI,GAAYJ,EAAiBM,GAAe,EAAIL,EAAWK,GAAe,EAEnHJ,EAAcG,GADZ7B,IAAc6B,EACeL,EAAiBK,GAAiBJ,EAAWM,GAE7CP,EAAiBJ,GAAqBS,IAGhEH,EAYT,SAASzS,GAAK+S,EAAKC,GAEjB,OAAIC,MAAM5S,UAAUL,KACX+S,EAAI/S,KAAKgT,GAIXD,EAAItS,OAAOuS,GAAO,GAqC3B,SAASE,GAAaC,EAAWjc,EAAMkc,GAoBrC,YAnB8B5G,IAAT4G,EAAqBD,EAAYA,EAAU9X,MAAM,EA1BxE,SAAmB0X,EAAKM,EAAMje,GAE5B,GAAI6d,MAAM5S,UAAUiT,UAClB,OAAOP,EAAIO,WAAU,SAAUC,GAC7B,OAAOA,EAAIF,KAAUje,KAKzB,IAAII,EAAQwK,GAAK+S,GAAK,SAAU9e,GAC9B,OAAOA,EAAIof,KAAUje,KAEvB,OAAO2d,EAAIzZ,QAAQ9D,GAcsD8d,CAAUH,EAAW,OAAQC,KAEvFne,SAAQ,SAAU6X,GAC3BA,EAAmB,UAErB0G,QAAQC,KAAK,yDAEf,IAAI7Z,EAAKkT,EAAmB,UAAKA,EAASlT,GACtCkT,EAAS4G,SAAW9J,GAAWhQ,KAIjC1C,EAAKwX,QAAQ4B,OAAS7B,GAAcvX,EAAKwX,QAAQ4B,QACjDpZ,EAAKwX,QAAQ/D,UAAY8D,GAAcvX,EAAKwX,QAAQ/D,WAEpDzT,EAAO0C,EAAG1C,EAAM4V,OAIb5V,EAUT,SAASyc,KAEP,IAAI3Z,KAAK4X,MAAMgC,YAAf,CAIA,IAAI1c,EAAO,CACTM,SAAUwC,KACVkT,OAAQ,GACR2G,YAAa,GACb7U,WAAY,GACZ8U,SAAS,EACTpF,QAAS,IAIXxX,EAAKwX,QAAQ/D,UAAYgH,GAAoB3X,KAAK4X,MAAO5X,KAAKsW,OAAQtW,KAAK2Q,UAAW3Q,KAAK+Z,QAAQC,eAKnG9c,EAAK6Z,UAAYD,GAAqB9W,KAAK+Z,QAAQhD,UAAW7Z,EAAKwX,QAAQ/D,UAAW3Q,KAAKsW,OAAQtW,KAAK2Q,UAAW3Q,KAAK+Z,QAAQZ,UAAUc,KAAKzD,kBAAmBxW,KAAK+Z,QAAQZ,UAAUc,KAAK1D,SAG9LrZ,EAAKgd,kBAAoBhd,EAAK6Z,UAE9B7Z,EAAK8c,cAAgBha,KAAK+Z,QAAQC,cAGlC9c,EAAKwX,QAAQ4B,OAASgC,GAAiBtY,KAAKsW,OAAQpZ,EAAKwX,QAAQ/D,UAAWzT,EAAK6Z,WAEjF7Z,EAAKwX,QAAQ4B,OAAO3Q,SAAW3F,KAAK+Z,QAAQC,cAAgB,QAAU,WAGtE9c,EAAOgc,GAAalZ,KAAKmZ,UAAWjc,GAI/B8C,KAAK4X,MAAMuC,UAIdna,KAAK+Z,QAAQK,SAASld,IAHtB8C,KAAK4X,MAAMuC,WAAY,EACvBna,KAAK+Z,QAAQM,SAASnd,KAY1B,SAASod,GAAkBnB,EAAWoB,GACpC,OAAOpB,EAAUqB,MAAK,SAAUvU,GAC9B,IAAIwU,EAAOxU,EAAKwU,KAEhB,OADcxU,EAAKyT,SACDe,IAASF,KAW/B,SAASG,GAAyBxf,GAIhC,IAHA,IAAIyf,EAAW,EAAC,EAAO,KAAM,SAAU,MAAO,KAC1CC,EAAY1f,EAAS6F,OAAO,GAAGlF,cAAgBX,EAASmG,MAAM,GAEzD3C,EAAI,EAAGA,EAAIic,EAAS/b,OAAQF,IAAK,CACxC,IAAItG,EAASuiB,EAASjc,GAClBmc,EAAUziB,EAAS,GAAKA,EAASwiB,EAAY1f,EACjD,QAA4C,IAAjC1C,SAASkE,KAAKX,MAAM8e,GAC7B,OAAOA,EAGX,OAAO,KAQT,SAASC,KAsBP,OArBA9a,KAAK4X,MAAMgC,aAAc,EAGrBU,GAAkBta,KAAKmZ,UAAW,gBACpCnZ,KAAKsW,OAAOxR,gBAAgB,eAC5B9E,KAAKsW,OAAOva,MAAM4J,SAAW,GAC7B3F,KAAKsW,OAAOva,MAAMwJ,IAAM,GACxBvF,KAAKsW,OAAOva,MAAM0J,KAAO,GACzBzF,KAAKsW,OAAOva,MAAMiX,MAAQ,GAC1BhT,KAAKsW,OAAOva,MAAMgX,OAAS,GAC3B/S,KAAKsW,OAAOva,MAAMgf,WAAa,GAC/B/a,KAAKsW,OAAOva,MAAM2e,GAAyB,cAAgB,IAG7D1a,KAAKgb,wBAIDhb,KAAK+Z,QAAQkB,iBACfjb,KAAKsW,OAAOta,WAAWyH,YAAYzD,KAAKsW,QAEnCtW,KAQT,SAASkb,GAAUviB,GACjB,IAAIqX,EAAgBrX,EAAQqX,cAC5B,OAAOA,EAAgBA,EAAcC,YAAc5W,OAoBrD,SAAS8hB,GAAoBxK,EAAWoJ,EAASnC,EAAOwD,GAEtDxD,EAAMwD,YAAcA,EACpBF,GAAUvK,GAAWpW,iBAAiB,SAAUqd,EAAMwD,YAAa,CAAEC,SAAS,IAG9E,IAAIC,EAAgBjL,GAAgBM,GAKpC,OA5BF,SAAS4K,EAAsBhG,EAAc1W,EAAOhC,EAAU2e,GAC5D,IAAIC,EAAmC,SAA1BlG,EAAapF,SACtBpQ,EAAS0b,EAASlG,EAAavF,cAAcC,YAAcsF,EAC/DxV,EAAOxF,iBAAiBsE,EAAOhC,EAAU,CAAEwe,SAAS,IAE/CI,GACHF,EAAsBlL,GAAgBtQ,EAAO/D,YAAa6C,EAAOhC,EAAU2e,GAE7EA,EAAczU,KAAKhH,GAgBnBwb,CAAsBD,EAAe,SAAU1D,EAAMwD,YAAaxD,EAAM4D,eACxE5D,EAAM0D,cAAgBA,EACtB1D,EAAM8D,eAAgB,EAEf9D,EAST,SAAS+D,KACF3b,KAAK4X,MAAM8D,gBACd1b,KAAK4X,MAAQuD,GAAoBnb,KAAK2Q,UAAW3Q,KAAK+Z,QAAS/Z,KAAK4X,MAAO5X,KAAK4b,iBAkCpF,SAASZ,KAxBT,IAA8BrK,EAAWiH,EAyBnC5X,KAAK4X,MAAM8D,gBACbG,qBAAqB7b,KAAK4b,gBAC1B5b,KAAK4X,OA3BqBjH,EA2BQ3Q,KAAK2Q,UA3BFiH,EA2Ba5X,KAAK4X,MAzBzDsD,GAAUvK,GAAWlW,oBAAoB,SAAUmd,EAAMwD,aAGzDxD,EAAM4D,cAAcvgB,SAAQ,SAAU8E,GACpCA,EAAOtF,oBAAoB,SAAUmd,EAAMwD,gBAI7CxD,EAAMwD,YAAc,KACpBxD,EAAM4D,cAAgB,GACtB5D,EAAM0D,cAAgB,KACtB1D,EAAM8D,eAAgB,EACf9D,IAwBT,SAASkE,GAAUC,GACjB,MAAa,KAANA,IAAaC,MAAMtiB,WAAWqiB,KAAOE,SAASF,GAWvD,SAASG,GAAUvjB,EAASua,GAC1BnY,OAAOC,KAAKkY,GAAQjY,SAAQ,SAAUoe,GACpC,IAAI8C,EAAO,IAEkE,IAAzE,CAAC,QAAS,SAAU,MAAO,QAAS,SAAU,QAAQ7c,QAAQ+Z,IAAgByC,GAAU5I,EAAOmG,MACjG8C,EAAO,MAETxjB,EAAQoD,MAAMsd,GAAQnG,EAAOmG,GAAQ8C,KAgIzC,IAAIC,GAAYjN,IAAa,WAAWxT,KAAKmN,UAAUwG,WA8GvD,SAAS+M,GAAmBlD,EAAWmD,EAAgBC,GACrD,IAAIC,EAAaxW,GAAKmT,GAAW,SAAUlT,GAEzC,OADWA,EAAKwU,OACA6B,KAGdG,IAAeD,GAAcrD,EAAUqB,MAAK,SAAU1H,GACxD,OAAOA,EAAS2H,OAAS8B,GAAiBzJ,EAAS4G,SAAW5G,EAASnB,MAAQ6K,EAAW7K,SAG5F,IAAK8K,EAAY,CACf,IAAIC,EAAc,IAAMJ,EAAiB,IACrCK,EAAY,IAAMJ,EAAgB,IACtC/C,QAAQC,KAAKkD,EAAY,4BAA8BD,EAAc,4DAA8DA,EAAc,KAEnJ,OAAOD,EAoIT,IAAIG,GAAa,CAAC,aAAc,OAAQ,WAAY,YAAa,MAAO,UAAW,cAAe,QAAS,YAAa,aAAc,SAAU,eAAgB,WAAY,OAAQ,cAGhLC,GAAkBD,GAAWvb,MAAM,GAYvC,SAASyb,GAAU/F,GACjB,IAAIgG,EAAUxK,UAAU3T,OAAS,QAAsB4T,IAAjBD,UAAU,IAAmBA,UAAU,GAEzE1I,EAAQgT,GAAgBvd,QAAQyX,GAChCgC,EAAM8D,GAAgBxb,MAAMwI,EAAQ,GAAG1D,OAAO0W,GAAgBxb,MAAM,EAAGwI,IAC3E,OAAOkT,EAAUhE,EAAIiE,UAAYjE,EAGnC,IAAIkE,GACI,OADJA,GAES,YAFTA,GAGgB,mBAiMpB,SAASC,GAAY9X,EAAQqT,EAAeF,EAAkB4E,GAC5D,IAAIzI,EAAU,CAAC,EAAG,GAKd0I,GAA0D,IAA9C,CAAC,QAAS,QAAQ9d,QAAQ6d,GAItCE,EAAYjY,EAAOxL,MAAM,WAAWud,KAAI,SAAUmG,GACpD,OAAOA,EAAKvkB,UAKVwkB,EAAUF,EAAU/d,QAAQ0G,GAAKqX,GAAW,SAAUC,GACxD,OAAgC,IAAzBA,EAAKE,OAAO,YAGjBH,EAAUE,KAAiD,IAArCF,EAAUE,GAASje,QAAQ,MACnDka,QAAQC,KAAK,gFAKf,IAAIgE,EAAa,cACbC,GAAmB,IAAbH,EAAiB,CAACF,EAAUhc,MAAM,EAAGkc,GAASpX,OAAO,CAACkX,EAAUE,GAAS3jB,MAAM6jB,GAAY,KAAM,CAACJ,EAAUE,GAAS3jB,MAAM6jB,GAAY,IAAItX,OAAOkX,EAAUhc,MAAMkc,EAAU,KAAO,CAACF,GAqC9L,OAlCAK,EAAMA,EAAIvG,KAAI,SAAUwG,EAAI9T,GAE1B,IAAIgP,GAAyB,IAAVhP,GAAeuT,EAAYA,GAAa,SAAW,QAClEQ,GAAoB,EACxB,OAAOD,EAGNE,QAAO,SAAUvG,EAAGC,GACnB,MAAwB,KAApBD,EAAEA,EAAE1Y,OAAS,KAAwC,IAA3B,CAAC,IAAK,KAAKU,QAAQiY,IAC/CD,EAAEA,EAAE1Y,OAAS,GAAK2Y,EAClBqG,GAAoB,EACbtG,GACEsG,GACTtG,EAAEA,EAAE1Y,OAAS,IAAM2Y,EACnBqG,GAAoB,EACbtG,GAEAA,EAAEnR,OAAOoR,KAEjB,IAEFJ,KAAI,SAAU2G,GACb,OAxGN,SAAiBA,EAAKjF,EAAaJ,EAAeF,GAEhD,IAAI3e,EAAQkkB,EAAItiB,MAAM,6BAClBJ,GAASxB,EAAM,GACfuiB,EAAOviB,EAAM,GAGjB,IAAKwB,EACH,OAAO0iB,EAGT,GAA0B,IAAtB3B,EAAK7c,QAAQ,KAAY,CAC3B,IAAI3G,OAAU,EACd,OAAQwjB,GACN,IAAK,KACHxjB,EAAU8f,EACV,MACF,IAAK,IACL,IAAK,KACL,QACE9f,EAAU4f,EAId,OADW9D,GAAc9b,GACbkgB,GAAe,IAAMzd,EAC5B,GAAa,OAAT+gB,GAA0B,OAATA,EAQ1B,OALa,OAATA,EACK9jB,KAAKmb,IAAIhb,SAAS0N,gBAAgB4O,aAAczb,OAAO2c,aAAe,GAEtE3d,KAAKmb,IAAIhb,SAAS0N,gBAAgB2O,YAAaxb,OAAO0c,YAAc,IAE/D,IAAM3a,EAIpB,OAAOA,EAmEE2iB,CAAQD,EAAKjF,EAAaJ,EAAeF,UAKhDtd,SAAQ,SAAU0iB,EAAI9T,GACxB8T,EAAG1iB,SAAQ,SAAUqiB,EAAMU,GACrBlC,GAAUwB,KACZ5I,EAAQ7K,IAAUyT,GAA2B,MAAnBK,EAAGK,EAAS,IAAc,EAAI,UAIvDtJ,EA2OT,IAkWIuJ,GAAW,CAKblH,UAAW,SAMXiD,eAAe,EAMf0B,eAAe,EAOfT,iBAAiB,EAQjBZ,SAAU,aAUVD,SAAU,aAOVjB,UAnZc,CASd+E,MAAO,CAELvM,MAAO,IAEP+H,SAAS,EAET9Z,GA9HJ,SAAe1C,GACb,IAAI6Z,EAAY7Z,EAAK6Z,UACjBoG,EAAgBpG,EAAUnd,MAAM,KAAK,GACrCukB,EAAiBpH,EAAUnd,MAAM,KAAK,GAG1C,GAAIukB,EAAgB,CAClB,IAAIC,EAAgBlhB,EAAKwX,QACrB/D,EAAYyN,EAAczN,UAC1B2F,EAAS8H,EAAc9H,OAEvB+H,GAA2D,IAA9C,CAAC,SAAU,OAAO/e,QAAQ6d,GACvC7K,EAAO+L,EAAa,OAAS,MAC7BxF,EAAcwF,EAAa,QAAU,SAErCC,EAAe,CACjB9T,MAAOjI,GAAe,GAAI+P,EAAM3B,EAAU2B,IAC1C1H,IAAKrI,GAAe,GAAI+P,EAAM3B,EAAU2B,GAAQ3B,EAAUkI,GAAevC,EAAOuC,KAGlF3b,EAAKwX,QAAQ4B,OAASrR,GAAS,GAAIqR,EAAQgI,EAAaH,IAG1D,OAAOjhB,IAgJPkI,OAAQ,CAENuM,MAAO,IAEP+H,SAAS,EAET9Z,GA7RJ,SAAgB1C,EAAM+I,GACpB,IAAIb,EAASa,EAAKb,OACd2R,EAAY7Z,EAAK6Z,UACjBqH,EAAgBlhB,EAAKwX,QACrB4B,EAAS8H,EAAc9H,OACvB3F,EAAYyN,EAAczN,UAE1BwM,EAAgBpG,EAAUnd,MAAM,KAAK,GAErC8a,OAAU,EAsBd,OApBEA,EADEoH,IAAW1W,GACH,EAAEA,EAAQ,GAEV8X,GAAY9X,EAAQkR,EAAQ3F,EAAWwM,GAG7B,SAAlBA,GACF7G,EAAO/Q,KAAOmP,EAAQ,GACtB4B,EAAO7Q,MAAQiP,EAAQ,IACI,UAAlByI,GACT7G,EAAO/Q,KAAOmP,EAAQ,GACtB4B,EAAO7Q,MAAQiP,EAAQ,IACI,QAAlByI,GACT7G,EAAO7Q,MAAQiP,EAAQ,GACvB4B,EAAO/Q,KAAOmP,EAAQ,IACK,WAAlByI,IACT7G,EAAO7Q,MAAQiP,EAAQ,GACvB4B,EAAO/Q,KAAOmP,EAAQ,IAGxBxX,EAAKoZ,OAASA,EACPpZ,GAkQLkI,OAAQ,GAoBVmZ,gBAAiB,CAEf5M,MAAO,IAEP+H,SAAS,EAET9Z,GAlRJ,SAAyB1C,EAAM6c,GAC7B,IAAIvD,EAAoBuD,EAAQvD,mBAAqBrF,GAAgBjU,EAAKM,SAAS8Y,QAK/EpZ,EAAKM,SAASmT,YAAc6F,IAC9BA,EAAoBrF,GAAgBqF,IAMtC,IAAIgI,EAAgB9D,GAAyB,aACzC+D,EAAevhB,EAAKM,SAAS8Y,OAAOva,MACpCwJ,EAAMkZ,EAAalZ,IACnBE,EAAOgZ,EAAahZ,KACpBiZ,EAAYD,EAAaD,GAE7BC,EAAalZ,IAAM,GACnBkZ,EAAahZ,KAAO,GACpBgZ,EAAaD,GAAiB,GAE9B,IAAI/H,EAAaJ,GAAcnZ,EAAKM,SAAS8Y,OAAQpZ,EAAKM,SAASmT,UAAWoJ,EAAQxD,QAASC,EAAmBtZ,EAAK8c,eAIvHyE,EAAalZ,IAAMA,EACnBkZ,EAAahZ,KAAOA,EACpBgZ,EAAaD,GAAiBE,EAE9B3E,EAAQtD,WAAaA,EAErB,IAAI9E,EAAQoI,EAAQ4E,SAChBrI,EAASpZ,EAAKwX,QAAQ4B,OAEtB0C,EAAQ,CACV4F,QAAS,SAAiB7H,GACxB,IAAI3b,EAAQkb,EAAOS,GAInB,OAHIT,EAAOS,GAAaN,EAAWM,KAAegD,EAAQ8E,sBACxDzjB,EAAQ/C,KAAKmb,IAAI8C,EAAOS,GAAYN,EAAWM,KAE1CxU,GAAe,GAAIwU,EAAW3b,IAEvC0jB,UAAW,SAAmB/H,GAC5B,IAAI4B,EAAyB,UAAd5B,EAAwB,OAAS,MAC5C3b,EAAQkb,EAAOqC,GAInB,OAHIrC,EAAOS,GAAaN,EAAWM,KAAegD,EAAQ8E,sBACxDzjB,EAAQ/C,KAAK0mB,IAAIzI,EAAOqC,GAAWlC,EAAWM,IAA4B,UAAdA,EAAwBT,EAAO3C,MAAQ2C,EAAO5C,UAErGnR,GAAe,GAAIoW,EAAUvd,KAWxC,OAPAuW,EAAM1W,SAAQ,SAAU8b,GACtB,IAAIzE,GAA+C,IAAxC,CAAC,OAAQ,OAAOhT,QAAQyX,GAAoB,UAAY,YACnET,EAASrR,GAAS,GAAIqR,EAAQ0C,EAAM1G,GAAMyE,OAG5C7Z,EAAKwX,QAAQ4B,OAASA,EAEfpZ,GA2NLyhB,SAAU,CAAC,OAAQ,QAAS,MAAO,UAOnCpI,QAAS,EAMTC,kBAAmB,gBAYrBwI,aAAc,CAEZrN,MAAO,IAEP+H,SAAS,EAET9Z,GAlgBJ,SAAsB1C,GACpB,IAAIkhB,EAAgBlhB,EAAKwX,QACrB4B,EAAS8H,EAAc9H,OACvB3F,EAAYyN,EAAczN,UAE1BoG,EAAY7Z,EAAK6Z,UAAUnd,MAAM,KAAK,GACtCtB,EAAQD,KAAKC,MACb+lB,GAAuD,IAA1C,CAAC,MAAO,UAAU/e,QAAQyX,GACvCzE,EAAO+L,EAAa,QAAU,SAC9BY,EAASZ,EAAa,OAAS,MAC/BxF,EAAcwF,EAAa,QAAU,SASzC,OAPI/H,EAAOhE,GAAQha,EAAMqY,EAAUsO,MACjC/hB,EAAKwX,QAAQ4B,OAAO2I,GAAU3mB,EAAMqY,EAAUsO,IAAW3I,EAAOuC,IAE9DvC,EAAO2I,GAAU3mB,EAAMqY,EAAU2B,MACnCpV,EAAKwX,QAAQ4B,OAAO2I,GAAU3mB,EAAMqY,EAAU2B,KAGzCpV,IA4fPgiB,MAAO,CAELvN,MAAO,IAEP+H,SAAS,EAET9Z,GApxBJ,SAAe1C,EAAM6c,GACnB,IAAIoF,EAGJ,IAAK9C,GAAmBnf,EAAKM,SAAS2b,UAAW,QAAS,gBACxD,OAAOjc,EAGT,IAAIkiB,EAAerF,EAAQphB,QAG3B,GAA4B,iBAAjBymB,GAIT,KAHAA,EAAeliB,EAAKM,SAAS8Y,OAAOrd,cAAcmmB,IAIhD,OAAOliB,OAKT,IAAKA,EAAKM,SAAS8Y,OAAO/S,SAAS6b,GAEjC,OADA5F,QAAQC,KAAK,iEACNvc,EAIX,IAAI6Z,EAAY7Z,EAAK6Z,UAAUnd,MAAM,KAAK,GACtCwkB,EAAgBlhB,EAAKwX,QACrB4B,EAAS8H,EAAc9H,OACvB3F,EAAYyN,EAAczN,UAE1B0N,GAAuD,IAA1C,CAAC,OAAQ,SAAS/e,QAAQyX,GAEvCpY,EAAM0f,EAAa,SAAW,QAC9BgB,EAAkBhB,EAAa,MAAQ,OACvC/L,EAAO+M,EAAgB5jB,cACvB6jB,EAAUjB,EAAa,OAAS,MAChCY,EAASZ,EAAa,SAAW,QACjCkB,EAAmBzH,GAAcsH,GAAczgB,GAQ/CgS,EAAUsO,GAAUM,EAAmBjJ,EAAOhE,KAChDpV,EAAKwX,QAAQ4B,OAAOhE,IAASgE,EAAOhE,IAAS3B,EAAUsO,GAAUM,IAG/D5O,EAAU2B,GAAQiN,EAAmBjJ,EAAO2I,KAC9C/hB,EAAKwX,QAAQ4B,OAAOhE,IAAS3B,EAAU2B,GAAQiN,EAAmBjJ,EAAO2I,IAE3E/hB,EAAKwX,QAAQ4B,OAAS7B,GAAcvX,EAAKwX,QAAQ4B,QAGjD,IAAIkJ,EAAS7O,EAAU2B,GAAQ3B,EAAUhS,GAAO,EAAI4gB,EAAmB,EAInExP,EAAMD,GAAyB5S,EAAKM,SAAS8Y,QAC7CmJ,EAAmB/lB,WAAWqW,EAAI,SAAWsP,IAC7CK,EAAmBhmB,WAAWqW,EAAI,SAAWsP,EAAkB,UAC/DM,EAAYH,EAAStiB,EAAKwX,QAAQ4B,OAAOhE,GAAQmN,EAAmBC,EAQxE,OALAC,EAAYtnB,KAAKmb,IAAInb,KAAK0mB,IAAIzI,EAAO3X,GAAO4gB,EAAkBI,GAAY,GAE1EziB,EAAKkiB,aAAeA,EACpBliB,EAAKwX,QAAQwK,OAAmC3c,GAA1B4c,EAAsB,GAAwC7M,EAAMja,KAAKunB,MAAMD,IAAapd,GAAe4c,EAAqBG,EAAS,IAAKH,GAE7JjiB,GA8sBLvE,QAAS,aAcXshB,KAAM,CAEJtI,MAAO,IAEP+H,SAAS,EAET9Z,GA5oBJ,SAAc1C,EAAM6c,GAElB,GAAIO,GAAkBpd,EAAKM,SAAS2b,UAAW,SAC7C,OAAOjc,EAGT,GAAIA,EAAK4c,SAAW5c,EAAK6Z,YAAc7Z,EAAKgd,kBAE1C,OAAOhd,EAGT,IAAIuZ,EAAaJ,GAAcnZ,EAAKM,SAAS8Y,OAAQpZ,EAAKM,SAASmT,UAAWoJ,EAAQxD,QAASwD,EAAQvD,kBAAmBtZ,EAAK8c,eAE3HjD,EAAY7Z,EAAK6Z,UAAUnd,MAAM,KAAK,GACtCimB,EAAoB1H,GAAqBpB,GACzCW,EAAYxa,EAAK6Z,UAAUnd,MAAM,KAAK,IAAM,GAE5CkmB,EAAY,GAEhB,OAAQ/F,EAAQgG,UACd,KAAK9C,GACH6C,EAAY,CAAC/I,EAAW8I,GACxB,MACF,KAAK5C,GACH6C,EAAYhD,GAAU/F,GACtB,MACF,KAAKkG,GACH6C,EAAYhD,GAAU/F,GAAW,GACjC,MACF,QACE+I,EAAY/F,EAAQgG,SAyDxB,OAtDAD,EAAU7kB,SAAQ,SAAU+kB,EAAMnW,GAChC,GAAIkN,IAAciJ,GAAQF,EAAUlhB,SAAWiL,EAAQ,EACrD,OAAO3M,EAGT6Z,EAAY7Z,EAAK6Z,UAAUnd,MAAM,KAAK,GACtCimB,EAAoB1H,GAAqBpB,GAEzC,IAAI0B,EAAgBvb,EAAKwX,QAAQ4B,OAC7B2J,EAAa/iB,EAAKwX,QAAQ/D,UAG1BrY,EAAQD,KAAKC,MACb4nB,EAA4B,SAAdnJ,GAAwBze,EAAMmgB,EAAczF,OAAS1a,EAAM2nB,EAAWxa,OAAuB,UAAdsR,GAAyBze,EAAMmgB,EAAchT,MAAQnN,EAAM2nB,EAAWjN,QAAwB,QAAd+D,GAAuBze,EAAMmgB,EAAc1F,QAAUza,EAAM2nB,EAAW1a,MAAsB,WAAdwR,GAA0Bze,EAAMmgB,EAAclT,KAAOjN,EAAM2nB,EAAWlN,QAEjUoN,EAAgB7nB,EAAMmgB,EAAchT,MAAQnN,EAAMme,EAAWhR,MAC7D2a,EAAiB9nB,EAAMmgB,EAAczF,OAAS1a,EAAMme,EAAWzD,OAC/DqN,EAAe/nB,EAAMmgB,EAAclT,KAAOjN,EAAMme,EAAWlR,KAC3D+a,EAAkBhoB,EAAMmgB,EAAc1F,QAAUza,EAAMme,EAAW1D,QAEjEwN,EAAoC,SAAdxJ,GAAwBoJ,GAA+B,UAAdpJ,GAAyBqJ,GAAgC,QAAdrJ,GAAuBsJ,GAA8B,WAAdtJ,GAA0BuJ,EAG3KjC,GAAuD,IAA1C,CAAC,MAAO,UAAU/e,QAAQyX,GAGvCyJ,IAA0BzG,EAAQ0G,iBAAmBpC,GAA4B,UAAd3G,GAAyByI,GAAiB9B,GAA4B,QAAd3G,GAAuB0I,IAAmB/B,GAA4B,UAAd3G,GAAyB2I,IAAiBhC,GAA4B,QAAd3G,GAAuB4I,GAGlQI,IAA8B3G,EAAQ4G,0BAA4BtC,GAA4B,UAAd3G,GAAyB0I,GAAkB/B,GAA4B,QAAd3G,GAAuByI,IAAkB9B,GAA4B,UAAd3G,GAAyB4I,IAAoBjC,GAA4B,QAAd3G,GAAuB2I,GAElRO,EAAmBJ,GAAyBE,GAE5CR,GAAeK,GAAuBK,KAExC1jB,EAAK4c,SAAU,GAEXoG,GAAeK,KACjBxJ,EAAY+I,EAAUjW,EAAQ,IAG5B+W,IACFlJ,EAvJR,SAA8BA,GAC5B,MAAkB,QAAdA,EACK,QACgB,UAAdA,EACF,MAEFA,EAiJWmJ,CAAqBnJ,IAGnCxa,EAAK6Z,UAAYA,GAAaW,EAAY,IAAMA,EAAY,IAI5Dxa,EAAKwX,QAAQ4B,OAASrR,GAAS,GAAI/H,EAAKwX,QAAQ4B,OAAQgC,GAAiBpb,EAAKM,SAAS8Y,OAAQpZ,EAAKwX,QAAQ/D,UAAWzT,EAAK6Z,YAE5H7Z,EAAOgc,GAAahc,EAAKM,SAAS2b,UAAWjc,EAAM,YAGhDA,GA4jBL6iB,SAAU,OAKVxJ,QAAS,EAOTC,kBAAmB,WAQnBiK,gBAAgB,EAQhBE,yBAAyB,GAU3BG,MAAO,CAELnP,MAAO,IAEP+H,SAAS,EAET9Z,GArQJ,SAAe1C,GACb,IAAI6Z,EAAY7Z,EAAK6Z,UACjBoG,EAAgBpG,EAAUnd,MAAM,KAAK,GACrCwkB,EAAgBlhB,EAAKwX,QACrB4B,EAAS8H,EAAc9H,OACvB3F,EAAYyN,EAAczN,UAE1B+H,GAAwD,IAA9C,CAAC,OAAQ,SAASpZ,QAAQ6d,GAEpC4D,GAA6D,IAA5C,CAAC,MAAO,QAAQzhB,QAAQ6d,GAO7C,OALA7G,EAAOoC,EAAU,OAAS,OAAS/H,EAAUwM,IAAkB4D,EAAiBzK,EAAOoC,EAAU,QAAU,UAAY,GAEvHxb,EAAK6Z,UAAYoB,GAAqBpB,GACtC7Z,EAAKwX,QAAQ4B,OAAS7B,GAAc6B,GAE7BpZ,IAkQP8Q,KAAM,CAEJ2D,MAAO,IAEP+H,SAAS,EAET9Z,GA9TJ,SAAc1C,GACZ,IAAKmf,GAAmBnf,EAAKM,SAAS2b,UAAW,OAAQ,mBACvD,OAAOjc,EAGT,IAAI8Z,EAAU9Z,EAAKwX,QAAQ/D,UACvBqQ,EAAQhb,GAAK9I,EAAKM,SAAS2b,WAAW,SAAUrG,GAClD,MAAyB,oBAAlBA,EAAS2H,QACfhE,WAEH,GAAIO,EAAQjE,OAASiO,EAAMzb,KAAOyR,EAAQvR,KAAOub,EAAMhO,OAASgE,EAAQzR,IAAMyb,EAAMjO,QAAUiE,EAAQhE,MAAQgO,EAAMvb,KAAM,CAExH,IAAkB,IAAdvI,EAAK8Q,KACP,OAAO9Q,EAGTA,EAAK8Q,MAAO,EACZ9Q,EAAK8H,WAAW,uBAAyB,OACpC,CAEL,IAAkB,IAAd9H,EAAK8Q,KACP,OAAO9Q,EAGTA,EAAK8Q,MAAO,EACZ9Q,EAAK8H,WAAW,wBAAyB,EAG3C,OAAO9H,IAoTP+jB,aAAc,CAEZtP,MAAO,IAEP+H,SAAS,EAET9Z,GAtgCJ,SAAsB1C,EAAM6c,GAC1B,IAAIhC,EAAIgC,EAAQhC,EACZE,EAAI8B,EAAQ9B,EACZ3B,EAASpZ,EAAKwX,QAAQ4B,OAItB4K,EAA8Blb,GAAK9I,EAAKM,SAAS2b,WAAW,SAAUrG,GACxE,MAAyB,eAAlBA,EAAS2H,QACf0G,qBACiC3O,IAAhC0O,GACF1H,QAAQC,KAAK,iIAEf,IAAI0H,OAAkD3O,IAAhC0O,EAA4CA,EAA8BnH,EAAQoH,gBAEpG9P,EAAeF,GAAgBjU,EAAKM,SAAS8Y,QAC7C8K,EAAmB9b,GAAsB+L,GAGzC6B,EAAS,CACXvN,SAAU2Q,EAAO3Q,UAGf+O,EA9DN,SAA2BxX,EAAMmkB,GAC/B,IAAIjD,EAAgBlhB,EAAKwX,QACrB4B,EAAS8H,EAAc9H,OACvB3F,EAAYyN,EAAczN,UAC1BiP,EAAQvnB,KAAKunB,MACbtnB,EAAQD,KAAKC,MAEbgpB,EAAU,SAAiBC,GAC7B,OAAOA,GAGLC,EAAiB5B,EAAMjP,EAAUgD,OACjC8N,EAAc7B,EAAMtJ,EAAO3C,OAE3B0K,GAA4D,IAA/C,CAAC,OAAQ,SAAS/e,QAAQpC,EAAK6Z,WAC5C2K,GAA+C,IAAjCxkB,EAAK6Z,UAAUzX,QAAQ,KAIrCqiB,EAAuBN,EAAwBhD,GAAcqD,GAH3CF,EAAiB,GAAMC,EAAc,EAGuC7B,EAAQtnB,EAAjEgpB,EACrCM,EAAqBP,EAAwBzB,EAAV0B,EAEvC,MAAO,CACL7b,KAAMkc,EANWH,EAAiB,GAAM,GAAKC,EAAc,GAAM,IAMtBC,GAAeL,EAAc/K,EAAO7Q,KAAO,EAAI6Q,EAAO7Q,MACjGF,IAAKqc,EAAkBtL,EAAO/Q,KAC9BwN,OAAQ6O,EAAkBtL,EAAOvD,QACjCC,MAAO2O,EAAoBrL,EAAOtD,QAoCtB6O,CAAkB3kB,EAAM7D,OAAOyoB,iBAAmB,IAAM1F,IAElEhJ,EAAc,WAAN2E,EAAiB,MAAQ,SACjC1E,EAAc,UAAN4E,EAAgB,OAAS,QAKjC8J,EAAmBrH,GAAyB,aAW5CjV,OAAO,EACPF,OAAM,EAqBV,GAhBIA,EAJU,WAAV6N,EAG4B,SAA1B/B,EAAalB,UACRkB,EAAayD,aAAeJ,EAAQ3B,QAEpCqO,EAAiB1N,OAASgB,EAAQ3B,OAGrC2B,EAAQnP,IAIZE,EAFU,UAAV4N,EAC4B,SAA1BhC,EAAalB,UACPkB,EAAawD,YAAcH,EAAQ1B,OAEnCoO,EAAiBzN,MAAQe,EAAQ1B,MAGpC0B,EAAQjP,KAEb0b,GAAmBY,EACrB7O,EAAO6O,GAAoB,eAAiBtc,EAAO,OAASF,EAAM,SAClE2N,EAAOE,GAAS,EAChBF,EAAOG,GAAS,EAChBH,EAAO6H,WAAa,gBACf,CAEL,IAAIiH,EAAsB,WAAV5O,GAAsB,EAAI,EACtC6O,EAAuB,UAAV5O,GAAqB,EAAI,EAC1CH,EAAOE,GAAS7N,EAAMyc,EACtB9O,EAAOG,GAAS5N,EAAOwc,EACvB/O,EAAO6H,WAAa3H,EAAQ,KAAOC,EAIrC,IAAIrO,EAAa,CACfkd,cAAehlB,EAAK6Z,WAQtB,OAJA7Z,EAAK8H,WAAaC,GAAS,GAAID,EAAY9H,EAAK8H,YAChD9H,EAAKgW,OAASjO,GAAS,GAAIiO,EAAQhW,EAAKgW,QACxChW,EAAK2c,YAAc5U,GAAS,GAAI/H,EAAKwX,QAAQwK,MAAOhiB,EAAK2c,aAElD3c,GAo7BLikB,iBAAiB,EAMjBpJ,EAAG,SAMHE,EAAG,SAkBLkK,WAAY,CAEVxQ,MAAO,IAEP+H,SAAS,EAET9Z,GAzpCJ,SAAoB1C,GApBpB,IAAuBvE,EAASqM,EAoC9B,OAXAkX,GAAUhf,EAAKM,SAAS8Y,OAAQpZ,EAAKgW,QAzBhBva,EA6BPuE,EAAKM,SAAS8Y,OA7BEtR,EA6BM9H,EAAK8H,WA5BzCjK,OAAOC,KAAKgK,GAAY/J,SAAQ,SAAUoe,IAE1B,IADFrU,EAAWqU,GAErB1gB,EAAQyL,aAAaiV,EAAMrU,EAAWqU,IAEtC1gB,EAAQmM,gBAAgBuU,MA0BxBnc,EAAKkiB,cAAgBrkB,OAAOC,KAAKkC,EAAK2c,aAAajb,QACrDsd,GAAUhf,EAAKkiB,aAAcliB,EAAK2c,aAG7B3c,GA2oCLklB,OA9nCJ,SAA0BzR,EAAW2F,EAAQyD,EAASsI,EAAiBzK,GAErE,IAAIW,EAAmBZ,GAAoBC,EAAOtB,EAAQ3F,EAAWoJ,EAAQC,eAKzEjD,EAAYD,GAAqBiD,EAAQhD,UAAWwB,EAAkBjC,EAAQ3F,EAAWoJ,EAAQZ,UAAUc,KAAKzD,kBAAmBuD,EAAQZ,UAAUc,KAAK1D,SAQ9J,OANAD,EAAOlS,aAAa,cAAe2S,GAInCmF,GAAU5F,EAAQ,CAAE3Q,SAAUoU,EAAQC,cAAgB,QAAU,aAEzDD,GAsnCLoH,qBAAiB3O,KAuGjB8P,GAAS,WASX,SAASA,EAAO3R,EAAW2F,GACzB,IAAIlT,EAAQpD,KAER+Z,EAAUxH,UAAU3T,OAAS,QAAsB4T,IAAjBD,UAAU,GAAmBA,UAAU,GAAK,GAClFqB,GAAe5T,KAAMsiB,GAErBtiB,KAAK4b,eAAiB,WACpB,OAAO2G,sBAAsBnf,EAAMuW,SAIrC3Z,KAAK2Z,OAASpK,GAASvP,KAAK2Z,OAAOhQ,KAAK3J,OAGxCA,KAAK+Z,QAAU9U,GAAS,GAAIqd,EAAOrE,SAAUlE,GAG7C/Z,KAAK4X,MAAQ,CACXgC,aAAa,EACbO,WAAW,EACXqB,cAAe,IAIjBxb,KAAK2Q,UAAYA,GAAaA,EAAU7B,OAAS6B,EAAU,GAAKA,EAChE3Q,KAAKsW,OAASA,GAAUA,EAAOxH,OAASwH,EAAO,GAAKA,EAGpDtW,KAAK+Z,QAAQZ,UAAY,GACzBpe,OAAOC,KAAKiK,GAAS,GAAIqd,EAAOrE,SAAS9E,UAAWY,EAAQZ,YAAYle,SAAQ,SAAUwf,GACxFrX,EAAM2W,QAAQZ,UAAUsB,GAAQxV,GAAS,GAAIqd,EAAOrE,SAAS9E,UAAUsB,IAAS,GAAIV,EAAQZ,UAAYY,EAAQZ,UAAUsB,GAAQ,OAIpIza,KAAKmZ,UAAYpe,OAAOC,KAAKgF,KAAK+Z,QAAQZ,WAAWhC,KAAI,SAAUsD,GACjE,OAAOxV,GAAS,CACdwV,KAAMA,GACLrX,EAAM2W,QAAQZ,UAAUsB,OAG5BpD,MAAK,SAAUC,EAAGC,GACjB,OAAOD,EAAE3F,MAAQ4F,EAAE5F,SAOrB3R,KAAKmZ,UAAUle,SAAQ,SAAUonB,GAC3BA,EAAgB3I,SAAW9J,GAAWyS,EAAgBD,SACxDC,EAAgBD,OAAOhf,EAAMuN,UAAWvN,EAAMkT,OAAQlT,EAAM2W,QAASsI,EAAiBjf,EAAMwU,UAKhG5X,KAAK2Z,SAEL,IAAI+B,EAAgB1b,KAAK+Z,QAAQ2B,cAC7BA,GAEF1b,KAAK2b,uBAGP3b,KAAK4X,MAAM8D,cAAgBA,EAqD7B,OA9CA7H,GAAYyO,EAAQ,CAAC,CACnBrlB,IAAK,SACL7B,MAAO,WACL,OAAOue,GAAOpe,KAAKyE,QAEpB,CACD/C,IAAK,UACL7B,MAAO,WACL,OAAO0f,GAAQvf,KAAKyE,QAErB,CACD/C,IAAK,uBACL7B,MAAO,WACL,OAAOugB,GAAqBpgB,KAAKyE,QAElC,CACD/C,IAAK,wBACL7B,MAAO,WACL,OAAO4f,GAAsBzf,KAAKyE,UA4B/BsiB,EA7HI,GAqJbA,GAAOE,OAA2B,oBAAXnpB,OAAyBA,OAASopB,QAAQC,YACjEJ,GAAO1F,WAAaA,GACpB0F,GAAOrE,SAAWA,GCxhFlB,IAAMxb,GAAO,WAaPkgB,GAAiB,IAAIjnB,OAAUknB,4BAiC/Btb,GAAU,CACdlC,OAAQ,EACR6U,MAAM,EACN4I,SAAU,eACVlS,UAAW,SACXxU,QAAS,UACT2mB,aAAc,MAGVjb,GAAc,CAClBzC,OAAQ,2BACR6U,KAAM,UACN4I,SAAU,mBACVlS,UAAW,mBACXxU,QAAS,SACT2mB,aAAc,iBASVC,GAAAA,WACJ,SAAAA,EAAYpqB,EAASkC,GACnBmF,KAAK2C,SAAWhK,EAChBqH,KAAKgjB,QAAU,KACfhjB,KAAK0I,QAAU1I,KAAK2I,WAAW9N,GAC/BmF,KAAKijB,MAAQjjB,KAAKkjB,kBAClBljB,KAAKmjB,UAAYnjB,KAAKojB,gBAEtBpjB,KAAKkJ,qBACL3L,EAAa5E,EA7EA,cA6EmBqH,iCAmBlCmE,OAAA,WACE,IAAInE,KAAK2C,SAAS0gB,WAAYrjB,KAAK2C,SAASU,UAAUE,SA3E9B,YA2ExB,CAIA,IAAM+f,EAAWtjB,KAAK2C,SAASU,UAAUE,SA9ErB,QAgFpBwf,EAASQ,aAELD,GAIJtjB,KAAKiO,WAGPA,KAAA,WACE,KAAIjO,KAAK2C,SAAS0gB,UAAYrjB,KAAK2C,SAASU,UAAUE,SA3F9B,aA2F+DvD,KAAKijB,MAAM5f,UAAUE,SA1FxF,SA0FpB,CAIA,IAAM4J,EAAS4V,EAASS,qBAAqBxjB,KAAK2C,UAC5C+I,EAAgB,CACpBA,cAAe1L,KAAK2C,UAKtB,IAFkBzC,EAAaqB,QAAQvB,KAAK2C,SA3GhC,mBA2GsD+I,GAEpD5J,iBAAd,CAKA,IAAK9B,KAAKmjB,UAAW,CACnB,QAAsB,IAAXb,GACT,MAAM,IAAIxV,UAAU,mEAGtB,IAAI2W,EAAmBzjB,KAAK2C,SAEG,WAA3B3C,KAAK0I,QAAQiI,UACf8S,EAAmBtW,EACVnT,EAAUgG,KAAK0I,QAAQiI,aAChC8S,EAAmBzjB,KAAK0I,QAAQiI,eAGa,IAAlC3Q,KAAK0I,QAAQiI,UAAU7B,SAChC2U,EAAmBzjB,KAAK0I,QAAQiI,UAAU,KAOhB,iBAA1B3Q,KAAK0I,QAAQma,UACf1V,EAAO9J,UAAU2H,IA1HU,mBA6H7BhL,KAAKgjB,QAAU,IAAIV,GAAOmB,EAAkBzjB,KAAKijB,MAAOjjB,KAAK0jB,oBAQvB,IAAAzd,EADxC,GAAI,iBAAkBzN,SAAS0N,kBAC5BiH,EAAOhK,QAhIc,gBAiItB8C,EAAA,IAAGE,OAAH9F,MAAA4F,EAAazN,SAASkE,KAAK6J,UACxBtL,SAAQ,SAAAwS,GAAI,OAAIvN,EAAaQ,GAAG+M,EAAM,YAAa,MV1BzC,kBU6BfzN,KAAK2C,SAASghB,QACd3jB,KAAK2C,SAASyB,aAAa,iBAAiB,GAE5CpE,KAAKijB,MAAM5f,UAAUc,OAnJD,QAoJpBnE,KAAK2C,SAASU,UAAUc,OApJJ,QAqJpBjE,EAAaqB,QAAQ4L,EA5JR,oBA4J6BzB,QAG5CsC,KAAA,WACE,IAAIhO,KAAK2C,SAAS0gB,WAAYrjB,KAAK2C,SAASU,UAAUE,SA1J9B,aA0JgEvD,KAAKijB,MAAM5f,UAAUE,SAzJzF,QAyJpB,CAIA,IAAM4J,EAAS4V,EAASS,qBAAqBxjB,KAAK2C,UAC5C+I,EAAgB,CACpBA,cAAe1L,KAAK2C,UAGJzC,EAAaqB,QAAQ4L,EA5K3B,mBA4K+CzB,GAE7C5J,mBAIV9B,KAAKgjB,SACPhjB,KAAKgjB,QAAQlI,UAGf9a,KAAKijB,MAAM5f,UAAUc,OA5KD,QA6KpBnE,KAAK2C,SAASU,UAAUc,OA7KJ,QA8KpBjE,EAAaqB,QAAQ4L,EAvLP,qBAuL6BzB,QAG7CxI,QAAA,WACE3F,EAAgByC,KAAK2C,SAzMR,eA0MbzC,EAAaC,IAAIH,KAAK2C,SAzMX,gBA0MX3C,KAAK2C,SAAW,KAChB3C,KAAKijB,MAAQ,KACTjjB,KAAKgjB,UACPhjB,KAAKgjB,QAAQlI,UACb9a,KAAKgjB,QAAU,SAInBrJ,OAAA,WACE3Z,KAAKmjB,UAAYnjB,KAAKojB,gBAClBpjB,KAAKgjB,SACPhjB,KAAKgjB,QAAQpH,oBAMjB1S,mBAAA,WAAqB,IAAA9F,EAAApD,KACnBE,EAAaQ,GAAGV,KAAK2C,SA5MR,qBA4M+B,SAAA9D,GAC1CA,EAAM2D,iBACN3D,EAAM+kB,kBACNxgB,EAAKe,eAITwE,WAAA,SAAW9N,GAST,OARAA,EAAMoK,EAAA,GACDjF,KAAK6jB,YAAYvc,QACjB3C,EAAYI,kBAAkB/E,KAAK2C,UACnC9H,GAGLF,EAAgB8H,GAAM5H,EAAQmF,KAAK6jB,YAAYhc,aAExChN,KAGTqoB,gBAAA,WACE,OAAOpd,EAAeqB,KAAKnH,KAAK2C,SAhNd,kBAgNuC,MAG3DmhB,cAAA,WACE,IAAMC,EAAiB/jB,KAAK2C,SAAS3G,WACjC+a,EA/MiB,eA8NrB,OAZIgN,EAAe1gB,UAAUE,SAjOP,UAkOpBwT,EAAY/W,KAAKijB,MAAM5f,UAAUE,SA/NV,uBAWJ,UADH,YAwNPwgB,EAAe1gB,UAAUE,SApOX,aAqOvBwT,EArNkB,cAsNTgN,EAAe1gB,UAAUE,SArOZ,YAsOtBwT,EAtNiB,aAuNR/W,KAAKijB,MAAM5f,UAAUE,SAtOP,yBAuOvBwT,EA1NsB,cA6NjBA,KAGTqM,cAAA,WACE,OAAO3iB,QAAQT,KAAK2C,SAASQ,QAAd,eAGjB6gB,WAAA,WAAa,IAAA5Z,EAAApK,KACLoF,EAAS,GAef,MAbmC,mBAAxBpF,KAAK0I,QAAQtD,OACtBA,EAAOxF,GAAK,SAAA1C,GAMV,OALAA,EAAKwX,QAALzP,EAAA,GACK/H,EAAKwX,QACJtK,EAAK1B,QAAQtD,OAAOlI,EAAKwX,QAAStK,EAAKzH,WAAa,IAGnDzF,GAGTkI,EAAOA,OAASpF,KAAK0I,QAAQtD,OAGxBA,KAGTse,iBAAA,WACE,IAAMZ,EAAe,CACnB/L,UAAW/W,KAAK8jB,gBAChB3K,UAAW,CACT/T,OAAQpF,KAAKgkB,aACb/J,KAAM,CACJP,QAAS1Z,KAAK0I,QAAQuR,MAExBsE,gBAAiB,CACf/H,kBAAmBxW,KAAK0I,QAAQma,YAYtC,MAN6B,WAAzB7iB,KAAK0I,QAAQvM,UACf2mB,EAAa3J,UAAUgJ,WAAa,CAClCzI,SAAS,IAIbzU,EAAA,GACK6d,EACA9iB,KAAK0I,QAAQoa,iBAMbmB,kBAAP,SAAyBtrB,EAASkC,GAChC,IAAIqC,EAAOK,EAAa5E,EA7TX,eAoUb,GAJKuE,IACHA,EAAO,IAAI6lB,EAASpqB,EAHY,iBAAXkC,EAAsBA,EAAS,OAMhC,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,SAIF6I,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACfof,EAASkB,kBAAkBjkB,KAAMnF,SAI9B0oB,WAAP,SAAkB1kB,GAChB,IAAIA,GA3UmB,IA2UTA,EAAM6F,SACF,UAAf7F,EAAMuB,MA/UG,QA+UiBvB,EAAM5B,KAMnC,IAFA,IAAMinB,EAAUpe,EAAeE,KA1TN,4BA4ThBtH,EAAI,EAAGC,EAAMulB,EAAQtlB,OAAQF,EAAIC,EAAKD,IAAK,CAClD,IAAMyO,EAAS4V,EAASS,qBAAqBU,EAAQxlB,IAC/CylB,EAAU5mB,EAAa2mB,EAAQxlB,GA7V1B,eA8VLgN,EAAgB,CACpBA,cAAewY,EAAQxlB,IAOzB,GAJIG,GAAwB,UAAfA,EAAMuB,OACjBsL,EAAc0Y,WAAavlB,GAGxBslB,EAAL,CAIA,IAAME,EAAeF,EAAQlB,MAC7B,GAAKiB,EAAQxlB,GAAG2E,UAAUE,SApVR,QAwVlB,KAAI1E,IAA0B,UAAfA,EAAMuB,MACjB,kBAAkBzE,KAAKkD,EAAMkB,OAAOmL,UACpB,UAAfrM,EAAMuB,MA3WD,QA2WqBvB,EAAM5B,MACjConB,EAAa9gB,SAAS1E,EAAMkB,SAKhC,IADkBG,EAAaqB,QAAQ4L,EAzW7B,mBAyWiDzB,GAC7C5J,iBAAd,CAMgD,IAAA0E,EAAhD,GAAI,iBAAkBhO,SAAS0N,iBAC7BM,EAAA,IAAGL,OAAH9F,MAAAmG,EAAahO,SAASkE,KAAK6J,UACxBtL,SAAQ,SAAAwS,GAAI,OAAIvN,EAAaC,IAAIsN,EAAM,YAAa,MVrP5C,kBUwPbyW,EAAQxlB,GAAG0F,aAAa,gBAAiB,SAErC+f,EAAQnB,SACVmB,EAAQnB,QAAQlI,UAGlBuJ,EAAahhB,UAAUC,OAjXL,QAkXlB4gB,EAAQxlB,GAAG2E,UAAUC,OAlXH,QAmXlBpD,EAAaqB,QAAQ4L,EA5XT,qBA4X+BzB,SAIxC8X,qBAAP,SAA4B7qB,GAC1B,OAAOO,EAAuBP,IAAYA,EAAQqD,cAG7CsoB,sBAAP,SAA6BzlB,GAQ3B,KAAI,kBAAkBlD,KAAKkD,EAAMkB,OAAOmL,SArZ1B,UAsZZrM,EAAM5B,KAvZO,WAuZe4B,EAAM5B,MAnZjB,cAoZf4B,EAAM5B,KArZO,YAqZmB4B,EAAM5B,KACtC4B,EAAMkB,OAAOoD,QA5XC,oBA6Xfwf,GAAehnB,KAAKkD,EAAM5B,QAI7B4B,EAAM2D,iBACN3D,EAAM+kB,mBAEF5jB,KAAKqjB,WAAYrjB,KAAKqD,UAAUE,SA/YZ,aA+YxB,CAIA,IAAM4J,EAAS4V,EAASS,qBAAqBxjB,MACvCsjB,EAAWtjB,KAAKqD,UAAUE,SAnZZ,QAqZpB,GAxae,WAwaX1E,EAAM5B,IAIR,OAHe+C,KAAK+F,QA9YG,4BA8Y6B/F,KAAO8F,EAAekB,KAAKhH,KA9YxD,4BA8YoF,IACpG2jB,aACPZ,EAASQ,aAIX,GAAKD,GA9aS,UA8aGzkB,EAAM5B,IAAvB,CAKA,IAAMsnB,EAAQze,EAAeE,KArZF,8DAqZ+BmH,GAAQ1G,OAAO3K,GAEzE,GAAKyoB,EAAM3lB,OAAX,CAIA,IAAIiL,EAAQ0a,EAAMjlB,QAAQT,EAAMkB,QAvbf,YAybblB,EAAM5B,KAAwB4M,EAAQ,GACxCA,IAzbiB,cA4bfhL,EAAM5B,KAA0B4M,EAAQ0a,EAAM3lB,OAAS,GACzDiL,IAMF0a,EAFA1a,GAAmB,IAAXA,EAAe,EAAIA,GAEd8Z,cAvBXZ,EAASQ,iBA0BNzf,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EA/cP,wDAmFb,MApFY,+CAwFZ,OAAO2O,uCAIP,OAAOO,SAvBLkb,GAqZN7iB,EAAaQ,GAAGlI,SAtcY,+BAYC,2BA0b2CuqB,GAASuB,uBACjFpkB,EAAaQ,GAAGlI,SAvcY,+BAcN,iBAyb2CuqB,GAASuB,uBAC1EpkB,EAAaQ,GAAGlI,SAzcU,6BAycsBuqB,GAASQ,YACzDrjB,EAAaQ,GAAGlI,SAxcU,6BAwcsBuqB,GAASQ,YACzDrjB,EAAaQ,GAAGlI,SA3cU,6BAaG,4BA8byC,SAAUqG,GAC9EA,EAAM2D,iBACN3D,EAAM+kB,kBACNb,GAASkB,kBAAkBjkB,KAAM,aAEnCE,EAAaQ,GAAGlI,SAhdU,6BAcE,kBAkcyC,SAAAuS,GAAC,OAAIA,EAAE6Y,qBAS5EhnB,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAG6C,IAChCf,EAAE9B,GAAG6C,IAAQsgB,GAASrf,gBACtBhC,EAAE9B,GAAG6C,IAAMuB,YAAc+e,GACzBrhB,EAAE9B,GAAG6C,IAAMwB,WAAa,WAEtB,OADAvC,EAAE9B,GAAG6C,IAAQsB,EACNgf,GAASrf,qBCrftB,IAOM4D,GAAU,CACdkd,UAAU,EACVhd,UAAU,EACVmc,OAAO,EACP1V,MAAM,GAGFpG,GAAc,CAClB2c,SAAU,mBACVhd,SAAU,UACVmc,MAAO,UACP1V,KAAM,WAoCFwW,GAAAA,WACJ,SAAAA,EAAY9rB,EAASkC,GACnBmF,KAAK0I,QAAU1I,KAAK2I,WAAW9N,GAC/BmF,KAAK2C,SAAWhK,EAChBqH,KAAK0kB,QAAU5e,EAAeQ,QAjBV,gBAiBmC3N,GACvDqH,KAAK2kB,UAAY,KACjB3kB,KAAK4kB,UAAW,EAChB5kB,KAAK6kB,oBAAqB,EAC1B7kB,KAAK8kB,sBAAuB,EAC5B9kB,KAAKqN,kBAAmB,EACxBrN,KAAK+kB,gBAAkB,EACvBxnB,EAAa5E,EA/DA,WA+DmBqH,iCAelCmE,OAAA,SAAOuH,GACL,OAAO1L,KAAK4kB,SAAW5kB,KAAKgO,OAAShO,KAAKiO,KAAKvC,MAGjDuC,KAAA,SAAKvC,GAAe,IAAAtI,EAAApD,KAClB,IAAIA,KAAK4kB,WAAY5kB,KAAKqN,iBAA1B,CAIIrN,KAAK2C,SAASU,UAAUE,SApDR,UAqDlBvD,KAAKqN,kBAAmB,GAG1B,IAAM2X,EAAY9kB,EAAaqB,QAAQvB,KAAK2C,SArEhC,gBAqEsD,CAChE+I,cAAAA,IAGE1L,KAAK4kB,UAAYI,EAAUljB,mBAI/B9B,KAAK4kB,UAAW,EAEhB5kB,KAAKilB,kBACLjlB,KAAKklB,gBAELllB,KAAKmlB,gBAELnlB,KAAKolB,kBACLplB,KAAKqlB,kBAELnlB,EAAaQ,GAAGV,KAAK2C,SAnFA,yBAgBK,0BAsExB,SAAA9D,GAAK,OAAIuE,EAAK4K,KAAKnP,MAGrBqB,EAAaQ,GAAGV,KAAK0kB,QAtFI,8BAsF8B,WACrDxkB,EAAaS,IAAIyC,EAAKT,SAxFD,4BAwFkC,SAAA9D,GACjDA,EAAMkB,SAAWqD,EAAKT,WACxBS,EAAK0hB,sBAAuB,SAKlC9kB,KAAKslB,eAAc,WAAA,OAAMliB,EAAKmiB,aAAa7Z,WAG7CsC,KAAA,SAAKnP,GAAO,IAAAuL,EAAApK,KAKV,IAJInB,GACFA,EAAM2D,iBAGHxC,KAAK4kB,WAAY5kB,KAAKqN,oBAITnN,EAAaqB,QAAQvB,KAAK2C,SApHhC,iBAsHEb,iBAAd,CAIA9B,KAAK4kB,UAAW,EAChB,IAAMY,EAAaxlB,KAAK2C,SAASU,UAAUE,SA3GvB,QA2HpB,GAdIiiB,IACFxlB,KAAKqN,kBAAmB,GAG1BrN,KAAKolB,kBACLplB,KAAKqlB,kBAELnlB,EAAaC,IAAI3H,SA/HF,oBAiIfwH,KAAK2C,SAASU,UAAUC,OArHJ,QAuHpBpD,EAAaC,IAAIH,KAAK2C,SAjID,0BAkIrBzC,EAAaC,IAAIH,KAAK0kB,QA/HG,8BAiIrBc,EAAY,CACd,IAAMjsB,EAAqBJ,EAAiC6G,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,SXvLL,iBWuL+B,SAAA9D,GAAK,OAAIuL,EAAKqb,WAAW5mB,MACzE1E,EAAqB6F,KAAK2C,SAAUpJ,QAEpCyG,KAAKylB,iBAITviB,QAAA,WACE,CAAC7J,OAAQ2G,KAAK2C,SAAU3C,KAAK0kB,SAC1BzpB,SAAQ,SAAAyqB,GAAW,OAAIxlB,EAAaC,IAAIulB,EAzKhC,gBAgLXxlB,EAAaC,IAAI3H,SAzJF,oBA2Jf+E,EAAgByC,KAAK2C,SAnLR,YAqLb3C,KAAK0I,QAAU,KACf1I,KAAK2C,SAAW,KAChB3C,KAAK0kB,QAAU,KACf1kB,KAAK2kB,UAAY,KACjB3kB,KAAK4kB,SAAW,KAChB5kB,KAAK6kB,mBAAqB,KAC1B7kB,KAAK8kB,qBAAuB,KAC5B9kB,KAAKqN,iBAAmB,KACxBrN,KAAK+kB,gBAAkB,QAGzBY,aAAA,WACE3lB,KAAKmlB,mBAKPxc,WAAA,SAAW9N,GAMT,OALAA,EAAMoK,EAAA,GACDqC,GACAzM,GAELF,EA7MS,QA6MaE,EAAQgN,IACvBhN,KAGT0qB,aAAA,SAAa7Z,GAAe,IAAAnB,EAAAvK,KACpBwlB,EAAaxlB,KAAK2C,SAASU,UAAUE,SA7KvB,QA8KdqiB,EAAY9f,EAAeQ,QAzKT,cAyKsCtG,KAAK0kB,SAE9D1kB,KAAK2C,SAAS3G,YACfgE,KAAK2C,SAAS3G,WAAW9B,WAAa2M,KAAKC,cAE7CtO,SAASkE,KAAKmpB,YAAY7lB,KAAK2C,UAGjC3C,KAAK2C,SAAS5G,MAAMI,QAAU,QAC9B6D,KAAK2C,SAASmC,gBAAgB,eAC9B9E,KAAK2C,SAASyB,aAAa,cAAc,GACzCpE,KAAK2C,SAASyB,aAAa,OAAQ,UACnCpE,KAAK2C,SAAS6C,UAAY,EAEtBogB,IACFA,EAAUpgB,UAAY,GAGpBggB,GACFlpB,EAAO0D,KAAK2C,UAGd3C,KAAK2C,SAASU,UAAU2H,IAnMJ,QAqMhBhL,KAAK0I,QAAQib,OACf3jB,KAAK8lB,gBAGP,IAAMC,EAAqB,WACrBxb,EAAK7B,QAAQib,OACfpZ,EAAK5H,SAASghB,QAGhBpZ,EAAK8C,kBAAmB,EACxBnN,EAAaqB,QAAQgJ,EAAK5H,SA5Nf,iBA4NsC,CAC/C+I,cAAAA,KAIJ,GAAI8Z,EAAY,CACd,IAAMjsB,EAAqBJ,EAAiC6G,KAAK0kB,SAEjExkB,EAAaS,IAAIX,KAAK0kB,QXjRL,gBWiR8BqB,GAC/C5rB,EAAqB6F,KAAK0kB,QAASnrB,QAEnCwsB,OAIJD,cAAA,WAAgB,IAAAvZ,EAAAvM,KACdE,EAAaC,IAAI3H,SA3OF,oBA4Of0H,EAAaQ,GAAGlI,SA5OD,oBA4O0B,SAAAqG,GACnCrG,WAAaqG,EAAMkB,QACnBwM,EAAK5J,WAAa9D,EAAMkB,QACvBwM,EAAK5J,SAASY,SAAS1E,EAAMkB,SAChCwM,EAAK5J,SAASghB,cAKpByB,gBAAA,WAAkB,IAAAY,EAAAhmB,KACZA,KAAK4kB,SACP1kB,EAAaQ,GAAGV,KAAK2C,SApPA,4BAoPiC,SAAA9D,GAChDmnB,EAAKtd,QAAQlB,UA7QN,WA6QkB3I,EAAM5B,KACjC4B,EAAM2D,iBACNwjB,EAAKhY,QACKgY,EAAKtd,QAAQlB,UAhRd,WAgR0B3I,EAAM5B,KACzC+oB,EAAKC,gCAIT/lB,EAAaC,IAAIH,KAAK2C,SA7PD,+BAiQzB0iB,gBAAA,WAAkB,IAAAa,EAAAlmB,KACZA,KAAK4kB,SACP1kB,EAAaQ,GAAGrH,OArQJ,mBAqQ0B,WAAA,OAAM6sB,EAAKf,mBAEjDjlB,EAAaC,IAAI9G,OAvQL,sBA2QhBosB,WAAA,WAAa,IAAAU,EAAAnmB,KACXA,KAAK2C,SAAS5G,MAAMI,QAAU,OAC9B6D,KAAK2C,SAASyB,aAAa,eAAe,GAC1CpE,KAAK2C,SAASmC,gBAAgB,cAC9B9E,KAAK2C,SAASmC,gBAAgB,QAC9B9E,KAAKqN,kBAAmB,EACxBrN,KAAKslB,eAAc,WACjB9sB,SAASkE,KAAK2G,UAAUC,OAzQN,cA0QlB6iB,EAAKC,oBACLD,EAAKE,kBACLnmB,EAAaqB,QAAQ4kB,EAAKxjB,SAzRd,yBA6RhB2jB,gBAAA,WACEtmB,KAAK2kB,UAAU3oB,WAAWyH,YAAYzD,KAAK2kB,WAC3C3kB,KAAK2kB,UAAY,QAGnBW,cAAA,SAAczoB,GAAU,IAAA0pB,EAAAvmB,KAChBwmB,EAAUxmB,KAAK2C,SAASU,UAAUE,SArRpB,QAAA,OAuRlB,GAEF,GAAIvD,KAAK4kB,UAAY5kB,KAAK0I,QAAQ8b,SAAU,CA6B1C,GA5BAxkB,KAAK2kB,UAAYnsB,SAASiuB,cAAc,OACxCzmB,KAAK2kB,UAAU+B,UA7RO,iBA+RlBF,GACFxmB,KAAK2kB,UAAUthB,UAAU2H,IAAIwb,GAG/BhuB,SAASkE,KAAKmpB,YAAY7lB,KAAK2kB,WAE/BzkB,EAAaQ,GAAGV,KAAK2C,SA5SF,0BA4SiC,SAAA9D,GAC9C0nB,EAAKzB,qBACPyB,EAAKzB,sBAAuB,EAI1BjmB,EAAMkB,SAAWlB,EAAM8nB,eAI3BJ,EAAKN,gCAGHO,GACFlqB,EAAO0D,KAAK2kB,WAGd3kB,KAAK2kB,UAAUthB,UAAU2H,IAnTP,SAqTbwb,EAEH,YADA3pB,IAIF,IAAM+pB,EAA6BztB,EAAiC6G,KAAK2kB,WAEzEzkB,EAAaS,IAAIX,KAAK2kB,UXtXL,gBWsXgC9nB,GACjD1C,EAAqB6F,KAAK2kB,UAAWiC,QAChC,IAAK5mB,KAAK4kB,UAAY5kB,KAAK2kB,UAAW,CAC3C3kB,KAAK2kB,UAAUthB,UAAUC,OA/TP,QAiUlB,IAAMujB,EAAiB,WACrBN,EAAKD,kBACLzpB,KAGF,GAAImD,KAAK2C,SAASU,UAAUE,SAvUV,QAuUqC,CACrD,IAAMqjB,EAA6BztB,EAAiC6G,KAAK2kB,WACzEzkB,EAAaS,IAAIX,KAAK2kB,UXlYP,gBWkYkCkC,GACjD1sB,EAAqB6F,KAAK2kB,UAAWiC,QAErCC,SAGFhqB,OAIJopB,2BAAA,WAA6B,IAAAa,EAAA9mB,KAC3B,GAA8B,WAA1BA,KAAK0I,QAAQ8b,SAAuB,CAEtC,GADkBtkB,EAAaqB,QAAQvB,KAAK2C,SApWxB,0BAqWNb,iBACZ,OAGF,IAAMilB,EAAqB/mB,KAAK2C,SAASqkB,aAAexuB,SAAS0N,gBAAgB4O,aAE5EiS,IACH/mB,KAAK2C,SAAS5G,MAAM0U,UAAY,UAGlCzQ,KAAK2C,SAASU,UAAU2H,IA9VJ,gBA+VpB,IAAMic,EAA0B9tB,EAAiC6G,KAAK0kB,SACtExkB,EAAaC,IAAIH,KAAK2C,SX3ZL,iBW4ZjBzC,EAAaS,IAAIX,KAAK2C,SX5ZL,iBW4Z+B,WAC9CmkB,EAAKnkB,SAASU,UAAUC,OAlWN,gBAmWbyjB,IACH7mB,EAAaS,IAAImmB,EAAKnkB,SX/ZT,iBW+ZmC,WAC9CmkB,EAAKnkB,SAAS5G,MAAM0U,UAAY,MAElCtW,EAAqB2sB,EAAKnkB,SAAUskB,OAGxC9sB,EAAqB6F,KAAK2C,SAAUskB,GACpCjnB,KAAK2C,SAASghB,aAEd3jB,KAAKgO,UAQTmX,cAAA,WACE,IAAM4B,EACJ/mB,KAAK2C,SAASqkB,aAAexuB,SAAS0N,gBAAgB4O,cAEnD9U,KAAK6kB,oBAAsBkC,IAC9B/mB,KAAK2C,SAAS5G,MAAMmrB,YAAiBlnB,KAAK+kB,gBAA1C,MAGE/kB,KAAK6kB,qBAAuBkC,IAC9B/mB,KAAK2C,SAAS5G,MAAMorB,aAAkBnnB,KAAK+kB,gBAA3C,SAIJqB,kBAAA,WACEpmB,KAAK2C,SAAS5G,MAAMmrB,YAAc,GAClClnB,KAAK2C,SAAS5G,MAAMorB,aAAe,MAGrClC,gBAAA,WACE,IAAM5f,EAAO7M,SAASkE,KAAK4I,wBAC3BtF,KAAK6kB,mBAAqBxsB,KAAKunB,MAAMva,EAAKI,KAAOJ,EAAK2N,OAAS3Z,OAAO0c,WACtE/V,KAAK+kB,gBAAkB/kB,KAAKonB,wBAG9BlC,cAAA,WAAgB,IAAAmC,EAAArnB,KACd,GAAIA,KAAK6kB,mBAAoB,CAK3B/e,EAAeE,KA7YU,qDA8YtB/K,SAAQ,SAAAtC,GACP,IAAM2uB,EAAgB3uB,EAAQoD,MAAMorB,aAC9BI,EAAoBluB,OAAOC,iBAAiBX,GAAS,iBAC3DgM,EAAYC,iBAAiBjM,EAAS,gBAAiB2uB,GACvD3uB,EAAQoD,MAAMorB,aAAkBztB,WAAW6tB,GAAqBF,EAAKtC,gBAArE,QAIJjf,EAAeE,KArZW,eAsZvB/K,SAAQ,SAAAtC,GACP,IAAM6uB,EAAe7uB,EAAQoD,MAAMmc,YAC7BuP,EAAmBpuB,OAAOC,iBAAiBX,GAAS,gBAC1DgM,EAAYC,iBAAiBjM,EAAS,eAAgB6uB,GACtD7uB,EAAQoD,MAAMmc,YAAiBxe,WAAW+tB,GAAoBJ,EAAKtC,gBAAnE,QAIJ,IAAMuC,EAAgB9uB,SAASkE,KAAKX,MAAMorB,aACpCI,EAAoBluB,OAAOC,iBAAiBd,SAASkE,MAAM,iBAEjEiI,EAAYC,iBAAiBpM,SAASkE,KAAM,gBAAiB4qB,GAC7D9uB,SAASkE,KAAKX,MAAMorB,aAAkBztB,WAAW6tB,GAAqBvnB,KAAK+kB,gBAA3E,KAGFvsB,SAASkE,KAAK2G,UAAU2H,IA/aJ,iBAkbtBqb,gBAAA,WAEEvgB,EAAeE,KA3aY,qDA4axB/K,SAAQ,SAAAtC,GACP,IAAM4d,EAAU5R,EAAYQ,iBAAiBxM,EAAS,sBAC/B,IAAZ4d,IACT5R,EAAYE,oBAAoBlM,EAAS,iBACzCA,EAAQoD,MAAMorB,aAAe5Q,MAKnCzQ,EAAeE,KApba,eAqbzB/K,SAAQ,SAAAtC,GACP,IAAM+uB,EAAS/iB,EAAYQ,iBAAiBxM,EAAS,qBAC/B,IAAX+uB,IACT/iB,EAAYE,oBAAoBlM,EAAS,gBACzCA,EAAQoD,MAAMmc,YAAcwP,MAKlC,IAAMnR,EAAU5R,EAAYQ,iBAAiB3M,SAASkE,KAAM,sBACrC,IAAZ6Z,EACT/d,SAASkE,KAAKX,MAAMorB,aAAe,IAEnCxiB,EAAYE,oBAAoBrM,SAASkE,KAAM,iBAC/ClE,SAASkE,KAAKX,MAAMorB,aAAe5Q,MAIvC6Q,mBAAA,WACE,IAAMO,EAAYnvB,SAASiuB,cAAc,OACzCkB,EAAUjB,UArdwB,0BAsdlCluB,SAASkE,KAAKmpB,YAAY8B,GAC1B,IAAMC,EAAiBD,EAAUriB,wBAAwBqO,MAAQgU,EAAU9S,YAE3E,OADArc,SAASkE,KAAK+G,YAAYkkB,GACnBC,KAKFlkB,gBAAP,SAAuB7I,EAAQ6Q,GAC7B,OAAO1L,KAAK2D,MAAK,WACf,IAAIzG,EAAOK,EAAayC,KAhgBb,YAigBL0I,EAAOzD,EAAA,GACRqC,GACA3C,EAAYI,kBAAkB/E,MACX,iBAAXnF,GAAuBA,EAASA,EAAS,IAOtD,GAJKqC,IACHA,EAAO,IAAIunB,EAAMzkB,KAAM0I,IAGH,iBAAX7N,EAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,GAAQ6Q,QACJhD,EAAQuF,MACjB/Q,EAAK+Q,KAAKvC,SAKT5H,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAxhBP,qDAqEb,MAtEY,+CA0EZ,OAAO2O,SArBLmd,GA8eNvkB,EAAaQ,GAAGlI,SApgBU,0BAWG,yBAyfyC,SAAUqG,GAAO,IAAAgpB,EAAA7nB,KAC/ED,EAAS7G,EAAuB8G,MAEjB,MAAjBA,KAAKkL,SAAoC,SAAjBlL,KAAKkL,SAC/BrM,EAAM2D,iBAGRtC,EAAaS,IAAIZ,EAnhBH,iBAmhBuB,SAAAilB,GAC/BA,EAAUljB,kBAKd5B,EAAaS,IAAIZ,EA1hBH,mBA0hByB,WACjCjE,EAAU+rB,IACZA,EAAKlE,cAKX,IAAIzmB,EAAOK,EAAawC,EAtjBT,YAujBf,IAAK7C,EAAM,CACT,IAAMrC,EAAMoK,EAAA,GACPN,EAAYI,kBAAkBhF,GAC9B4E,EAAYI,kBAAkB/E,OAGnC9C,EAAO,IAAIunB,GAAM1kB,EAAQlF,GAG3BqC,EAAK+Q,KAAKjO,SAUZpD,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAF,MAC3B8B,EAAE9B,GAAF,MAAa6kB,GAAM/gB,gBACnBhC,EAAE9B,GAAF,MAAWoE,YAAcygB,GACzB/iB,EAAE9B,GAAF,MAAWqE,WAAa,WAEtB,OADAvC,EAAE9B,GAAF,MAAamE,EACN0gB,GAAM/gB,qBC3mBnB,IAAMokB,GAAW,CACf,aACA,OACA,OACA,WACA,WACA,SACA,MACA,cAUIC,GAAmB,8DAOnBC,GAAmB,qIAyBZC,GAAmB,CAE9BC,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAzCP,kBA0C7B5Q,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BF,KAAM,GACNG,EAAG,GACH4Q,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJpqB,EAAG,GACHqqB,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IAGC,SAASC,GAAaC,EAAYC,EAAWC,GAAY,IAAA9jB,EAC9D,IAAK4jB,EAAWjrB,OACd,OAAOirB,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAQpB,IALA,IACMG,GADY,IAAI3wB,OAAO4wB,WACKC,gBAAgBL,EAAY,aACxDM,EAAgBpvB,OAAOC,KAAK8uB,GAC5BM,GAAWnkB,EAAA,IAAGE,OAAH9F,MAAA4F,EAAa+jB,EAAgBttB,KAAKoD,iBAAiB,MAZNuqB,EAAA,SAcrD3rB,EAAOC,GAd8C,IAAA6H,EAetD4P,EAAKgU,EAAS1rB,GACd4rB,EAASlU,EAAGjG,SAAS1U,cAE3B,IAAuC,IAAnC0uB,EAAc7qB,QAAQgrB,GAGxB,OAFAlU,EAAGpa,WAAWyH,YAAY2S,GAE1B,WAGF,IAAMmU,GAAgB/jB,EAAA,IAAGL,OAAH9F,MAAAmG,EAAa4P,EAAGpR,YAChCwlB,EAAoB,GAAGrkB,OAAO2jB,EAAU,MAAQ,GAAIA,EAAUQ,IAAW,IAE/EC,EAActvB,SAAQ,SAAAwvB,IApFD,SAACA,EAAMC,GAC9B,IAAMC,EAAWF,EAAKta,SAAS1U,cAE/B,IAAgD,IAA5CivB,EAAqBprB,QAAQqrB,GAC/B,OAAoC,IAAhC7C,GAASxoB,QAAQqrB,IACZlqB,QAAQgqB,EAAKG,UAAUpvB,MAAMusB,KAAqB0C,EAAKG,UAAUpvB,MAAMwsB,KASlF,IAHA,IAAM6C,EAASH,EAAqBjkB,QAAO,SAAAqkB,GAAS,OAAIA,aAAqBpvB,UAGpEgD,EAAI,EAAGC,EAAMksB,EAAOjsB,OAAQF,EAAIC,EAAKD,IAC5C,GAAIisB,EAASnvB,MAAMqvB,EAAOnsB,IACxB,OAAO,EAIX,OAAO,GAiEEqsB,CAAiBN,EAAMD,IAC1BpU,EAAGtR,gBAAgB2lB,EAAKta,cAfrBzR,EAAI,EAAGC,EAAMyrB,EAASxrB,OAAQF,EAAIC,EAAKD,IAAK2rB,EAA5C3rB,GAoBT,OAAOsrB,EAAgBttB,KAAKsuB,UC1F9B,IAAMvoB,GAAO,UAKPwoB,GAAqB,IAAIvvB,OAAJ,wBAAyC,KAC9DwvB,GAAwB,CAAC,WAAY,YAAa,cAElDrjB,GAAc,CAClBsjB,UAAW,UACXC,SAAU,SACVC,MAAO,4BACP9pB,QAAS,SACT+pB,MAAO,kBACP5Y,KAAM,UACN9Z,SAAU,mBACVme,UAAW,oBACX3R,OAAQ,2BACRgJ,UAAW,2BACXmd,kBAAmB,iBACnB1I,SAAU,mBACV2I,SAAU,UACVzB,WAAY,kBACZD,UAAW,SACXhH,aAAc,iBAGV2I,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAO,QACPC,OAAQ,SACRC,KAAM,QAGFxkB,GAAU,CACd6jB,WAAW,EACXC,SAAU,+GAGV7pB,QAAS,cACT8pB,MAAO,GACPC,MAAO,EACP5Y,MAAM,EACN9Z,UAAU,EACVme,UAAW,MACX3R,OAAQ,EACRgJ,WAAW,EACXmd,kBAAmB,OACnB1I,SAAU,eACV2I,UAAU,EACVzB,WAAY,KACZD,UAAW7B,GACXnF,aAAc,MAGV/oB,GAAQ,CACZgyB,KAAI,kBACJC,OAAM,oBACNC,KAAI,kBACJC,MAAK,mBACLC,SAAQ,sBACRC,MAAK,mBACLC,QAAO,qBACPC,SAAQ,sBACRC,WAAU,wBACVC,WAAU,yBAuBNC,GAAAA,WACJ,SAAAA,EAAY9zB,EAASkC,GACnB,QAAsB,IAAXynB,GACT,MAAM,IAAIxV,UAAU,kEAItB9M,KAAK0sB,YAAa,EAClB1sB,KAAK2sB,SAAW,EAChB3sB,KAAK4sB,YAAc,GACnB5sB,KAAK6sB,eAAiB,GACtB7sB,KAAKgjB,QAAU,KAGfhjB,KAAKrH,QAAUA,EACfqH,KAAKnF,OAASmF,KAAK2I,WAAW9N,GAC9BmF,KAAK8sB,IAAM,KAEX9sB,KAAK+sB,gBACLxvB,EAAa5E,EAASqH,KAAK6jB,YAAYmJ,SAAUhtB,iCAmCnDitB,OAAA,WACEjtB,KAAK0sB,YAAa,KAGpBQ,QAAA,WACEltB,KAAK0sB,YAAa,KAGpBS,cAAA,WACEntB,KAAK0sB,YAAc1sB,KAAK0sB,cAG1BvoB,OAAA,SAAOtF,GACL,GAAKmB,KAAK0sB,WAIV,GAAI7tB,EAAO,CACT,IAAMuuB,EAAUptB,KAAK6jB,YAAYmJ,SAC7B7I,EAAU5mB,EAAasB,EAAMoB,eAAgBmtB,GAE5CjJ,IACHA,EAAU,IAAInkB,KAAK6jB,YACjBhlB,EAAMoB,eACND,KAAKqtB,sBAEP9vB,EAAasB,EAAMoB,eAAgBmtB,EAASjJ,IAG9CA,EAAQ0I,eAAeS,OAASnJ,EAAQ0I,eAAeS,MAEnDnJ,EAAQoJ,uBACVpJ,EAAQqJ,OAAO,KAAMrJ,GAErBA,EAAQsJ,OAAO,KAAMtJ,OAElB,CACL,GAAInkB,KAAK0tB,gBAAgBrqB,UAAUE,SA7GjB,QA+GhB,YADAvD,KAAKytB,OAAO,KAAMztB,MAIpBA,KAAKwtB,OAAO,KAAMxtB,UAItBkD,QAAA,WACE2H,aAAa7K,KAAK2sB,UAElBpvB,EAAgByC,KAAKrH,QAASqH,KAAK6jB,YAAYmJ,UAE/C9sB,EAAaC,IAAIH,KAAKrH,QAASqH,KAAK6jB,YAAYxc,WAChDnH,EAAaC,IAAIH,KAAKrH,QAAQwK,QAAb,UAA8C,gBAAiBnD,KAAK2tB,mBAEjF3tB,KAAK8sB,KACP9sB,KAAK8sB,IAAI9wB,WAAWyH,YAAYzD,KAAK8sB,KAGvC9sB,KAAK0sB,WAAa,KAClB1sB,KAAK2sB,SAAW,KAChB3sB,KAAK4sB,YAAc,KACnB5sB,KAAK6sB,eAAiB,KAClB7sB,KAAKgjB,SACPhjB,KAAKgjB,QAAQlI,UAGf9a,KAAKgjB,QAAU,KACfhjB,KAAKrH,QAAU,KACfqH,KAAKnF,OAAS,KACdmF,KAAK8sB,IAAM,QAGb7e,KAAA,WAAO,IAAA7K,EAAApD,KACL,GAAmC,SAA/BA,KAAKrH,QAAQoD,MAAMI,QACrB,MAAM,IAAIP,MAAM,uCAGlB,GAAIoE,KAAK4tB,iBAAmB5tB,KAAK0sB,WAAY,CAC3C,IAAM1H,EAAY9kB,EAAaqB,QAAQvB,KAAKrH,QAASqH,KAAK6jB,YAAY9pB,MAAMkyB,MACtE4B,EbhHW,SAAjBC,EAAiBn1B,GACrB,IAAKH,SAAS0N,gBAAgB6nB,aAC5B,OAAO,KAIT,GAAmC,mBAAxBp1B,EAAQq1B,YAA4B,CAC7C,IAAMC,EAAOt1B,EAAQq1B,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAIt1B,aAAmBu1B,WACdv1B,EAIJA,EAAQqD,WAIN8xB,EAAen1B,EAAQqD,YAHrB,Ka+Fc8xB,CAAe9tB,KAAKrH,SACjCw1B,EAA4B,OAAfN,EACjB7tB,KAAKrH,QAAQqX,cAAc9J,gBAAgB3C,SAASvD,KAAKrH,SACzDk1B,EAAWtqB,SAASvD,KAAKrH,SAE3B,GAAIqsB,EAAUljB,mBAAqBqsB,EACjC,OAGF,IAAMrB,EAAM9sB,KAAK0tB,gBACXU,EAAQj2B,EAAO6H,KAAK6jB,YAAYphB,MAEtCqqB,EAAI1oB,aAAa,KAAMgqB,GACvBpuB,KAAKrH,QAAQyL,aAAa,mBAAoBgqB,GAE9CpuB,KAAKquB,aAEDruB,KAAKnF,OAAOswB,WACd2B,EAAIzpB,UAAU2H,IA3KE,QA8KlB,IAAM+L,EAA6C,mBAA1B/W,KAAKnF,OAAOkc,UACnC/W,KAAKnF,OAAOkc,UAAUxb,KAAKyE,KAAM8sB,EAAK9sB,KAAKrH,SAC3CqH,KAAKnF,OAAOkc,UAERuX,EAAatuB,KAAKuuB,eAAexX,GACvC/W,KAAKwuB,oBAAoBF,GAEzB,IAiBgDroB,EAjB1CmI,EAAYpO,KAAKyuB,gBAiBvB,GAhBAlxB,EAAauvB,EAAK9sB,KAAK6jB,YAAYmJ,SAAUhtB,MAExCA,KAAKrH,QAAQqX,cAAc9J,gBAAgB3C,SAASvD,KAAK8sB,MAC5D1e,EAAUyX,YAAYiH,GAGxB5sB,EAAaqB,QAAQvB,KAAKrH,QAASqH,KAAK6jB,YAAY9pB,MAAMoyB,UAE1DnsB,KAAKgjB,QAAU,IAAIV,GAAOtiB,KAAKrH,QAASm0B,EAAK9sB,KAAK0jB,iBAAiB4K,IAEnExB,EAAIzpB,UAAU2H,IA9LI,QAoMd,iBAAkBxS,SAAS0N,iBAC7BD,EAAA,IAAGE,OAAH9F,MAAA4F,EAAazN,SAASkE,KAAK6J,UAAUtL,SAAQ,SAAAtC,GAC3CuH,EAAaQ,GAAG/H,EAAS,abxIhB,kBa4Ib,IAAM+1B,EAAW,WACXtrB,EAAKvI,OAAOswB,WACd/nB,EAAKurB,iBAGP,IAAMC,EAAiBxrB,EAAKwpB,YAC5BxpB,EAAKwpB,YAAc,KAEnB1sB,EAAaqB,QAAQ6B,EAAKzK,QAASyK,EAAKygB,YAAY9pB,MAAMmyB,OA/M1C,QAiNZ0C,GACFxrB,EAAKqqB,OAAO,KAAMrqB,IAItB,GAAIpD,KAAK8sB,IAAIzpB,UAAUE,SA3NL,QA2NgC,CAChD,IAAMhK,EAAqBJ,EAAiC6G,KAAK8sB,KACjE5sB,EAAaS,IAAIX,KAAK8sB,Ib5TP,gBa4T4B4B,GAC3Cv0B,EAAqB6F,KAAK8sB,IAAKvzB,QAE/Bm1B,QAKN1gB,KAAA,WAAO,IAAA5D,EAAApK,KACL,GAAKA,KAAKgjB,QAAV,CAIA,IAAM8J,EAAM9sB,KAAK0tB,gBACXgB,EAAW,WAvOI,SAwOftkB,EAAKwiB,aAAoCE,EAAI9wB,YAC/C8wB,EAAI9wB,WAAWyH,YAAYqpB,GAG7B1iB,EAAKykB,iBACLzkB,EAAKzR,QAAQmM,gBAAgB,oBAC7B5E,EAAaqB,QAAQ6I,EAAKzR,QAASyR,EAAKyZ,YAAY9pB,MAAMiyB,QAC1D5hB,EAAK4Y,QAAQlI,WAIf,IADkB5a,EAAaqB,QAAQvB,KAAKrH,QAASqH,KAAK6jB,YAAY9pB,MAAMgyB,MAC9DjqB,iBAAd,CAQgD,IAAA0E,EAAhD,GAJAsmB,EAAIzpB,UAAUC,OAzPM,QA6PhB,iBAAkB9K,SAAS0N,iBAC7BM,EAAA,IAAGL,OAAH9F,MAAAmG,EAAahO,SAASkE,KAAK6J,UACxBtL,SAAQ,SAAAtC,GAAO,OAAIuH,EAAaC,IAAIxH,EAAS,YAAa0D,MAO/D,GAJA2D,KAAK6sB,eAAL,OAAqC,EACrC7sB,KAAK6sB,eAAL,OAAqC,EACrC7sB,KAAK6sB,eAAL,OAAqC,EAEjC7sB,KAAK8sB,IAAIzpB,UAAUE,SAxQH,QAwQ8B,CAChD,IAAMhK,EAAqBJ,EAAiC2zB,GAE5D5sB,EAAaS,IAAImsB,Eb1WA,gBa0WqB4B,GACtCv0B,EAAqB2yB,EAAKvzB,QAE1Bm1B,IAGF1uB,KAAK4sB,YAAc,QAGrBjT,OAAA,WACuB,OAAjB3Z,KAAKgjB,SACPhjB,KAAKgjB,QAAQpH,oBAMjBgS,cAAA,WACE,OAAOntB,QAAQT,KAAK8uB,eAGtBpB,cAAA,WACE,GAAI1tB,KAAK8sB,IACP,OAAO9sB,KAAK8sB,IAGd,IAAMn0B,EAAUH,SAASiuB,cAAc,OAIvC,OAHA9tB,EAAQqyB,UAAYhrB,KAAKnF,OAAOuwB,SAEhCprB,KAAK8sB,IAAMn0B,EAAQ4N,SAAS,GACrBvG,KAAK8sB,OAGduB,WAAA,WACE,IAAMvB,EAAM9sB,KAAK0tB,gBACjB1tB,KAAK+uB,kBAAkBjpB,EAAeQ,QAvSX,iBAuS2CwmB,GAAM9sB,KAAK8uB,YACjFhC,EAAIzpB,UAAUC,OA/SM,OAEA,WAgTtByrB,kBAAA,SAAkBp2B,EAASq2B,GACzB,GAAgB,OAAZr2B,EAIJ,MAAuB,iBAAZq2B,GAAwBh1B,EAAUg1B,IACvCA,EAAQlgB,SACVkgB,EAAUA,EAAQ,SAIhBhvB,KAAKnF,OAAO6X,KACVsc,EAAQhzB,aAAerD,IACzBA,EAAQqyB,UAAY,GACpBryB,EAAQktB,YAAYmJ,IAGtBr2B,EAAQs2B,YAAcD,EAAQC,mBAM9BjvB,KAAKnF,OAAO6X,MACV1S,KAAKnF,OAAO2wB,WACdwD,EAAUpF,GAAaoF,EAAShvB,KAAKnF,OAAOivB,UAAW9pB,KAAKnF,OAAOkvB,aAGrEpxB,EAAQqyB,UAAYgE,GAEpBr2B,EAAQs2B,YAAcD,MAI1BF,SAAA,WACE,IAAIzD,EAAQrrB,KAAKrH,QAAQE,aAAa,uBAQtC,OANKwyB,IACHA,EAAqC,mBAAtBrrB,KAAKnF,OAAOwwB,MACzBrrB,KAAKnF,OAAOwwB,MAAM9vB,KAAKyE,KAAKrH,SAC5BqH,KAAKnF,OAAOwwB,OAGTA,KAKT3H,iBAAA,SAAiB4K,GAAY,IAAA/jB,EAAAvK,KAuB3B,OAAAiF,EAAA,GAtBwB,CACtB8R,UAAWuX,EACXnV,UAAW,CACT/T,OAAQpF,KAAKgkB,aACb/J,KAAM,CACJ8F,SAAU/f,KAAKnF,OAAO0wB,mBAExBrM,MAAO,CACLvmB,QAAO,IAAMqH,KAAK6jB,YAAYphB,KAAvB,UAET8b,gBAAiB,CACf/H,kBAAmBxW,KAAKnF,OAAOgoB,WAGnCxI,SAAU,SAAAnd,GACJA,EAAKgd,oBAAsBhd,EAAK6Z,WAClCxM,EAAK2kB,6BAA6BhyB,IAGtCkd,SAAU,SAAAld,GAAI,OAAIqN,EAAK2kB,6BAA6BhyB,KAKjD8C,KAAKnF,OAAOioB,iBAInB0L,oBAAA,SAAoBF,GAClBtuB,KAAK0tB,gBAAgBrqB,UAAU2H,IAAOmkB,cAAgBb,MAGxDtK,WAAA,WAAa,IAAAzX,EAAAvM,KACLoF,EAAS,GAef,MAbkC,mBAAvBpF,KAAKnF,OAAOuK,OACrBA,EAAOxF,GAAK,SAAA1C,GAMV,OALAA,EAAKwX,QAALzP,EAAA,GACK/H,EAAKwX,QACJnI,EAAK1R,OAAOuK,OAAOlI,EAAKwX,QAASnI,EAAK5T,UAAY,IAGjDuE,GAGTkI,EAAOA,OAASpF,KAAKnF,OAAOuK,OAGvBA,KAGTqpB,cAAA,WACE,OAA8B,IAA1BzuB,KAAKnF,OAAOuT,UACP5V,SAASkE,KAGd1C,EAAUgG,KAAKnF,OAAOuT,WACjBpO,KAAKnF,OAAOuT,UAGdtI,EAAeQ,QAAQtG,KAAKnF,OAAOuT,cAG5CmgB,eAAA,SAAexX,GACb,OAAO0U,GAAc1U,EAAUlb,kBAGjCkxB,cAAA,WAAgB,IAAA/G,EAAAhmB,KACGA,KAAKnF,OAAO0G,QAAQ3H,MAAM,KAElCqB,SAAQ,SAAAsG,GACf,GAAgB,UAAZA,EACFrB,EAAaQ,GAAGslB,EAAKrtB,QACnBqtB,EAAKnC,YAAY9pB,MAAMqyB,MACvBpG,EAAKnrB,OAAOjC,UACZ,SAAAiG,GAAK,OAAImnB,EAAK7hB,OAAOtF,WAElB,GApaU,WAoaN0C,EAA4B,CACrC,IAAM6tB,EAxaQ,UAwaE7tB,EACdykB,EAAKnC,YAAY9pB,MAAMwyB,WACvBvG,EAAKnC,YAAY9pB,MAAMsyB,QACnBgD,EA3aQ,UA2aG9tB,EACfykB,EAAKnC,YAAY9pB,MAAMyyB,WACvBxG,EAAKnC,YAAY9pB,MAAMuyB,SAEzBpsB,EAAaQ,GAAGslB,EAAKrtB,QACnBy2B,EACApJ,EAAKnrB,OAAOjC,UACZ,SAAAiG,GAAK,OAAImnB,EAAKwH,OAAO3uB,MAEvBqB,EAAaQ,GAAGslB,EAAKrtB,QACnB02B,EACArJ,EAAKnrB,OAAOjC,UACZ,SAAAiG,GAAK,OAAImnB,EAAKyH,OAAO5uB,UAK3BmB,KAAK2tB,kBAAoB,WACnB3H,EAAKrtB,SACPqtB,EAAKhY,QAIT9N,EAAaQ,GAAGV,KAAKrH,QAAQwK,QAAb,UACd,gBACAnD,KAAK2tB,mBAGH3tB,KAAKnF,OAAOjC,SACdoH,KAAKnF,OAALoK,EAAA,GACKjF,KAAKnF,OADV,CAEE0G,QAAS,SACT3I,SAAU,KAGZoH,KAAKsvB,eAITA,UAAA,WACE,IAAMC,SAAmBvvB,KAAKrH,QAAQE,aAAa,wBAE/CmH,KAAKrH,QAAQE,aAAa,UAA0B,WAAd02B,KACxCvvB,KAAKrH,QAAQyL,aACX,sBACApE,KAAKrH,QAAQE,aAAa,UAAY,IAGxCmH,KAAKrH,QAAQyL,aAAa,QAAS,QAIvCopB,OAAA,SAAO3uB,EAAOslB,GACZ,IAAMiJ,EAAUptB,KAAK6jB,YAAYmJ,UACjC7I,EAAUA,GAAW5mB,EAAasB,EAAMoB,eAAgBmtB,MAGtDjJ,EAAU,IAAInkB,KAAK6jB,YACjBhlB,EAAMoB,eACND,KAAKqtB,sBAEP9vB,EAAasB,EAAMoB,eAAgBmtB,EAASjJ,IAG1CtlB,IACFslB,EAAQ0I,eACS,YAAfhuB,EAAMuB,KA5eQ,QADA,UA8eZ,GAGF+jB,EAAQuJ,gBAAgBrqB,UAAUE,SAxflB,SAEC,SAufjB4gB,EAAQyI,YACVzI,EAAQyI,YAxfW,QA4frB/hB,aAAasZ,EAAQwI,UAErBxI,EAAQyI,YA9fa,OAggBhBzI,EAAQtpB,OAAOywB,OAAUnH,EAAQtpB,OAAOywB,MAAMrd,KAKnDkW,EAAQwI,SAAWjyB,YAAW,WArgBT,SAsgBfypB,EAAQyI,aACVzI,EAAQlW,SAETkW,EAAQtpB,OAAOywB,MAAMrd,MARtBkW,EAAQlW,WAWZwf,OAAA,SAAO5uB,EAAOslB,GACZ,IAAMiJ,EAAUptB,KAAK6jB,YAAYmJ,UACjC7I,EAAUA,GAAW5mB,EAAasB,EAAMoB,eAAgBmtB,MAGtDjJ,EAAU,IAAInkB,KAAK6jB,YACjBhlB,EAAMoB,eACND,KAAKqtB,sBAEP9vB,EAAasB,EAAMoB,eAAgBmtB,EAASjJ,IAG1CtlB,IACFslB,EAAQ0I,eACS,aAAfhuB,EAAMuB,KAphBQ,QADA,UAshBZ,GAGF+jB,EAAQoJ,yBAIZ1iB,aAAasZ,EAAQwI,UAErBxI,EAAQyI,YAniBY,MAqiBfzI,EAAQtpB,OAAOywB,OAAUnH,EAAQtpB,OAAOywB,MAAMtd,KAKnDmW,EAAQwI,SAAWjyB,YAAW,WA1iBV,QA2iBdypB,EAAQyI,aACVzI,EAAQnW,SAETmW,EAAQtpB,OAAOywB,MAAMtd,MARtBmW,EAAQnW,WAWZuf,qBAAA,WACE,IAAK,IAAMhsB,KAAWvB,KAAK6sB,eACzB,GAAI7sB,KAAK6sB,eAAetrB,GACtB,OAAO,EAIX,OAAO,KAGToH,WAAA,SAAW9N,GACT,IAAM20B,EAAiB7qB,EAAYI,kBAAkB/E,KAAKrH,SAuC1D,OArCAoC,OAAOC,KAAKw0B,GAAgBv0B,SAAQ,SAAAw0B,IACe,IAA7CvE,GAAsB5rB,QAAQmwB,WACzBD,EAAeC,MAItB50B,GAAsC,iBAArBA,EAAOuT,WAA0BvT,EAAOuT,UAAUU,SACrEjU,EAAOuT,UAAYvT,EAAOuT,UAAU,IASV,iBAN5BvT,EAAMoK,EAAA,GACDjF,KAAK6jB,YAAYvc,QACjBkoB,EACmB,iBAAX30B,GAAuBA,EAASA,EAAS,KAGpCywB,QAChBzwB,EAAOywB,MAAQ,CACbrd,KAAMpT,EAAOywB,MACbtd,KAAMnT,EAAOywB,QAIW,iBAAjBzwB,EAAOwwB,QAChBxwB,EAAOwwB,MAAQxwB,EAAOwwB,MAAM/vB,YAGA,iBAAnBT,EAAOm0B,UAChBn0B,EAAOm0B,QAAUn0B,EAAOm0B,QAAQ1zB,YAGlCX,EAAgB8H,GAAM5H,EAAQmF,KAAK6jB,YAAYhc,aAE3ChN,EAAO2wB,WACT3wB,EAAOuwB,SAAWxB,GAAa/uB,EAAOuwB,SAAUvwB,EAAOivB,UAAWjvB,EAAOkvB,aAGpElvB,KAGTwyB,mBAAA,WACE,IAAMxyB,EAAS,GAEf,GAAImF,KAAKnF,OACP,IAAK,IAAMoC,KAAO+C,KAAKnF,OACjBmF,KAAK6jB,YAAYvc,QAAQrK,KAAS+C,KAAKnF,OAAOoC,KAChDpC,EAAOoC,GAAO+C,KAAKnF,OAAOoC,IAKhC,OAAOpC,KAGTg0B,eAAA,WACE,IAAM/B,EAAM9sB,KAAK0tB,gBACXgC,EAAW5C,EAAIj0B,aAAa,SAAS2C,MAAMyvB,IAChC,OAAbyE,GAAqBA,EAAS9wB,OAAS,GACzC8wB,EAASvY,KAAI,SAAAwY,GAAK,OAAIA,EAAM52B,UACzBkC,SAAQ,SAAA20B,GAAM,OAAI9C,EAAIzpB,UAAUC,OAAOssB,SAI9CV,6BAAA,SAA6BW,GAC3B7vB,KAAK8sB,IAAM+C,EAAWryB,SAAS8Y,OAC/BtW,KAAK6uB,iBACL7uB,KAAKwuB,oBAAoBxuB,KAAKuuB,eAAesB,EAAW9Y,eAG1D4X,eAAA,WACE,IAAM7B,EAAM9sB,KAAK0tB,gBACXoC,EAAsB9vB,KAAKnF,OAAOswB,UACA,OAApC2B,EAAIj0B,aAAa,iBAIrBi0B,EAAIzpB,UAAUC,OA/oBM,QAgpBpBtD,KAAKnF,OAAOswB,WAAY,EACxBnrB,KAAKgO,OACLhO,KAAKiO,OACLjO,KAAKnF,OAAOswB,UAAY2E,MAKnBpsB,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACf,IAAIzG,EAAOK,EAAayC,KA7tBb,cA8tBL0I,EAA4B,iBAAX7N,GAAuBA,EAE9C,IAAKqC,IAAQ,eAAevB,KAAKd,MAI5BqC,IACHA,EAAO,IAAIuvB,EAAQzsB,KAAM0I,IAGL,iBAAX7N,GAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,YAKJiJ,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAnvBP,uDAgHb,MAjHY,+CAqHZ,OAAO2O,gCAIP,OAAO7E,oCAIP,MA5Ha,2CAgIb,OAAO1I,qCAIP,MAnIW,kDAuIX,OAAO8N,SAjDL4kB,GAuqBN7vB,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAG6C,IAChCf,EAAE9B,GAAG6C,IAAQgqB,GAAQ/oB,gBACrBhC,EAAE9B,GAAG6C,IAAMuB,YAAcyoB,GACzB/qB,EAAE9B,GAAG6C,IAAMwB,WAAa,WAEtB,OADAvC,EAAE9B,GAAG6C,IAAQsB,EACN0oB,GAAQ/oB,qBC1xBrB,IAAMjB,GAAO,UAKPwoB,GAAqB,IAAIvvB,OAAJ,wBAAyC,KAE9D4L,GAAOrC,EAAA,GACRwnB,GAAQnlB,QADA,CAEXyP,UAAW,QACXxV,QAAS,QACTytB,QAAS,GACT5D,SAAU,gJAMNvjB,GAAW5C,EAAA,GACZwnB,GAAQ5kB,YADI,CAEfmnB,QAAS,8BAGLj1B,GAAQ,CACZgyB,KAAI,kBACJC,OAAM,oBACNC,KAAI,kBACJC,MAAK,mBACLC,SAAQ,sBACRC,MAAK,mBACLC,QAAO,qBACPC,SAAQ,sBACRC,WAAU,wBACVC,WAAU,yBAeNuD,GAAAA,SAAAA,+KAiCJnC,cAAA,WACE,OAAO5tB,KAAK8uB,YAAc9uB,KAAKgwB,iBAGjC3B,WAAA,WACE,IAAMvB,EAAM9sB,KAAK0tB,gBAGjB1tB,KAAK+uB,kBAAkBjpB,EAAeQ,QAlDnB,kBAkD2CwmB,GAAM9sB,KAAK8uB,YACzE,IAAIE,EAAUhvB,KAAKgwB,cACI,mBAAZhB,IACTA,EAAUA,EAAQzzB,KAAKyE,KAAKrH,UAG9BqH,KAAK+uB,kBAAkBjpB,EAAeQ,QAvDjB,gBAuD2CwmB,GAAMkC,GAEtElC,EAAIzpB,UAAUC,OA7DM,OACA,WAiEtBkrB,oBAAA,SAAoBF,GAClBtuB,KAAK0tB,gBAAgBrqB,UAAU2H,IAAOmkB,cAAgBb,MAGxD0B,YAAA,WACE,OAAOhwB,KAAKrH,QAAQE,aAAa,iBAC/BmH,KAAKnF,OAAOm0B,WAGhBH,eAAA,WACE,IAAM/B,EAAM9sB,KAAK0tB,gBACXgC,EAAW5C,EAAIj0B,aAAa,SAAS2C,MAAMyvB,IAChC,OAAbyE,GAAqBA,EAAS9wB,OAAS,GACzC8wB,EAASvY,KAAI,SAAAwY,GAAK,OAAIA,EAAM52B,UACzBkC,SAAQ,SAAA20B,GAAM,OAAI9C,EAAIzpB,UAAUC,OAAOssB,SAMvClsB,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACf,IAAIzG,EAAOK,EAAayC,KA1Hb,cA2HL0I,EAA4B,iBAAX7N,EAAsBA,EAAS,KAEtD,IAAKqC,IAAQ,eAAevB,KAAKd,MAI5BqC,IACHA,EAAO,IAAI6yB,EAAQ/vB,KAAM0I,GACzBnL,EAAayC,KAnIJ,aAmIoB9C,IAGT,iBAAXrC,GAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,YAKJiJ,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAjJP,uDAkDb,MAnDY,+CAuDZ,OAAO2O,gCAIP,OAAO7E,oCAIP,MA9Da,2CAkEb,OAAO1I,qCAIP,MArEW,kDAyEX,OAAO8N,SA5BLkoB,CAAgBtD,IA8GtB7vB,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAG6C,IAChCf,EAAE9B,GAAG6C,IAAQstB,GAAQrsB,gBACrBhC,EAAE9B,GAAG6C,IAAMuB,YAAc+rB,GACzBruB,EAAE9B,GAAG6C,IAAMwB,WAAa,WAEtB,OADAvC,EAAE9B,GAAG6C,IAAQsB,EACNgsB,GAAQrsB,qBC/JrB,IAAMjB,GAAO,YAMP6E,GAAU,CACdlC,OAAQ,GACR6qB,OAAQ,OACRlwB,OAAQ,IAGJ8H,GAAc,CAClBzC,OAAQ,SACR6qB,OAAQ,SACRlwB,OAAQ,oBA2BJmwB,GAAAA,WACJ,SAAAA,EAAYv3B,EAASkC,GAAQ,IAAAuI,EAAApD,KAC3BA,KAAK2C,SAAWhK,EAChBqH,KAAKmwB,eAAqC,SAApBx3B,EAAQuS,QAAqB7R,OAASV,EAC5DqH,KAAK0I,QAAU1I,KAAK2I,WAAW9N,GAC/BmF,KAAK4N,UAAe5N,KAAK0I,QAAQ3I,OAAbC,eAA8CA,KAAK0I,QAAQ3I,OAA3DC,sBAA6FA,KAAK0I,QAAQ3I,OAA1GC,kBACpBA,KAAKowB,SAAW,GAChBpwB,KAAKqwB,SAAW,GAChBrwB,KAAKswB,cAAgB,KACrBtwB,KAAKuwB,cAAgB,EAErBrwB,EAAaQ,GAAGV,KAAKmwB,eAlCP,uBAkCqC,SAAAtxB,GAAK,OAAIuE,EAAKotB,SAAS3xB,MAE1EmB,KAAKywB,UACLzwB,KAAKwwB,WAELjzB,EAAa5E,EAxDA,eAwDmBqH,iCAelCywB,QAAA,WAAU,IAAArmB,EAAApK,KACF0wB,EAAa1wB,KAAKmwB,iBAAmBnwB,KAAKmwB,eAAe92B,OAzC7C,SACE,WA4Cds3B,EAAuC,SAAxB3wB,KAAK0I,QAAQunB,OAChCS,EACA1wB,KAAK0I,QAAQunB,OAETW,EAhDc,aAgDDD,EACjB3wB,KAAK6wB,gBACL,EAEF7wB,KAAKowB,SAAW,GAChBpwB,KAAKqwB,SAAW,GAChBrwB,KAAKuwB,cAAgBvwB,KAAK8wB,mBAEVhrB,EAAeE,KAAKhG,KAAK4N,WAEjCuJ,KAAI,SAAAxe,GACV,IAAMo4B,EAAiB/3B,EAAuBL,GACxCoH,EAASgxB,EAAiBjrB,EAAeQ,QAAQyqB,GAAkB,KAEzE,GAAIhxB,EAAQ,CACV,IAAMixB,EAAYjxB,EAAOuF,wBACzB,GAAI0rB,EAAUrd,OAASqd,EAAUtd,OAC/B,MAAO,CACL/O,EAAYgsB,GAAc5wB,GAAQwF,IAAMqrB,EACxCG,GAKN,OAAO,QAENtqB,QAAO,SAAAwqB,GAAI,OAAIA,KACf5Z,MAAK,SAACC,EAAGC,GAAJ,OAAUD,EAAE,GAAKC,EAAE,MACxBtc,SAAQ,SAAAg2B,GACP7mB,EAAKgmB,SAASrpB,KAAKkqB,EAAK,IACxB7mB,EAAKimB,SAAStpB,KAAKkqB,EAAK,UAI9B/tB,QAAA,WACE3F,EAAgByC,KAAK2C,SAnHR,gBAoHbzC,EAAaC,IAAIH,KAAKmwB,eAnHX,iBAqHXnwB,KAAK2C,SAAW,KAChB3C,KAAKmwB,eAAiB,KACtBnwB,KAAK0I,QAAU,KACf1I,KAAK4N,UAAY,KACjB5N,KAAKowB,SAAW,KAChBpwB,KAAKqwB,SAAW,KAChBrwB,KAAKswB,cAAgB,KACrBtwB,KAAKuwB,cAAgB,QAKvB5nB,WAAA,SAAW9N,GAMT,GAA6B,iBAL7BA,EAAMoK,EAAA,GACDqC,GACmB,iBAAXzM,GAAuBA,EAASA,EAAS,KAGpCkF,QAAuB/F,EAAUa,EAAOkF,QAAS,CAAA,IAC3D7H,EAAO2C,EAAOkF,OAAd7H,GACDA,IACHA,EAAKC,EAAOsK,IACZ5H,EAAOkF,OAAO7H,GAAKA,GAGrB2C,EAAOkF,OAAP,IAAoB7H,EAKtB,OAFAyC,EAAgB8H,GAAM5H,EAAQgN,IAEvBhN,KAGTg2B,cAAA,WACE,OAAO7wB,KAAKmwB,iBAAmB92B,OAC7B2G,KAAKmwB,eAAee,YACpBlxB,KAAKmwB,eAAe3qB,aAGxBsrB,iBAAA,WACE,OAAO9wB,KAAKmwB,eAAenJ,cAAgB3uB,KAAKmb,IAC9Chb,SAASkE,KAAKsqB,aACdxuB,SAAS0N,gBAAgB8gB,iBAI7BmK,iBAAA,WACE,OAAOnxB,KAAKmwB,iBAAmB92B,OAC7BA,OAAO2c,YACPhW,KAAKmwB,eAAe7qB,wBAAwBoO,UAGhD8c,SAAA,WACE,IAAMhrB,EAAYxF,KAAK6wB,gBAAkB7wB,KAAK0I,QAAQtD,OAChD4hB,EAAehnB,KAAK8wB,mBACpBM,EAAYpxB,KAAK0I,QAAQtD,OAC7B4hB,EACAhnB,KAAKmxB,mBAMP,GAJInxB,KAAKuwB,gBAAkBvJ,GACzBhnB,KAAKywB,UAGHjrB,GAAa4rB,EAAjB,CACE,IAAMrxB,EAASC,KAAKqwB,SAASrwB,KAAKqwB,SAASzxB,OAAS,GAEhDoB,KAAKswB,gBAAkBvwB,GACzBC,KAAKqxB,UAAUtxB,OAJnB,CAUA,GAAIC,KAAKswB,eAAiB9qB,EAAYxF,KAAKowB,SAAS,IAAMpwB,KAAKowB,SAAS,GAAK,EAG3E,OAFApwB,KAAKswB,cAAgB,UACrBtwB,KAAKsxB,SAIP,IAAK,IAAI5yB,EAAIsB,KAAKowB,SAASxxB,OAAQF,KAAM,CAChBsB,KAAKswB,gBAAkBtwB,KAAKqwB,SAAS3xB,IACxD8G,GAAaxF,KAAKowB,SAAS1xB,UACM,IAAzBsB,KAAKowB,SAAS1xB,EAAI,IACtB8G,EAAYxF,KAAKowB,SAAS1xB,EAAI,KAGpCsB,KAAKqxB,UAAUrxB,KAAKqwB,SAAS3xB,SAKnC2yB,UAAA,SAAUtxB,GACRC,KAAKswB,cAAgBvwB,EAErBC,KAAKsxB,SAEL,IAAMC,EAAUvxB,KAAK4N,UAAUhU,MAAM,KAClCud,KAAI,SAAAve,GAAQ,OAAOA,EAAP,iBAAgCmH,EAAhC,MAA4CnH,EAA5C,UAA8DmH,EAA9D,QAETyxB,EAAO1rB,EAAeQ,QAAQirB,EAAQE,KAAK,MAE7CD,EAAKnuB,UAAUE,SAvMU,kBAwM3BuC,EAAeQ,QA/LY,mBA+LsBkrB,EAAKruB,QAhMlC,cAiMjBE,UAAU2H,IAxMO,UA0MpBwmB,EAAKnuB,UAAU2H,IA1MK,YA6MpBwmB,EAAKnuB,UAAU2H,IA7MK,UA+MpBlF,EAAea,QAAQ6qB,EA5MG,qBA6MvBv2B,SAAQ,SAAAy2B,GAGP5rB,EAAekB,KAAK0qB,EAAcC,+BAC/B12B,SAAQ,SAAAg2B,GAAI,OAAIA,EAAK5tB,UAAU2H,IApNlB,aAuNhBlF,EAAekB,KAAK0qB,EAlNH,aAmNdz2B,SAAQ,SAAA22B,GACP9rB,EAAeS,SAASqrB,EArNX,aAsNV32B,SAAQ,SAAAg2B,GAAI,OAAIA,EAAK5tB,UAAU2H,IA1NtB,oBA+NtB9K,EAAaqB,QAAQvB,KAAKmwB,eApOV,wBAoO0C,CACxDzkB,cAAe3L,OAInBuxB,OAAA,WACExrB,EAAeE,KAAKhG,KAAK4N,WACtBnH,QAAO,SAAA8K,GAAI,OAAIA,EAAKlO,UAAUE,SAtOX,aAuOnBtI,SAAQ,SAAAsW,GAAI,OAAIA,EAAKlO,UAAUC,OAvOZ,gBA4OjBI,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACf,IAAIzG,EAAOK,EAAayC,KAnQb,gBA0QX,GAJK9C,IACHA,EAAO,IAAIgzB,EAAUlwB,KAHW,iBAAXnF,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,YAKJiJ,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EArRP,yDA8Db,MA/DY,+CAmEZ,OAAO2O,SA1BL4oB,GAuPNhwB,EAAaQ,GAAGrH,OA7QS,8BA6QoB,WAC3CyM,EAAeE,KAzQS,uBA0QrB/K,SAAQ,SAAA42B,GAAG,OAAI,IAAI3B,GAAU2B,EAAKltB,EAAYI,kBAAkB8sB,UAUrEj1B,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAG6C,IAChCf,EAAE9B,GAAG6C,IAAQytB,GAAUxsB,gBACvBhC,EAAE9B,GAAG6C,IAAMuB,YAAcksB,GACzBxuB,EAAE9B,GAAG6C,IAAMwB,WAAa,WAEtB,OADAvC,EAAE9B,GAAG6C,IAAQsB,EACNmsB,GAAUxsB,qBCtTvB,IAgCMouB,GAAAA,WACJ,SAAAA,EAAYn5B,GACVqH,KAAK2C,SAAWhK,EAEhB4E,EAAayC,KAAK2C,SAlCL,SAkCyB3C,iCAWxCiO,KAAA,WAAO,IAAA7K,EAAApD,KACL,KAAKA,KAAK2C,SAAS3G,YACjBgE,KAAK2C,SAAS3G,WAAW9B,WAAa2M,KAAKC,cAC3C9G,KAAK2C,SAASU,UAAUE,SArCJ,WAsCpBvD,KAAK2C,SAASU,UAAUE,SArCF,aAkCxB,CAOA,IAAI0D,EACElH,EAAS7G,EAAuB8G,KAAK2C,UACrCovB,EAAc/xB,KAAK2C,SAASQ,QAtCN,qBAwC5B,GAAI4uB,EAAa,CACf,IAAMC,EAAwC,OAAzBD,EAAY5hB,UAA8C,OAAzB4hB,EAAY5hB,SAvC7C,wBADH,UA0ClBlJ,GADAA,EAAWnB,EAAeE,KAAKgsB,EAAcD,IACzB9qB,EAASrI,OAAS,GAGxC,IAAIqzB,EAAY,KAYhB,GAVIhrB,IACFgrB,EAAY/xB,EAAaqB,QAAQ0F,EA9DvB,cA8D6C,CACrDyE,cAAe1L,KAAK2C,cAINzC,EAAaqB,QAAQvB,KAAK2C,SAjEhC,cAiEsD,CAChE+I,cAAezE,IAGHnF,kBACG,OAAdmwB,GAAsBA,EAAUnwB,kBADnC,CAKA9B,KAAKqxB,UACHrxB,KAAK2C,SACLovB,GAGF,IAAMrD,EAAW,WACfxuB,EAAaqB,QAAQ0F,EAjFT,gBAiFiC,CAC3CyE,cAAetI,EAAKT,WAEtBzC,EAAaqB,QAAQ6B,EAAKT,SAlFf,eAkFsC,CAC/C+I,cAAezE,KAIflH,EACFC,KAAKqxB,UAAUtxB,EAAQA,EAAO/D,WAAY0yB,GAE1CA,SAIJxrB,QAAA,WACE3F,EAAgByC,KAAK2C,SAtGR,UAuGb3C,KAAK2C,SAAW,QAKlB0uB,UAAA,SAAU14B,EAASyV,EAAWvR,GAAU,IAAAuN,EAAApK,KAKhCkyB,IAJiB9jB,GAAqC,OAAvBA,EAAU+B,UAA4C,OAAvB/B,EAAU+B,SAE5ErK,EAAeS,SAAS6H,EA7FN,WA4FlBtI,EAAeE,KA3FM,wBA2FmBoI,IAGZ,GACxBS,EAAkBhS,GACrBq1B,GAAUA,EAAO7uB,UAAUE,SAtGV,QAwGdmrB,EAAW,WAAA,OAAMtkB,EAAK+nB,oBAC1Bx5B,EACAu5B,EACAr1B,IAGF,GAAIq1B,GAAUrjB,EAAiB,CAC7B,IAAMtV,EAAqBJ,EAAiC+4B,GAC5DA,EAAO7uB,UAAUC,OA/GC,QAiHlBpD,EAAaS,IAAIuxB,EhBlJA,gBgBkJwBxD,GACzCv0B,EAAqB+3B,EAAQ34B,QAE7Bm1B,OAIJyD,oBAAA,SAAoBx5B,EAASu5B,EAAQr1B,GACnC,GAAIq1B,EAAQ,CACVA,EAAO7uB,UAAUC,OA7HG,UA+HpB,IAAM8uB,EAAgBtsB,EAAeQ,QApHJ,kCAoH4C4rB,EAAOl2B,YAEhFo2B,GACFA,EAAc/uB,UAAUC,OAlIN,UAqIgB,QAAhC4uB,EAAOr5B,aAAa,SACtBq5B,EAAO9tB,aAAa,iBAAiB,IAIzCzL,EAAQ0K,UAAU2H,IA1II,UA2Ie,QAAjCrS,EAAQE,aAAa,SACvBF,EAAQyL,aAAa,iBAAiB,GAGxC9H,EAAO3D,GAEHA,EAAQ0K,UAAUE,SA/IF,SAgJlB5K,EAAQ0K,UAAU2H,IA/IA,QAkJhBrS,EAAQqD,YAAcrD,EAAQqD,WAAWqH,UAAUE,SAtJ1B,oBAuJH5K,EAAQwK,QAjJZ,cAoJlB2C,EAAeE,KA/IU,oBAgJtB/K,SAAQ,SAAAo3B,GAAQ,OAAIA,EAAShvB,UAAU2H,IA1JxB,aA6JpBrS,EAAQyL,aAAa,iBAAiB,IAGpCvH,GACFA,OAMG6G,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACf,IAAMzG,EAAOK,EAAayC,KApLf,WAoLkC,IAAI8xB,EAAI9xB,MAErD,GAAsB,iBAAXnF,EAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,YAKJiJ,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAjMP,mDAwCb,MAzCY,qBA+BVm5B,GA6KN5xB,EAAaQ,GAAGlI,SAnMU,wBAYG,mEAuLyC,SAAUqG,GAC9EA,EAAM2D,kBAEOjF,EAAayC,KA9MX,WA8M8B,IAAI8xB,GAAI9xB,OAChDiO,UAUPrR,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAF,IAC3B8B,EAAE9B,GAAF,IAAakyB,GAAIpuB,gBACjBhC,EAAE9B,GAAF,IAAWoE,YAAc8tB,GACzBpwB,EAAE9B,GAAF,IAAWqE,WAAa,WAEtB,OADAvC,EAAE9B,GAAF,IAAamE,EACN+tB,GAAIpuB,qBCpOjB,IAgBMmE,GAAc,CAClBsjB,UAAW,UACXmH,SAAU,UACVhH,MAAO,UAGHhkB,GAAU,CACd6jB,WAAW,EACXmH,UAAU,EACVhH,MAAO,KAWHiH,GAAAA,WACJ,SAAAA,EAAY55B,EAASkC,GACnBmF,KAAK2C,SAAWhK,EAChBqH,KAAK0I,QAAU1I,KAAK2I,WAAW9N,GAC/BmF,KAAK2sB,SAAW,KAChB3sB,KAAK+sB,gBACLxvB,EAAa5E,EAxCA,WAwCmBqH,iCAmBlCiO,KAAA,WAAO,IAAA7K,EAAApD,KAGL,IAFkBE,EAAaqB,QAAQvB,KAAK2C,SAtDhC,iBAwDEb,iBAAd,CAIA9B,KAAKwyB,gBAEDxyB,KAAK0I,QAAQyiB,WACfnrB,KAAK2C,SAASU,UAAU2H,IA5DN,QA+DpB,IAAM0jB,EAAW,WACftrB,EAAKT,SAASU,UAAUC,OA7DH,WA8DrBF,EAAKT,SAASU,UAAU2H,IA/DN,QAiElB9K,EAAaqB,QAAQ6B,EAAKT,SArEf,kBAuEPS,EAAKsF,QAAQ4pB,WACflvB,EAAKupB,SAAWjyB,YAAW,WACzB0I,EAAK4K,SACJ5K,EAAKsF,QAAQ4iB,SAOpB,GAHAtrB,KAAK2C,SAASU,UAAUC,OA3EJ,QA4EpBhH,EAAO0D,KAAK2C,UACZ3C,KAAK2C,SAASU,UAAU2H,IA3ED,WA4EnBhL,KAAK0I,QAAQyiB,UAAW,CAC1B,IAAM5xB,EAAqBJ,EAAiC6G,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,SjB9GL,gBiB8G+B+rB,GAChDv0B,EAAqB6F,KAAK2C,SAAUpJ,QAEpCm1B,QAIJ1gB,KAAA,WAAO,IAAA5D,EAAApK,KACL,GAAKA,KAAK2C,SAASU,UAAUE,SAxFT,UA4FFrD,EAAaqB,QAAQvB,KAAK2C,SAnGhC,iBAqGEb,iBAAd,CAIA,IAAM4sB,EAAW,WACftkB,EAAKzH,SAASU,UAAU2H,IApGN,QAqGlB9K,EAAaqB,QAAQ6I,EAAKzH,SA1Gd,oBA8Gd,GADA3C,KAAK2C,SAASU,UAAUC,OAvGJ,QAwGhBtD,KAAK0I,QAAQyiB,UAAW,CAC1B,IAAM5xB,EAAqBJ,EAAiC6G,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,SjBzIL,gBiByI+B+rB,GAChDv0B,EAAqB6F,KAAK2C,SAAUpJ,QAEpCm1B,QAIJxrB,QAAA,WACElD,KAAKwyB,gBAEDxyB,KAAK2C,SAASU,UAAUE,SArHR,SAsHlBvD,KAAK2C,SAASU,UAAUC,OAtHN,QAyHpBpD,EAAaC,IAAIH,KAAK2C,SAjID,0BAkIrBpF,EAAgByC,KAAK2C,SArIR,YAuIb3C,KAAK2C,SAAW,KAChB3C,KAAK0I,QAAU,QAKjBC,WAAA,SAAW9N,GAST,OARAA,EAAMoK,EAAA,GACDqC,GACA3C,EAAYI,kBAAkB/E,KAAK2C,UAChB,iBAAX9H,GAAuBA,EAASA,EAAS,IAGtDF,EAtJS,QAsJaE,EAAQmF,KAAK6jB,YAAYhc,aAExChN,KAGTkyB,cAAA,WAAgB,IAAAxiB,EAAAvK,KACdE,EAAaQ,GAAGV,KAAK2C,SAvJA,yBAuBK,0BAgIiD,WAAA,OAAM4H,EAAKyD,aAGxFwkB,cAAA,WACE3nB,aAAa7K,KAAK2sB,UAClB3sB,KAAK2sB,SAAW,QAKXjpB,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACf,IAAIzG,EAAOK,EAAayC,KAtKb,YA6KX,GAJK9C,IACHA,EAAO,IAAIq1B,EAAMvyB,KAHe,iBAAXnF,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,GAAQmF,aAKZ8D,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAxLP,qDA8Cb,MA/CY,mDAmDZ,OAAOkP,mCAIP,OAAOP,SApBLirB,UAiKN31B,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAF,MAC3B8B,EAAE9B,GAAF,MAAa2yB,GAAM7uB,gBACnBhC,EAAE9B,GAAF,MAAWoE,YAAcuuB,GACzB7wB,EAAE9B,GAAF,MAAWqE,WAAa,WAEtB,OADAvC,EAAE9B,GAAF,MAAamE,EACNwuB,GAAM7uB,qBCrNJ,CACbhB,MAAAA,EACAwB,OAAAA,EACA+D,SAAAA,EACAmF,SAAAA,EACA2V,SAAAA,GACA0B,MAAAA,GACAsL,QAAAA,GACAG,UAAAA,GACA4B,IAAAA,GACAS,MAAAA,GACA9F,QAAAA", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-target')\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href')\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let {\n    transitionDuration,\n    transitionDelay\n  } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = parseFloat(transitionDuration)\n  const floatTransitionDelay = parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (parseFloat(transitionDuration) + parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ?\n      'element' :\n      toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new Error(\n        `${componentName.toUpperCase()}: ` +\n        `Option \"${property}\" provided type \"${valueType}\" ` +\n        `but expected type \"${expectedTypes}\".`)\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nexport {\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst mapData = (() => {\n  const storeData = {}\n  let id = 1\n  return {\n    set(element, key, data) {\n      if (typeof element.bsKey === 'undefined') {\n        element.bsKey = {\n          key,\n          id\n        }\n        id++\n      }\n\n      storeData[element.bsKey.id] = data\n    },\n    get(element, key) {\n      if (!element || typeof element.bsKey === 'undefined') {\n        return null\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        return storeData[keyProperties.id]\n      }\n\n      return null\n    },\n    delete(element, key) {\n      if (typeof element.bsKey === 'undefined') {\n        return\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        delete storeData[keyProperties.id]\n        delete element.bsKey\n      }\n    }\n  }\n})()\n\nconst Data = {\n  setData(instance, key, data) {\n    mapData.set(instance, key, data)\n  },\n  getData(instance, key) {\n    return mapData.get(instance, key)\n  },\n  removeData(instance, key) {\n    mapData.delete(instance, key)\n  }\n}\n\nexport default Data\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = [\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n]\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.indexOf(namespace) > -1) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.charAt(0) === '.'\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.indexOf(handlerKey) > -1) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASSNAME_ALERT = 'alert'\nconst CLASSNAME_FADE = 'fade'\nconst CLASSNAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n\n    if (this._element) {\n      Data.setData(element, DATA_KEY, this)\n    }\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASSNAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASSNAME_SHOW)\n\n    if (!element.classList.contains(CLASSNAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler.one(element, TRANSITION_END, () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Alert.jQueryInterface\n    $.fn[NAME].Constructor = Alert\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Alert.jQueryInterface\n    }\n  }\n})\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.getData(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Button.jQueryInterface\n    $.fn[NAME].Constructor = Button\n\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Button.jQueryInterface\n    }\n  }\n})\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {\n      ...element.dataset\n    }\n\n    Object.keys(attributes).forEach(key => {\n      attributes[key] = normalizeData(attributes[key])\n    })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  matches(element, selector) {\n    return element.matches(selector)\n  },\n\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    const children = [].concat(...element.children)\n\n    return children.filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (this.matches(ancestor, selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (this.matches(next, selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_RIGHT = 'carousel-item-right'\nconst CLASS_NAME_LEFT = 'carousel-item-left'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-slide], [data-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-ride=\"carousel\"]'\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel {\n  constructor(element, config) {\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._element = element\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._items = null\n    this._config = null\n    this._element = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      if (event.touches && event.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.key) {\n      case ARROW_LEFT_KEY:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEY:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrevDirection && activeIndex === 0) ||\n                            (isNextDirection && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = SelectorEngine.find(SELECTOR_ACTIVE, this._indicatorsElement)\n      for (let i = 0; i < indicators.length; i++) {\n        indicators[i].classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        nextIndicator.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = parseInt(element.getAttribute('data-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || (activeElement &&\n      this._getItemByDirection(direction, activeElement))\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_LEFT\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_RIGHT\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler.one(activeElement, TRANSITION_END, () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(() => {\n          EventHandler.trigger(this._element, EVENT_SLID, {\n            relatedTarget: nextElement,\n            direction: eventDirectionName,\n            from: activeElementIndex,\n            to: nextElementIndex\n          })\n        }, 0)\n      })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.getData(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.getData(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Carousel.jQueryInterface\n    $.fn[NAME].Constructor = Carousel\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Carousel.jQueryInterface\n    }\n  }\n})\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element = element\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-target=\"#${element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.filter(elem => container !== elem)\n      activesData = tempActiveData[0] ? Data.getData(tempActiveData[0], DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.setData(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._parent = null\n    this._element = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.getData(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Collapse.jQueryInterface\n    $.fn[NAME].Constructor = Collapse\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Collapse.jQueryInterface\n    }\n  }\n})\n\nexport default Collapse\n", "/**!\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version 1.16.1\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && typeof navigator !== 'undefined';\n\nvar timeoutDuration = function () {\n  var longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\n  for (var i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n    if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n      return 1;\n    }\n  }\n  return 0;\n}();\n\nfunction microtaskDebounce(fn) {\n  var called = false;\n  return function () {\n    if (called) {\n      return;\n    }\n    called = true;\n    window.Promise.resolve().then(function () {\n      called = false;\n      fn();\n    });\n  };\n}\n\nfunction taskDebounce(fn) {\n  var scheduled = false;\n  return function () {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(function () {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nvar supportsMicroTasks = isBrowser && window.Promise;\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nvar debounce = supportsMicroTasks ? microtaskDebounce : taskDebounce;\n\n/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nfunction isFunction(functionToCheck) {\n  var getType = {};\n  return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n}\n\n/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  var window = element.ownerDocument.defaultView;\n  var css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n\n/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body;\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body;\n    case '#document':\n      return element.body;\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n\n  var _getStyleComputedProp = getStyleComputedProperty(element),\n      overflow = _getStyleComputedProp.overflow,\n      overflowX = _getStyleComputedProp.overflowX,\n      overflowY = _getStyleComputedProp.overflowY;\n\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n\n/**\n * Returns the reference node of the reference object, or the reference object itself.\n * @method\n * @memberof Popper.Utils\n * @param {Element|Object} reference - the reference element (the popper will be relative to this)\n * @returns {Element} parent\n */\nfunction getReferenceNode(reference) {\n  return reference && reference.referenceNode ? reference.referenceNode : reference;\n}\n\nvar isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nvar isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nfunction isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nfunction getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  var noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  var offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  var nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n\nfunction isOffsetContainer(element) {\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  var order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  var start = order ? element1 : element2;\n  var end = order ? element2 : element1;\n\n  // Get common ancestor container\n  var range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  var commonAncestorContainer = range.commonAncestorContainer;\n\n  // Both nodes are inside #document\n\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  var element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nfunction getScroll(element) {\n  var side = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'top';\n\n  var upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    var html = element.ownerDocument.documentElement;\n    var scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nfunction includeScroll(rect, element) {\n  var subtract = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var scrollTop = getScroll(element, 'top');\n  var scrollLeft = getScroll(element, 'left');\n  var modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n\n/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nfunction getBordersSize(styles, axis) {\n  var sideA = axis === 'x' ? 'Left' : 'Top';\n  var sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return parseFloat(styles['border' + sideA + 'Width']) + parseFloat(styles['border' + sideB + 'Width']);\n}\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(body['offset' + axis], body['scroll' + axis], html['client' + axis], html['offset' + axis], html['scroll' + axis], isIE(10) ? parseInt(html['offset' + axis]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Top' : 'Left')]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Bottom' : 'Right')]) : 0);\n}\n\nfunction getWindowSizes(document) {\n  var body = document.body;\n  var html = document.documentElement;\n  var computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle)\n  };\n}\n\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\n\n\n\n\nvar defineProperty = function (obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n};\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\n/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nfunction getClientRect(offsets) {\n  return _extends({}, offsets, {\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height\n  });\n}\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nfunction getBoundingClientRect(element) {\n  var rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      var scrollTop = getScroll(element, 'top');\n      var scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } else {\n      rect = element.getBoundingClientRect();\n    }\n  } catch (e) {}\n\n  var result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n\n  // subtract scrollbar size from sizes\n  var sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  var width = sizes.width || element.clientWidth || result.width;\n  var height = sizes.height || element.clientHeight || result.height;\n\n  var horizScrollbar = element.offsetWidth - width;\n  var vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    var styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n\nfunction getOffsetRectRelativeToArbitraryNode(children, parent) {\n  var fixedPosition = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var isIE10 = isIE(10);\n  var isHTML = parent.nodeName === 'HTML';\n  var childrenRect = getBoundingClientRect(children);\n  var parentRect = getBoundingClientRect(parent);\n  var scrollParent = getScrollParent(children);\n\n  var styles = getStyleComputedProperty(parent);\n  var borderTopWidth = parseFloat(styles.borderTopWidth);\n  var borderLeftWidth = parseFloat(styles.borderLeftWidth);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if (fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  var offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    var marginTop = parseFloat(styles.marginTop);\n    var marginLeft = parseFloat(styles.marginLeft);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (isIE10 && !fixedPosition ? parent.contains(scrollParent) : parent === scrollParent && scrollParent.nodeName !== 'BODY') {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  var excludeScroll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var html = element.ownerDocument.documentElement;\n  var relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  var width = Math.max(html.clientWidth, window.innerWidth || 0);\n  var height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  var scrollTop = !excludeScroll ? getScroll(html) : 0;\n  var scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  var offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width: width,\n    height: height\n  };\n\n  return getClientRect(offset);\n}\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {Boolean} answer to \"isFixed?\"\n */\nfunction isFixed(element) {\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  var parentNode = getParentNode(element);\n  if (!parentNode) {\n    return false;\n  }\n  return isFixed(parentNode);\n}\n\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nfunction getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  var el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n}\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nfunction getBoundaries(popper, reference, padding, boundariesElement) {\n  var fixedPosition = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n\n  // NOTE: 1 DOM access here\n\n  var boundaries = { top: 0, left: 0 };\n  var offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    var boundariesNode = void 0;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    var offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent, fixedPosition);\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      var _getWindowSizes = getWindowSizes(popper.ownerDocument),\n          height = _getWindowSizes.height,\n          width = _getWindowSizes.width;\n\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  var isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0;\n  boundaries.top += isPaddingNumber ? padding : padding.top || 0;\n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0;\n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0;\n\n  return boundaries;\n}\n\nfunction getArea(_ref) {\n  var width = _ref.width,\n      height = _ref.height;\n\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeAutoPlacement(placement, refRect, popper, reference, boundariesElement) {\n  var padding = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;\n\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  var boundaries = getBoundaries(popper, reference, padding, boundariesElement);\n\n  var rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height\n    }\n  };\n\n  var sortedAreas = Object.keys(rects).map(function (key) {\n    return _extends({\n      key: key\n    }, rects[key], {\n      area: getArea(rects[key])\n    });\n  }).sort(function (a, b) {\n    return b.area - a.area;\n  });\n\n  var filteredAreas = sortedAreas.filter(function (_ref2) {\n    var width = _ref2.width,\n        height = _ref2.height;\n    return width >= popper.clientWidth && height >= popper.clientHeight;\n  });\n\n  var computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].key : sortedAreas[0].key;\n\n  var variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? '-' + variation : '');\n}\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nfunction getReferenceOffsets(state, popper, reference) {\n  var fixedPosition = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n\n  var commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n\n/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nfunction getOuterSizes(element) {\n  var window = element.ownerDocument.defaultView;\n  var styles = window.getComputedStyle(element);\n  var x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  var y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  var result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x\n  };\n  return result;\n}\n\n/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nfunction getOppositePlacement(placement) {\n  var hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nfunction getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  var popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  var popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  var isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  var mainSide = isHoriz ? 'top' : 'left';\n  var secondarySide = isHoriz ? 'left' : 'top';\n  var measurement = isHoriz ? 'height' : 'width';\n  var secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] = referenceOffsets[mainSide] + referenceOffsets[measurement] / 2 - popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] = referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] = referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n\n/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(function (cur) {\n      return cur[prop] === value;\n    });\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  var match = find(arr, function (obj) {\n    return obj[prop] === value;\n  });\n  return arr.indexOf(match);\n}\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nfunction runModifiers(modifiers, data, ends) {\n  var modifiersToRun = ends === undefined ? modifiers : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(function (modifier) {\n    if (modifier['function']) {\n      // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    var fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nfunction update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  var data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {}\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(this.state, this.popper, this.reference, this.options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(this.options.placement, data.offsets.reference, this.popper, this.reference, this.options.modifiers.flip.boundariesElement, this.options.modifiers.flip.padding);\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  data.positionFixed = this.options.positionFixed;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(this.popper, data.offsets.reference, data.placement);\n\n  data.offsets.popper.position = this.options.positionFixed ? 'fixed' : 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n\n/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nfunction isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(function (_ref) {\n    var name = _ref.name,\n        enabled = _ref.enabled;\n    return enabled && name === modifierName;\n  });\n}\n\n/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nfunction getSupportedPropertyName(property) {\n  var prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  var upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (var i = 0; i < prefixes.length; i++) {\n    var prefix = prefixes[i];\n    var toCheck = prefix ? '' + prefix + upperProp : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n\n/**\n * Destroys the popper.\n * @method\n * @memberof Popper\n */\nfunction destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style.left = '';\n    this.popper.style.right = '';\n    this.popper.style.bottom = '';\n    this.popper.style.willChange = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicitly asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n\n/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nfunction getWindow(element) {\n  var ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  var isBody = scrollParent.nodeName === 'BODY';\n  var target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(getScrollParent(target.parentNode), event, callback, scrollParents);\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction setupEventListeners(reference, options, state, updateBound) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  var scrollElement = getScrollParent(reference);\n  attachToScrollParents(scrollElement, 'scroll', state.updateBound, state.scrollParents);\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nfunction enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(this.reference, this.options, this.state, this.scheduleUpdate);\n  }\n}\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(function (target) {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger `onUpdate` callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nfunction disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n\n/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setStyles(element, styles) {\n  Object.keys(styles).forEach(function (prop) {\n    var unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n\n/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function (prop) {\n    var value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nfunction applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper\n * @param {Object} options - Popper.js options\n */\nfunction applyStyleOnLoad(reference, popper, options, modifierOptions, state) {\n  // compute reference element offsets\n  var referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  var placement = computeAutoPlacement(options.placement, referenceOffsets, popper, reference, options.modifiers.flip.boundariesElement, options.modifiers.flip.padding);\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: options.positionFixed ? 'fixed' : 'absolute' });\n\n  return options;\n}\n\n/**\n * @function\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Boolean} shouldRound - If the offsets should be rounded at all\n * @returns {Object} The popper's position offsets rounded\n *\n * The tale of pixel-perfect positioning. It's still not 100% perfect, but as\n * good as it can be within reason.\n * Discussion here: https://github.com/FezVrasta/popper.js/pull/715\n *\n * Low DPI screens cause a popper to be blurry if not using full pixels (Safari\n * as well on High DPI screens).\n *\n * Firefox prefers no rounding for positioning and does not have blurriness on\n * high DPI screens.\n *\n * Only horizontal placement and left/right values need to be considered.\n */\nfunction getRoundedOffsets(data, shouldRound) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n  var round = Math.round,\n      floor = Math.floor;\n\n  var noRound = function noRound(v) {\n    return v;\n  };\n\n  var referenceWidth = round(reference.width);\n  var popperWidth = round(popper.width);\n\n  var isVertical = ['left', 'right'].indexOf(data.placement) !== -1;\n  var isVariation = data.placement.indexOf('-') !== -1;\n  var sameWidthParity = referenceWidth % 2 === popperWidth % 2;\n  var bothOddWidth = referenceWidth % 2 === 1 && popperWidth % 2 === 1;\n\n  var horizontalToInteger = !shouldRound ? noRound : isVertical || isVariation || sameWidthParity ? round : floor;\n  var verticalToInteger = !shouldRound ? noRound : round;\n\n  return {\n    left: horizontalToInteger(bothOddWidth && !isVariation && shouldRound ? popper.left - 1 : popper.left),\n    top: verticalToInteger(popper.top),\n    bottom: verticalToInteger(popper.bottom),\n    right: horizontalToInteger(popper.right)\n  };\n}\n\nvar isFirefox = isBrowser && /Firefox/i.test(navigator.userAgent);\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeStyle(data, options) {\n  var x = options.x,\n      y = options.y;\n  var popper = data.offsets.popper;\n\n  // Remove this legacy support in Popper.js v2\n\n  var legacyGpuAccelerationOption = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'applyStyle';\n  }).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');\n  }\n  var gpuAcceleration = legacyGpuAccelerationOption !== undefined ? legacyGpuAccelerationOption : options.gpuAcceleration;\n\n  var offsetParent = getOffsetParent(data.instance.popper);\n  var offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  var styles = {\n    position: popper.position\n  };\n\n  var offsets = getRoundedOffsets(data, window.devicePixelRatio < 2 || !isFirefox);\n\n  var sideA = x === 'bottom' ? 'top' : 'bottom';\n  var sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  var prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  var left = void 0,\n      top = void 0;\n  if (sideA === 'bottom') {\n    // when offsetParent is <html> the positioning is relative to the bottom of the screen (excluding the scrollbar)\n    // and not the bottom of the html element\n    if (offsetParent.nodeName === 'HTML') {\n      top = -offsetParent.clientHeight + offsets.bottom;\n    } else {\n      top = -offsetParentRect.height + offsets.bottom;\n    }\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    if (offsetParent.nodeName === 'HTML') {\n      left = -offsetParent.clientWidth + offsets.right;\n    } else {\n      left = -offsetParentRect.width + offsets.right;\n    }\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    var invertTop = sideA === 'bottom' ? -1 : 1;\n    var invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = sideA + ', ' + sideB;\n  }\n\n  // Attributes\n  var attributes = {\n    'x-placement': data.placement\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = _extends({}, attributes, data.attributes);\n  data.styles = _extends({}, styles, data.styles);\n  data.arrowStyles = _extends({}, data.offsets.arrow, data.arrowStyles);\n\n  return data;\n}\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nfunction isModifierRequired(modifiers, requestingName, requestedName) {\n  var requesting = find(modifiers, function (_ref) {\n    var name = _ref.name;\n    return name === requestingName;\n  });\n\n  var isRequired = !!requesting && modifiers.some(function (modifier) {\n    return modifier.name === requestedName && modifier.enabled && modifier.order < requesting.order;\n  });\n\n  if (!isRequired) {\n    var _requesting = '`' + requestingName + '`';\n    var requested = '`' + requestedName + '`';\n    console.warn(requested + ' modifier is required by ' + _requesting + ' modifier in order to work, be sure to include it before ' + _requesting + '!');\n  }\n  return isRequired;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction arrow(data, options) {\n  var _data$offsets$arrow;\n\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  var arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn('WARNING: `arrow.element` must be child of its popper element!');\n      return data;\n    }\n  }\n\n  var placement = data.placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  var len = isVertical ? 'height' : 'width';\n  var sideCapitalized = isVertical ? 'Top' : 'Left';\n  var side = sideCapitalized.toLowerCase();\n  var altSide = isVertical ? 'left' : 'top';\n  var opSide = isVertical ? 'bottom' : 'right';\n  var arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjunction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] += reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  var center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  var css = getStyleComputedProperty(data.instance.popper);\n  var popperMarginSide = parseFloat(css['margin' + sideCapitalized]);\n  var popperBorderSide = parseFloat(css['border' + sideCapitalized + 'Width']);\n  var sideValue = center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = (_data$offsets$arrow = {}, defineProperty(_data$offsets$arrow, side, Math.round(sideValue)), defineProperty(_data$offsets$arrow, altSide, ''), _data$offsets$arrow);\n\n  return data;\n}\n\n/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nfunction getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n\n/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-end` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nvar placements = ['auto-start', 'auto', 'auto-end', 'top-start', 'top', 'top-end', 'right-start', 'right', 'right-end', 'bottom-end', 'bottom', 'bottom-start', 'left-end', 'left', 'left-start'];\n\n// Get rid of `auto` `auto-start` and `auto-end`\nvar validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nfunction clockwise(placement) {\n  var counter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var index = validPlacements.indexOf(placement);\n  var arr = validPlacements.slice(index + 1).concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n\nvar BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise'\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, options.boundariesElement, data.positionFixed);\n\n  var placement = data.placement.split('-')[0];\n  var placementOpposite = getOppositePlacement(placement);\n  var variation = data.placement.split('-')[1] || '';\n\n  var flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach(function (step, index) {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    var popperOffsets = data.offsets.popper;\n    var refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    var floor = Math.floor;\n    var overlapsRef = placement === 'left' && floor(popperOffsets.right) > floor(refOffsets.left) || placement === 'right' && floor(popperOffsets.left) < floor(refOffsets.right) || placement === 'top' && floor(popperOffsets.bottom) > floor(refOffsets.top) || placement === 'bottom' && floor(popperOffsets.top) < floor(refOffsets.bottom);\n\n    var overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    var overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    var overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    var overflowsBottom = floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    var overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom;\n\n    // flip the variation if required\n    var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n\n    // flips variation if reference element overflows boundaries\n    var flippedVariationByRef = !!options.flipVariations && (isVertical && variation === 'start' && overflowsLeft || isVertical && variation === 'end' && overflowsRight || !isVertical && variation === 'start' && overflowsTop || !isVertical && variation === 'end' && overflowsBottom);\n\n    // flips variation if popper content overflows boundaries\n    var flippedVariationByContent = !!options.flipVariationsByContent && (isVertical && variation === 'start' && overflowsRight || isVertical && variation === 'end' && overflowsLeft || !isVertical && variation === 'start' && overflowsBottom || !isVertical && variation === 'end' && overflowsTop);\n\n    var flippedVariation = flippedVariationByRef || flippedVariationByContent;\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = _extends({}, data.offsets.popper, getPopperOffsets(data.instance.popper, data.offsets.reference, data.placement));\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction keepTogether(data) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var placement = data.placement.split('-')[0];\n  var floor = Math.floor;\n  var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  var side = isVertical ? 'right' : 'bottom';\n  var opSide = isVertical ? 'left' : 'top';\n  var measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] = floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nfunction toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  var split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  var value = +split[1];\n  var unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    var element = void 0;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    var rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    var size = void 0;\n    if (unit === 'vh') {\n      size = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);\n    } else {\n      size = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nfunction parseOffset(offset, popperOffsets, referenceOffsets, basePlacement) {\n  var offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  var useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  var fragments = offset.split(/(\\+|\\-)/).map(function (frag) {\n    return frag.trim();\n  });\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  var divider = fragments.indexOf(find(fragments, function (frag) {\n    return frag.search(/,|\\s/) !== -1;\n  }));\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  var splitRegex = /\\s*,\\s*|\\s+/;\n  var ops = divider !== -1 ? [fragments.slice(0, divider).concat([fragments[divider].split(splitRegex)[0]]), [fragments[divider].split(splitRegex)[1]].concat(fragments.slice(divider + 1))] : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map(function (op, index) {\n    // Most of the units rely on the orientation of the popper\n    var measurement = (index === 1 ? !useHeight : useHeight) ? 'height' : 'width';\n    var mergeWithPrevious = false;\n    return op\n    // This aggregates any `+` or `-` sign that aren't considered operators\n    // e.g.: 10 + +5 => [10, +, +5]\n    .reduce(function (a, b) {\n      if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n        a[a.length - 1] = b;\n        mergeWithPrevious = true;\n        return a;\n      } else if (mergeWithPrevious) {\n        a[a.length - 1] += b;\n        mergeWithPrevious = false;\n        return a;\n      } else {\n        return a.concat(b);\n      }\n    }, [])\n    // Here we convert the string values into number values (in px)\n    .map(function (str) {\n      return toValue(str, measurement, popperOffsets, referenceOffsets);\n    });\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach(function (op, index) {\n    op.forEach(function (frag, index2) {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nfunction offset(data, _ref) {\n  var offset = _ref.offset;\n  var placement = data.placement,\n      _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var basePlacement = placement.split('-')[0];\n\n  var offsets = void 0;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction preventOverflow(data, options) {\n  var boundariesElement = options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  // NOTE: DOM access here\n  // resets the popper's position so that the document size can be calculated excluding\n  // the size of the popper element itself\n  var transformProp = getSupportedPropertyName('transform');\n  var popperStyles = data.instance.popper.style; // assignment to help minification\n  var top = popperStyles.top,\n      left = popperStyles.left,\n      transform = popperStyles[transformProp];\n\n  popperStyles.top = '';\n  popperStyles.left = '';\n  popperStyles[transformProp] = '';\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, boundariesElement, data.positionFixed);\n\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  popperStyles.top = top;\n  popperStyles.left = left;\n  popperStyles[transformProp] = transform;\n\n  options.boundaries = boundaries;\n\n  var order = options.priority;\n  var popper = data.offsets.popper;\n\n  var check = {\n    primary: function primary(placement) {\n      var value = popper[placement];\n      if (popper[placement] < boundaries[placement] && !options.escapeWithReference) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return defineProperty({}, placement, value);\n    },\n    secondary: function secondary(placement) {\n      var mainSide = placement === 'right' ? 'left' : 'top';\n      var value = popper[mainSide];\n      if (popper[placement] > boundaries[placement] && !options.escapeWithReference) {\n        value = Math.min(popper[mainSide], boundaries[placement] - (placement === 'right' ? popper.width : popper.height));\n      }\n      return defineProperty({}, mainSide, value);\n    }\n  };\n\n  order.forEach(function (placement) {\n    var side = ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = _extends({}, popper, check[side](placement));\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction shift(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    var _data$offsets = data.offsets,\n        reference = _data$offsets.reference,\n        popper = _data$offsets.popper;\n\n    var isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    var side = isVertical ? 'left' : 'top';\n    var measurement = isVertical ? 'width' : 'height';\n\n    var shiftOffsets = {\n      start: defineProperty({}, side, reference[side]),\n      end: defineProperty({}, side, reference[side] + reference[measurement] - popper[measurement])\n    };\n\n    data.offsets.popper = _extends({}, popper, shiftOffsets[shiftvariation]);\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  var refRect = data.offsets.reference;\n  var bound = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'preventOverflow';\n  }).boundaries;\n\n  if (refRect.bottom < bound.top || refRect.left > bound.right || refRect.top > bound.bottom || refRect.right < bound.left) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction inner(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  var subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] = reference[basePlacement] - (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nvar modifiers = {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unit-less, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the `height`.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > You can read more on this at this [issue](https://github.com/FezVrasta/popper.js/issues/373).\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * A scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper. This makes sure the popper always has a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier. Can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent'\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near each other\n   * without leaving any gap between the two. Especially useful when the arrow is\n   * enabled and you want to ensure that it points to its reference element.\n   * It cares only about the first axis. You can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjunction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]'\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations)\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position.\n     * The popper will never be placed outside of the defined boundaries\n     * (except if `keepTogether` is enabled)\n     */\n    boundariesElement: 'viewport',\n    /**\n     * @prop {Boolean} flipVariations=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the reference element overlaps its boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariations: false,\n    /**\n     * @prop {Boolean} flipVariationsByContent=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the popper element overlaps its reference boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariationsByContent: false\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right'\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define your own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: undefined\n  }\n};\n\n/**\n * The `dataObject` is an object containing all the information used by Popper.js.\n * This object is passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overridden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass an object with the same\n * structure of the `options` object, as the 3rd argument. For example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nvar Defaults = {\n  /**\n   * Popper's placement.\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Set this to true if you want popper to position it self in 'fixed' mode\n   * @prop {Boolean} positionFixed=false\n   */\n  positionFixed: false,\n\n  /**\n   * Whether events (resize, scroll) are initially enabled.\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: function onCreate() {},\n\n  /**\n   * Callback called when the popper is updated. This callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: function onUpdate() {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js.\n   * @prop {modifiers}\n   */\n  modifiers: modifiers\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n\n// Utils\n// Methods\nvar Popper = function () {\n  /**\n   * Creates a new Popper.js instance.\n   * @class Popper\n   * @param {Element|referenceObject} reference - The reference element used to position the popper\n   * @param {Element} popper - The HTML / XML element used as the popper\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  function Popper(reference, popper) {\n    var _this = this;\n\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    classCallCheck(this, Popper);\n\n    this.scheduleUpdate = function () {\n      return requestAnimationFrame(_this.update);\n    };\n\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = _extends({}, Popper.Defaults, options);\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: []\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys(_extends({}, Popper.Defaults.modifiers, options.modifiers)).forEach(function (name) {\n      _this.options.modifiers[name] = _extends({}, Popper.Defaults.modifiers[name] || {}, options.modifiers ? options.modifiers[name] : {});\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers).map(function (name) {\n      return _extends({\n        name: name\n      }, _this.options.modifiers[name]);\n    })\n    // sort the modifiers by order\n    .sort(function (a, b) {\n      return a.order - b.order;\n    });\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(function (modifierOptions) {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(_this.reference, _this.popper, _this.options, modifierOptions, _this.state);\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    var eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n\n\n  createClass(Popper, [{\n    key: 'update',\n    value: function update$$1() {\n      return update.call(this);\n    }\n  }, {\n    key: 'destroy',\n    value: function destroy$$1() {\n      return destroy.call(this);\n    }\n  }, {\n    key: 'enableEventListeners',\n    value: function enableEventListeners$$1() {\n      return enableEventListeners.call(this);\n    }\n  }, {\n    key: 'disableEventListeners',\n    value: function disableEventListeners$$1() {\n      return disableEventListeners.call(this);\n    }\n\n    /**\n     * Schedules an update. It will run on the next UI update available.\n     * @method scheduleUpdate\n     * @memberof Popper\n     */\n\n\n    /**\n     * Collection of utilities useful when writing custom modifiers.\n     * Starting from version 1.7, this method is available only if you\n     * include `popper-utils.js` before `popper.js`.\n     *\n     * **DEPRECATION**: This way to access PopperUtils is deprecated\n     * and will be removed in v2! Use the PopperUtils module directly instead.\n     * Due to the high instability of the methods contained in Utils, we can't\n     * guarantee them to follow semver. Use them at your own risk!\n     * @static\n     * @private\n     * @type {Object}\n     * @deprecated since version 1.8\n     * @member Utils\n     * @memberof Popper\n     */\n\n  }]);\n  return Popper;\n}();\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10.\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n\n\nPopper.Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\nPopper.placements = placements;\nPopper.Defaults = Defaults;\n\nexport default Popper;\n//# sourceMappingURL=popper.js.map\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPRIGHT = 'dropright'\nconst CLASS_NAME_DROPLEFT = 'dropleft'\nconst CLASS_NAME_MENURIGHT = 'dropdown-menu-right'\nconst CLASS_NAME_NAVBAR = 'navbar'\nconst CLASS_NAME_POSITION_STATIC = 'position-static'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = 'top-start'\nconst PLACEMENT_TOPEND = 'top-end'\nconst PLACEMENT_BOTTOM = 'bottom-start'\nconst PLACEMENT_BOTTOMEND = 'bottom-end'\nconst PLACEMENT_RIGHT = 'right-start'\nconst PLACEMENT_LEFT = 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'scrollParent',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element = element\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Disable totally Popper.js for Dropdown in Navbar\n    if (!this._inNavbar) {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper.js (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        parent.classList.add(CLASS_NAME_POSITION_STATIC)\n      }\n\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._element, EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n    let placement = PLACEMENT_BOTTOM\n\n    // Handle dropup\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      placement = this._menu.classList.contains(CLASS_NAME_MENURIGHT) ?\n        PLACEMENT_TOPEND :\n        PLACEMENT_TOP\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPRIGHT)) {\n      placement = PLACEMENT_RIGHT\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPLEFT)) {\n      placement = PLACEMENT_LEFT\n    } else if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n      placement = PLACEMENT_BOTTOMEND\n    }\n\n    return placement\n  }\n\n  _detectNavbar() {\n    return Boolean(this._element.closest(`.${CLASS_NAME_NAVBAR}`))\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...(this._config.offset(data.offsets, this._element) || {})\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper.js if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON ||\n      (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown.getParentFromElement(toggles[i])\n      const context = Data.getData(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && ((event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName)) ||\n          (event.type === 'keyup' && event.key === TAB_KEY)) &&\n          dropdownMenu.contains(event.target)) {\n        continue\n      }\n\n      const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.key === ARROW_UP_KEY && index > 0) { // Up\n      index--\n    }\n\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) { // Down\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  event.stopPropagation()\n  Dropdown.dropdownInterface(this, 'toggle')\n})\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => e.stopPropagation())\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Dropdown.jQueryInterface\n    $.fn[NAME].Constructor = Dropdown\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Dropdown.jQueryInterface\n    }\n  }\n})\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true,\n  show: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean',\n  show: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config = this._getConfig(config)\n    this._element = element\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      event => this.hide(event)\n    )\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._element = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (transition) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, TRANSITION_END, transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const animate = this._element.classList.contains(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE :\n      ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        this._triggerBackdropTransition()\n      })\n\n      if (animate) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, TRANSITION_END, callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (this._element.classList.contains(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, TRANSITION_END, callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _triggerBackdropTransition() {\n    if (this._config.backdrop === 'static') {\n      const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n      if (hideEvent.defaultPrevented) {\n        return\n      }\n\n      const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n      if (!isModalOverflowing) {\n        this._element.style.overflowY = 'hidden'\n      }\n\n      this._element.classList.add(CLASS_NAME_STATIC)\n      const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n      EventHandler.off(this._element, TRANSITION_END)\n      EventHandler.one(this._element, TRANSITION_END, () => {\n        this._element.classList.remove(CLASS_NAME_STATIC)\n        if (!isModalOverflowing) {\n          EventHandler.one(this._element, TRANSITION_END, () => {\n            this._element.style.overflowY = ''\n          })\n          emulateTransitionEnd(this._element, modalTransitionDuration)\n        }\n      })\n      emulateTransitionEnd(this._element, modalTransitionDuration)\n      this._element.focus()\n    } else {\n      this.hide()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n      // Adjust fixed content padding\n      SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n        .forEach(element => {\n          const actualPadding = element.style.paddingRight\n          const calculatedPadding = window.getComputedStyle(element)['padding-right']\n          Manipulator.setDataAttribute(element, 'padding-right', actualPadding)\n          element.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n        })\n\n      // Adjust sticky content margin\n      SelectorEngine.find(SELECTOR_STICKY_CONTENT)\n        .forEach(element => {\n          const actualMargin = element.style.marginRight\n          const calculatedMargin = window.getComputedStyle(element)['margin-right']\n          Manipulator.setDataAttribute(element, 'margin-right', actualMargin)\n          element.style.marginRight = `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`\n        })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = window.getComputedStyle(document.body)['padding-right']\n\n      Manipulator.setDataAttribute(document.body, 'padding-right', actualPadding)\n      document.body.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n      .forEach(element => {\n        const padding = Manipulator.getDataAttribute(element, 'padding-right')\n        if (typeof padding !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'padding-right')\n          element.style.paddingRight = padding\n        }\n      })\n\n    // Restore sticky content and navbar-toggler margin\n    SelectorEngine.find(`${SELECTOR_STICKY_CONTENT}`)\n      .forEach(element => {\n        const margin = Manipulator.getDataAttribute(element, 'margin-right')\n        if (typeof margin !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'margin-right')\n          element.style.marginRight = margin\n        }\n      })\n\n    // Restore body padding\n    const padding = Manipulator.getDataAttribute(document.body, 'padding-right')\n    if (typeof padding === 'undefined') {\n      document.body.style.paddingRight = ''\n    } else {\n      Manipulator.removeDataAttribute(document.body, 'padding-right')\n      document.body.style.paddingRight = padding\n    }\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.getData(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.show(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Modal.jQueryInterface\n    $.fn[NAME].Constructor = Modal\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Modal.jQueryInterface\n    }\n  }\n})\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (allowlistKeys.indexOf(elName) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'allowList', 'sanitizeFn']\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(number|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacement: '(string|array)',\n  boundary: '(string|element)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: 'right',\n  BOTTOM: 'bottom',\n  LEFT: 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"tooltip-arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: 0,\n  container: false,\n  fallbackPlacement: 'flip',\n  boundary: 'scrollParent',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper.js (https://popper.js.org)')\n    }\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.element = element\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n    Data.setData(element, this.constructor.DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = Data.getData(event.delegateTarget, dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.delegateTarget,\n          this._getDelegateConfig()\n        )\n        Data.setData(event.delegateTarget, dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    Data.removeData(this.element, this.constructor.DATA_KEY)\n\n    EventHandler.off(this.element, this.constructor.EVENT_KEY)\n    EventHandler.off(this.element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config = null\n    this.tip = null\n  }\n\n  show() {\n    if (this.element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (this.isWithContent() && this._isEnabled) {\n      const showEvent = EventHandler.trigger(this.element, this.constructor.Event.SHOW)\n      const shadowRoot = findShadowRoot(this.element)\n      const isInTheDom = shadowRoot === null ?\n        this.element.ownerDocument.documentElement.contains(this.element) :\n        shadowRoot.contains(this.element)\n\n      if (showEvent.defaultPrevented || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        tip.classList.add(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this.element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this._addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      Data.setData(tip, this.constructor.DATA_KEY, this)\n\n      if (!this.element.ownerDocument.documentElement.contains(this.tip)) {\n        container.appendChild(tip)\n      }\n\n      EventHandler.trigger(this.element, this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, this._getPopperConfig(attachment))\n\n      tip.classList.add(CLASS_NAME_SHOW)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children).forEach(element => {\n          EventHandler.on(element, 'mouseover', noop())\n        })\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n\n        const prevHoverState = this._hoverState\n        this._hoverState = null\n\n        EventHandler.trigger(this.element, this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n        const transitionDuration = getTransitionDurationFromElement(this.tip)\n        EventHandler.one(this.tip, TRANSITION_END, complete)\n        emulateTransitionEnd(this.tip, transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this.element, this.constructor.Event.HIDDEN)\n      this._popper.destroy()\n    }\n\n    const hideEvent = EventHandler.trigger(this.element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, TRANSITION_END, complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.allowList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this.element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          behavior: this.config.fallbackPlacement\n        },\n        arrow: {\n          element: `.${this.constructor.NAME}-arrow`\n        },\n        preventOverflow: {\n          boundariesElement: this.config.boundary\n        }\n      },\n      onCreate: data => {\n        if (data.originalPlacement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      },\n      onUpdate: data => this._handlePopperPlacementChange(data)\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...(this.config.offset(data.offsets, this.element) || {})\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this.element,\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this.element,\n          eventIn,\n          this.config.selector,\n          event => this._enter(event)\n        )\n        EventHandler.on(this.element,\n          eventOut,\n          this.config.selector,\n          event => this._leave(event)\n        )\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this.element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this.element.closest(`.${CLASS_NAME_MODAL}`),\n      'hide.bs.modal',\n      this._hideModalHandler\n    )\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) ||\n        context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this.element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    this.tip = popperData.instance.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tooltip.jQueryInterface\n    $.fn[NAME].Constructor = Tooltip\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tooltip.jQueryInterface\n    }\n  }\n})\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.setData(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Popover.jQueryInterface\n    $.fn[NAME].Constructor = Popover\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Popover.jQueryInterface\n    }\n  }\n})\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._element = null\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset +\n      scrollHeight -\n      this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = ScrollSpy.jQueryInterface\n    $.fn[NAME].Constructor = ScrollSpy\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return ScrollSpy.jQueryInterface\n    }\n  }\n})\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab {\n  constructor(element) {\n    this._element = element\n\n    Data.setData(this._element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    let hideEvent = null\n\n    if (previous) {\n      hideEvent = EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      })\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented ||\n      (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(\n      this._element,\n      listElement\n    )\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback &&\n      (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(\n      element,\n      active,\n      callback\n    )\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, TRANSITION_END, complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.getData(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.getData(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tab.jQueryInterface\n    $.fn[NAME].Constructor = Tab\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tab.jQueryInterface\n    }\n  }\n})\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._element = null\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Toast.jQueryInterface\n    $.fn[NAME].Constructor = Toast\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Toast.jQueryInterface\n    }\n  }\n})\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"]}