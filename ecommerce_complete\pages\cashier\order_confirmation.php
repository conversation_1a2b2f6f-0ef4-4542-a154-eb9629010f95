<?php
require_once '../../config/ecommerce_db.php';
require_once '../../config/session.php';

// Require login
requireLogin();

// Check if order_id is provided
if (!isset($_GET['order_id']) || empty($_GET['order_id'])) {
    header("Location: index.php");
    exit();
}

$order_id = $_GET['order_id'];

// Get order details
$order_sql = "SELECT o.*, CONCAT(u.first_name, ' ', u.last_name) as customer_name, u.email, u.phone, u.address
              FROM e_orders o
              JOIN e_users u ON o.user_id = u.id
              WHERE o.id = :order_id
              LIMIT 1";
$order = executeQuery($order_sql, [':order_id' => $order_id])->fetch();

// If order not found or not completed, redirect to index
if (!$order || $order['status'] !== 'completed') {
    header("Location: index.php");
    exit();
}

// Get order items
$items_sql = "SELECT oi.*, p.name, p.image_url
              FROM e_order_items oi
              JOIN e_products p ON oi.product_id = p.id
              WHERE oi.order_id = :order_id";
$items = executeQuery($items_sql, [':order_id' => $order_id])->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - Euro Spice</title>
    <link rel="stylesheet" href="../../plugins/bootstrap/css/bootstrap.min.css">
    <script defer src="../../plugins/fontawesome/js/all.js"></script>
    <style>
        .order-item {
            border-bottom: 1px solid #eee;
            padding: 10px 0;
        }
        .product-img {
            width: 60px;
            height: 60px;
            object-fit: cover;
        }
        .print-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-light border-bottom site-header sticky-top py-1"
        style="background-color: #f15b31;">
        <div class="container">
            <a class="navbar-brand fw-bold fs-4" href="../../index.php" style="color: white;"><b>Euro Spice</b></a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
                aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home"></i> Back to Cashier
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../account/settings.php">
                            <i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../account/logout.php">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container py-5" id="printable-content">
        <div class="text-center mb-4">
            <h2>Order Confirmation</h2>
            <p class="text-success"><i class="fas fa-check-circle"></i> Payment Completed</p>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <div class="d-flex justify-content-between">
                            <h5 class="mb-0">Order Details</h5>
                            <span>Order #<?php echo $order_id; ?></span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p><strong>Customer:</strong> <?php echo htmlspecialchars($order['customer_name']); ?></p>
                                <p><strong>Email:</strong> <?php echo htmlspecialchars($order['email']); ?></p>
                                <p><strong>Phone:</strong> <?php echo htmlspecialchars($order['phone']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Order Date:</strong> <?php echo date('F j, Y, g:i a', strtotime($order['created_at'])); ?></p>
                                <p><strong>Payment Status:</strong> <?php echo ucfirst($order['payment_status']); ?></p>
                                <p><strong>Order Status:</strong> <?php echo ucfirst($order['status']); ?></p>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <p><strong>Shipping Address:</strong><br>
                            <?php echo nl2br(htmlspecialchars($order['shipping_address'] ?? $order['address'])); ?></p>
                        </div>
                        
                        <h6 class="mt-4 mb-3">Order Items</h6>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th class="text-center">Quantity</th>
                                    <th class="text-end">Price</th>
                                    <th class="text-end">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($items as $item): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['name']); ?></td>
                                        <td class="text-center"><?php echo $item['quantity']; ?></td>
                                        <td class="text-end">PHP <?php echo number_format($item['price'], 2); ?></td>
                                        <td class="text-end">PHP <?php echo number_format($item['price'] * $item['quantity'], 2); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3" class="text-end">Subtotal:</th>
                                    <th class="text-end">PHP <?php echo number_format($order['total_amount'], 2); ?></th>
                                </tr>
                                <tr>
                                    <th colspan="3" class="text-end">Shipping:</th>
                                    <td class="text-end">Free</td>
                                </tr>
                                <tr>
                                    <th colspan="3" class="text-end">Total:</th>
                                    <th class="text-end">PHP <?php echo number_format($order['total_amount'], 2); ?></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="index.php" class="btn btn-primary me-2">Back to Cashier</a>
                    <button onclick="window.print();" class="btn btn-outline-secondary">
                        <i class="fas fa-print"></i> Print Receipt
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <button onclick="window.print();" class="btn btn-primary rounded-circle print-btn d-print-none">
        <i class="fas fa-print"></i>
    </button>

    <script src="../../plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <style type="text/css" media="print">
        @media print {
            nav, .print-btn {
                display: none !important;
            }
            .container {
                width: 100% !important;
                max-width: 100% !important;
            }
        }
    </style>
</body>

</html>
