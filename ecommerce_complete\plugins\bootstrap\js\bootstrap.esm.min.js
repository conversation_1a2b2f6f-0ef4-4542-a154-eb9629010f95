/*!
  * Bootstrap v5.0.0-alpha3 (https://getbootstrap.com/)
  * Copyright 2011-2020 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */
import <PERSON><PERSON> from"popper.js";function _defineProperties(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function _createClass(t,e,n){return e&&_defineProperties(t.prototype,e),n&&_defineProperties(t,n),t}function _extends(){return(_extends=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t}).apply(this,arguments)}function _inheritsLoose(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}var MAX_UID=1e6,MILLISECONDS_MULTIPLIER=1e3,TRANSITION_END="transitionend",toType=function(t){return null==t?""+t:{}.toString.call(t).match(/\s([a-z]+)/i)[1].toLowerCase()},getUID=function(t){do{t+=Math.floor(Math.random()*MAX_UID)}while(document.getElementById(t));return t},getSelector=function(t){var e=t.getAttribute("data-target");if(!e||"#"===e){var n=t.getAttribute("href");e=n&&"#"!==n?n.trim():null}return e},getSelectorFromElement=function(t){var e=getSelector(t);return e&&document.querySelector(e)?e:null},getElementFromSelector=function(t){var e=getSelector(t);return e?document.querySelector(e):null},getTransitionDurationFromElement=function(t){if(!t)return 0;var e=window.getComputedStyle(t),n=e.transitionDuration,i=e.transitionDelay,o=parseFloat(n),r=parseFloat(i);return o||r?(n=n.split(",")[0],i=i.split(",")[0],(parseFloat(n)+parseFloat(i))*MILLISECONDS_MULTIPLIER):0},triggerTransitionEnd=function(t){t.dispatchEvent(new Event(TRANSITION_END))},isElement=function(t){return(t[0]||t).nodeType},emulateTransitionEnd=function(t,e){var n=!1,i=e+5;t.addEventListener(TRANSITION_END,(function e(){n=!0,t.removeEventListener(TRANSITION_END,e)})),setTimeout((function(){n||triggerTransitionEnd(t)}),i)},typeCheckConfig=function(t,e,n){Object.keys(n).forEach((function(i){var o=n[i],r=e[i],a=r&&isElement(r)?"element":toType(r);if(!new RegExp(o).test(a))throw new Error(t.toUpperCase()+': Option "'+i+'" provided type "'+a+'" but expected type "'+o+'".')}))},isVisible=function(t){if(!t)return!1;if(t.style&&t.parentNode&&t.parentNode.style){var e=getComputedStyle(t),n=getComputedStyle(t.parentNode);return"none"!==e.display&&"none"!==n.display&&"hidden"!==e.visibility}return!1},findShadowRoot=function t(e){if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){var n=e.getRootNode();return n instanceof ShadowRoot?n:null}return e instanceof ShadowRoot?e:e.parentNode?t(e.parentNode):null},noop=function(){return function(){}},reflow=function(t){return t.offsetHeight},getjQuery=function(){var t=window.jQuery;return t&&!document.body.hasAttribute("data-no-jquery")?t:null},onDOMContentLoaded=function(t){"loading"===document.readyState?document.addEventListener("DOMContentLoaded",t):t()},mapData=function(){var t={},e=1;return{set:function(n,i,o){void 0===n.bsKey&&(n.bsKey={key:i,id:e},e++),t[n.bsKey.id]=o},get:function(e,n){if(!e||void 0===e.bsKey)return null;var i=e.bsKey;return i.key===n?t[i.id]:null},delete:function(e,n){if(void 0!==e.bsKey){var i=e.bsKey;i.key===n&&(delete t[i.id],delete e.bsKey)}}}}(),Data={setData:function(t,e,n){mapData.set(t,e,n)},getData:function(t,e){return mapData.get(t,e)},removeData:function(t,e){mapData.delete(t,e)}},namespaceRegex=/[^.]*(?=\..*)\.|.*/,stripNameRegex=/\..*/,stripUidRegex=/::\d+$/,eventRegistry={},uidEvent=1,customEvents={mouseenter:"mouseover",mouseleave:"mouseout"},nativeEvents=["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"];function getUidEvent(t,e){return e&&e+"::"+uidEvent++||t.uidEvent||uidEvent++}function getEvent(t){var e=getUidEvent(t);return t.uidEvent=e,eventRegistry[e]=eventRegistry[e]||{},eventRegistry[e]}function bootstrapHandler(t,e){return function n(i){return i.delegateTarget=t,n.oneOff&&EventHandler.off(t,i.type,e),e.apply(t,[i])}}function bootstrapDelegationHandler(t,e,n){return function i(o){for(var r=t.querySelectorAll(e),a=o.target;a&&a!==this;a=a.parentNode)for(var s=r.length;s--;)if(r[s]===a)return o.delegateTarget=a,i.oneOff&&EventHandler.off(t,o.type,n),n.apply(a,[o]);return null}}function findHandler(t,e,n){void 0===n&&(n=null);for(var i=Object.keys(t),o=0,r=i.length;o<r;o++){var a=t[i[o]];if(a.originalHandler===e&&a.delegationSelector===n)return a}return null}function normalizeParams(t,e,n){var i="string"==typeof e,o=i?n:e,r=t.replace(stripNameRegex,""),a=customEvents[r];return a&&(r=a),nativeEvents.indexOf(r)>-1||(r=t),[i,o,r]}function addHandler(t,e,n,i,o){if("string"==typeof e&&t){n||(n=i,i=null);var r=normalizeParams(e,n,i),a=r[0],s=r[1],l=r[2],E=getEvent(t),_=E[l]||(E[l]={}),c=findHandler(_,s,a?n:null);if(c)c.oneOff=c.oneOff&&o;else{var u=getUidEvent(s,e.replace(namespaceRegex,"")),f=a?bootstrapDelegationHandler(t,n,i):bootstrapHandler(t,n);f.delegationSelector=a?n:null,f.originalHandler=s,f.oneOff=o,f.uidEvent=u,_[u]=f,t.addEventListener(l,f,a)}}}function removeHandler(t,e,n,i,o){var r=findHandler(e[n],i,o);r&&(t.removeEventListener(n,r,Boolean(o)),delete e[n][r.uidEvent])}function removeNamespacedHandlers(t,e,n,i){var o=e[n]||{};Object.keys(o).forEach((function(r){if(r.indexOf(i)>-1){var a=o[r];removeHandler(t,e,n,a.originalHandler,a.delegationSelector)}}))}var EventHandler={on:function(t,e,n,i){addHandler(t,e,n,i,!1)},one:function(t,e,n,i){addHandler(t,e,n,i,!0)},off:function(t,e,n,i){if("string"==typeof e&&t){var o=normalizeParams(e,n,i),r=o[0],a=o[1],s=o[2],l=s!==e,E=getEvent(t),_="."===e.charAt(0);if(void 0===a){_&&Object.keys(E).forEach((function(n){removeNamespacedHandlers(t,E,n,e.slice(1))}));var c=E[s]||{};Object.keys(c).forEach((function(n){var i=n.replace(stripUidRegex,"");if(!l||e.indexOf(i)>-1){var o=c[n];removeHandler(t,E,s,o.originalHandler,o.delegationSelector)}}))}else{if(!E||!E[s])return;removeHandler(t,E,s,a,r?n:null)}}},trigger:function(t,e,n){if("string"!=typeof e||!t)return null;var i,o=getjQuery(),r=e.replace(stripNameRegex,""),a=e!==r,s=nativeEvents.indexOf(r)>-1,l=!0,E=!0,_=!1,c=null;return a&&o&&(i=o.Event(e,n),o(t).trigger(i),l=!i.isPropagationStopped(),E=!i.isImmediatePropagationStopped(),_=i.isDefaultPrevented()),s?(c=document.createEvent("HTMLEvents")).initEvent(r,l,!0):c=new CustomEvent(e,{bubbles:l,cancelable:!0}),void 0!==n&&Object.keys(n).forEach((function(t){Object.defineProperty(c,t,{get:function(){return n[t]}})})),_&&c.preventDefault(),E&&t.dispatchEvent(c),c.defaultPrevented&&void 0!==i&&i.preventDefault(),c}},NAME="alert",VERSION="5.0.0-alpha3",DATA_KEY="bs.alert",EVENT_KEY="."+DATA_KEY,DATA_API_KEY=".data-api",SELECTOR_DISMISS='[data-dismiss="alert"]',EVENT_CLOSE="close"+EVENT_KEY,EVENT_CLOSED="closed"+EVENT_KEY,EVENT_CLICK_DATA_API="click"+EVENT_KEY+DATA_API_KEY,CLASSNAME_ALERT="alert",CLASSNAME_FADE="fade",CLASSNAME_SHOW="show",Alert=function(){function t(t){this._element=t,this._element&&Data.setData(t,DATA_KEY,this)}var e=t.prototype;return e.close=function(t){var e=t?this._getRootElement(t):this._element,n=this._triggerCloseEvent(e);null===n||n.defaultPrevented||this._removeElement(e)},e.dispose=function(){Data.removeData(this._element,DATA_KEY),this._element=null},e._getRootElement=function(t){return getElementFromSelector(t)||t.closest("."+CLASSNAME_ALERT)},e._triggerCloseEvent=function(t){return EventHandler.trigger(t,EVENT_CLOSE)},e._removeElement=function(t){var e=this;if(t.classList.remove(CLASSNAME_SHOW),t.classList.contains(CLASSNAME_FADE)){var n=getTransitionDurationFromElement(t);EventHandler.one(t,TRANSITION_END,(function(){return e._destroyElement(t)})),emulateTransitionEnd(t,n)}else this._destroyElement(t)},e._destroyElement=function(t){t.parentNode&&t.parentNode.removeChild(t),EventHandler.trigger(t,EVENT_CLOSED)},t.jQueryInterface=function(e){return this.each((function(){var n=Data.getData(this,DATA_KEY);n||(n=new t(this)),"close"===e&&n[e](this)}))},t.handleDismiss=function(t){return function(e){e&&e.preventDefault(),t.close(this)}},t.getInstance=function(t){return Data.getData(t,DATA_KEY)},_createClass(t,null,[{key:"VERSION",get:function(){return VERSION}}]),t}();EventHandler.on(document,EVENT_CLICK_DATA_API,SELECTOR_DISMISS,Alert.handleDismiss(new Alert)),onDOMContentLoaded((function(){var t=getjQuery();if(t){var e=t.fn[NAME];t.fn[NAME]=Alert.jQueryInterface,t.fn[NAME].Constructor=Alert,t.fn[NAME].noConflict=function(){return t.fn[NAME]=e,Alert.jQueryInterface}}}));var NAME$1="button",VERSION$1="5.0.0-alpha3",DATA_KEY$1="bs.button",EVENT_KEY$1="."+DATA_KEY$1,DATA_API_KEY$1=".data-api",CLASS_NAME_ACTIVE="active",SELECTOR_DATA_TOGGLE='[data-toggle="button"]',EVENT_CLICK_DATA_API$1="click"+EVENT_KEY$1+DATA_API_KEY$1,Button=function(){function t(t){this._element=t,Data.setData(t,DATA_KEY$1,this)}var e=t.prototype;return e.toggle=function(){this._element.setAttribute("aria-pressed",this._element.classList.toggle(CLASS_NAME_ACTIVE))},e.dispose=function(){Data.removeData(this._element,DATA_KEY$1),this._element=null},t.jQueryInterface=function(e){return this.each((function(){var n=Data.getData(this,DATA_KEY$1);n||(n=new t(this)),"toggle"===e&&n[e]()}))},t.getInstance=function(t){return Data.getData(t,DATA_KEY$1)},_createClass(t,null,[{key:"VERSION",get:function(){return VERSION$1}}]),t}();function normalizeData(t){return"true"===t||"false"!==t&&(t===Number(t).toString()?Number(t):""===t||"null"===t?null:t)}function normalizeDataKey(t){return t.replace(/[A-Z]/g,(function(t){return"-"+t.toLowerCase()}))}EventHandler.on(document,EVENT_CLICK_DATA_API$1,SELECTOR_DATA_TOGGLE,(function(t){t.preventDefault();var e=t.target.closest(SELECTOR_DATA_TOGGLE),n=Data.getData(e,DATA_KEY$1);n||(n=new Button(e)),n.toggle()})),onDOMContentLoaded((function(){var t=getjQuery();if(t){var e=t.fn[NAME$1];t.fn[NAME$1]=Button.jQueryInterface,t.fn[NAME$1].Constructor=Button,t.fn[NAME$1].noConflict=function(){return t.fn[NAME$1]=e,Button.jQueryInterface}}}));var Manipulator={setDataAttribute:function(t,e,n){t.setAttribute("data-"+normalizeDataKey(e),n)},removeDataAttribute:function(t,e){t.removeAttribute("data-"+normalizeDataKey(e))},getDataAttributes:function(t){if(!t)return{};var e=_extends({},t.dataset);return Object.keys(e).forEach((function(t){e[t]=normalizeData(e[t])})),e},getDataAttribute:function(t,e){return normalizeData(t.getAttribute("data-"+normalizeDataKey(e)))},offset:function(t){var e=t.getBoundingClientRect();return{top:e.top+document.body.scrollTop,left:e.left+document.body.scrollLeft}},position:function(t){return{top:t.offsetTop,left:t.offsetLeft}}},NODE_TEXT=3,SelectorEngine={matches:function(t,e){return t.matches(e)},find:function(t,e){var n;return void 0===e&&(e=document.documentElement),(n=[]).concat.apply(n,Element.prototype.querySelectorAll.call(e,t))},findOne:function(t,e){return void 0===e&&(e=document.documentElement),Element.prototype.querySelector.call(e,t)},children:function(t,e){var n,i=(n=[]).concat.apply(n,t.children);return i.filter((function(t){return t.matches(e)}))},parents:function(t,e){for(var n=[],i=t.parentNode;i&&i.nodeType===Node.ELEMENT_NODE&&i.nodeType!==NODE_TEXT;)this.matches(i,e)&&n.push(i),i=i.parentNode;return n},prev:function(t,e){for(var n=t.previousElementSibling;n;){if(n.matches(e))return[n];n=n.previousElementSibling}return[]},next:function(t,e){for(var n=t.nextElementSibling;n;){if(this.matches(n,e))return[n];n=n.nextElementSibling}return[]}},NAME$2="carousel",VERSION$2="5.0.0-alpha3",DATA_KEY$2="bs.carousel",EVENT_KEY$2="."+DATA_KEY$2,DATA_API_KEY$2=".data-api",ARROW_LEFT_KEY="ArrowLeft",ARROW_RIGHT_KEY="ArrowRight",TOUCHEVENT_COMPAT_WAIT=500,SWIPE_THRESHOLD=40,Default={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},DefaultType={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},DIRECTION_NEXT="next",DIRECTION_PREV="prev",DIRECTION_LEFT="left",DIRECTION_RIGHT="right",EVENT_SLIDE="slide"+EVENT_KEY$2,EVENT_SLID="slid"+EVENT_KEY$2,EVENT_KEYDOWN="keydown"+EVENT_KEY$2,EVENT_MOUSEENTER="mouseenter"+EVENT_KEY$2,EVENT_MOUSELEAVE="mouseleave"+EVENT_KEY$2,EVENT_TOUCHSTART="touchstart"+EVENT_KEY$2,EVENT_TOUCHMOVE="touchmove"+EVENT_KEY$2,EVENT_TOUCHEND="touchend"+EVENT_KEY$2,EVENT_POINTERDOWN="pointerdown"+EVENT_KEY$2,EVENT_POINTERUP="pointerup"+EVENT_KEY$2,EVENT_DRAG_START="dragstart"+EVENT_KEY$2,EVENT_LOAD_DATA_API="load"+EVENT_KEY$2+DATA_API_KEY$2,EVENT_CLICK_DATA_API$2="click"+EVENT_KEY$2+DATA_API_KEY$2,CLASS_NAME_CAROUSEL="carousel",CLASS_NAME_ACTIVE$1="active",CLASS_NAME_SLIDE="slide",CLASS_NAME_RIGHT="carousel-item-right",CLASS_NAME_LEFT="carousel-item-left",CLASS_NAME_NEXT="carousel-item-next",CLASS_NAME_PREV="carousel-item-prev",CLASS_NAME_POINTER_EVENT="pointer-event",SELECTOR_ACTIVE=".active",SELECTOR_ACTIVE_ITEM=".active.carousel-item",SELECTOR_ITEM=".carousel-item",SELECTOR_ITEM_IMG=".carousel-item img",SELECTOR_NEXT_PREV=".carousel-item-next, .carousel-item-prev",SELECTOR_INDICATORS=".carousel-indicators",SELECTOR_DATA_SLIDE="[data-slide], [data-slide-to]",SELECTOR_DATA_RIDE='[data-ride="carousel"]',PointerType={TOUCH:"touch",PEN:"pen"},Carousel=function(){function t(t,e){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(e),this._element=t,this._indicatorsElement=SelectorEngine.findOne(SELECTOR_INDICATORS,this._element),this._touchSupported="ontouchstart"in document.documentElement||navigator.maxTouchPoints>0,this._pointerEvent=Boolean(window.PointerEvent),this._addEventListeners(),Data.setData(t,DATA_KEY$2,this)}var e=t.prototype;return e.next=function(){this._isSliding||this._slide(DIRECTION_NEXT)},e.nextWhenVisible=function(){!document.hidden&&isVisible(this._element)&&this.next()},e.prev=function(){this._isSliding||this._slide(DIRECTION_PREV)},e.pause=function(t){t||(this._isPaused=!0),SelectorEngine.findOne(SELECTOR_NEXT_PREV,this._element)&&(triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},e.cycle=function(t){t||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config&&this._config.interval&&!this._isPaused&&(this._updateInterval(),this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},e.to=function(t){var e=this;this._activeElement=SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM,this._element);var n=this._getItemIndex(this._activeElement);if(!(t>this._items.length-1||t<0))if(this._isSliding)EventHandler.one(this._element,EVENT_SLID,(function(){return e.to(t)}));else{if(n===t)return this.pause(),void this.cycle();var i=t>n?DIRECTION_NEXT:DIRECTION_PREV;this._slide(i,this._items[t])}},e.dispose=function(){EventHandler.off(this._element,EVENT_KEY$2),Data.removeData(this._element,DATA_KEY$2),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},e._getConfig=function(t){return t=_extends({},Default,t),typeCheckConfig(NAME$2,t,DefaultType),t},e._handleSwipe=function(){var t=Math.abs(this.touchDeltaX);if(!(t<=SWIPE_THRESHOLD)){var e=t/this.touchDeltaX;this.touchDeltaX=0,e>0&&this.prev(),e<0&&this.next()}},e._addEventListeners=function(){var t=this;this._config.keyboard&&EventHandler.on(this._element,EVENT_KEYDOWN,(function(e){return t._keydown(e)})),"hover"===this._config.pause&&(EventHandler.on(this._element,EVENT_MOUSEENTER,(function(e){return t.pause(e)})),EventHandler.on(this._element,EVENT_MOUSELEAVE,(function(e){return t.cycle(e)}))),this._config.touch&&this._touchSupported&&this._addTouchEventListeners()},e._addTouchEventListeners=function(){var t=this,e=function(e){t._pointerEvent&&PointerType[e.pointerType.toUpperCase()]?t.touchStartX=e.clientX:t._pointerEvent||(t.touchStartX=e.touches[0].clientX)},n=function(e){t._pointerEvent&&PointerType[e.pointerType.toUpperCase()]&&(t.touchDeltaX=e.clientX-t.touchStartX),t._handleSwipe(),"hover"===t._config.pause&&(t.pause(),t.touchTimeout&&clearTimeout(t.touchTimeout),t.touchTimeout=setTimeout((function(e){return t.cycle(e)}),TOUCHEVENT_COMPAT_WAIT+t._config.interval))};SelectorEngine.find(SELECTOR_ITEM_IMG,this._element).forEach((function(t){EventHandler.on(t,EVENT_DRAG_START,(function(t){return t.preventDefault()}))})),this._pointerEvent?(EventHandler.on(this._element,EVENT_POINTERDOWN,(function(t){return e(t)})),EventHandler.on(this._element,EVENT_POINTERUP,(function(t){return n(t)})),this._element.classList.add(CLASS_NAME_POINTER_EVENT)):(EventHandler.on(this._element,EVENT_TOUCHSTART,(function(t){return e(t)})),EventHandler.on(this._element,EVENT_TOUCHMOVE,(function(e){return function(e){e.touches&&e.touches.length>1?t.touchDeltaX=0:t.touchDeltaX=e.touches[0].clientX-t.touchStartX}(e)})),EventHandler.on(this._element,EVENT_TOUCHEND,(function(t){return n(t)})))},e._keydown=function(t){if(!/input|textarea/i.test(t.target.tagName))switch(t.key){case ARROW_LEFT_KEY:t.preventDefault(),this.prev();break;case ARROW_RIGHT_KEY:t.preventDefault(),this.next()}},e._getItemIndex=function(t){return this._items=t&&t.parentNode?SelectorEngine.find(SELECTOR_ITEM,t.parentNode):[],this._items.indexOf(t)},e._getItemByDirection=function(t,e){var n=t===DIRECTION_NEXT,i=t===DIRECTION_PREV,o=this._getItemIndex(e),r=this._items.length-1;if((i&&0===o||n&&o===r)&&!this._config.wrap)return e;var a=(o+(t===DIRECTION_PREV?-1:1))%this._items.length;return-1===a?this._items[this._items.length-1]:this._items[a]},e._triggerSlideEvent=function(t,e){var n=this._getItemIndex(t),i=this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM,this._element));return EventHandler.trigger(this._element,EVENT_SLIDE,{relatedTarget:t,direction:e,from:i,to:n})},e._setActiveIndicatorElement=function(t){if(this._indicatorsElement){for(var e=SelectorEngine.find(SELECTOR_ACTIVE,this._indicatorsElement),n=0;n<e.length;n++)e[n].classList.remove(CLASS_NAME_ACTIVE$1);var i=this._indicatorsElement.children[this._getItemIndex(t)];i&&i.classList.add(CLASS_NAME_ACTIVE$1)}},e._updateInterval=function(){var t=this._activeElement||SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM,this._element);if(t){var e=parseInt(t.getAttribute("data-interval"),10);e?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=e):this._config.interval=this._config.defaultInterval||this._config.interval}},e._slide=function(t,e){var n,i,o,r=this,a=SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM,this._element),s=this._getItemIndex(a),l=e||a&&this._getItemByDirection(t,a),E=this._getItemIndex(l),_=Boolean(this._interval);if(t===DIRECTION_NEXT?(n=CLASS_NAME_LEFT,i=CLASS_NAME_NEXT,o=DIRECTION_LEFT):(n=CLASS_NAME_RIGHT,i=CLASS_NAME_PREV,o=DIRECTION_RIGHT),l&&l.classList.contains(CLASS_NAME_ACTIVE$1))this._isSliding=!1;else if(!this._triggerSlideEvent(l,o).defaultPrevented&&a&&l){if(this._isSliding=!0,_&&this.pause(),this._setActiveIndicatorElement(l),this._activeElement=l,this._element.classList.contains(CLASS_NAME_SLIDE)){l.classList.add(i),reflow(l),a.classList.add(n),l.classList.add(n);var c=getTransitionDurationFromElement(a);EventHandler.one(a,TRANSITION_END,(function(){l.classList.remove(n,i),l.classList.add(CLASS_NAME_ACTIVE$1),a.classList.remove(CLASS_NAME_ACTIVE$1,i,n),r._isSliding=!1,setTimeout((function(){EventHandler.trigger(r._element,EVENT_SLID,{relatedTarget:l,direction:o,from:s,to:E})}),0)})),emulateTransitionEnd(a,c)}else a.classList.remove(CLASS_NAME_ACTIVE$1),l.classList.add(CLASS_NAME_ACTIVE$1),this._isSliding=!1,EventHandler.trigger(this._element,EVENT_SLID,{relatedTarget:l,direction:o,from:s,to:E});_&&this.cycle()}},t.carouselInterface=function(e,n){var i=Data.getData(e,DATA_KEY$2),o=_extends({},Default,Manipulator.getDataAttributes(e));"object"==typeof n&&(o=_extends({},o,n));var r="string"==typeof n?n:o.slide;if(i||(i=new t(e,o)),"number"==typeof n)i.to(n);else if("string"==typeof r){if(void 0===i[r])throw new TypeError('No method named "'+r+'"');i[r]()}else o.interval&&o.ride&&(i.pause(),i.cycle())},t.jQueryInterface=function(e){return this.each((function(){t.carouselInterface(this,e)}))},t.dataApiClickHandler=function(e){var n=getElementFromSelector(this);if(n&&n.classList.contains(CLASS_NAME_CAROUSEL)){var i=_extends({},Manipulator.getDataAttributes(n),Manipulator.getDataAttributes(this)),o=this.getAttribute("data-slide-to");o&&(i.interval=!1),t.carouselInterface(n,i),o&&Data.getData(n,DATA_KEY$2).to(o),e.preventDefault()}},t.getInstance=function(t){return Data.getData(t,DATA_KEY$2)},_createClass(t,null,[{key:"VERSION",get:function(){return VERSION$2}},{key:"Default",get:function(){return Default}}]),t}();EventHandler.on(document,EVENT_CLICK_DATA_API$2,SELECTOR_DATA_SLIDE,Carousel.dataApiClickHandler),EventHandler.on(window,EVENT_LOAD_DATA_API,(function(){for(var t=SelectorEngine.find(SELECTOR_DATA_RIDE),e=0,n=t.length;e<n;e++)Carousel.carouselInterface(t[e],Data.getData(t[e],DATA_KEY$2))})),onDOMContentLoaded((function(){var t=getjQuery();if(t){var e=t.fn[NAME$2];t.fn[NAME$2]=Carousel.jQueryInterface,t.fn[NAME$2].Constructor=Carousel,t.fn[NAME$2].noConflict=function(){return t.fn[NAME$2]=e,Carousel.jQueryInterface}}}));var NAME$3="collapse",VERSION$3="5.0.0-alpha3",DATA_KEY$3="bs.collapse",EVENT_KEY$3="."+DATA_KEY$3,DATA_API_KEY$3=".data-api",Default$1={toggle:!0,parent:""},DefaultType$1={toggle:"boolean",parent:"(string|element)"},EVENT_SHOW="show"+EVENT_KEY$3,EVENT_SHOWN="shown"+EVENT_KEY$3,EVENT_HIDE="hide"+EVENT_KEY$3,EVENT_HIDDEN="hidden"+EVENT_KEY$3,EVENT_CLICK_DATA_API$3="click"+EVENT_KEY$3+DATA_API_KEY$3,CLASS_NAME_SHOW="show",CLASS_NAME_COLLAPSE="collapse",CLASS_NAME_COLLAPSING="collapsing",CLASS_NAME_COLLAPSED="collapsed",WIDTH="width",HEIGHT="height",SELECTOR_ACTIVES=".show, .collapsing",SELECTOR_DATA_TOGGLE$1='[data-toggle="collapse"]',Collapse=function(){function t(t,e){this._isTransitioning=!1,this._element=t,this._config=this._getConfig(e),this._triggerArray=SelectorEngine.find(SELECTOR_DATA_TOGGLE$1+'[href="#'+t.id+'"],'+SELECTOR_DATA_TOGGLE$1+'[data-target="#'+t.id+'"]');for(var n=SelectorEngine.find(SELECTOR_DATA_TOGGLE$1),i=0,o=n.length;i<o;i++){var r=n[i],a=getSelectorFromElement(r),s=SelectorEngine.find(a).filter((function(e){return e===t}));null!==a&&s.length&&(this._selector=a,this._triggerArray.push(r))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle(),Data.setData(t,DATA_KEY$3,this)}var e=t.prototype;return e.toggle=function(){this._element.classList.contains(CLASS_NAME_SHOW)?this.hide():this.show()},e.show=function(){var e=this;if(!this._isTransitioning&&!this._element.classList.contains(CLASS_NAME_SHOW)){var n,i;this._parent&&0===(n=SelectorEngine.find(SELECTOR_ACTIVES,this._parent).filter((function(t){return"string"==typeof e._config.parent?t.getAttribute("data-parent")===e._config.parent:t.classList.contains(CLASS_NAME_COLLAPSE)}))).length&&(n=null);var o=SelectorEngine.findOne(this._selector);if(n){var r=n.filter((function(t){return o!==t}));if((i=r[0]?Data.getData(r[0],DATA_KEY$3):null)&&i._isTransitioning)return}if(!EventHandler.trigger(this._element,EVENT_SHOW).defaultPrevented){n&&n.forEach((function(e){o!==e&&t.collapseInterface(e,"hide"),i||Data.setData(e,DATA_KEY$3,null)}));var a=this._getDimension();this._element.classList.remove(CLASS_NAME_COLLAPSE),this._element.classList.add(CLASS_NAME_COLLAPSING),this._element.style[a]=0,this._triggerArray.length&&this._triggerArray.forEach((function(t){t.classList.remove(CLASS_NAME_COLLAPSED),t.setAttribute("aria-expanded",!0)})),this.setTransitioning(!0);var s="scroll"+(a[0].toUpperCase()+a.slice(1)),l=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,(function(){e._element.classList.remove(CLASS_NAME_COLLAPSING),e._element.classList.add(CLASS_NAME_COLLAPSE,CLASS_NAME_SHOW),e._element.style[a]="",e.setTransitioning(!1),EventHandler.trigger(e._element,EVENT_SHOWN)})),emulateTransitionEnd(this._element,l),this._element.style[a]=this._element[s]+"px"}}},e.hide=function(){var t=this;if(!this._isTransitioning&&this._element.classList.contains(CLASS_NAME_SHOW)&&!EventHandler.trigger(this._element,EVENT_HIDE).defaultPrevented){var e=this._getDimension();this._element.style[e]=this._element.getBoundingClientRect()[e]+"px",reflow(this._element),this._element.classList.add(CLASS_NAME_COLLAPSING),this._element.classList.remove(CLASS_NAME_COLLAPSE,CLASS_NAME_SHOW);var n=this._triggerArray.length;if(n>0)for(var i=0;i<n;i++){var o=this._triggerArray[i],r=getElementFromSelector(o);r&&!r.classList.contains(CLASS_NAME_SHOW)&&(o.classList.add(CLASS_NAME_COLLAPSED),o.setAttribute("aria-expanded",!1))}this.setTransitioning(!0);this._element.style[e]="";var a=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,(function(){t.setTransitioning(!1),t._element.classList.remove(CLASS_NAME_COLLAPSING),t._element.classList.add(CLASS_NAME_COLLAPSE),EventHandler.trigger(t._element,EVENT_HIDDEN)})),emulateTransitionEnd(this._element,a)}},e.setTransitioning=function(t){this._isTransitioning=t},e.dispose=function(){Data.removeData(this._element,DATA_KEY$3),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},e._getConfig=function(t){return(t=_extends({},Default$1,t)).toggle=Boolean(t.toggle),typeCheckConfig(NAME$3,t,DefaultType$1),t},e._getDimension=function(){return this._element.classList.contains(WIDTH)?WIDTH:HEIGHT},e._getParent=function(){var t=this,e=this._config.parent;isElement(e)?void 0===e.jquery&&void 0===e[0]||(e=e[0]):e=SelectorEngine.findOne(e);var n=SELECTOR_DATA_TOGGLE$1+'[data-parent="'+e+'"]';return SelectorEngine.find(n,e).forEach((function(e){var n=getElementFromSelector(e);t._addAriaAndCollapsedClass(n,[e])})),e},e._addAriaAndCollapsedClass=function(t,e){if(t&&e.length){var n=t.classList.contains(CLASS_NAME_SHOW);e.forEach((function(t){n?t.classList.remove(CLASS_NAME_COLLAPSED):t.classList.add(CLASS_NAME_COLLAPSED),t.setAttribute("aria-expanded",n)}))}},t.collapseInterface=function(e,n){var i=Data.getData(e,DATA_KEY$3),o=_extends({},Default$1,Manipulator.getDataAttributes(e),"object"==typeof n&&n?n:{});if(!i&&o.toggle&&"string"==typeof n&&/show|hide/.test(n)&&(o.toggle=!1),i||(i=new t(e,o)),"string"==typeof n){if(void 0===i[n])throw new TypeError('No method named "'+n+'"');i[n]()}},t.jQueryInterface=function(e){return this.each((function(){t.collapseInterface(this,e)}))},t.getInstance=function(t){return Data.getData(t,DATA_KEY$3)},_createClass(t,null,[{key:"VERSION",get:function(){return VERSION$3}},{key:"Default",get:function(){return Default$1}}]),t}();EventHandler.on(document,EVENT_CLICK_DATA_API$3,SELECTOR_DATA_TOGGLE$1,(function(t){"A"===t.target.tagName&&t.preventDefault();var e=Manipulator.getDataAttributes(this),n=getSelectorFromElement(this);SelectorEngine.find(n).forEach((function(t){var n,i=Data.getData(t,DATA_KEY$3);i?(null===i._parent&&"string"==typeof e.parent&&(i._config.parent=e.parent,i._parent=i._getParent()),n="toggle"):n=e,Collapse.collapseInterface(t,n)}))})),onDOMContentLoaded((function(){var t=getjQuery();if(t){var e=t.fn[NAME$3];t.fn[NAME$3]=Collapse.jQueryInterface,t.fn[NAME$3].Constructor=Collapse,t.fn[NAME$3].noConflict=function(){return t.fn[NAME$3]=e,Collapse.jQueryInterface}}}));var NAME$4="dropdown",VERSION$4="5.0.0-alpha3",DATA_KEY$4="bs.dropdown",EVENT_KEY$4="."+DATA_KEY$4,DATA_API_KEY$4=".data-api",ESCAPE_KEY="Escape",SPACE_KEY="Space",TAB_KEY="Tab",ARROW_UP_KEY="ArrowUp",ARROW_DOWN_KEY="ArrowDown",RIGHT_MOUSE_BUTTON=2,REGEXP_KEYDOWN=new RegExp(ARROW_UP_KEY+"|"+ARROW_DOWN_KEY+"|"+ESCAPE_KEY),EVENT_HIDE$1="hide"+EVENT_KEY$4,EVENT_HIDDEN$1="hidden"+EVENT_KEY$4,EVENT_SHOW$1="show"+EVENT_KEY$4,EVENT_SHOWN$1="shown"+EVENT_KEY$4,EVENT_CLICK="click"+EVENT_KEY$4,EVENT_CLICK_DATA_API$4="click"+EVENT_KEY$4+DATA_API_KEY$4,EVENT_KEYDOWN_DATA_API="keydown"+EVENT_KEY$4+DATA_API_KEY$4,EVENT_KEYUP_DATA_API="keyup"+EVENT_KEY$4+DATA_API_KEY$4,CLASS_NAME_DISABLED="disabled",CLASS_NAME_SHOW$1="show",CLASS_NAME_DROPUP="dropup",CLASS_NAME_DROPRIGHT="dropright",CLASS_NAME_DROPLEFT="dropleft",CLASS_NAME_MENURIGHT="dropdown-menu-right",CLASS_NAME_NAVBAR="navbar",CLASS_NAME_POSITION_STATIC="position-static",SELECTOR_DATA_TOGGLE$2='[data-toggle="dropdown"]',SELECTOR_FORM_CHILD=".dropdown form",SELECTOR_MENU=".dropdown-menu",SELECTOR_NAVBAR_NAV=".navbar-nav",SELECTOR_VISIBLE_ITEMS=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",PLACEMENT_TOP="top-start",PLACEMENT_TOPEND="top-end",PLACEMENT_BOTTOM="bottom-start",PLACEMENT_BOTTOMEND="bottom-end",PLACEMENT_RIGHT="right-start",PLACEMENT_LEFT="left-start",Default$2={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},DefaultType$2={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},Dropdown=function(){function t(t,e){this._element=t,this._popper=null,this._config=this._getConfig(e),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners(),Data.setData(t,DATA_KEY$4,this)}var e=t.prototype;return e.toggle=function(){if(!this._element.disabled&&!this._element.classList.contains(CLASS_NAME_DISABLED)){var e=this._element.classList.contains(CLASS_NAME_SHOW$1);t.clearMenus(),e||this.show()}},e.show=function(){if(!(this._element.disabled||this._element.classList.contains(CLASS_NAME_DISABLED)||this._menu.classList.contains(CLASS_NAME_SHOW$1))){var e=t.getParentFromElement(this._element),n={relatedTarget:this._element};if(!EventHandler.trigger(this._element,EVENT_SHOW$1,n).defaultPrevented){if(!this._inNavbar){if(void 0===Popper)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org)");var i=this._element;"parent"===this._config.reference?i=e:isElement(this._config.reference)&&(i=this._config.reference,void 0!==this._config.reference.jquery&&(i=this._config.reference[0])),"scrollParent"!==this._config.boundary&&e.classList.add(CLASS_NAME_POSITION_STATIC),this._popper=new Popper(i,this._menu,this._getPopperConfig())}var o;if("ontouchstart"in document.documentElement&&!e.closest(SELECTOR_NAVBAR_NAV))(o=[]).concat.apply(o,document.body.children).forEach((function(t){return EventHandler.on(t,"mouseover",null,noop())}));this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.toggle(CLASS_NAME_SHOW$1),this._element.classList.toggle(CLASS_NAME_SHOW$1),EventHandler.trigger(e,EVENT_SHOWN$1,n)}}},e.hide=function(){if(!this._element.disabled&&!this._element.classList.contains(CLASS_NAME_DISABLED)&&this._menu.classList.contains(CLASS_NAME_SHOW$1)){var e=t.getParentFromElement(this._element),n={relatedTarget:this._element};EventHandler.trigger(e,EVENT_HIDE$1,n).defaultPrevented||(this._popper&&this._popper.destroy(),this._menu.classList.toggle(CLASS_NAME_SHOW$1),this._element.classList.toggle(CLASS_NAME_SHOW$1),EventHandler.trigger(e,EVENT_HIDDEN$1,n))}},e.dispose=function(){Data.removeData(this._element,DATA_KEY$4),EventHandler.off(this._element,EVENT_KEY$4),this._element=null,this._menu=null,this._popper&&(this._popper.destroy(),this._popper=null)},e.update=function(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.scheduleUpdate()},e._addEventListeners=function(){var t=this;EventHandler.on(this._element,EVENT_CLICK,(function(e){e.preventDefault(),e.stopPropagation(),t.toggle()}))},e._getConfig=function(t){return t=_extends({},this.constructor.Default,Manipulator.getDataAttributes(this._element),t),typeCheckConfig(NAME$4,t,this.constructor.DefaultType),t},e._getMenuElement=function(){return SelectorEngine.next(this._element,SELECTOR_MENU)[0]},e._getPlacement=function(){var t=this._element.parentNode,e=PLACEMENT_BOTTOM;return t.classList.contains(CLASS_NAME_DROPUP)?e=this._menu.classList.contains(CLASS_NAME_MENURIGHT)?PLACEMENT_TOPEND:PLACEMENT_TOP:t.classList.contains(CLASS_NAME_DROPRIGHT)?e=PLACEMENT_RIGHT:t.classList.contains(CLASS_NAME_DROPLEFT)?e=PLACEMENT_LEFT:this._menu.classList.contains(CLASS_NAME_MENURIGHT)&&(e=PLACEMENT_BOTTOMEND),e},e._detectNavbar=function(){return Boolean(this._element.closest("."+CLASS_NAME_NAVBAR))},e._getOffset=function(){var t=this,e={};return"function"==typeof this._config.offset?e.fn=function(e){return e.offsets=_extends({},e.offsets,t._config.offset(e.offsets,t._element)||{}),e}:e.offset=this._config.offset,e},e._getPopperConfig=function(){var t={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(t.modifiers.applyStyle={enabled:!1}),_extends({},t,this._config.popperConfig)},t.dropdownInterface=function(e,n){var i=Data.getData(e,DATA_KEY$4);if(i||(i=new t(e,"object"==typeof n?n:null)),"string"==typeof n){if(void 0===i[n])throw new TypeError('No method named "'+n+'"');i[n]()}},t.jQueryInterface=function(e){return this.each((function(){t.dropdownInterface(this,e)}))},t.clearMenus=function(e){if(!e||e.button!==RIGHT_MOUSE_BUTTON&&("keyup"!==e.type||e.key===TAB_KEY))for(var n=SelectorEngine.find(SELECTOR_DATA_TOGGLE$2),i=0,o=n.length;i<o;i++){var r=t.getParentFromElement(n[i]),a=Data.getData(n[i],DATA_KEY$4),s={relatedTarget:n[i]};if(e&&"click"===e.type&&(s.clickEvent=e),a){var l=a._menu;if(n[i].classList.contains(CLASS_NAME_SHOW$1))if(!(e&&("click"===e.type&&/input|textarea/i.test(e.target.tagName)||"keyup"===e.type&&e.key===TAB_KEY)&&l.contains(e.target)))if(!EventHandler.trigger(r,EVENT_HIDE$1,s).defaultPrevented){var E;if("ontouchstart"in document.documentElement)(E=[]).concat.apply(E,document.body.children).forEach((function(t){return EventHandler.off(t,"mouseover",null,noop())}));n[i].setAttribute("aria-expanded","false"),a._popper&&a._popper.destroy(),l.classList.remove(CLASS_NAME_SHOW$1),n[i].classList.remove(CLASS_NAME_SHOW$1),EventHandler.trigger(r,EVENT_HIDDEN$1,s)}}}},t.getParentFromElement=function(t){return getElementFromSelector(t)||t.parentNode},t.dataApiKeydownHandler=function(e){if(!(/input|textarea/i.test(e.target.tagName)?e.key===SPACE_KEY||e.key!==ESCAPE_KEY&&(e.key!==ARROW_DOWN_KEY&&e.key!==ARROW_UP_KEY||e.target.closest(SELECTOR_MENU)):!REGEXP_KEYDOWN.test(e.key))&&(e.preventDefault(),e.stopPropagation(),!this.disabled&&!this.classList.contains(CLASS_NAME_DISABLED))){var n=t.getParentFromElement(this),i=this.classList.contains(CLASS_NAME_SHOW$1);if(e.key===ESCAPE_KEY)return(this.matches(SELECTOR_DATA_TOGGLE$2)?this:SelectorEngine.prev(this,SELECTOR_DATA_TOGGLE$2)[0]).focus(),void t.clearMenus();if(i&&e.key!==SPACE_KEY){var o=SelectorEngine.find(SELECTOR_VISIBLE_ITEMS,n).filter(isVisible);if(o.length){var r=o.indexOf(e.target);e.key===ARROW_UP_KEY&&r>0&&r--,e.key===ARROW_DOWN_KEY&&r<o.length-1&&r++,o[r=-1===r?0:r].focus()}}else t.clearMenus()}},t.getInstance=function(t){return Data.getData(t,DATA_KEY$4)},_createClass(t,null,[{key:"VERSION",get:function(){return VERSION$4}},{key:"Default",get:function(){return Default$2}},{key:"DefaultType",get:function(){return DefaultType$2}}]),t}();EventHandler.on(document,EVENT_KEYDOWN_DATA_API,SELECTOR_DATA_TOGGLE$2,Dropdown.dataApiKeydownHandler),EventHandler.on(document,EVENT_KEYDOWN_DATA_API,SELECTOR_MENU,Dropdown.dataApiKeydownHandler),EventHandler.on(document,EVENT_CLICK_DATA_API$4,Dropdown.clearMenus),EventHandler.on(document,EVENT_KEYUP_DATA_API,Dropdown.clearMenus),EventHandler.on(document,EVENT_CLICK_DATA_API$4,SELECTOR_DATA_TOGGLE$2,(function(t){t.preventDefault(),t.stopPropagation(),Dropdown.dropdownInterface(this,"toggle")})),EventHandler.on(document,EVENT_CLICK_DATA_API$4,SELECTOR_FORM_CHILD,(function(t){return t.stopPropagation()})),onDOMContentLoaded((function(){var t=getjQuery();if(t){var e=t.fn[NAME$4];t.fn[NAME$4]=Dropdown.jQueryInterface,t.fn[NAME$4].Constructor=Dropdown,t.fn[NAME$4].noConflict=function(){return t.fn[NAME$4]=e,Dropdown.jQueryInterface}}}));var NAME$5="modal",VERSION$5="5.0.0-alpha3",DATA_KEY$5="bs.modal",EVENT_KEY$5="."+DATA_KEY$5,DATA_API_KEY$5=".data-api",ESCAPE_KEY$1="Escape",Default$3={backdrop:!0,keyboard:!0,focus:!0,show:!0},DefaultType$3={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},EVENT_HIDE$2="hide"+EVENT_KEY$5,EVENT_HIDE_PREVENTED="hidePrevented"+EVENT_KEY$5,EVENT_HIDDEN$2="hidden"+EVENT_KEY$5,EVENT_SHOW$2="show"+EVENT_KEY$5,EVENT_SHOWN$2="shown"+EVENT_KEY$5,EVENT_FOCUSIN="focusin"+EVENT_KEY$5,EVENT_RESIZE="resize"+EVENT_KEY$5,EVENT_CLICK_DISMISS="click.dismiss"+EVENT_KEY$5,EVENT_KEYDOWN_DISMISS="keydown.dismiss"+EVENT_KEY$5,EVENT_MOUSEUP_DISMISS="mouseup.dismiss"+EVENT_KEY$5,EVENT_MOUSEDOWN_DISMISS="mousedown.dismiss"+EVENT_KEY$5,EVENT_CLICK_DATA_API$5="click"+EVENT_KEY$5+DATA_API_KEY$5,CLASS_NAME_SCROLLBAR_MEASURER="modal-scrollbar-measure",CLASS_NAME_BACKDROP="modal-backdrop",CLASS_NAME_OPEN="modal-open",CLASS_NAME_FADE="fade",CLASS_NAME_SHOW$2="show",CLASS_NAME_STATIC="modal-static",SELECTOR_DIALOG=".modal-dialog",SELECTOR_MODAL_BODY=".modal-body",SELECTOR_DATA_TOGGLE$3='[data-toggle="modal"]',SELECTOR_DATA_DISMISS='[data-dismiss="modal"]',SELECTOR_FIXED_CONTENT=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",SELECTOR_STICKY_CONTENT=".sticky-top",Modal=function(){function t(t,e){this._config=this._getConfig(e),this._element=t,this._dialog=SelectorEngine.findOne(SELECTOR_DIALOG,t),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0,Data.setData(t,DATA_KEY$5,this)}var e=t.prototype;return e.toggle=function(t){return this._isShown?this.hide():this.show(t)},e.show=function(t){var e=this;if(!this._isShown&&!this._isTransitioning){this._element.classList.contains(CLASS_NAME_FADE)&&(this._isTransitioning=!0);var n=EventHandler.trigger(this._element,EVENT_SHOW$2,{relatedTarget:t});this._isShown||n.defaultPrevented||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),EventHandler.on(this._element,EVENT_CLICK_DISMISS,SELECTOR_DATA_DISMISS,(function(t){return e.hide(t)})),EventHandler.on(this._dialog,EVENT_MOUSEDOWN_DISMISS,(function(){EventHandler.one(e._element,EVENT_MOUSEUP_DISMISS,(function(t){t.target===e._element&&(e._ignoreBackdropClick=!0)}))})),this._showBackdrop((function(){return e._showElement(t)})))}},e.hide=function(t){var e=this;if((t&&t.preventDefault(),this._isShown&&!this._isTransitioning)&&!EventHandler.trigger(this._element,EVENT_HIDE$2).defaultPrevented){this._isShown=!1;var n=this._element.classList.contains(CLASS_NAME_FADE);if(n&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),EventHandler.off(document,EVENT_FOCUSIN),this._element.classList.remove(CLASS_NAME_SHOW$2),EventHandler.off(this._element,EVENT_CLICK_DISMISS),EventHandler.off(this._dialog,EVENT_MOUSEDOWN_DISMISS),n){var i=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,(function(t){return e._hideModal(t)})),emulateTransitionEnd(this._element,i)}else this._hideModal()}},e.dispose=function(){[window,this._element,this._dialog].forEach((function(t){return EventHandler.off(t,EVENT_KEY$5)})),EventHandler.off(document,EVENT_FOCUSIN),Data.removeData(this._element,DATA_KEY$5),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},e.handleUpdate=function(){this._adjustDialog()},e._getConfig=function(t){return t=_extends({},Default$3,t),typeCheckConfig(NAME$5,t,DefaultType$3),t},e._showElement=function(t){var e=this,n=this._element.classList.contains(CLASS_NAME_FADE),i=SelectorEngine.findOne(SELECTOR_MODAL_BODY,this._dialog);this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0,i&&(i.scrollTop=0),n&&reflow(this._element),this._element.classList.add(CLASS_NAME_SHOW$2),this._config.focus&&this._enforceFocus();var o=function(){e._config.focus&&e._element.focus(),e._isTransitioning=!1,EventHandler.trigger(e._element,EVENT_SHOWN$2,{relatedTarget:t})};if(n){var r=getTransitionDurationFromElement(this._dialog);EventHandler.one(this._dialog,TRANSITION_END,o),emulateTransitionEnd(this._dialog,r)}else o()},e._enforceFocus=function(){var t=this;EventHandler.off(document,EVENT_FOCUSIN),EventHandler.on(document,EVENT_FOCUSIN,(function(e){document===e.target||t._element===e.target||t._element.contains(e.target)||t._element.focus()}))},e._setEscapeEvent=function(){var t=this;this._isShown?EventHandler.on(this._element,EVENT_KEYDOWN_DISMISS,(function(e){t._config.keyboard&&e.key===ESCAPE_KEY$1?(e.preventDefault(),t.hide()):t._config.keyboard||e.key!==ESCAPE_KEY$1||t._triggerBackdropTransition()})):EventHandler.off(this._element,EVENT_KEYDOWN_DISMISS)},e._setResizeEvent=function(){var t=this;this._isShown?EventHandler.on(window,EVENT_RESIZE,(function(){return t._adjustDialog()})):EventHandler.off(window,EVENT_RESIZE)},e._hideModal=function(){var t=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop((function(){document.body.classList.remove(CLASS_NAME_OPEN),t._resetAdjustments(),t._resetScrollbar(),EventHandler.trigger(t._element,EVENT_HIDDEN$2)}))},e._removeBackdrop=function(){this._backdrop.parentNode.removeChild(this._backdrop),this._backdrop=null},e._showBackdrop=function(t){var e=this,n=this._element.classList.contains(CLASS_NAME_FADE)?CLASS_NAME_FADE:"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className=CLASS_NAME_BACKDROP,n&&this._backdrop.classList.add(n),document.body.appendChild(this._backdrop),EventHandler.on(this._element,EVENT_CLICK_DISMISS,(function(t){e._ignoreBackdropClick?e._ignoreBackdropClick=!1:t.target===t.currentTarget&&e._triggerBackdropTransition()})),n&&reflow(this._backdrop),this._backdrop.classList.add(CLASS_NAME_SHOW$2),!n)return void t();var i=getTransitionDurationFromElement(this._backdrop);EventHandler.one(this._backdrop,TRANSITION_END,t),emulateTransitionEnd(this._backdrop,i)}else if(!this._isShown&&this._backdrop){this._backdrop.classList.remove(CLASS_NAME_SHOW$2);var o=function(){e._removeBackdrop(),t()};if(this._element.classList.contains(CLASS_NAME_FADE)){var r=getTransitionDurationFromElement(this._backdrop);EventHandler.one(this._backdrop,TRANSITION_END,o),emulateTransitionEnd(this._backdrop,r)}else o()}else t()},e._triggerBackdropTransition=function(){var t=this;if("static"===this._config.backdrop){if(EventHandler.trigger(this._element,EVENT_HIDE_PREVENTED).defaultPrevented)return;var e=this._element.scrollHeight>document.documentElement.clientHeight;e||(this._element.style.overflowY="hidden"),this._element.classList.add(CLASS_NAME_STATIC);var n=getTransitionDurationFromElement(this._dialog);EventHandler.off(this._element,TRANSITION_END),EventHandler.one(this._element,TRANSITION_END,(function(){t._element.classList.remove(CLASS_NAME_STATIC),e||(EventHandler.one(t._element,TRANSITION_END,(function(){t._element.style.overflowY=""})),emulateTransitionEnd(t._element,n))})),emulateTransitionEnd(this._element,n),this._element.focus()}else this.hide()},e._adjustDialog=function(){var t=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&t&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!t&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},e._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},e._checkScrollbar=function(){var t=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(t.left+t.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},e._setScrollbar=function(){var t=this;if(this._isBodyOverflowing){SelectorEngine.find(SELECTOR_FIXED_CONTENT).forEach((function(e){var n=e.style.paddingRight,i=window.getComputedStyle(e)["padding-right"];Manipulator.setDataAttribute(e,"padding-right",n),e.style.paddingRight=parseFloat(i)+t._scrollbarWidth+"px"})),SelectorEngine.find(SELECTOR_STICKY_CONTENT).forEach((function(e){var n=e.style.marginRight,i=window.getComputedStyle(e)["margin-right"];Manipulator.setDataAttribute(e,"margin-right",n),e.style.marginRight=parseFloat(i)-t._scrollbarWidth+"px"}));var e=document.body.style.paddingRight,n=window.getComputedStyle(document.body)["padding-right"];Manipulator.setDataAttribute(document.body,"padding-right",e),document.body.style.paddingRight=parseFloat(n)+this._scrollbarWidth+"px"}document.body.classList.add(CLASS_NAME_OPEN)},e._resetScrollbar=function(){SelectorEngine.find(SELECTOR_FIXED_CONTENT).forEach((function(t){var e=Manipulator.getDataAttribute(t,"padding-right");void 0!==e&&(Manipulator.removeDataAttribute(t,"padding-right"),t.style.paddingRight=e)})),SelectorEngine.find(""+SELECTOR_STICKY_CONTENT).forEach((function(t){var e=Manipulator.getDataAttribute(t,"margin-right");void 0!==e&&(Manipulator.removeDataAttribute(t,"margin-right"),t.style.marginRight=e)}));var t=Manipulator.getDataAttribute(document.body,"padding-right");void 0===t?document.body.style.paddingRight="":(Manipulator.removeDataAttribute(document.body,"padding-right"),document.body.style.paddingRight=t)},e._getScrollbarWidth=function(){var t=document.createElement("div");t.className=CLASS_NAME_SCROLLBAR_MEASURER,document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e},t.jQueryInterface=function(e,n){return this.each((function(){var i=Data.getData(this,DATA_KEY$5),o=_extends({},Default$3,Manipulator.getDataAttributes(this),"object"==typeof e&&e?e:{});if(i||(i=new t(this,o)),"string"==typeof e){if(void 0===i[e])throw new TypeError('No method named "'+e+'"');i[e](n)}else o.show&&i.show(n)}))},t.getInstance=function(t){return Data.getData(t,DATA_KEY$5)},_createClass(t,null,[{key:"VERSION",get:function(){return VERSION$5}},{key:"Default",get:function(){return Default$3}}]),t}();EventHandler.on(document,EVENT_CLICK_DATA_API$5,SELECTOR_DATA_TOGGLE$3,(function(t){var e=this,n=getElementFromSelector(this);"A"!==this.tagName&&"AREA"!==this.tagName||t.preventDefault(),EventHandler.one(n,EVENT_SHOW$2,(function(t){t.defaultPrevented||EventHandler.one(n,EVENT_HIDDEN$2,(function(){isVisible(e)&&e.focus()}))}));var i=Data.getData(n,DATA_KEY$5);if(!i){var o=_extends({},Manipulator.getDataAttributes(n),Manipulator.getDataAttributes(this));i=new Modal(n,o)}i.show(this)})),onDOMContentLoaded((function(){var t=getjQuery();if(t){var e=t.fn[NAME$5];t.fn[NAME$5]=Modal.jQueryInterface,t.fn[NAME$5].Constructor=Modal,t.fn[NAME$5].noConflict=function(){return t.fn[NAME$5]=e,Modal.jQueryInterface}}}));var uriAttrs=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],ARIA_ATTRIBUTE_PATTERN=/^aria-[\w-]*$/i,SAFE_URL_PATTERN=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi,DATA_URL_PATTERN=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i,allowedAttribute=function(t,e){var n=t.nodeName.toLowerCase();if(-1!==e.indexOf(n))return-1===uriAttrs.indexOf(n)||Boolean(t.nodeValue.match(SAFE_URL_PATTERN)||t.nodeValue.match(DATA_URL_PATTERN));for(var i=e.filter((function(t){return t instanceof RegExp})),o=0,r=i.length;o<r;o++)if(n.match(i[o]))return!0;return!1},DefaultAllowlist={"*":["class","dir","id","lang","role",ARIA_ATTRIBUTE_PATTERN],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]};function sanitizeHtml(t,e,n){var i;if(!t.length)return t;if(n&&"function"==typeof n)return n(t);for(var o=(new window.DOMParser).parseFromString(t,"text/html"),r=Object.keys(e),a=(i=[]).concat.apply(i,o.body.querySelectorAll("*")),s=function(t,n){var i,o=a[t],s=o.nodeName.toLowerCase();if(-1===r.indexOf(s))return o.parentNode.removeChild(o),"continue";var l=(i=[]).concat.apply(i,o.attributes),E=[].concat(e["*"]||[],e[s]||[]);l.forEach((function(t){allowedAttribute(t,E)||o.removeAttribute(t.nodeName)}))},l=0,E=a.length;l<E;l++)s(l);return o.body.innerHTML}var NAME$6="tooltip",VERSION$6="5.0.0-alpha3",DATA_KEY$6="bs.tooltip",EVENT_KEY$6="."+DATA_KEY$6,CLASS_PREFIX="bs-tooltip",BSCLS_PREFIX_REGEX=new RegExp("(^|\\s)"+CLASS_PREFIX+"\\S+","g"),DISALLOWED_ATTRIBUTES=["sanitize","allowList","sanitizeFn"],DefaultType$4={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",allowList:"object",popperConfig:"(null|object)"},AttachmentMap={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},Default$4={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,allowList:DefaultAllowlist,popperConfig:null},Event$1={HIDE:"hide"+EVENT_KEY$6,HIDDEN:"hidden"+EVENT_KEY$6,SHOW:"show"+EVENT_KEY$6,SHOWN:"shown"+EVENT_KEY$6,INSERTED:"inserted"+EVENT_KEY$6,CLICK:"click"+EVENT_KEY$6,FOCUSIN:"focusin"+EVENT_KEY$6,FOCUSOUT:"focusout"+EVENT_KEY$6,MOUSEENTER:"mouseenter"+EVENT_KEY$6,MOUSELEAVE:"mouseleave"+EVENT_KEY$6},CLASS_NAME_FADE$1="fade",CLASS_NAME_MODAL="modal",CLASS_NAME_SHOW$3="show",HOVER_STATE_SHOW="show",HOVER_STATE_OUT="out",SELECTOR_TOOLTIP_INNER=".tooltip-inner",TRIGGER_HOVER="hover",TRIGGER_FOCUS="focus",TRIGGER_CLICK="click",TRIGGER_MANUAL="manual",Tooltip=function(){function t(t,e){if(void 0===Popper)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=t,this.config=this._getConfig(e),this.tip=null,this._setListeners(),Data.setData(t,this.constructor.DATA_KEY,this)}var e=t.prototype;return e.enable=function(){this._isEnabled=!0},e.disable=function(){this._isEnabled=!1},e.toggleEnabled=function(){this._isEnabled=!this._isEnabled},e.toggle=function(t){if(this._isEnabled)if(t){var e=this.constructor.DATA_KEY,n=Data.getData(t.delegateTarget,e);n||(n=new this.constructor(t.delegateTarget,this._getDelegateConfig()),Data.setData(t.delegateTarget,e,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)}else{if(this.getTipElement().classList.contains(CLASS_NAME_SHOW$3))return void this._leave(null,this);this._enter(null,this)}},e.dispose=function(){clearTimeout(this._timeout),Data.removeData(this.element,this.constructor.DATA_KEY),EventHandler.off(this.element,this.constructor.EVENT_KEY),EventHandler.off(this.element.closest("."+CLASS_NAME_MODAL),"hide.bs.modal",this._hideModalHandler),this.tip&&this.tip.parentNode.removeChild(this.tip),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},e.show=function(){var t=this;if("none"===this.element.style.display)throw new Error("Please use show on visible elements");if(this.isWithContent()&&this._isEnabled){var e=EventHandler.trigger(this.element,this.constructor.Event.SHOW),n=findShadowRoot(this.element),i=null===n?this.element.ownerDocument.documentElement.contains(this.element):n.contains(this.element);if(e.defaultPrevented||!i)return;var o=this.getTipElement(),r=getUID(this.constructor.NAME);o.setAttribute("id",r),this.element.setAttribute("aria-describedby",r),this.setContent(),this.config.animation&&o.classList.add(CLASS_NAME_FADE$1);var a="function"==typeof this.config.placement?this.config.placement.call(this,o,this.element):this.config.placement,s=this._getAttachment(a);this._addAttachmentClass(s);var l,E=this._getContainer();if(Data.setData(o,this.constructor.DATA_KEY,this),this.element.ownerDocument.documentElement.contains(this.tip)||E.appendChild(o),EventHandler.trigger(this.element,this.constructor.Event.INSERTED),this._popper=new Popper(this.element,o,this._getPopperConfig(s)),o.classList.add(CLASS_NAME_SHOW$3),"ontouchstart"in document.documentElement)(l=[]).concat.apply(l,document.body.children).forEach((function(t){EventHandler.on(t,"mouseover",noop())}));var _=function(){t.config.animation&&t._fixTransition();var e=t._hoverState;t._hoverState=null,EventHandler.trigger(t.element,t.constructor.Event.SHOWN),e===HOVER_STATE_OUT&&t._leave(null,t)};if(this.tip.classList.contains(CLASS_NAME_FADE$1)){var c=getTransitionDurationFromElement(this.tip);EventHandler.one(this.tip,TRANSITION_END,_),emulateTransitionEnd(this.tip,c)}else _()}},e.hide=function(){var t=this;if(this._popper){var e=this.getTipElement(),n=function(){t._hoverState!==HOVER_STATE_SHOW&&e.parentNode&&e.parentNode.removeChild(e),t._cleanTipClass(),t.element.removeAttribute("aria-describedby"),EventHandler.trigger(t.element,t.constructor.Event.HIDDEN),t._popper.destroy()};if(!EventHandler.trigger(this.element,this.constructor.Event.HIDE).defaultPrevented){var i;if(e.classList.remove(CLASS_NAME_SHOW$3),"ontouchstart"in document.documentElement)(i=[]).concat.apply(i,document.body.children).forEach((function(t){return EventHandler.off(t,"mouseover",noop)}));if(this._activeTrigger[TRIGGER_CLICK]=!1,this._activeTrigger[TRIGGER_FOCUS]=!1,this._activeTrigger[TRIGGER_HOVER]=!1,this.tip.classList.contains(CLASS_NAME_FADE$1)){var o=getTransitionDurationFromElement(e);EventHandler.one(e,TRANSITION_END,n),emulateTransitionEnd(e,o)}else n();this._hoverState=""}}},e.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},e.isWithContent=function(){return Boolean(this.getTitle())},e.getTipElement=function(){if(this.tip)return this.tip;var t=document.createElement("div");return t.innerHTML=this.config.template,this.tip=t.children[0],this.tip},e.setContent=function(){var t=this.getTipElement();this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER,t),this.getTitle()),t.classList.remove(CLASS_NAME_FADE$1,CLASS_NAME_SHOW$3)},e.setElementContent=function(t,e){if(null!==t)return"object"==typeof e&&isElement(e)?(e.jquery&&(e=e[0]),void(this.config.html?e.parentNode!==t&&(t.innerHTML="",t.appendChild(e)):t.textContent=e.textContent)):void(this.config.html?(this.config.sanitize&&(e=sanitizeHtml(e,this.config.allowList,this.config.sanitizeFn)),t.innerHTML=e):t.textContent=e)},e.getTitle=function(){var t=this.element.getAttribute("data-original-title");return t||(t="function"==typeof this.config.title?this.config.title.call(this.element):this.config.title),t},e._getPopperConfig=function(t){var e=this;return _extends({},{placement:t,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:"."+this.constructor.NAME+"-arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(t){t.originalPlacement!==t.placement&&e._handlePopperPlacementChange(t)},onUpdate:function(t){return e._handlePopperPlacementChange(t)}},this.config.popperConfig)},e._addAttachmentClass=function(t){this.getTipElement().classList.add(CLASS_PREFIX+"-"+t)},e._getOffset=function(){var t=this,e={};return"function"==typeof this.config.offset?e.fn=function(e){return e.offsets=_extends({},e.offsets,t.config.offset(e.offsets,t.element)||{}),e}:e.offset=this.config.offset,e},e._getContainer=function(){return!1===this.config.container?document.body:isElement(this.config.container)?this.config.container:SelectorEngine.findOne(this.config.container)},e._getAttachment=function(t){return AttachmentMap[t.toUpperCase()]},e._setListeners=function(){var t=this;this.config.trigger.split(" ").forEach((function(e){if("click"===e)EventHandler.on(t.element,t.constructor.Event.CLICK,t.config.selector,(function(e){return t.toggle(e)}));else if(e!==TRIGGER_MANUAL){var n=e===TRIGGER_HOVER?t.constructor.Event.MOUSEENTER:t.constructor.Event.FOCUSIN,i=e===TRIGGER_HOVER?t.constructor.Event.MOUSELEAVE:t.constructor.Event.FOCUSOUT;EventHandler.on(t.element,n,t.config.selector,(function(e){return t._enter(e)})),EventHandler.on(t.element,i,t.config.selector,(function(e){return t._leave(e)}))}})),this._hideModalHandler=function(){t.element&&t.hide()},EventHandler.on(this.element.closest("."+CLASS_NAME_MODAL),"hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=_extends({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},e._fixTitle=function(){var t=typeof this.element.getAttribute("data-original-title");(this.element.getAttribute("title")||"string"!==t)&&(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},e._enter=function(t,e){var n=this.constructor.DATA_KEY;(e=e||Data.getData(t.delegateTarget,n))||(e=new this.constructor(t.delegateTarget,this._getDelegateConfig()),Data.setData(t.delegateTarget,n,e)),t&&(e._activeTrigger["focusin"===t.type?TRIGGER_FOCUS:TRIGGER_HOVER]=!0),e.getTipElement().classList.contains(CLASS_NAME_SHOW$3)||e._hoverState===HOVER_STATE_SHOW?e._hoverState=HOVER_STATE_SHOW:(clearTimeout(e._timeout),e._hoverState=HOVER_STATE_SHOW,e.config.delay&&e.config.delay.show?e._timeout=setTimeout((function(){e._hoverState===HOVER_STATE_SHOW&&e.show()}),e.config.delay.show):e.show())},e._leave=function(t,e){var n=this.constructor.DATA_KEY;(e=e||Data.getData(t.delegateTarget,n))||(e=new this.constructor(t.delegateTarget,this._getDelegateConfig()),Data.setData(t.delegateTarget,n,e)),t&&(e._activeTrigger["focusout"===t.type?TRIGGER_FOCUS:TRIGGER_HOVER]=!1),e._isWithActiveTrigger()||(clearTimeout(e._timeout),e._hoverState=HOVER_STATE_OUT,e.config.delay&&e.config.delay.hide?e._timeout=setTimeout((function(){e._hoverState===HOVER_STATE_OUT&&e.hide()}),e.config.delay.hide):e.hide())},e._isWithActiveTrigger=function(){for(var t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1},e._getConfig=function(t){var e=Manipulator.getDataAttributes(this.element);return Object.keys(e).forEach((function(t){-1!==DISALLOWED_ATTRIBUTES.indexOf(t)&&delete e[t]})),t&&"object"==typeof t.container&&t.container.jquery&&(t.container=t.container[0]),"number"==typeof(t=_extends({},this.constructor.Default,e,"object"==typeof t&&t?t:{})).delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),typeCheckConfig(NAME$6,t,this.constructor.DefaultType),t.sanitize&&(t.template=sanitizeHtml(t.template,t.allowList,t.sanitizeFn)),t},e._getDelegateConfig=function(){var t={};if(this.config)for(var e in this.config)this.constructor.Default[e]!==this.config[e]&&(t[e]=this.config[e]);return t},e._cleanTipClass=function(){var t=this.getTipElement(),e=t.getAttribute("class").match(BSCLS_PREFIX_REGEX);null!==e&&e.length>0&&e.map((function(t){return t.trim()})).forEach((function(e){return t.classList.remove(e)}))},e._handlePopperPlacementChange=function(t){this.tip=t.instance.popper,this._cleanTipClass(),this._addAttachmentClass(this._getAttachment(t.placement))},e._fixTransition=function(){var t=this.getTipElement(),e=this.config.animation;null===t.getAttribute("x-placement")&&(t.classList.remove(CLASS_NAME_FADE$1),this.config.animation=!1,this.hide(),this.show(),this.config.animation=e)},t.jQueryInterface=function(e){return this.each((function(){var n=Data.getData(this,DATA_KEY$6),i="object"==typeof e&&e;if((n||!/dispose|hide/.test(e))&&(n||(n=new t(this,i)),"string"==typeof e)){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}}))},t.getInstance=function(t){return Data.getData(t,DATA_KEY$6)},_createClass(t,null,[{key:"VERSION",get:function(){return VERSION$6}},{key:"Default",get:function(){return Default$4}},{key:"NAME",get:function(){return NAME$6}},{key:"DATA_KEY",get:function(){return DATA_KEY$6}},{key:"Event",get:function(){return Event$1}},{key:"EVENT_KEY",get:function(){return EVENT_KEY$6}},{key:"DefaultType",get:function(){return DefaultType$4}}]),t}();onDOMContentLoaded((function(){var t=getjQuery();if(t){var e=t.fn[NAME$6];t.fn[NAME$6]=Tooltip.jQueryInterface,t.fn[NAME$6].Constructor=Tooltip,t.fn[NAME$6].noConflict=function(){return t.fn[NAME$6]=e,Tooltip.jQueryInterface}}}));var NAME$7="popover",VERSION$7="5.0.0-alpha3",DATA_KEY$7="bs.popover",EVENT_KEY$7="."+DATA_KEY$7,CLASS_PREFIX$1="bs-popover",BSCLS_PREFIX_REGEX$1=new RegExp("(^|\\s)"+CLASS_PREFIX$1+"\\S+","g"),Default$5=_extends({},Tooltip.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),DefaultType$5=_extends({},Tooltip.DefaultType,{content:"(string|element|function)"}),Event$2={HIDE:"hide"+EVENT_KEY$7,HIDDEN:"hidden"+EVENT_KEY$7,SHOW:"show"+EVENT_KEY$7,SHOWN:"shown"+EVENT_KEY$7,INSERTED:"inserted"+EVENT_KEY$7,CLICK:"click"+EVENT_KEY$7,FOCUSIN:"focusin"+EVENT_KEY$7,FOCUSOUT:"focusout"+EVENT_KEY$7,MOUSEENTER:"mouseenter"+EVENT_KEY$7,MOUSELEAVE:"mouseleave"+EVENT_KEY$7},CLASS_NAME_FADE$2="fade",CLASS_NAME_SHOW$4="show",SELECTOR_TITLE=".popover-header",SELECTOR_CONTENT=".popover-body",Popover=function(t){function e(){return t.apply(this,arguments)||this}_inheritsLoose(e,t);var n=e.prototype;return n.isWithContent=function(){return this.getTitle()||this._getContent()},n.setContent=function(){var t=this.getTipElement();this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE,t),this.getTitle());var e=this._getContent();"function"==typeof e&&(e=e.call(this.element)),this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT,t),e),t.classList.remove(CLASS_NAME_FADE$2,CLASS_NAME_SHOW$4)},n._addAttachmentClass=function(t){this.getTipElement().classList.add(CLASS_PREFIX$1+"-"+t)},n._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},n._cleanTipClass=function(){var t=this.getTipElement(),e=t.getAttribute("class").match(BSCLS_PREFIX_REGEX$1);null!==e&&e.length>0&&e.map((function(t){return t.trim()})).forEach((function(e){return t.classList.remove(e)}))},e.jQueryInterface=function(t){return this.each((function(){var n=Data.getData(this,DATA_KEY$7),i="object"==typeof t?t:null;if((n||!/dispose|hide/.test(t))&&(n||(n=new e(this,i),Data.setData(this,DATA_KEY$7,n)),"string"==typeof t)){if(void 0===n[t])throw new TypeError('No method named "'+t+'"');n[t]()}}))},e.getInstance=function(t){return Data.getData(t,DATA_KEY$7)},_createClass(e,null,[{key:"VERSION",get:function(){return VERSION$7}},{key:"Default",get:function(){return Default$5}},{key:"NAME",get:function(){return NAME$7}},{key:"DATA_KEY",get:function(){return DATA_KEY$7}},{key:"Event",get:function(){return Event$2}},{key:"EVENT_KEY",get:function(){return EVENT_KEY$7}},{key:"DefaultType",get:function(){return DefaultType$5}}]),e}(Tooltip);onDOMContentLoaded((function(){var t=getjQuery();if(t){var e=t.fn[NAME$7];t.fn[NAME$7]=Popover.jQueryInterface,t.fn[NAME$7].Constructor=Popover,t.fn[NAME$7].noConflict=function(){return t.fn[NAME$7]=e,Popover.jQueryInterface}}}));var NAME$8="scrollspy",VERSION$8="5.0.0-alpha3",DATA_KEY$8="bs.scrollspy",EVENT_KEY$8="."+DATA_KEY$8,DATA_API_KEY$6=".data-api",Default$6={offset:10,method:"auto",target:""},DefaultType$6={offset:"number",method:"string",target:"(string|element)"},EVENT_ACTIVATE="activate"+EVENT_KEY$8,EVENT_SCROLL="scroll"+EVENT_KEY$8,EVENT_LOAD_DATA_API$1="load"+EVENT_KEY$8+DATA_API_KEY$6,CLASS_NAME_DROPDOWN_ITEM="dropdown-item",CLASS_NAME_ACTIVE$2="active",SELECTOR_DATA_SPY='[data-spy="scroll"]',SELECTOR_NAV_LIST_GROUP=".nav, .list-group",SELECTOR_NAV_LINKS=".nav-link",SELECTOR_NAV_ITEMS=".nav-item",SELECTOR_LIST_ITEMS=".list-group-item",SELECTOR_DROPDOWN=".dropdown",SELECTOR_DROPDOWN_TOGGLE=".dropdown-toggle",METHOD_OFFSET="offset",METHOD_POSITION="position",ScrollSpy=function(){function t(t,e){var n=this;this._element=t,this._scrollElement="BODY"===t.tagName?window:t,this._config=this._getConfig(e),this._selector=this._config.target+" "+SELECTOR_NAV_LINKS+", "+this._config.target+" "+SELECTOR_LIST_ITEMS+", "+this._config.target+" ."+CLASS_NAME_DROPDOWN_ITEM,this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,EventHandler.on(this._scrollElement,EVENT_SCROLL,(function(t){return n._process(t)})),this.refresh(),this._process(),Data.setData(t,DATA_KEY$8,this)}var e=t.prototype;return e.refresh=function(){var t=this,e=this._scrollElement===this._scrollElement.window?METHOD_OFFSET:METHOD_POSITION,n="auto"===this._config.method?e:this._config.method,i=n===METHOD_POSITION?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),SelectorEngine.find(this._selector).map((function(t){var e=getSelectorFromElement(t),o=e?SelectorEngine.findOne(e):null;if(o){var r=o.getBoundingClientRect();if(r.width||r.height)return[Manipulator[n](o).top+i,e]}return null})).filter((function(t){return t})).sort((function(t,e){return t[0]-e[0]})).forEach((function(e){t._offsets.push(e[0]),t._targets.push(e[1])}))},e.dispose=function(){Data.removeData(this._element,DATA_KEY$8),EventHandler.off(this._scrollElement,EVENT_KEY$8),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},e._getConfig=function(t){if("string"!=typeof(t=_extends({},Default$6,"object"==typeof t&&t?t:{})).target&&isElement(t.target)){var e=t.target.id;e||(e=getUID(NAME$8),t.target.id=e),t.target="#"+e}return typeCheckConfig(NAME$8,t,DefaultType$6),t},e._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},e._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},e._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},e._process=function(){var t=this._getScrollTop()+this._config.offset,e=this._getScrollHeight(),n=this._config.offset+e-this._getOffsetHeight();if(this._scrollHeight!==e&&this.refresh(),t>=n){var i=this._targets[this._targets.length-1];this._activeTarget!==i&&this._activate(i)}else{if(this._activeTarget&&t<this._offsets[0]&&this._offsets[0]>0)return this._activeTarget=null,void this._clear();for(var o=this._offsets.length;o--;){this._activeTarget!==this._targets[o]&&t>=this._offsets[o]&&(void 0===this._offsets[o+1]||t<this._offsets[o+1])&&this._activate(this._targets[o])}}},e._activate=function(t){this._activeTarget=t,this._clear();var e=this._selector.split(",").map((function(e){return e+'[data-target="'+t+'"],'+e+'[href="'+t+'"]'})),n=SelectorEngine.findOne(e.join(","));n.classList.contains(CLASS_NAME_DROPDOWN_ITEM)?(SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE,n.closest(SELECTOR_DROPDOWN)).classList.add(CLASS_NAME_ACTIVE$2),n.classList.add(CLASS_NAME_ACTIVE$2)):(n.classList.add(CLASS_NAME_ACTIVE$2),SelectorEngine.parents(n,SELECTOR_NAV_LIST_GROUP).forEach((function(t){SelectorEngine.prev(t,SELECTOR_NAV_LINKS+", "+SELECTOR_LIST_ITEMS).forEach((function(t){return t.classList.add(CLASS_NAME_ACTIVE$2)})),SelectorEngine.prev(t,SELECTOR_NAV_ITEMS).forEach((function(t){SelectorEngine.children(t,SELECTOR_NAV_LINKS).forEach((function(t){return t.classList.add(CLASS_NAME_ACTIVE$2)}))}))}))),EventHandler.trigger(this._scrollElement,EVENT_ACTIVATE,{relatedTarget:t})},e._clear=function(){SelectorEngine.find(this._selector).filter((function(t){return t.classList.contains(CLASS_NAME_ACTIVE$2)})).forEach((function(t){return t.classList.remove(CLASS_NAME_ACTIVE$2)}))},t.jQueryInterface=function(e){return this.each((function(){var n=Data.getData(this,DATA_KEY$8);if(n||(n=new t(this,"object"==typeof e&&e)),"string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}}))},t.getInstance=function(t){return Data.getData(t,DATA_KEY$8)},_createClass(t,null,[{key:"VERSION",get:function(){return VERSION$8}},{key:"Default",get:function(){return Default$6}}]),t}();EventHandler.on(window,EVENT_LOAD_DATA_API$1,(function(){SelectorEngine.find(SELECTOR_DATA_SPY).forEach((function(t){return new ScrollSpy(t,Manipulator.getDataAttributes(t))}))})),onDOMContentLoaded((function(){var t=getjQuery();if(t){var e=t.fn[NAME$8];t.fn[NAME$8]=ScrollSpy.jQueryInterface,t.fn[NAME$8].Constructor=ScrollSpy,t.fn[NAME$8].noConflict=function(){return t.fn[NAME$8]=e,ScrollSpy.jQueryInterface}}}));var NAME$9="tab",VERSION$9="5.0.0-alpha3",DATA_KEY$9="bs.tab",EVENT_KEY$9="."+DATA_KEY$9,DATA_API_KEY$7=".data-api",EVENT_HIDE$3="hide"+EVENT_KEY$9,EVENT_HIDDEN$3="hidden"+EVENT_KEY$9,EVENT_SHOW$3="show"+EVENT_KEY$9,EVENT_SHOWN$3="shown"+EVENT_KEY$9,EVENT_CLICK_DATA_API$6="click"+EVENT_KEY$9+DATA_API_KEY$7,CLASS_NAME_DROPDOWN_MENU="dropdown-menu",CLASS_NAME_ACTIVE$3="active",CLASS_NAME_DISABLED$1="disabled",CLASS_NAME_FADE$3="fade",CLASS_NAME_SHOW$5="show",SELECTOR_DROPDOWN$1=".dropdown",SELECTOR_NAV_LIST_GROUP$1=".nav, .list-group",SELECTOR_ACTIVE$1=".active",SELECTOR_ACTIVE_UL=":scope > li > .active",SELECTOR_DATA_TOGGLE$4='[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',SELECTOR_DROPDOWN_TOGGLE$1=".dropdown-toggle",SELECTOR_DROPDOWN_ACTIVE_CHILD=":scope > .dropdown-menu .active",Tab=function(){function t(t){this._element=t,Data.setData(this._element,DATA_KEY$9,this)}var e=t.prototype;return e.show=function(){var t=this;if(!(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&this._element.classList.contains(CLASS_NAME_ACTIVE$3)||this._element.classList.contains(CLASS_NAME_DISABLED$1))){var e,n=getElementFromSelector(this._element),i=this._element.closest(SELECTOR_NAV_LIST_GROUP$1);if(i){var o="UL"===i.nodeName||"OL"===i.nodeName?SELECTOR_ACTIVE_UL:SELECTOR_ACTIVE$1;e=(e=SelectorEngine.find(o,i))[e.length-1]}var r=null;if(e&&(r=EventHandler.trigger(e,EVENT_HIDE$3,{relatedTarget:this._element})),!(EventHandler.trigger(this._element,EVENT_SHOW$3,{relatedTarget:e}).defaultPrevented||null!==r&&r.defaultPrevented)){this._activate(this._element,i);var a=function(){EventHandler.trigger(e,EVENT_HIDDEN$3,{relatedTarget:t._element}),EventHandler.trigger(t._element,EVENT_SHOWN$3,{relatedTarget:e})};n?this._activate(n,n.parentNode,a):a()}}},e.dispose=function(){Data.removeData(this._element,DATA_KEY$9),this._element=null},e._activate=function(t,e,n){var i=this,o=(!e||"UL"!==e.nodeName&&"OL"!==e.nodeName?SelectorEngine.children(e,SELECTOR_ACTIVE$1):SelectorEngine.find(SELECTOR_ACTIVE_UL,e))[0],r=n&&o&&o.classList.contains(CLASS_NAME_FADE$3),a=function(){return i._transitionComplete(t,o,n)};if(o&&r){var s=getTransitionDurationFromElement(o);o.classList.remove(CLASS_NAME_SHOW$5),EventHandler.one(o,TRANSITION_END,a),emulateTransitionEnd(o,s)}else a()},e._transitionComplete=function(t,e,n){if(e){e.classList.remove(CLASS_NAME_ACTIVE$3);var i=SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD,e.parentNode);i&&i.classList.remove(CLASS_NAME_ACTIVE$3),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!1)}(t.classList.add(CLASS_NAME_ACTIVE$3),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!0),reflow(t),t.classList.contains(CLASS_NAME_FADE$3)&&t.classList.add(CLASS_NAME_SHOW$5),t.parentNode&&t.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU))&&(t.closest(SELECTOR_DROPDOWN$1)&&SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE$1).forEach((function(t){return t.classList.add(CLASS_NAME_ACTIVE$3)})),t.setAttribute("aria-expanded",!0));n&&n()},t.jQueryInterface=function(e){return this.each((function(){var n=Data.getData(this,DATA_KEY$9)||new t(this);if("string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}}))},t.getInstance=function(t){return Data.getData(t,DATA_KEY$9)},_createClass(t,null,[{key:"VERSION",get:function(){return VERSION$9}}]),t}();EventHandler.on(document,EVENT_CLICK_DATA_API$6,SELECTOR_DATA_TOGGLE$4,(function(t){t.preventDefault(),(Data.getData(this,DATA_KEY$9)||new Tab(this)).show()})),onDOMContentLoaded((function(){var t=getjQuery();if(t){var e=t.fn[NAME$9];t.fn[NAME$9]=Tab.jQueryInterface,t.fn[NAME$9].Constructor=Tab,t.fn[NAME$9].noConflict=function(){return t.fn[NAME$9]=e,Tab.jQueryInterface}}}));var NAME$a="toast",VERSION$a="5.0.0-alpha3",DATA_KEY$a="bs.toast",EVENT_KEY$a="."+DATA_KEY$a,EVENT_CLICK_DISMISS$1="click.dismiss"+EVENT_KEY$a,EVENT_HIDE$4="hide"+EVENT_KEY$a,EVENT_HIDDEN$4="hidden"+EVENT_KEY$a,EVENT_SHOW$4="show"+EVENT_KEY$a,EVENT_SHOWN$4="shown"+EVENT_KEY$a,CLASS_NAME_FADE$4="fade",CLASS_NAME_HIDE="hide",CLASS_NAME_SHOW$6="show",CLASS_NAME_SHOWING="showing",DefaultType$7={animation:"boolean",autohide:"boolean",delay:"number"},Default$7={animation:!0,autohide:!0,delay:5e3},SELECTOR_DATA_DISMISS$1='[data-dismiss="toast"]',Toast=function(){function t(t,e){this._element=t,this._config=this._getConfig(e),this._timeout=null,this._setListeners(),Data.setData(t,DATA_KEY$a,this)}var e=t.prototype;return e.show=function(){var t=this;if(!EventHandler.trigger(this._element,EVENT_SHOW$4).defaultPrevented){this._clearTimeout(),this._config.animation&&this._element.classList.add(CLASS_NAME_FADE$4);var e=function(){t._element.classList.remove(CLASS_NAME_SHOWING),t._element.classList.add(CLASS_NAME_SHOW$6),EventHandler.trigger(t._element,EVENT_SHOWN$4),t._config.autohide&&(t._timeout=setTimeout((function(){t.hide()}),t._config.delay))};if(this._element.classList.remove(CLASS_NAME_HIDE),reflow(this._element),this._element.classList.add(CLASS_NAME_SHOWING),this._config.animation){var n=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,e),emulateTransitionEnd(this._element,n)}else e()}},e.hide=function(){var t=this;if(this._element.classList.contains(CLASS_NAME_SHOW$6)&&!EventHandler.trigger(this._element,EVENT_HIDE$4).defaultPrevented){var e=function(){t._element.classList.add(CLASS_NAME_HIDE),EventHandler.trigger(t._element,EVENT_HIDDEN$4)};if(this._element.classList.remove(CLASS_NAME_SHOW$6),this._config.animation){var n=getTransitionDurationFromElement(this._element);EventHandler.one(this._element,TRANSITION_END,e),emulateTransitionEnd(this._element,n)}else e()}},e.dispose=function(){this._clearTimeout(),this._element.classList.contains(CLASS_NAME_SHOW$6)&&this._element.classList.remove(CLASS_NAME_SHOW$6),EventHandler.off(this._element,EVENT_CLICK_DISMISS$1),Data.removeData(this._element,DATA_KEY$a),this._element=null,this._config=null},e._getConfig=function(t){return t=_extends({},Default$7,Manipulator.getDataAttributes(this._element),"object"==typeof t&&t?t:{}),typeCheckConfig(NAME$a,t,this.constructor.DefaultType),t},e._setListeners=function(){var t=this;EventHandler.on(this._element,EVENT_CLICK_DISMISS$1,SELECTOR_DATA_DISMISS$1,(function(){return t.hide()}))},e._clearTimeout=function(){clearTimeout(this._timeout),this._timeout=null},t.jQueryInterface=function(e){return this.each((function(){var n=Data.getData(this,DATA_KEY$a);if(n||(n=new t(this,"object"==typeof e&&e)),"string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e](this)}}))},t.getInstance=function(t){return Data.getData(t,DATA_KEY$a)},_createClass(t,null,[{key:"VERSION",get:function(){return VERSION$a}},{key:"DefaultType",get:function(){return DefaultType$7}},{key:"Default",get:function(){return Default$7}}]),t}();onDOMContentLoaded((function(){var t=getjQuery();if(t){var e=t.fn[NAME$a];t.fn[NAME$a]=Toast.jQueryInterface,t.fn[NAME$a].Constructor=Toast,t.fn[NAME$a].noConflict=function(){return t.fn[NAME$a]=e,Toast.jQueryInterface}}}));export{Alert,Button,Carousel,Collapse,Dropdown,Modal,Popover,ScrollSpy,Tab,Toast,Tooltip};
//# sourceMappingURL=bootstrap.esm.min.js.map