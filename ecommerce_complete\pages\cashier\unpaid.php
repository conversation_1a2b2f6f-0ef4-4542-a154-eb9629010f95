<?php
require_once '../../config/ecommerce_db.php';
require_once '../../config/session.php';

// Require login and staff privileges
requireStaff();

// Get all unpaid orders
$unpaid_sql = "SELECT o.*, CONCAT(u.first_name, ' ', u.last_name) as customer_name, u.email, u.phone
              FROM e_orders o
              JOIN e_users u ON o.user_id = u.id
              WHERE o.payment_status = 'unpaid'
              ORDER BY o.created_at DESC";
$unpaid_orders = executeQuery($unpaid_sql)->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unpaid Orders - Euro Spice</title>
    <link rel="stylesheet" href="../../plugins/bootstrap/css/bootstrap.min.css">
    <script defer src="../../plugins/fontawesome/js/all.js"></script>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-light border-bottom site-header sticky-top py-1"
        style="background-color: #f15b31;">
        <div class="container">
            <a class="navbar-brand fw-bold fs-4" href="../../index.php" style="color: white;"><b>Euro Spice</b></a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
                aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home"></i> Back to Cashier
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../account/settings.php">
                            <i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../account/logout.php">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container py-5">
        <h2 class="mb-4">Unpaid Orders</h2>
        
        <?php if (empty($unpaid_orders)): ?>
            <div class="alert alert-info">
                <p>No unpaid orders found.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Order #</th>
                            <th>Customer</th>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($unpaid_orders as $order): ?>
                            <tr>
                                <td><?php echo $order['id']; ?></td>
                                <td>
                                    <?php echo htmlspecialchars($order['customer_name']); ?><br>
                                    <small class="text-muted"><?php echo htmlspecialchars($order['email']); ?></small>
                                </td>
                                <td><?php echo date('M j, Y, g:i a', strtotime($order['created_at'])); ?></td>
                                <td>PHP <?php echo number_format($order['total_amount'], 2); ?></td>
                                <td>
                                    <span class="badge bg-warning text-dark">Unpaid</span>
                                    <span class="badge bg-<?php echo $order['status'] === 'pending' ? 'secondary' : 'info'; ?>">
                                        <?php echo ucfirst($order['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="process_payment.php?order_id=<?php echo $order['id']; ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-money-bill-wave"></i> Process Payment
                                    </a>
                                    <a href="view_order.php?order_id=<?php echo $order['id']; ?>" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
        
        <div class="mt-4">
            <a href="index.php" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> Back to Cashier
            </a>
        </div>
    </div>

    <script src="../../plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
</body>

</html>
