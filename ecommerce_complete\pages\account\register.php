<?php
require_once '../../config/ecommerce_db.php';
require_once '../../config/session.php';

$error = '';
$success = '';

// Check if user is already logged in and not forcing registration
$force_register = isset($_GET['force']) && $_GET['force'] == 1;

if (isLoggedIn() && !$force_register) {
    header("Location: ../../index.php");
    exit();
}

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $first_name = $_POST['first_name'] ?? '';
    $last_name = $_POST['last_name'] ?? '';

    // Validate input
    if (empty($username) || empty($email) || empty($password) || empty($confirm_password) || empty($first_name) || empty($last_name)) {
        $error = "All fields are required.";
    } elseif ($password !== $confirm_password) {
        $error = "Passwords do not match.";
    } elseif (strlen($password) < 6) {
        $error = "Password must be at least 6 characters long.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format.";
    } else {
        // Check if username or email already exists
        $sql = "SELECT * FROM e_users WHERE username = :username OR email = :email LIMIT 1";
        $stmt = executeQuery($sql, [
            ':username' => $username,
            ':email' => $email
        ]);

        if ($stmt->rowCount() > 0) {
            $error = "Username or email already exists.";
        } else {
            // Create new user
            $password_hash = password_hash($password, PASSWORD_DEFAULT);

            $sql = "INSERT INTO e_users (username, password, email, first_name, last_name, role)
                    VALUES (:username, :password, :email, :first_name, :last_name, 'customer')";

            $stmt = executeQuery($sql, [
                ':username' => $username,
                ':password' => $password_hash,
                ':email' => $email,
                ':first_name' => $first_name,
                ':last_name' => $last_name
            ]);

            if ($stmt->rowCount() > 0) {
                $success = "Registration successful! You can now login.";

                // Redirect to login page after successful registration
                header("Location: login.php" . ($force_register ? "?force=1&registered=1" : "?registered=1"));
                exit();
            } else {
                $error = "Registration failed. Please try again.";
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Euro Spice</title>
    <link rel="stylesheet" href="../../plugins/bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../dist/login.css">
    <script defer src="../../plugins/fontawesome/js/all.js"></script>
</head>

<body>
    <div class="container">
        <div class="row">
            <div class="col-sm-9 col-md-7 col-lg-5 mx-auto">
                <div class="card border-0 shadow rounded-3 my-5">
                    <div class="card-body p-4 p-sm-5">
                        <h5 class="card-title text-center mb-5 fw-bold fs-5">Create an Account</h5>

                        <?php if ($error): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success"><?php echo $success; ?></div>
                            <div class="d-grid mb-2">
                                <a href="login.php<?php echo $force_register ? '?force=1' : ''; ?>" class="btn btn-primary btn-login text-uppercase fw-bold">
                                    Go to Login
                                </a>
                            </div>
                        <?php else: ?>
                            <form method="POST">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3 mb-md-0">
                                            <input type="text" class="form-control" id="first_name" name="first_name" placeholder="First Name" required>
                                            <label for="first_name">First Name</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-floating">
                                            <input type="text" class="form-control" id="last_name" name="last_name" placeholder="Last Name" required>
                                            <label for="last_name">Last Name</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="username" name="username" placeholder="Username" required>
                                    <label for="username">Username</label>
                                </div>

                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="email" name="email" placeholder="Email" required>
                                    <label for="email">Email address</label>
                                </div>

                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="password" name="password" placeholder="Password" required>
                                    <label for="password">Password</label>
                                </div>

                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Confirm Password" required>
                                    <label for="confirm_password">Confirm Password</label>
                                </div>

                                <div class="d-grid">
                                    <button class="btn btn-primary btn-login text-uppercase fw-bold" type="submit">
                                        Register
                                    </button>
                                </div>
                                <hr class="my-4">
                                <div class="d-grid mb-2">
                                    <a href="login.php<?php echo $force_register ? '?force=1' : ''; ?>" class="btn btn-outline-secondary btn-login text-uppercase fw-bold">
                                        Already have an account? Login
                                    </a>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
</body>

</html>