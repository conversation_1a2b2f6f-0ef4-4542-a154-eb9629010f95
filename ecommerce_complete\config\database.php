<?php

/**
 * Database Connection
 *
 * This file handles the database connection for the Euro Spice ecommerce system.
 */

// Database configuration
$db_host = 'localhost';
$db_name = 'finance';
$db_user = 'root';
$db_pass = '';

// Create connection
try {
    $conn = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
    // Set the PDO error mode to exception
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    // Set default fetch mode to associative array
    $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Log error and display user-friendly message
    error_log("Connection failed: " . $e->getMessage());
    die("Database connection failed. Please try again later.");
}

/**
 * Helper function to execute queries with parameters
 *
 * @param string $sql SQL query with placeholders
 * @param array $params Parameters to bind to the query
 * @return PDOStatement The executed statement
 */
function executeQuery($sql, $params = [])
{
    global $conn;

    try {
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("Query execution failed: " . $e->getMessage());
        die("An error occurred while processing your request.");
    }
}
