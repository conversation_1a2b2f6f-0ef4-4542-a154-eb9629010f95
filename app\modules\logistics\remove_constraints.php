<?php
session_start();
require_once '../../config/config.php';

// Set the content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Function to get all foreign key constraints in the database
function getForeignKeyConstraints($conn)
{
    $constraints = [];

    try {
        $sql = "SELECT 
                    tc.TABLE_NAME, 
                    tc.CONSTRAINT_NAME,
                    kcu.COLUMN_NAME,
                    kcu.REFERENCED_TABLE_NAME,
                    kcu.REFERENCED_COLUMN_NAME
                FROM 
                    information_schema.TABLE_CONSTRAINTS tc
                JOIN 
                    information_schema.KEY_COLUMN_USAGE kcu
                ON 
                    tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
                WHERE 
                    tc.CONSTRAINT_TYPE = 'FOREIGN KEY' 
                    AND tc.TABLE_SCHEMA = DATABASE()";

        $stmt = $conn->query($sql);
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $constraints[] = $row;
        }
    } catch (PDOException $e) {
        error_log("Error getting foreign key constraints: " . $e->getMessage());
    }

    return $constraints;
}

// Function to drop a foreign key constraint
function dropForeignKeyConstraint($conn, $tableName, $constraintName)
{
    try {
        $sql = "ALTER TABLE `$tableName` DROP FOREIGN KEY `$constraintName`";
        $conn->exec($sql);
        return true;
    } catch (PDOException $e) {
        error_log("Error dropping foreign key constraint: " . $e->getMessage());
        return false;
    }
}

// Get all foreign key constraints
$constraints = getForeignKeyConstraints($conn);

// Results tracking
$results = [
    'success' => true,
    'message' => 'Foreign key constraints removed successfully',
    'removed_constraints' => [],
    'failed_constraints' => []
];

// Drop each constraint
foreach ($constraints as $constraint) {
    $tableName = $constraint['TABLE_NAME'];
    $constraintName = $constraint['CONSTRAINT_NAME'];

    if (dropForeignKeyConstraint($conn, $tableName, $constraintName)) {
        $results['removed_constraints'][] = [
            'table' => $tableName,
            'constraint' => $constraintName,
            'column' => $constraint['COLUMN_NAME'],
            'referenced_table' => $constraint['REFERENCED_TABLE_NAME'],
            'referenced_column' => $constraint['REFERENCED_COLUMN_NAME']
        ];
    } else {
        $results['failed_constraints'][] = [
            'table' => $tableName,
            'constraint' => $constraintName
        ];
        $results['success'] = false;
        $results['message'] = 'Some constraints could not be removed';
    }
}

// Return the results
echo json_encode($results, JSON_PRETTY_PRINT);
