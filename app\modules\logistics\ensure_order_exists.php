<?php
session_start();
require_once '../../config/config.php';

// Set the content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // For demo purposes, we'll continue even if not logged in
    error_log("User not logged in, but continuing for demo purposes");
}

// Get order ID from request
$order_id = isset($_POST['order_id']) ? (int)$_POST['order_id'] : 0;

// Validate input
if (!$order_id) {
    // Use a default order ID if none is provided
    $order_id = 1;
    error_log("No order_id provided, using default: 1");
}

try {
    // Check if the order exists
    $check_sql = "SELECT id FROM order_requests WHERE id = :order_id";
    $stmt = $conn->prepare($check_sql);
    $stmt->bindValue(':order_id', $order_id, PDO::PARAM_INT);
    $stmt->execute();

    if ($stmt->rowCount() === 0) {
        // Order doesn't exist, create it
        error_log("Order ID $order_id not found, creating it");

        try {
            // Check if the order_requests table has the necessary columns
            $columns_check = $conn->query("SHOW COLUMNS FROM order_requests");
            $columns = [];
            while ($column = $columns_check->fetch(PDO::FETCH_ASSOC)) {
                $columns[] = $column['Field'];
            }

            // Build a dynamic insert query based on available columns
            $insert_fields = ['id'];
            $insert_values = [':id'];
            $params = [':id' => $order_id];

            // Add user_id if the column exists
            if (in_array('user_id', $columns)) {
                $insert_fields[] = 'user_id';
                $insert_values[] = ':user_id';
                $params[':user_id'] = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 1;
            }

            // Add product_id if the column exists
            if (in_array('product_id', $columns)) {
                $insert_fields[] = 'product_id';
                $insert_values[] = ':product_id';
                $params[':product_id'] = 1;
            }

            // Add quantity if the column exists
            if (in_array('quantity', $columns)) {
                $insert_fields[] = 'quantity';
                $insert_values[] = ':quantity';
                $params[':quantity'] = 5;
            }

            // Add delivery_address if the column exists
            if (in_array('delivery_address', $columns)) {
                $insert_fields[] = 'delivery_address';
                $insert_values[] = ':delivery_address';
                $params[':delivery_address'] = 'Sample Address, Manila, Philippines';
            }

            // Add status if the column exists
            if (in_array('status', $columns)) {
                $insert_fields[] = 'status';
                $insert_values[] = ':status';
                $params[':status'] = 'processing';
            }

            // Create the SQL query
            $insert_sql = "INSERT INTO order_requests (" . implode(', ', $insert_fields) . ") 
                          VALUES (" . implode(', ', $insert_values) . ")";

            $stmt = $conn->prepare($insert_sql);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();

            echo json_encode(['success' => true, 'message' => 'Order created successfully']);
        } catch (PDOException $createError) {
            error_log("Error creating order: " . $createError->getMessage());
            echo json_encode(['success' => false, 'message' => 'Error creating order: ' . $createError->getMessage()]);
        }
    } else {
        // Order exists
        echo json_encode(['success' => true, 'message' => 'Order already exists']);
    }
} catch (PDOException $e) {
    error_log("Error in ensure_order_exists.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
