<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Database Connection Test</h1>";

try {
    // Include database configuration
    require_once 'config/database.php';

    echo "<p style='color: green;'>✓ Database connection successful</p>";

    // Check if required tables exist
    $tables = ['categories', 'products', 'orders', 'order_items', 'raw_materials', 'product_materials'];
    $missing_tables = [];

    foreach ($tables as $table) {
        try {
            $result = $conn->query("SELECT 1 FROM $table LIMIT 1");
            echo "<p style='color: green;'>✓ Table '$table' exists</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ Table '$table' does not exist</p>";
            $missing_tables[] = $table;
        }
    }

    if (!empty($missing_tables)) {
        echo "<h2>Missing Tables</h2>";
        echo "<p>The following tables are missing. Please import the SQL file to create them:</p>";
        echo "<pre>ecommerce_complete/eurospice.sql</pre>";

        echo "<h2>SQL Import Instructions</h2>";
        echo "<ol>";
        echo "<li>Open phpMyAdmin (usually at <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a>)</li>";
        echo "<li>Select your 'finance' database from the left sidebar</li>";
        echo "<li>Click on the 'Import' tab at the top</li>";
        echo "<li>Click 'Choose File' and select the eurospice.sql file</li>";
        echo "<li>Click 'Go' at the bottom to import the file</li>";
        echo "</ol>";
    } else {
        echo "<p style='color: green;'>All required tables exist!</p>";
    }

    // Check if users table has required columns
    try {
        $stmt = $conn->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        $required_columns = ['role_id', 'phone_number', 'department_id'];
        $missing_columns = array_diff($required_columns, $columns);

        if (!empty($missing_columns)) {
            echo "<h2>Missing Columns in Users Table</h2>";
            echo "<p>The following columns are missing in the users table:</p>";
            echo "<ul>";
            foreach ($missing_columns as $column) {
                echo "<li>$column</li>";
            }
            echo "</ul>";
            echo "<p>Please import the SQL file to add these columns.</p>";
        } else {
            echo "<p style='color: green;'>✓ Users table has all required columns</p>";
        }
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ Could not check users table structure: " . $e->getMessage() . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}
