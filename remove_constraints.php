<?php
// Include database configuration
require_once 'app/config/config.php';

try {
    // Start transaction
    $conn->beginTransaction();
    
    echo "Attempting to remove constraints from inventory table...\n";
    
    // First, check if the inventory table exists
    $tableExists = $conn->query("SHOW TABLES LIKE 'inventory'")->rowCount() > 0;
    
    if (!$tableExists) {
        echo "Inventory table does not exist. Creating it without constraints...\n";
        
        // Create inventory table without constraints
        $conn->exec("CREATE TABLE IF NOT EXISTS inventory (
            id INT AUTO_INCREMENT PRIMARY KEY,
            prod_image VARCHAR(255),
            prod_name VARCHAR(100) NOT NULL,
            brand_name VARCHAR(100) NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            prod_measure VARCHAR(50),
            pack_type VARCHAR(50),
            expiry_date DATETIME,
            delivered_date DATETIME,
            country VARCHAR(100),
            batch_code VARCHAR(50),
            stocks DECIMAL(10,2) NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'approved',
            order_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        echo "Inventory table created successfully without constraints.\n";
    } else {
        echo "Inventory table exists. Checking for constraints...\n";
        
        // Get all constraints
        $query = "SELECT 
                    tc.CONSTRAINT_NAME, 
                    tc.CONSTRAINT_TYPE
                  FROM information_schema.TABLE_CONSTRAINTS tc
                  WHERE tc.TABLE_SCHEMA = DATABASE()
                    AND tc.TABLE_NAME = 'inventory'
                    AND tc.CONSTRAINT_TYPE != 'PRIMARY KEY'";
        
        $result = $conn->query($query);
        
        if ($result->rowCount() > 0) {
            echo "Found constraints to remove:\n";
            
            while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                $constraintName = $row['CONSTRAINT_NAME'];
                $constraintType = $row['CONSTRAINT_TYPE'];
                
                echo "- Removing {$constraintType} constraint: {$constraintName}\n";
                
                if ($constraintType == 'FOREIGN KEY') {
                    $conn->exec("ALTER TABLE inventory DROP FOREIGN KEY {$constraintName}");
                } else {
                    $conn->exec("ALTER TABLE inventory DROP INDEX {$constraintName}");
                }
            }
        } else {
            echo "No constraints found to remove.\n";
        }
        
        // Check for unique indexes
        $query = "SHOW INDEXES FROM inventory WHERE Non_unique = 0 AND Key_name != 'PRIMARY'";
        $result = $conn->query($query);
        
        if ($result->rowCount() > 0) {
            echo "Found unique indexes to remove:\n";
            
            while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                $indexName = $row['Key_name'];
                echo "- Removing unique index: {$indexName}\n";
                $conn->exec("ALTER TABLE inventory DROP INDEX {$indexName}");
            }
        } else {
            echo "No unique indexes found to remove.\n";
        }
        
        // Create a new inventory table without constraints
        echo "Creating a new inventory table without constraints...\n";
        
        // Rename the old table
        $conn->exec("RENAME TABLE inventory TO inventory_old");
        
        // Create a new table without constraints
        $conn->exec("CREATE TABLE inventory (
            id INT AUTO_INCREMENT PRIMARY KEY,
            prod_image VARCHAR(255),
            prod_name VARCHAR(100) NOT NULL,
            brand_name VARCHAR(100) NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            prod_measure VARCHAR(50),
            pack_type VARCHAR(50),
            expiry_date DATETIME,
            delivered_date DATETIME,
            country VARCHAR(100),
            batch_code VARCHAR(50),
            stocks DECIMAL(10,2) NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'approved',
            order_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // Copy data from old table to new table
        $conn->exec("INSERT INTO inventory (
            id, prod_image, prod_name, brand_name, price, prod_measure, 
            pack_type, expiry_date, delivered_date, country, batch_code, 
            stocks, status, order_id, created_at
        ) 
        SELECT 
            id, prod_image, prod_name, brand_name, price, prod_measure, 
            pack_type, expiry_date, delivered_date, country, batch_code, 
            stocks, status, order_id, created_at
        FROM inventory_old");
        
        // Drop the old table
        $conn->exec("DROP TABLE inventory_old");
        
        echo "Successfully recreated inventory table without constraints.\n";
    }
    
    // Commit transaction
    $conn->commit();
    
    echo "All constraints have been removed from the inventory table.\n";
    
} catch (PDOException $e) {
    // Rollback transaction on error
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    echo "Error: " . $e->getMessage() . "\n";
}
?>
