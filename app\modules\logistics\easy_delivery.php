<?php
session_start();
require_once '../../config/config.php';

// Get order ID from URL parameter
$order_id = isset($_GET['order_id']) ? (int)$_GET['order_id'] : 0;

// Get order details
$order = null;
if ($order_id) {
    try {
        $stmt = $conn->prepare("SELECT
                                orq.*,
                                p.prod_name,
                                p.price,
                                p.prod_image,
                                a.id as approved_id,
                                a.brand_name,
                                a.price as approved_price,
                                a.prod_measure,
                                a.pack_type,
                                a.batch_code,
                                a.stocks,
                                a.country,
                                a.expiry_date
                            FROM order_requests orq
                            LEFT JOIN products p ON orq.product_id = p.id
                            LEFT JOIN approved a ON a.prod_name = p.prod_name OR a.id = orq.product_id
                            WHERE orq.id = :order_id");
        $stmt->bindValue(':order_id', $order_id, PDO::PARAM_INT);
        $stmt->execute();
        $order = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error fetching order: " . $e->getMessage());
    }
}

// If order doesn't exist, create a dummy order for demonstration
if (!$order) {
    $order = [
        'id' => $order_id ?: 1,
        'user_id' => 1,
        'product_id' => 1,
        'quantity' => 5,
        'delivery_address' => 'Sample Address, Manila, Philippines',
        'special_instructions' => 'Sample delivery instructions',
        'status' => 'processing',
        'driver_id' => null,
        'pickup_time' => null,
        'delivery_time' => null,
        'delivery_notes' => null,
        'prod_name' => 'Sample Product',
        'price' => 100.00,
        'prod_image' => '',
        'brand_name' => 'Sample Brand',
        'approved_price' => 100.00,
        'prod_measure' => 'kg',
        'pack_type' => 'box',
        'batch_code' => 'ABC123',
        'stocks' => 100,
        'country' => 'Philippines',
        'expiry_date' => date('Y-m-d', strtotime('+1 year'))
    ];
}

// Get customer information
$customer_name = "John Doe";
$customer_phone = "************";
if (isset($order['user_id'])) {
    try {
        $stmt = $conn->prepare("SELECT name, phone FROM users WHERE id = :user_id");
        $stmt->bindValue(':user_id', $order['user_id'], PDO::PARAM_INT);
        $stmt->execute();
        $customer = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($customer) {
            $customer_name = $customer['name'];
            $customer_phone = $customer['phone'];
        }
    } catch (PDOException $e) {
        error_log("Error fetching customer: " . $e->getMessage());
    }
}
$order['customer_name'] = $customer_name;
$order['customer_phone'] = $customer_phone;
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Easy Delivery</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            padding-top: 20px;
            padding-bottom: 70px;
        }

        .card {
            margin-bottom: 20px;
        }

        .delivery-progress-bar {
            display: flex;
            align-items: center;
        }

        .delivery-progress-bar i {
            margin-right: 10px;
            font-size: 1.2em;
        }

        .btn-lg {
            padding: 15px 30px;
            font-size: 1.2em;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .action-buttons .btn {
            flex: 1;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="mb-4">Easy Delivery</h1>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Delivery Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="status-container" class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            Status: <?php echo ucfirst($order['status']); ?>
                        </div>

                        <div class="progress mb-3" style="height: 25px;">
                            <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated"
                                role="progressbar" style="width: 0%;"
                                aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>

                        <div class="action-buttons mb-4">
                            <button id="startDeliveryBtn" class="btn btn-primary btn-lg">
                                <i class="fas fa-truck"></i> Start Delivery
                            </button>
                            <button id="completeDeliveryBtn" class="btn btn-success btn-lg">
                                <i class="fas fa-check-circle"></i> Complete Delivery
                            </button>
                        </div>

                        <div id="delivery-log" class="border p-3 bg-light" style="height: 200px; overflow-y: auto;">
                            <p><i class="fas fa-clock"></i> Waiting to start delivery...</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">Order Details</h5>
                    </div>
                    <div class="card-body">
                        <h6><?php echo htmlspecialchars($order['prod_name'] ?? ''); ?></h6>
                        <?php if (isset($order['brand_name']) && !empty($order['brand_name'])): ?>
                            <span class="badge bg-info mb-2"><?php echo htmlspecialchars($order['brand_name'] ?? ''); ?></span>
                        <?php endif; ?>

                        <p class="mb-1">
                            <strong>Customer:</strong> <?php echo htmlspecialchars($order['customer_name'] ?? ''); ?><br>
                            <strong>Phone:</strong> <?php echo htmlspecialchars($order['customer_phone'] ?? ''); ?><br>
                            <strong>From:</strong> <?php echo htmlspecialchars($order['delivery_from'] ?? '3708 N Guevarra St., Zone 1 Dasmariñas, Cavite Philippines 4114'); ?><br>
                            <strong>To:</strong> <?php echo htmlspecialchars($order['delivery_address'] ?? ''); ?><br>
                            <strong>Quantity:</strong> <?php echo $order['quantity']; ?><br>
                            <?php
                            // Use approved price if available, otherwise use regular price
                            $price = isset($order['approved_price']) && $order['approved_price'] > 0 ? $order['approved_price'] : (isset($order['price']) ? $order['price'] : 0);
                            ?>
                            <strong>Unit Price:</strong> ₱<?php echo number_format((float)$price, 2); ?><br>
                            <strong>Total:</strong> ₱<?php
                                                        $total = isset($order['total_price']) && $order['total_price'] ?
                                                            (float)$order['total_price'] : ((isset($order['quantity']) ? (float)$order['quantity'] : 0) * (float)$price);
                                                        echo number_format($total, 2);
                                                        ?><br>
                            <strong>Order Date:</strong> <?php echo isset($order['created_at']) && $order['created_at'] ? date('M d, Y H:i', strtotime($order['created_at'])) : 'N/A'; ?>
                        </p>

                        <?php if (isset($order['special_instructions']) && $order['special_instructions']): ?>
                            <hr>
                            <h6>Special Instructions</h6>
                            <p class="mb-1"><?php echo nl2br(htmlspecialchars($order['special_instructions'] ?? '')); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Fixed progress bar at the bottom -->
    <div class="fixed-bottom bg-dark text-white py-2" style="box-shadow: 0 -2px 10px rgba(0,0,0,0.3);">
        <div class="container">
            <div class="delivery-progress-bar">
                <i class="fas fa-truck"></i>
                <span id="bottom-progress-text">Delivery status: <?php echo ucfirst($order['status']); ?></span>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Simple delivery simulation
        document.addEventListener('DOMContentLoaded', function() {
            const startBtn = document.getElementById('startDeliveryBtn');
            const completeBtn = document.getElementById('completeDeliveryBtn');
            const statusContainer = document.getElementById('status-container');
            const progressBar = document.getElementById('progress-bar');
            const bottomProgressText = document.getElementById('bottom-progress-text');
            const deliveryLog = document.getElementById('delivery-log');

            let progress = 0;
            let interval;

            function addLogEntry(message) {
                const now = new Date();
                const timeStr = now.toLocaleTimeString();
                deliveryLog.innerHTML = `<p><i class="fas fa-clock"></i> ${timeStr}: ${message}</p>` + deliveryLog.innerHTML;
            }

            function updateProgress(value) {
                progress = value;
                progressBar.style.width = `${progress}%`;
                progressBar.textContent = `${progress}%`;
                progressBar.setAttribute('aria-valuenow', progress);
                bottomProgressText.textContent = `Delivery in progress... ${progress}% complete`;
            }

            startBtn.addEventListener('click', function() {
                statusContainer.innerHTML = '<i class="fas fa-truck"></i> Delivery in progress... Driver is on the way!';
                statusContainer.className = 'alert alert-info';

                addLogEntry('Delivery started. Driver is on the way.');

                // Start progress simulation
                progress = 0;
                updateProgress(progress);

                interval = setInterval(function() {
                    if (progress < 100) {
                        progress += 5;
                        updateProgress(progress);

                        if (progress === 25) {
                            addLogEntry('Driver has picked up the package.');
                        } else if (progress === 50) {
                            addLogEntry('Driver is halfway to the destination.');
                        } else if (progress === 75) {
                            addLogEntry('Driver is approaching the destination.');
                        } else if (progress === 95) {
                            addLogEntry('Driver has arrived at the destination.');
                        }
                    } else {
                        clearInterval(interval);
                    }
                }, 1000);

                // Update status in database
                fetch('update_order_status.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `order_id=<?php echo $order_id; ?>&status=in_transit`
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Status updated:', data);
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
            });

            completeBtn.addEventListener('click', function() {
                // Clear any existing interval
                if (interval) {
                    clearInterval(interval);
                }

                // Set progress to 100%
                updateProgress(100);

                statusContainer.innerHTML = `
                    <i class="fas fa-check-circle"></i> Delivery completed! Product added to inventory.
                    <div class="mt-2">
                        <a href="../inventory/inventory.php" class="btn btn-sm btn-primary" target="_blank">View Inventory</a>
                    </div>
                `;
                statusContainer.className = 'alert alert-success';
                bottomProgressText.textContent = 'Delivery completed successfully';

                addLogEntry('Delivery completed. Product added to inventory.');

                // Show a notification
                const notification = document.createElement('div');
                notification.className = 'alert alert-success position-fixed top-0 start-50 translate-middle-x mt-3';
                notification.style.zIndex = '9999';
                notification.innerHTML = '<i class="fas fa-check-circle"></i> Delivery completed! The product has been added to your inventory.';
                document.body.appendChild(notification);

                // Remove the notification after 5 seconds
                setTimeout(() => {
                    notification.remove();
                }, 5000);

                // Update status in database
                fetch('update_delivery_status_bypass.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `order_id=<?php echo $order_id; ?>&status=delivered&notes=Delivered successfully`
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log('Delivery completed:', data);
                        if (data && data.success) {
                            const productName = data.product && data.product.name ?
                                data.product.name :
                                '<?php echo htmlspecialchars($order['prod_name'] ?? ''); ?>';
                            addLogEntry(`Product "${productName}" added to inventory.`);
                        } else {
                            addLogEntry('Delivery marked as complete in the system.');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        addLogEntry('Error updating delivery status, but delivery is marked as complete.');
                    });
            });

            // If status is already delivered, show completed message
            if ('<?php echo $order['status']; ?>' === 'delivered') {
                statusContainer.innerHTML = `
                    <i class="fas fa-check-circle"></i> This order has been successfully delivered and added to inventory!
                    <div class="mt-2">
                        <a href="../inventory/inventory.php" class="btn btn-sm btn-primary" target="_blank">View Inventory</a>
                    </div>
                `;
                statusContainer.className = 'alert alert-success';
                updateProgress(100);
                bottomProgressText.textContent = 'Delivery completed successfully';
                addLogEntry('This order has already been delivered.');

                // Disable buttons if already delivered
                startBtn.disabled = true;
                completeBtn.disabled = true;
            }
        });
    </script>
</body>

</html>