<?php
// Include database configuration
require_once 'app/config/config.php';

try {
    echo "Starting to remove constraints from finance database tables...\n";
    
    // Start transaction
    $conn->beginTransaction();
    
    // First, identify tables with constraints
    echo "Identifying tables with constraints...\n";
    $query = "SELECT 
                tc.TABLE_NAME, 
                tc.CONSTRAINT_NAME, 
                tc.CONSTRAINT_TYPE
              FROM 
                information_schema.TABLE_CONSTRAINTS tc
              WHERE 
                tc.TABLE_SCHEMA = DATABASE()
                AND tc.CONSTRAINT_TYPE IN ('UNIQUE', 'FOREIGN KEY')";
    
    $result = $conn->query($query);
    
    if ($result->rowCount() > 0) {
        echo "Found constraints:\n";
        echo "--------------------\n";
        
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "Table: " . $row['TABLE_NAME'] . ", Constraint: " . $row['CONSTRAINT_NAME'] . ", Type: " . $row['CONSTRAINT_TYPE'] . "\n";
        }
    } else {
        echo "No constraints found in the database.\n";
    }
    
    // Check if approved table exists
    $tableExists = $conn->query("SHOW TABLES LIKE 'approved'")->rowCount() > 0;
    
    if ($tableExists) {
        echo "\nRemoving constraints from approved table...\n";
        
        // Get the table structure
        $result = $conn->query("DESCRIBE approved");
        echo "Current approved table structure:\n";
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo $row['Field'] . " - " . $row['Type'] . " - " . ($row['Null'] === 'YES' ? 'NULL' : 'NOT NULL') . "\n";
        }
        
        // Get indexes
        $result = $conn->query("SHOW INDEX FROM approved");
        echo "\nCurrent indexes on approved table:\n";
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "Index: " . $row['Key_name'] . ", Column: " . $row['Column_name'] . ", Unique: " . ($row['Non_unique'] == 0 ? 'Yes' : 'No') . "\n";
        }
        
        // Try to drop indexes
        try {
            $conn->exec("ALTER TABLE approved DROP INDEX IF EXISTS brand_name");
            echo "Dropped index brand_name if it existed.\n";
        } catch (PDOException $e) {
            echo "Error dropping index brand_name: " . $e->getMessage() . "\n";
        }
        
        try {
            $conn->exec("ALTER TABLE approved DROP INDEX IF EXISTS brand");
            echo "Dropped index brand if it existed.\n";
        } catch (PDOException $e) {
            echo "Error dropping index brand: " . $e->getMessage() . "\n";
        }
        
        try {
            $conn->exec("ALTER TABLE approved DROP INDEX IF EXISTS brand_unique");
            echo "Dropped index brand_unique if it existed.\n";
        } catch (PDOException $e) {
            echo "Error dropping index brand_unique: " . $e->getMessage() . "\n";
        }
        
        try {
            $conn->exec("ALTER TABLE approved DROP INDEX IF EXISTS unique_brand");
            echo "Dropped index unique_brand if it existed.\n";
        } catch (PDOException $e) {
            echo "Error dropping index unique_brand: " . $e->getMessage() . "\n";
        }
        
        try {
            $conn->exec("ALTER TABLE approved DROP INDEX IF EXISTS idx_brand_name");
            echo "Dropped index idx_brand_name if it existed.\n";
        } catch (PDOException $e) {
            echo "Error dropping index idx_brand_name: " . $e->getMessage() . "\n";
        }
        
        try {
            $conn->exec("ALTER TABLE approved DROP INDEX IF EXISTS idx_brand");
            echo "Dropped index idx_brand if it existed.\n";
        } catch (PDOException $e) {
            echo "Error dropping index idx_brand: " . $e->getMessage() . "\n";
        }
        
        // Modify the brand_name column to allow duplicates
        try {
            $conn->exec("ALTER TABLE approved MODIFY COLUMN brand_name VARCHAR(100)");
            echo "Modified brand_name column to remove UNIQUE constraint.\n";
        } catch (PDOException $e) {
            echo "Error modifying brand_name column: " . $e->getMessage() . "\n";
        }
    } else {
        echo "\napproved table does not exist.\n";
    }
    
    // Check if products table exists
    $tableExists = $conn->query("SHOW TABLES LIKE 'products'")->rowCount() > 0;
    
    if ($tableExists) {
        echo "\nRemoving constraints from products table...\n";
        
        // Get indexes
        $result = $conn->query("SHOW INDEX FROM products");
        echo "Current indexes on products table:\n";
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "Index: " . $row['Key_name'] . ", Column: " . $row['Column_name'] . ", Unique: " . ($row['Non_unique'] == 0 ? 'Yes' : 'No') . "\n";
        }
        
        // Try to drop indexes
        try {
            $conn->exec("ALTER TABLE products DROP INDEX IF EXISTS brand");
            echo "Dropped index brand if it existed.\n";
        } catch (PDOException $e) {
            echo "Error dropping index brand: " . $e->getMessage() . "\n";
        }
        
        try {
            $conn->exec("ALTER TABLE products DROP INDEX IF EXISTS brand_name");
            echo "Dropped index brand_name if it existed.\n";
        } catch (PDOException $e) {
            echo "Error dropping index brand_name: " . $e->getMessage() . "\n";
        }
        
        // Modify the brand column
        try {
            $conn->exec("ALTER TABLE products MODIFY COLUMN brand VARCHAR(100)");
            echo "Modified brand column to remove UNIQUE constraint.\n";
        } catch (PDOException $e) {
            echo "Error modifying brand column: " . $e->getMessage() . "\n";
        }
    } else {
        echo "\nproducts table does not exist.\n";
    }
    
    // Check if inventory table exists
    $tableExists = $conn->query("SHOW TABLES LIKE 'inventory'")->rowCount() > 0;
    
    if ($tableExists) {
        echo "\nRemoving constraints from inventory table...\n";
        
        // Try to drop indexes
        try {
            $conn->exec("ALTER TABLE inventory DROP INDEX IF EXISTS brand_name");
            echo "Dropped index brand_name if it existed.\n";
        } catch (PDOException $e) {
            echo "Error dropping index brand_name: " . $e->getMessage() . "\n";
        }
        
        try {
            $conn->exec("ALTER TABLE inventory DROP INDEX IF EXISTS brand");
            echo "Dropped index brand if it existed.\n";
        } catch (PDOException $e) {
            echo "Error dropping index brand: " . $e->getMessage() . "\n";
        }
        
        // Modify the brand_name column
        try {
            $conn->exec("ALTER TABLE inventory MODIFY COLUMN brand_name VARCHAR(100)");
            echo "Modified brand_name column to remove UNIQUE constraint.\n";
        } catch (PDOException $e) {
            echo "Error modifying brand_name column: " . $e->getMessage() . "\n";
        }
    } else {
        echo "\ninventory table does not exist.\n";
    }
    
    // Commit transaction
    $conn->commit();
    
    echo "\nAll constraints have been removed successfully!\n";
    
} catch (PDOException $e) {
    // Rollback transaction on error
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    echo "Error: " . $e->getMessage() . "\n";
}
?>
