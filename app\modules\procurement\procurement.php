<?php
session_start();
require_once '../../config/config.php';
require_once '../../includes/auth_check.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../auth/login.php");
    exit();
}

$user_id = $_SESSION['user_id'];
$user_role = isset($_SESSION['role']) ? $_SESSION['role'] : '';

// Get user's department
$dept_sql = "SELECT d.* FROM departments d
             JOIN users u ON u.department_id = d.id
             WHERE u.id = $user_id";
$dept_result = $conn->query($dept_sql);
$department = $dept_result->fetch(PDO::FETCH_ASSOC);

// Get all departments for dropdown
$departments_sql = "SELECT * FROM departments";
$departments_result = $conn->query($departments_sql);

// Get all brands
// $brands_sql = "SELECT DISTINCT brand FROM products ORDER BY brand";
// $brands_result = $conn->query($brands_sql);

// Fetch suppliers
$suppliers = [];
$suppliers_result = $conn->query("SELECT id, supplier_name FROM suppliers ORDER BY supplier_name ASC");
if ($suppliers_result) {
    $suppliers = $suppliers_result->fetchAll(PDO::FETCH_ASSOC);
}

// Fetch brands
$brands = [];
$brands_result = $conn->query("SELECT id, brand_name FROM brand ORDER BY brand_name ASC");
if ($brands_result) {
    $brands = $brands_result->fetchAll(PDO::FETCH_ASSOC);
}

// Fetch spice categories
$categories = [];
$categories_result = $conn->query("SELECT id, categories_name FROM categories ORDER BY categories_name ASC");
if ($categories_result) {
    $categories = $categories_result->fetchAll(PDO::FETCH_ASSOC);
}

// Fetch measurement units
$units = [];
$check_units_table = $conn->query("SHOW TABLES LIKE 'measurement_units'");
if ($check_units_table->rowCount() > 0) {
    $units_result = $conn->query("SELECT id, unit_name, unit_symbol FROM measurement_units ORDER BY unit_name ASC");
    if ($units_result) {
        $units = $units_result->fetchAll(PDO::FETCH_ASSOC);
    }
}

// Fetch pack types
$pack_types = [];
$check_packs_table = $conn->query("SHOW TABLES LIKE 'pack_types'");
if ($check_packs_table->rowCount() > 0) {
    $packs_result = $conn->query("SELECT id, pack_name FROM pack_types ORDER BY pack_name ASC");
    if ($packs_result) {
        $pack_types = $packs_result->fetchAll(PDO::FETCH_ASSOC);
    }
}

// Process new order
if (isset($_POST['create_order'])) {
    $department_id = (int)$_POST['department_id'];
    $notes = $_POST['notes'];

    // Generate unique order number
    $order_number = 'PO-' . date('Ymd') . '-' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

    // Start transaction
    $conn->beginTransaction();

    try {
        // Insert order
        $order_sql = "INSERT INTO procurement_orders (order_number, requested_by, department_id, notes) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($order_sql);
        $stmt->execute([$order_number, $user_id, $department_id, $notes]);
        $order_id = $conn->lastInsertId();

        // Insert order items
        foreach ($_POST['items'] as $index => $item) {
            $product_name = $item['product_name'];
            $brand_name = $item['brand_name'];
            $quantity = (int)$item['quantity'];
            $unit_price = (float)$item['unit_price'];
            $prod_measure = $item['prod_measure'];
            $pack_type = $item['pack_type'];
            $expiry_date = $item['expiry_date'];
            $delivered_date = $item['delivered_date'];
            $country = $item['country'];
            $batch_code = $item['batch_code'];
            // Handle image upload
            $prod_image = null;
            if (isset($_FILES['items']['name'][$index]['prod_image']) && $_FILES['items']['error'][$index]['prod_image'] == 0) {
                $img_name = $_FILES['items']['name'][$index]['prod_image'];
                $img_tmp = $_FILES['items']['tmp_name'][$index]['prod_image'];
                $img_dest = 'uploads/' . uniqid() . '_' . basename($img_name);
                move_uploaded_file($img_tmp, $img_dest);
                $prod_image = basename($img_dest);
            }
            // Validation for brand
            if (empty($brand_name)) {
                throw new Exception('Brand is required and must be valid.');
            }

            $item_sql = "INSERT INTO procurement_order_items (order_id, product_name, brand, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?, ?)";
            $item_stmt = $conn->prepare($item_sql);
            $item_stmt->execute([$order_id, $product_name, $brand_name, $quantity, $unit_price, $quantity * $unit_price]);

            // Insert into pending table for PAR.php
            $pending_sql = "INSERT INTO pending (prod_image, prod_name, brand_name, price, prod_measure, pack_type, expiry_date, delivered_date, country, batch_code, stocks, status, order_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $pending_stmt = $conn->prepare($pending_sql);
            $pending_stmt->execute([
                $prod_image,
                $product_name,
                $brand_name,
                $unit_price,
                $prod_measure,
                $pack_type,
                $expiry_date,
                $delivered_date,
                $country,
                $batch_code,
                $quantity,
                'pending',
                $order_id
            ]);
        }

        // Update order total
        $total_sql = "UPDATE procurement_orders SET total_amount = (
                        SELECT SUM(total_price) FROM procurement_order_items WHERE order_id = $order_id
                     ) WHERE id = $order_id";
        $conn->query($total_sql);

        // Set initial status based on user role
        $initial_status = ($user_role == 'finance') ? 'pending_procurement' : 'pending_finance';
        $status_sql = "UPDATE procurement_orders SET status = '$initial_status' WHERE id = $order_id";
        $conn->query($status_sql);

        $conn->commit();
        $success_message = "Order created successfully";
    } catch (Exception $e) {
        $conn->rollBack();
        $error_message = "Error creating order: " . $e->getMessage();
    }
}

// Get orders based on user role and department
$orders_sql = "SELECT po.*, u.first_name, u.last_name, d.name as department_name
               FROM procurement_orders po
               JOIN users u ON po.requested_by = u.id
               JOIN departments d ON po.department_id = d.id
               WHERE 1=1";

if ($user_role == 'department_head') {
    $orders_sql .= " AND po.department_id = {$department['id']}";
} elseif ($user_role == 'finance') {
    $orders_sql .= " AND po.status IN ('pending_finance', 'approved')";
} elseif ($user_role == 'procurement') {
    $orders_sql .= " AND po.status IN ('pending_procurement', 'approved')";
}

$orders_sql .= " ORDER BY po.created_at DESC";
$orders_result = $conn->query($orders_sql);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Procurement Management</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
    <style>
        .order-card {
            margin-bottom: 1rem;
            border: 1px solid #ddd;
            border-radius: 0.5rem;
            padding: 1rem;
        }

        .status-badge {
            font-size: 0.9rem;
            padding: 0.5rem;
            border-radius: 0.25rem;
        }

        .item-row {
            margin-bottom: 0.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }

        /* Title styling with orange background */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            background-color: #f15b31;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: inline-block;
            min-width: 200px;
            text-align: center;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Procurement Management</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="spices.php">Manage Spices</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="brands.php">Manage Brands</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="suppliers.php">Manage Suppliers</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="measurement_units.php">Manage Units</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pack_types.php">Manage Pack Types</a>
                    </li>
                </ul>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link active" href="procurement.php">Orders</a>
                    <a class="nav-link" href="../auth/logout.php">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success">
                <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger">
                <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <div class="row mb-4">
            <div class="col">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createOrderModal">
                    Create New Order
                </button>
            </div>
        </div>

        <div class="row">
            <div class="col">
                <h3>Orders</h3>
                <?php while ($order = $orders_result->fetch(PDO::FETCH_ASSOC)): ?>
                    <div class="order-card">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>Order #<?php echo htmlspecialchars($order['order_number']); ?></h5>
                            <span class="status-badge bg-<?php
                                                            echo $order['status'] == 'approved' ? 'success' : ($order['status'] == 'rejected' ? 'danger' : 'warning');
                                                            ?>">
                                <?php echo ucfirst(str_replace('_', ' ', $order['status'])); ?>
                            </span>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p><strong>Requested by:</strong> <?php echo htmlspecialchars($order['first_name'] . ' ' . $order['last_name']); ?></p>
                                <p><strong>Department:</strong> <?php echo htmlspecialchars($order['department_name']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Date:</strong> <?php echo date('M d, Y', strtotime($order['order_date'])); ?></p>
                                <p><strong>Total Amount:</strong> $<?php echo number_format($order['total_amount'], 2); ?></p>
                            </div>
                        </div>

                        <?php
                        // Get order items
                        $items_sql = "SELECT * FROM procurement_order_items WHERE order_id = {$order['id']}";
                        $items_result = $conn->query($items_sql);
                        ?>

                        <div class="order-items">
                            <h6>Items:</h6>
                            <?php while ($item = $items_result->fetch(PDO::FETCH_ASSOC)): ?>
                                <div class="item-row">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <?php echo htmlspecialchars($item['product_name']); ?>
                                            (<?php echo htmlspecialchars($item['brand']); ?>)
                                        </div>
                                        <div class="col-md-2">
                                            Qty: <?php echo $item['quantity']; ?>
                                        </div>
                                        <div class="col-md-3">
                                            Price: $<?php echo number_format($item['unit_price'], 2); ?>
                                        </div>
                                        <div class="col-md-3">
                                            Total: $<?php echo number_format($item['total_price'], 2); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>

                        <?php if ($order['status'] == 'pending_finance' && $user_role == 'finance'): ?>
                            <div class="mt-3">
                                <button class="btn btn-success btn-sm" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'approved')">Approve</button>
                                <button class="btn btn-danger btn-sm" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'rejected')">Reject</button>
                            </div>
                        <?php elseif ($order['status'] == 'pending_procurement' && $user_role == 'procurement'): ?>
                            <div class="mt-3">
                                <button class="btn btn-success btn-sm" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'approved')">Approve</button>
                                <button class="btn btn-danger btn-sm" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'rejected')">Reject</button>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endwhile; ?>
            </div>
        </div>
    </div>

    <!-- Create Order Modal -->
    <div class="modal fade" id="createOrderModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Order</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="department_id" class="form-label">Department</label>
                            <select class="form-select" id="department_id" name="department_id" required>
                                <?php while ($dept = $departments_result->fetch(PDO::FETCH_ASSOC)): ?>
                                    <option value="<?php echo $dept['id']; ?>" <?php echo ($department && $department['id'] == $dept['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($dept['name']); ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>

                        <div id="orderItems">
                            <h6>Order Items</h6>
                            <div class="item-entry mb-3">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="items[0][supplier_id]" class="form-label">Supplier</label>
                                        <select class="form-select" id="items[0][supplier_id]" name="items[0][supplier_id]" required>
                                            <option value="">Select Supplier</option>
                                            <?php foreach ($suppliers as $supplier): ?>
                                                <option value="<?php echo $supplier['id']; ?>"><?php echo htmlspecialchars($supplier['supplier_name']); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="items[0][brand_name]" class="form-label">Brand</label>
                                        <select class="form-select" id="items[0][brand_name]" name="items[0][brand_name]" required>
                                            <option value="">Select Brand</option>
                                            <?php foreach ($brands as $brand): ?>
                                                <option value="<?php echo htmlspecialchars($brand['brand_name']); ?>"><?php echo htmlspecialchars($brand['brand_name']); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="items[0][category_id]" class="form-label">Spice Category</label>
                                        <select class="form-select" id="items[0][category_id]" name="items[0][category_id]" required>
                                            <option value="">Select Category</option>
                                            <?php foreach ($categories as $cat): ?>
                                                <option value="<?php echo $cat['id']; ?>"><?php echo htmlspecialchars($cat['categories_name']); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="items[0][product_name]" class="form-label">Product Name</label>
                                        <input type="text" class="form-control" id="items[0][product_name]" name="items[0][product_name]" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="items[0][quantity]" class="form-label">Quantity</label>
                                        <input type="number" class="form-control" id="items[0][quantity]" name="items[0][quantity]" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="items[0][unit_price]" class="form-label">Unit Price</label>
                                        <input type="number" step="0.01" class="form-control" id="items[0][unit_price]" name="items[0][unit_price]" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="items[0][prod_measure]" class="form-label">Measurement</label>
                                        <select class="form-select" id="items[0][prod_measure]" name="items[0][prod_measure]" required>
                                            <option value="">Select Unit</option>
                                            <?php foreach ($units as $unit): ?>
                                                <option value="<?php echo htmlspecialchars($unit['unit_name'] . ' (' . $unit['unit_symbol'] . ')'); ?>"><?php echo htmlspecialchars($unit['unit_name'] . ' (' . $unit['unit_symbol'] . ')'); ?></option>
                                            <?php endforeach; ?>
                                            <?php if (empty($units)): ?>
                                                <option value="kg">Kilogram (kg)</option>
                                                <option value="g">Gram (g)</option>
                                                <option value="l">Liter (l)</option>
                                                <option value="ml">Milliliter (ml)</option>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="items[0][pack_type]" class="form-label">Package Type</label>
                                        <select class="form-select" id="items[0][pack_type]" name="items[0][pack_type]" required>
                                            <option value="">Select Package</option>
                                            <?php foreach ($pack_types as $pack): ?>
                                                <option value="<?php echo htmlspecialchars($pack['pack_name']); ?>"><?php echo htmlspecialchars($pack['pack_name']); ?></option>
                                            <?php endforeach; ?>
                                            <?php if (empty($pack_types)): ?>
                                                <option value="Box">Box</option>
                                                <option value="Bottle">Bottle</option>
                                                <option value="Bag">Bag</option>
                                                <option value="Jar">Jar</option>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="items[0][expiry_date]" class="form-label">Expiry Date</label>
                                        <input type="date" class="form-control" id="items[0][expiry_date]" name="items[0][expiry_date]" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="items[0][delivered_date]" class="form-label">Delivered Date</label>
                                        <input type="date" class="form-control" id="items[0][delivered_date]" name="items[0][delivered_date]" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="items[0][country]" class="form-label">Country</label>
                                        <input type="text" class="form-control" id="items[0][country]" name="items[0][country]" value="Philippines" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="items[0][batch_code]" class="form-label">Batch Code</label>
                                        <input type="text" class="form-control" id="items[0][batch_code]" name="items[0][batch_code]" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="items[0][prod_image]" class="form-label">Product Image</label>
                                        <input type="file" class="form-control" id="items[0][prod_image]" name="items[0][prod_image]" accept="image/*">
                                    </div>
                                    <div class="col-md-1">
                                        <button type="button" class="btn btn-danger btn-sm remove-item">×</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="button" class="btn btn-secondary btn-sm" id="addItem">Add Item</button>

                        <div class="mb-3 mt-3">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" name="create_order" class="btn btn-primary">Create Order</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add new item row
        document.getElementById('addItem').addEventListener('click', function() {
            const itemsContainer = document.getElementById('orderItems');
            const itemCount = itemsContainer.getElementsByClassName('item-entry').length;
            const newItem = document.querySelector('.item-entry').cloneNode(true);

            // Update input names, IDs, and labels
            newItem.querySelectorAll('input').forEach(input => {
                const newName = input.name.replace('[0]', `[${itemCount}]`);
                input.name = newName;
                input.id = newName;

                // Keep the Philippines default for country field
                if (input.name.includes('country')) {
                    input.value = 'Philippines';
                } else {
                    input.value = '';
                }

                // Find and update the associated label
                const label = input.previousElementSibling;
                if (label && label.tagName === 'LABEL') {
                    label.setAttribute('for', newName);
                }
            });

            newItem.querySelectorAll('select').forEach(select => {
                const newName = select.name.replace('[0]', `[${itemCount}]`);
                select.name = newName;
                select.id = newName;
                select.selectedIndex = 0; // Always select the first (empty) option

                // Find and update the associated label
                const label = select.previousElementSibling;
                if (label && label.tagName === 'LABEL') {
                    label.setAttribute('for', newName);
                }
            });

            itemsContainer.appendChild(newItem);
        });

        // Remove item row
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-item')) {
                const itemsContainer = document.getElementById('orderItems');
                if (itemsContainer.getElementsByClassName('item-entry').length > 1) {
                    e.target.closest('.item-entry').remove();
                }
            }
        });

        // Update order status
        function updateOrderStatus(orderId, status) {
            if (confirm(`Are you sure you want to ${status} this order?`)) {
                fetch('update_order_status.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `order_id=${orderId}&status=${status}`
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert('Error updating order status');
                        }
                    });
            }
        }
    </script>

    <!-- Back to Dashboard Button -->
    <div class="text-center mt-4 mb-4">
        <a href="../admin/admin_dashboard.php" class="btn" style="background-color: #f15b31; color: white;">Back to Dashboard</a>
    </div>
</body>

</html>