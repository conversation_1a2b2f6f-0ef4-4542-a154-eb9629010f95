<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database configuration
require_once 'config/database.php';

echo "<h1>Create Test User</h1>";

// Default test user credentials
$test_username = 'testuser';
$test_password = 'password123';
$test_email = '<EMAIL>';
$test_first_name = 'Test';
$test_last_name = 'User';
$test_role_id = 1; // Admin role

// Check if user already exists
$check_sql = "SELECT * FROM users WHERE username = :username OR email = :email LIMIT 1";
$check_stmt = $conn->prepare($check_sql);
$check_stmt->execute([
    ':username' => $test_username,
    ':email' => $test_email
]);
$existing_user = $check_stmt->fetch();

if ($existing_user) {
    echo "<p>Test user already exists:</p>";
    echo "<ul>";
    echo "<li>Username: " . htmlspecialchars($existing_user['username']) . "</li>";
    echo "<li>Email: " . htmlspecialchars($existing_user['email']) . "</li>";
    echo "<li>Role ID: " . htmlspecialchars($existing_user['role_id']) . "</li>";
    echo "</ul>";
    
    // Update password if requested
    if (isset($_GET['update']) && $_GET['update'] === 'yes') {
        $password_hash = password_hash($test_password, PASSWORD_DEFAULT);
        $update_sql = "UPDATE users SET password = :password WHERE id = :id";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->execute([
            ':password' => $password_hash,
            ':id' => $existing_user['id']
        ]);
        
        echo "<p>Password updated for existing user. New hash: " . $password_hash . "</p>";
    } else {
        echo "<p>To update the password for this user, <a href='?update=yes'>click here</a>.</p>";
    }
} else {
    // Create new test user
    try {
        $password_hash = password_hash($test_password, PASSWORD_DEFAULT);
        
        // Check if the users table has the expected columns
        $columns_sql = "DESCRIBE users";
        $columns_stmt = $conn->query($columns_sql);
        $columns = $columns_stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<p>Available columns in users table:</p>";
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>" . $column . "</li>";
        }
        echo "</ul>";
        
        // Prepare SQL based on available columns
        $sql = "INSERT INTO users (username, password, email, first_name, last_name";
        
        // Add role_id if it exists
        if (in_array('role_id', $columns)) {
            $sql .= ", role_id";
        }
        
        $sql .= ") VALUES (:username, :password, :email, :first_name, :last_name";
        
        // Add role_id value if it exists
        if (in_array('role_id', $columns)) {
            $sql .= ", :role_id";
        }
        
        $sql .= ")";
        
        $stmt = $conn->prepare($sql);
        
        $params = [
            ':username' => $test_username,
            ':password' => $password_hash,
            ':email' => $test_email,
            ':first_name' => $test_first_name,
            ':last_name' => $test_last_name
        ];
        
        // Add role_id param if it exists
        if (in_array('role_id', $columns)) {
            $params[':role_id'] = $test_role_id;
        }
        
        $stmt->execute($params);
        
        echo "<p>Test user created successfully:</p>";
        echo "<ul>";
        echo "<li>Username: " . htmlspecialchars($test_username) . "</li>";
        echo "<li>Password: " . htmlspecialchars($test_password) . "</li>";
        echo "<li>Email: " . htmlspecialchars($test_email) . "</li>";
        echo "<li>Role ID: " . htmlspecialchars($test_role_id) . "</li>";
        echo "</ul>";
        
        echo "<p>Password hash: " . $password_hash . "</p>";
    } catch (PDOException $e) {
        echo "<p>Error creating test user: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>Login with Test User</h2>";
echo "<p>You can now try to login with the following credentials:</p>";
echo "<ul>";
echo "<li>Username: " . htmlspecialchars($test_username) . "</li>";
echo "<li>Password: " . htmlspecialchars($test_password) . "</li>";
echo "</ul>";

echo "<p><a href='pages/account/login.php'>Go to Login Page</a></p>";
?>
