<?php
session_start();
require_once '../../config/config.php';

// Set the content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

$results = [
    'success' => true,
    'message' => 'Inventory table fixed successfully',
    'actions' => []
];

try {
    // Check if inventory table exists
    $inventory_exists = $conn->query("SHOW TABLES LIKE 'inventory'")->rowCount() > 0;

    // First, let's just drop the inventory table completely and recreate it
    if ($inventory_exists) {
        // Drop the table
        $conn->exec("DROP TABLE inventory");
        $results['actions'][] = "Dropped existing inventory table";
    }

    // Create a fresh inventory table without constraints
    $conn->exec("CREATE TABLE inventory (
        id INT AUTO_INCREMENT PRIMARY KEY,
        prod_image VARCHAR(255),
        prod_name VARCHAR(100) NOT NULL,
        brand_name VARCHAR(100) NOT NULL,
        price DECIMAL(10,2) NOT NULL,
        prod_measure VARCHAR(50),
        pack_type VARCHAR(50),
        expiry_date DATETIME,
        delivered_date DATETIME,
        country VARCHAR(100),
        batch_code VARCHAR(50),
        stocks DECIMAL(10,2) NOT NULL,
        status VARCHAR(20) NOT NULL DEFAULT 'approved',
        order_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB");
    $results['actions'][] = "Created fresh inventory table without constraints";

    // Let's also check for any tables that reference inventory
    $referencing_tables = $conn->query("
        SELECT
            tc.TABLE_NAME,
            tc.CONSTRAINT_NAME
        FROM
            information_schema.TABLE_CONSTRAINTS tc
        JOIN
            information_schema.KEY_COLUMN_USAGE kcu
        ON
            tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
        WHERE
            tc.CONSTRAINT_TYPE = 'FOREIGN KEY'
            AND tc.TABLE_SCHEMA = DATABASE()
            AND kcu.REFERENCED_TABLE_NAME = 'inventory'
    ")->fetchAll(PDO::FETCH_ASSOC);

    // Drop constraints on tables that reference inventory
    foreach ($referencing_tables as $table) {
        $table_name = $table['TABLE_NAME'];
        $constraint_name = $table['CONSTRAINT_NAME'];
        try {
            $conn->exec("ALTER TABLE `$table_name` DROP FOREIGN KEY `$constraint_name`");
            $results['actions'][] = "Dropped foreign key constraint on $table_name that referenced inventory";
        } catch (PDOException $e) {
            $results['actions'][] = "Failed to drop constraint on $table_name: " . $e->getMessage();
        }
    }
    // Also check and fix the products table
    $products_exists = $conn->query("SHOW TABLES LIKE 'products'")->rowCount() > 0;

    if ($products_exists) {
        // Get constraints on products table
        $constraints = $conn->query("
            SELECT
                tc.CONSTRAINT_NAME,
                kcu.COLUMN_NAME,
                kcu.REFERENCED_TABLE_NAME,
                kcu.REFERENCED_COLUMN_NAME
            FROM
                information_schema.TABLE_CONSTRAINTS tc
            JOIN
                information_schema.KEY_COLUMN_USAGE kcu
            ON
                tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
            WHERE
                tc.CONSTRAINT_TYPE = 'FOREIGN KEY'
                AND tc.TABLE_SCHEMA = DATABASE()
                AND tc.TABLE_NAME = 'products'
        ")->fetchAll(PDO::FETCH_ASSOC);

        // Drop all foreign key constraints
        foreach ($constraints as $constraint) {
            $constraint_name = $constraint['CONSTRAINT_NAME'];
            try {
                $conn->exec("ALTER TABLE products DROP FOREIGN KEY `$constraint_name`");
                $results['actions'][] = "Dropped foreign key constraint from products table: $constraint_name";
            } catch (PDOException $e) {
                $results['actions'][] = "Failed to drop constraint from products: " . $e->getMessage();
            }
        }
    }



    // Check for any other tables with foreign key constraints to products or inventory
    $other_constraints = $conn->query("
        SELECT
            tc.TABLE_NAME,
            tc.CONSTRAINT_NAME,
            kcu.COLUMN_NAME,
            kcu.REFERENCED_TABLE_NAME,
            kcu.REFERENCED_COLUMN_NAME
        FROM
            information_schema.TABLE_CONSTRAINTS tc
        JOIN
            information_schema.KEY_COLUMN_USAGE kcu
        ON
            tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
        WHERE
            tc.CONSTRAINT_TYPE = 'FOREIGN KEY'
            AND tc.TABLE_SCHEMA = DATABASE()
            AND (kcu.REFERENCED_TABLE_NAME = 'products' OR kcu.REFERENCED_TABLE_NAME = 'inventory')
    ")->fetchAll(PDO::FETCH_ASSOC);

    // Drop these constraints too
    foreach ($other_constraints as $constraint) {
        $table_name = $constraint['TABLE_NAME'];
        $constraint_name = $constraint['CONSTRAINT_NAME'];
        $conn->exec("ALTER TABLE `$table_name` DROP FOREIGN KEY `$constraint_name`");
        $results['actions'][] = "Dropped foreign key constraint from $table_name: $constraint_name";
    }
} catch (PDOException $e) {
    $results['success'] = false;
    $results['message'] = 'Error fixing inventory table: ' . $e->getMessage();
    $results['error'] = $e->getMessage();
}

// Return the results
echo json_encode($results, JSON_PRETTY_PRINT);
