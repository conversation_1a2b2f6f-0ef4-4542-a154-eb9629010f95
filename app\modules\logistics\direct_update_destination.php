<?php
session_start();
require_once '../../config/config.php';

// Set the content type to JSON
header('Content-Type: application/json');

// Get parameters from request
$order_id = isset($_POST['order_id']) ? (int)$_POST['order_id'] : 0;
$latitude = isset($_POST['latitude']) ? (float)$_POST['latitude'] : null;
$longitude = isset($_POST['longitude']) ? (float)$_POST['longitude'] : null;

// Validate input
if (!$order_id) {
    // Use a default order ID if none is provided
    $order_id = 1;
    error_log("No order_id provided, using default: 1");
}

if ($latitude === null || $longitude === null) {
    // Use default coordinates if none are provided
    $latitude = 14.3294;
    $longitude = 120.9367;
    error_log("No coordinates provided, using defaults: $latitude, $longitude");
}

// Log the parameters we're using
error_log("direct_update_destination.php: Using order_id=$order_id, latitude=$latitude, longitude=$longitude");

try {
    // First, ensure the order exists
    $check_sql = "SELECT COUNT(*) FROM order_requests WHERE id = :order_id";
    $stmt = $conn->prepare($check_sql);
    $stmt->bindValue(':order_id', $order_id, PDO::PARAM_INT);
    $stmt->execute();
    $order_exists = (int)$stmt->fetchColumn() > 0;

    if (!$order_exists) {
        // Create a minimal order record
        $create_sql = "INSERT INTO order_requests (id, status) VALUES (:id, 'processing')";
        try {
            $stmt = $conn->prepare($create_sql);
            $stmt->bindValue(':id', $order_id, PDO::PARAM_INT);
            $stmt->execute();
            error_log("Created minimal order record with ID: $order_id");
        } catch (PDOException $e) {
            error_log("Error creating minimal order: " . $e->getMessage());
            // Continue anyway - we'll try to update the coordinates
        }
    }

    // Check if destination_coordinates columns exist
    $columns = $conn->query("SHOW COLUMNS FROM order_requests LIKE 'destination_latitude'");
    if ($columns->rowCount() === 0) {
        // Add destination coordinates columns if they don't exist
        try {
            $conn->exec("ALTER TABLE order_requests
                        ADD COLUMN destination_latitude DECIMAL(10, 8) DEFAULT NULL,
                        ADD COLUMN destination_longitude DECIMAL(11, 8) DEFAULT NULL");
            error_log("Added destination coordinate columns to order_requests table");
        } catch (PDOException $e) {
            error_log("Error adding coordinate columns: " . $e->getMessage());
            // Continue anyway - we'll try a different approach
        }
    }

    // Try to update the order with destination coordinates
    try {
        $update_sql = "UPDATE order_requests
                      SET destination_latitude = :latitude,
                          destination_longitude = :longitude
                      WHERE id = :order_id";
        $stmt = $conn->prepare($update_sql);
        $stmt->bindValue(':latitude', $latitude, PDO::PARAM_STR);
        $stmt->bindValue(':longitude', $longitude, PDO::PARAM_STR);
        $stmt->bindValue(':order_id', $order_id, PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            echo json_encode(['success' => true, 'message' => 'Destination updated successfully']);
        } else {
            // If update didn't affect any rows, try a direct insert
            $conn->exec("INSERT INTO order_requests (id, destination_latitude, destination_longitude) 
                        VALUES ($order_id, $latitude, $longitude)
                        ON DUPLICATE KEY UPDATE 
                        destination_latitude = VALUES(destination_latitude),
                        destination_longitude = VALUES(destination_longitude)");

            echo json_encode(['success' => true, 'message' => 'Destination inserted successfully']);
        }
    } catch (PDOException $e) {
        error_log("Error updating destination: " . $e->getMessage());

        // As a last resort, store in a separate table
        try {
            // Check if our fallback table exists
            $table_check = $conn->query("SHOW TABLES LIKE 'delivery_destinations'");
            if ($table_check->rowCount() === 0) {
                // Create the fallback table
                $conn->exec("CREATE TABLE delivery_destinations (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            order_id INT NOT NULL,
                            latitude DECIMAL(10, 8) NOT NULL,
                            longitude DECIMAL(11, 8) NOT NULL,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )");
            }

            // Insert into fallback table
            $fallback_sql = "INSERT INTO delivery_destinations (order_id, latitude, longitude)
                            VALUES (:order_id, :latitude, :longitude)";
            $stmt = $conn->prepare($fallback_sql);
            $stmt->bindValue(':order_id', $order_id, PDO::PARAM_INT);
            $stmt->bindValue(':latitude', $latitude, PDO::PARAM_STR);
            $stmt->bindValue(':longitude', $longitude, PDO::PARAM_STR);
            $stmt->execute();

            echo json_encode(['success' => true, 'message' => 'Destination saved to fallback table']);
        } catch (PDOException $fallbackError) {
            error_log("Error saving to fallback table: " . $fallbackError->getMessage());
            echo json_encode(['success' => false, 'message' => 'All attempts to save destination failed']);
        }
    }
} catch (PDOException $e) {
    error_log("Error in direct_update_destination.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
