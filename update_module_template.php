<?php
/**
 * This script helps update module pages with the new template
 * It will:
 * 1. Create a backup of the original file
 * 2. Extract the main content from the original file
 * 3. Create a new file with the template and the extracted content
 */

// Configuration
$base_dir = __DIR__;
$modules_dir = $base_dir . '/app/modules';
$backup_dir = $base_dir . '/app/modules/backups';

// Create backup directory if it doesn't exist
if (!is_dir($backup_dir)) {
    mkdir($backup_dir, 0755, true);
}

// Function to update a module file
function updateModuleFile($file_path) {
    global $backup_dir;
    
    // Skip if file doesn't exist
    if (!file_exists($file_path)) {
        echo "File not found: $file_path\n";
        return false;
    }
    
    // Create backup
    $backup_path = $backup_dir . '/' . basename($file_path) . '.bak';
    copy($file_path, $backup_path);
    
    // Read file content
    $content = file_get_contents($file_path);
    
    // Extract PHP code at the beginning
    $php_start = strpos($content, '<?php');
    $php_end = strpos($content, '?>');
    
    if ($php_start === false || $php_end === false) {
        echo "Could not find PHP tags in file: $file_path\n";
        return false;
    }
    
    $php_code = substr($content, $php_start + 5, $php_end - $php_start - 5);
    
    // Extract HTML content
    $html_start = strpos($content, '<div class="container');
    $html_end = strrpos($content, '</div>');
    
    if ($html_start === false || $html_end === false) {
        echo "Could not find HTML content in file: $file_path\n";
        return false;
    }
    
    $html_content = substr($content, $html_start, $html_end - $html_start + 6);
    
    // Create new content with template
    $new_content = '<?php
' . $php_code . '
// Include the header template
require_once \'../../includes/header_template.php\';
?>

<!-- Page content -->
' . $html_content . '

<?php
// Include the footer template
require_once \'../../includes/footer_template.php\';
?>';
    
    // Write new content to file
    file_put_contents($file_path, $new_content);
    
    echo "Updated file: $file_path\n";
    return true;
}

// Function to process all module files
function updateAllModules() {
    global $modules_dir;
    
    // Get all module directories
    $module_dirs = glob($modules_dir . '/*', GLOB_ONLYDIR);
    
    foreach ($module_dirs as $dir) {
        // Skip auth module and backups
        if (basename($dir) === 'auth' || basename($dir) === 'backups') {
            continue;
        }
        
        // Get all PHP files in the directory
        $files = glob($dir . '/*.php');
        
        foreach ($files as $file) {
            // Skip files that are not main pages
            if (strpos(basename($file), '_') === 0 || 
                strpos(basename($file), 'ajax_') === 0 ||
                strpos(basename($file), 'api_') === 0) {
                continue;
            }
            
            updateModuleFile($file);
        }
    }
}

// Example usage:
// updateModuleFile('/path/to/module/file.php');
// updateAllModules();

echo "This is a helper script to update module files with the new template.\n";
echo "Please edit this file to specify which files to update, then run it.\n";
