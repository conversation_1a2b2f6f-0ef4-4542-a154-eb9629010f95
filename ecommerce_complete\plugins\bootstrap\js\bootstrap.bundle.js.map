{"version": 3, "file": "bootstrap.bundle.js", "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/popper.js/dist/esm/popper.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-target')\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href')\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let {\n    transitionDuration,\n    transitionDelay\n  } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = parseFloat(transitionDuration)\n  const floatTransitionDelay = parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (parseFloat(transitionDuration) + parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ?\n      'element' :\n      toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new Error(\n        `${componentName.toUpperCase()}: ` +\n        `Option \"${property}\" provided type \"${valueType}\" ` +\n        `but expected type \"${expectedTypes}\".`)\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nexport {\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst mapData = (() => {\n  const storeData = {}\n  let id = 1\n  return {\n    set(element, key, data) {\n      if (typeof element.bsKey === 'undefined') {\n        element.bsKey = {\n          key,\n          id\n        }\n        id++\n      }\n\n      storeData[element.bsKey.id] = data\n    },\n    get(element, key) {\n      if (!element || typeof element.bsKey === 'undefined') {\n        return null\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        return storeData[keyProperties.id]\n      }\n\n      return null\n    },\n    delete(element, key) {\n      if (typeof element.bsKey === 'undefined') {\n        return\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        delete storeData[keyProperties.id]\n        delete element.bsKey\n      }\n    }\n  }\n})()\n\nconst Data = {\n  setData(instance, key, data) {\n    mapData.set(instance, key, data)\n  },\n  getData(instance, key) {\n    return mapData.get(instance, key)\n  },\n  removeData(instance, key) {\n    mapData.delete(instance, key)\n  }\n}\n\nexport default Data\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = [\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n]\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.indexOf(namespace) > -1) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.charAt(0) === '.'\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.indexOf(handlerKey) > -1) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASSNAME_ALERT = 'alert'\nconst CLASSNAME_FADE = 'fade'\nconst CLASSNAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n\n    if (this._element) {\n      Data.setData(element, DATA_KEY, this)\n    }\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASSNAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASSNAME_SHOW)\n\n    if (!element.classList.contains(CLASSNAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler.one(element, TRANSITION_END, () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Alert.jQueryInterface\n    $.fn[NAME].Constructor = Alert\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Alert.jQueryInterface\n    }\n  }\n})\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.getData(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Button.jQueryInterface\n    $.fn[NAME].Constructor = Button\n\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Button.jQueryInterface\n    }\n  }\n})\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {\n      ...element.dataset\n    }\n\n    Object.keys(attributes).forEach(key => {\n      attributes[key] = normalizeData(attributes[key])\n    })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  matches(element, selector) {\n    return element.matches(selector)\n  },\n\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    const children = [].concat(...element.children)\n\n    return children.filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (this.matches(ancestor, selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (this.matches(next, selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_RIGHT = 'carousel-item-right'\nconst CLASS_NAME_LEFT = 'carousel-item-left'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-slide], [data-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-ride=\"carousel\"]'\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel {\n  constructor(element, config) {\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._element = element\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._items = null\n    this._config = null\n    this._element = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      if (event.touches && event.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.key) {\n      case ARROW_LEFT_KEY:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEY:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrevDirection && activeIndex === 0) ||\n                            (isNextDirection && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = SelectorEngine.find(SELECTOR_ACTIVE, this._indicatorsElement)\n      for (let i = 0; i < indicators.length; i++) {\n        indicators[i].classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        nextIndicator.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = parseInt(element.getAttribute('data-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || (activeElement &&\n      this._getItemByDirection(direction, activeElement))\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_LEFT\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_RIGHT\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler.one(activeElement, TRANSITION_END, () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(() => {\n          EventHandler.trigger(this._element, EVENT_SLID, {\n            relatedTarget: nextElement,\n            direction: eventDirectionName,\n            from: activeElementIndex,\n            to: nextElementIndex\n          })\n        }, 0)\n      })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.getData(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.getData(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Carousel.jQueryInterface\n    $.fn[NAME].Constructor = Carousel\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Carousel.jQueryInterface\n    }\n  }\n})\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element = element\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-target=\"#${element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.filter(elem => container !== elem)\n      activesData = tempActiveData[0] ? Data.getData(tempActiveData[0], DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.setData(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._parent = null\n    this._element = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.getData(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Collapse.jQueryInterface\n    $.fn[NAME].Constructor = Collapse\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Collapse.jQueryInterface\n    }\n  }\n})\n\nexport default Collapse\n", "/**!\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version 1.16.1\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && typeof navigator !== 'undefined';\n\nvar timeoutDuration = function () {\n  var longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\n  for (var i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n    if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n      return 1;\n    }\n  }\n  return 0;\n}();\n\nfunction microtaskDebounce(fn) {\n  var called = false;\n  return function () {\n    if (called) {\n      return;\n    }\n    called = true;\n    window.Promise.resolve().then(function () {\n      called = false;\n      fn();\n    });\n  };\n}\n\nfunction taskDebounce(fn) {\n  var scheduled = false;\n  return function () {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(function () {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nvar supportsMicroTasks = isBrowser && window.Promise;\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nvar debounce = supportsMicroTasks ? microtaskDebounce : taskDebounce;\n\n/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nfunction isFunction(functionToCheck) {\n  var getType = {};\n  return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n}\n\n/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  var window = element.ownerDocument.defaultView;\n  var css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n\n/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body;\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body;\n    case '#document':\n      return element.body;\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n\n  var _getStyleComputedProp = getStyleComputedProperty(element),\n      overflow = _getStyleComputedProp.overflow,\n      overflowX = _getStyleComputedProp.overflowX,\n      overflowY = _getStyleComputedProp.overflowY;\n\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n\n/**\n * Returns the reference node of the reference object, or the reference object itself.\n * @method\n * @memberof Popper.Utils\n * @param {Element|Object} reference - the reference element (the popper will be relative to this)\n * @returns {Element} parent\n */\nfunction getReferenceNode(reference) {\n  return reference && reference.referenceNode ? reference.referenceNode : reference;\n}\n\nvar isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nvar isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nfunction isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nfunction getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  var noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  var offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  var nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n\nfunction isOffsetContainer(element) {\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  var order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  var start = order ? element1 : element2;\n  var end = order ? element2 : element1;\n\n  // Get common ancestor container\n  var range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  var commonAncestorContainer = range.commonAncestorContainer;\n\n  // Both nodes are inside #document\n\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  var element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nfunction getScroll(element) {\n  var side = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'top';\n\n  var upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    var html = element.ownerDocument.documentElement;\n    var scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nfunction includeScroll(rect, element) {\n  var subtract = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var scrollTop = getScroll(element, 'top');\n  var scrollLeft = getScroll(element, 'left');\n  var modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n\n/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nfunction getBordersSize(styles, axis) {\n  var sideA = axis === 'x' ? 'Left' : 'Top';\n  var sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return parseFloat(styles['border' + sideA + 'Width']) + parseFloat(styles['border' + sideB + 'Width']);\n}\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(body['offset' + axis], body['scroll' + axis], html['client' + axis], html['offset' + axis], html['scroll' + axis], isIE(10) ? parseInt(html['offset' + axis]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Top' : 'Left')]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Bottom' : 'Right')]) : 0);\n}\n\nfunction getWindowSizes(document) {\n  var body = document.body;\n  var html = document.documentElement;\n  var computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle)\n  };\n}\n\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\n\n\n\n\nvar defineProperty = function (obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n};\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\n/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nfunction getClientRect(offsets) {\n  return _extends({}, offsets, {\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height\n  });\n}\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nfunction getBoundingClientRect(element) {\n  var rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      var scrollTop = getScroll(element, 'top');\n      var scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } else {\n      rect = element.getBoundingClientRect();\n    }\n  } catch (e) {}\n\n  var result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n\n  // subtract scrollbar size from sizes\n  var sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  var width = sizes.width || element.clientWidth || result.width;\n  var height = sizes.height || element.clientHeight || result.height;\n\n  var horizScrollbar = element.offsetWidth - width;\n  var vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    var styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n\nfunction getOffsetRectRelativeToArbitraryNode(children, parent) {\n  var fixedPosition = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var isIE10 = isIE(10);\n  var isHTML = parent.nodeName === 'HTML';\n  var childrenRect = getBoundingClientRect(children);\n  var parentRect = getBoundingClientRect(parent);\n  var scrollParent = getScrollParent(children);\n\n  var styles = getStyleComputedProperty(parent);\n  var borderTopWidth = parseFloat(styles.borderTopWidth);\n  var borderLeftWidth = parseFloat(styles.borderLeftWidth);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if (fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  var offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    var marginTop = parseFloat(styles.marginTop);\n    var marginLeft = parseFloat(styles.marginLeft);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (isIE10 && !fixedPosition ? parent.contains(scrollParent) : parent === scrollParent && scrollParent.nodeName !== 'BODY') {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  var excludeScroll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var html = element.ownerDocument.documentElement;\n  var relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  var width = Math.max(html.clientWidth, window.innerWidth || 0);\n  var height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  var scrollTop = !excludeScroll ? getScroll(html) : 0;\n  var scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  var offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width: width,\n    height: height\n  };\n\n  return getClientRect(offset);\n}\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {Boolean} answer to \"isFixed?\"\n */\nfunction isFixed(element) {\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  var parentNode = getParentNode(element);\n  if (!parentNode) {\n    return false;\n  }\n  return isFixed(parentNode);\n}\n\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nfunction getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  var el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n}\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nfunction getBoundaries(popper, reference, padding, boundariesElement) {\n  var fixedPosition = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n\n  // NOTE: 1 DOM access here\n\n  var boundaries = { top: 0, left: 0 };\n  var offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    var boundariesNode = void 0;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    var offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent, fixedPosition);\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      var _getWindowSizes = getWindowSizes(popper.ownerDocument),\n          height = _getWindowSizes.height,\n          width = _getWindowSizes.width;\n\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  var isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0;\n  boundaries.top += isPaddingNumber ? padding : padding.top || 0;\n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0;\n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0;\n\n  return boundaries;\n}\n\nfunction getArea(_ref) {\n  var width = _ref.width,\n      height = _ref.height;\n\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeAutoPlacement(placement, refRect, popper, reference, boundariesElement) {\n  var padding = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;\n\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  var boundaries = getBoundaries(popper, reference, padding, boundariesElement);\n\n  var rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height\n    }\n  };\n\n  var sortedAreas = Object.keys(rects).map(function (key) {\n    return _extends({\n      key: key\n    }, rects[key], {\n      area: getArea(rects[key])\n    });\n  }).sort(function (a, b) {\n    return b.area - a.area;\n  });\n\n  var filteredAreas = sortedAreas.filter(function (_ref2) {\n    var width = _ref2.width,\n        height = _ref2.height;\n    return width >= popper.clientWidth && height >= popper.clientHeight;\n  });\n\n  var computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].key : sortedAreas[0].key;\n\n  var variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? '-' + variation : '');\n}\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nfunction getReferenceOffsets(state, popper, reference) {\n  var fixedPosition = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n\n  var commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n\n/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nfunction getOuterSizes(element) {\n  var window = element.ownerDocument.defaultView;\n  var styles = window.getComputedStyle(element);\n  var x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  var y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  var result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x\n  };\n  return result;\n}\n\n/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nfunction getOppositePlacement(placement) {\n  var hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nfunction getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  var popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  var popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  var isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  var mainSide = isHoriz ? 'top' : 'left';\n  var secondarySide = isHoriz ? 'left' : 'top';\n  var measurement = isHoriz ? 'height' : 'width';\n  var secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] = referenceOffsets[mainSide] + referenceOffsets[measurement] / 2 - popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] = referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] = referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n\n/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(function (cur) {\n      return cur[prop] === value;\n    });\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  var match = find(arr, function (obj) {\n    return obj[prop] === value;\n  });\n  return arr.indexOf(match);\n}\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nfunction runModifiers(modifiers, data, ends) {\n  var modifiersToRun = ends === undefined ? modifiers : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(function (modifier) {\n    if (modifier['function']) {\n      // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    var fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nfunction update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  var data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {}\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(this.state, this.popper, this.reference, this.options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(this.options.placement, data.offsets.reference, this.popper, this.reference, this.options.modifiers.flip.boundariesElement, this.options.modifiers.flip.padding);\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  data.positionFixed = this.options.positionFixed;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(this.popper, data.offsets.reference, data.placement);\n\n  data.offsets.popper.position = this.options.positionFixed ? 'fixed' : 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n\n/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nfunction isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(function (_ref) {\n    var name = _ref.name,\n        enabled = _ref.enabled;\n    return enabled && name === modifierName;\n  });\n}\n\n/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nfunction getSupportedPropertyName(property) {\n  var prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  var upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (var i = 0; i < prefixes.length; i++) {\n    var prefix = prefixes[i];\n    var toCheck = prefix ? '' + prefix + upperProp : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n\n/**\n * Destroys the popper.\n * @method\n * @memberof Popper\n */\nfunction destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style.left = '';\n    this.popper.style.right = '';\n    this.popper.style.bottom = '';\n    this.popper.style.willChange = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicitly asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n\n/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nfunction getWindow(element) {\n  var ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  var isBody = scrollParent.nodeName === 'BODY';\n  var target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(getScrollParent(target.parentNode), event, callback, scrollParents);\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction setupEventListeners(reference, options, state, updateBound) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  var scrollElement = getScrollParent(reference);\n  attachToScrollParents(scrollElement, 'scroll', state.updateBound, state.scrollParents);\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nfunction enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(this.reference, this.options, this.state, this.scheduleUpdate);\n  }\n}\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(function (target) {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger `onUpdate` callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nfunction disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n\n/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setStyles(element, styles) {\n  Object.keys(styles).forEach(function (prop) {\n    var unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n\n/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function (prop) {\n    var value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nfunction applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper\n * @param {Object} options - Popper.js options\n */\nfunction applyStyleOnLoad(reference, popper, options, modifierOptions, state) {\n  // compute reference element offsets\n  var referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  var placement = computeAutoPlacement(options.placement, referenceOffsets, popper, reference, options.modifiers.flip.boundariesElement, options.modifiers.flip.padding);\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: options.positionFixed ? 'fixed' : 'absolute' });\n\n  return options;\n}\n\n/**\n * @function\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Boolean} shouldRound - If the offsets should be rounded at all\n * @returns {Object} The popper's position offsets rounded\n *\n * The tale of pixel-perfect positioning. It's still not 100% perfect, but as\n * good as it can be within reason.\n * Discussion here: https://github.com/FezVrasta/popper.js/pull/715\n *\n * Low DPI screens cause a popper to be blurry if not using full pixels (Safari\n * as well on High DPI screens).\n *\n * Firefox prefers no rounding for positioning and does not have blurriness on\n * high DPI screens.\n *\n * Only horizontal placement and left/right values need to be considered.\n */\nfunction getRoundedOffsets(data, shouldRound) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n  var round = Math.round,\n      floor = Math.floor;\n\n  var noRound = function noRound(v) {\n    return v;\n  };\n\n  var referenceWidth = round(reference.width);\n  var popperWidth = round(popper.width);\n\n  var isVertical = ['left', 'right'].indexOf(data.placement) !== -1;\n  var isVariation = data.placement.indexOf('-') !== -1;\n  var sameWidthParity = referenceWidth % 2 === popperWidth % 2;\n  var bothOddWidth = referenceWidth % 2 === 1 && popperWidth % 2 === 1;\n\n  var horizontalToInteger = !shouldRound ? noRound : isVertical || isVariation || sameWidthParity ? round : floor;\n  var verticalToInteger = !shouldRound ? noRound : round;\n\n  return {\n    left: horizontalToInteger(bothOddWidth && !isVariation && shouldRound ? popper.left - 1 : popper.left),\n    top: verticalToInteger(popper.top),\n    bottom: verticalToInteger(popper.bottom),\n    right: horizontalToInteger(popper.right)\n  };\n}\n\nvar isFirefox = isBrowser && /Firefox/i.test(navigator.userAgent);\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeStyle(data, options) {\n  var x = options.x,\n      y = options.y;\n  var popper = data.offsets.popper;\n\n  // Remove this legacy support in Popper.js v2\n\n  var legacyGpuAccelerationOption = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'applyStyle';\n  }).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');\n  }\n  var gpuAcceleration = legacyGpuAccelerationOption !== undefined ? legacyGpuAccelerationOption : options.gpuAcceleration;\n\n  var offsetParent = getOffsetParent(data.instance.popper);\n  var offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  var styles = {\n    position: popper.position\n  };\n\n  var offsets = getRoundedOffsets(data, window.devicePixelRatio < 2 || !isFirefox);\n\n  var sideA = x === 'bottom' ? 'top' : 'bottom';\n  var sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  var prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  var left = void 0,\n      top = void 0;\n  if (sideA === 'bottom') {\n    // when offsetParent is <html> the positioning is relative to the bottom of the screen (excluding the scrollbar)\n    // and not the bottom of the html element\n    if (offsetParent.nodeName === 'HTML') {\n      top = -offsetParent.clientHeight + offsets.bottom;\n    } else {\n      top = -offsetParentRect.height + offsets.bottom;\n    }\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    if (offsetParent.nodeName === 'HTML') {\n      left = -offsetParent.clientWidth + offsets.right;\n    } else {\n      left = -offsetParentRect.width + offsets.right;\n    }\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    var invertTop = sideA === 'bottom' ? -1 : 1;\n    var invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = sideA + ', ' + sideB;\n  }\n\n  // Attributes\n  var attributes = {\n    'x-placement': data.placement\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = _extends({}, attributes, data.attributes);\n  data.styles = _extends({}, styles, data.styles);\n  data.arrowStyles = _extends({}, data.offsets.arrow, data.arrowStyles);\n\n  return data;\n}\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nfunction isModifierRequired(modifiers, requestingName, requestedName) {\n  var requesting = find(modifiers, function (_ref) {\n    var name = _ref.name;\n    return name === requestingName;\n  });\n\n  var isRequired = !!requesting && modifiers.some(function (modifier) {\n    return modifier.name === requestedName && modifier.enabled && modifier.order < requesting.order;\n  });\n\n  if (!isRequired) {\n    var _requesting = '`' + requestingName + '`';\n    var requested = '`' + requestedName + '`';\n    console.warn(requested + ' modifier is required by ' + _requesting + ' modifier in order to work, be sure to include it before ' + _requesting + '!');\n  }\n  return isRequired;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction arrow(data, options) {\n  var _data$offsets$arrow;\n\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  var arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn('WARNING: `arrow.element` must be child of its popper element!');\n      return data;\n    }\n  }\n\n  var placement = data.placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  var len = isVertical ? 'height' : 'width';\n  var sideCapitalized = isVertical ? 'Top' : 'Left';\n  var side = sideCapitalized.toLowerCase();\n  var altSide = isVertical ? 'left' : 'top';\n  var opSide = isVertical ? 'bottom' : 'right';\n  var arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjunction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] += reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  var center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  var css = getStyleComputedProperty(data.instance.popper);\n  var popperMarginSide = parseFloat(css['margin' + sideCapitalized]);\n  var popperBorderSide = parseFloat(css['border' + sideCapitalized + 'Width']);\n  var sideValue = center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = (_data$offsets$arrow = {}, defineProperty(_data$offsets$arrow, side, Math.round(sideValue)), defineProperty(_data$offsets$arrow, altSide, ''), _data$offsets$arrow);\n\n  return data;\n}\n\n/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nfunction getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n\n/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-end` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nvar placements = ['auto-start', 'auto', 'auto-end', 'top-start', 'top', 'top-end', 'right-start', 'right', 'right-end', 'bottom-end', 'bottom', 'bottom-start', 'left-end', 'left', 'left-start'];\n\n// Get rid of `auto` `auto-start` and `auto-end`\nvar validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nfunction clockwise(placement) {\n  var counter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var index = validPlacements.indexOf(placement);\n  var arr = validPlacements.slice(index + 1).concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n\nvar BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise'\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, options.boundariesElement, data.positionFixed);\n\n  var placement = data.placement.split('-')[0];\n  var placementOpposite = getOppositePlacement(placement);\n  var variation = data.placement.split('-')[1] || '';\n\n  var flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach(function (step, index) {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    var popperOffsets = data.offsets.popper;\n    var refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    var floor = Math.floor;\n    var overlapsRef = placement === 'left' && floor(popperOffsets.right) > floor(refOffsets.left) || placement === 'right' && floor(popperOffsets.left) < floor(refOffsets.right) || placement === 'top' && floor(popperOffsets.bottom) > floor(refOffsets.top) || placement === 'bottom' && floor(popperOffsets.top) < floor(refOffsets.bottom);\n\n    var overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    var overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    var overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    var overflowsBottom = floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    var overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom;\n\n    // flip the variation if required\n    var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n\n    // flips variation if reference element overflows boundaries\n    var flippedVariationByRef = !!options.flipVariations && (isVertical && variation === 'start' && overflowsLeft || isVertical && variation === 'end' && overflowsRight || !isVertical && variation === 'start' && overflowsTop || !isVertical && variation === 'end' && overflowsBottom);\n\n    // flips variation if popper content overflows boundaries\n    var flippedVariationByContent = !!options.flipVariationsByContent && (isVertical && variation === 'start' && overflowsRight || isVertical && variation === 'end' && overflowsLeft || !isVertical && variation === 'start' && overflowsBottom || !isVertical && variation === 'end' && overflowsTop);\n\n    var flippedVariation = flippedVariationByRef || flippedVariationByContent;\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = _extends({}, data.offsets.popper, getPopperOffsets(data.instance.popper, data.offsets.reference, data.placement));\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction keepTogether(data) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var placement = data.placement.split('-')[0];\n  var floor = Math.floor;\n  var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  var side = isVertical ? 'right' : 'bottom';\n  var opSide = isVertical ? 'left' : 'top';\n  var measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] = floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nfunction toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  var split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  var value = +split[1];\n  var unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    var element = void 0;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    var rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    var size = void 0;\n    if (unit === 'vh') {\n      size = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);\n    } else {\n      size = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nfunction parseOffset(offset, popperOffsets, referenceOffsets, basePlacement) {\n  var offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  var useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  var fragments = offset.split(/(\\+|\\-)/).map(function (frag) {\n    return frag.trim();\n  });\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  var divider = fragments.indexOf(find(fragments, function (frag) {\n    return frag.search(/,|\\s/) !== -1;\n  }));\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  var splitRegex = /\\s*,\\s*|\\s+/;\n  var ops = divider !== -1 ? [fragments.slice(0, divider).concat([fragments[divider].split(splitRegex)[0]]), [fragments[divider].split(splitRegex)[1]].concat(fragments.slice(divider + 1))] : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map(function (op, index) {\n    // Most of the units rely on the orientation of the popper\n    var measurement = (index === 1 ? !useHeight : useHeight) ? 'height' : 'width';\n    var mergeWithPrevious = false;\n    return op\n    // This aggregates any `+` or `-` sign that aren't considered operators\n    // e.g.: 10 + +5 => [10, +, +5]\n    .reduce(function (a, b) {\n      if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n        a[a.length - 1] = b;\n        mergeWithPrevious = true;\n        return a;\n      } else if (mergeWithPrevious) {\n        a[a.length - 1] += b;\n        mergeWithPrevious = false;\n        return a;\n      } else {\n        return a.concat(b);\n      }\n    }, [])\n    // Here we convert the string values into number values (in px)\n    .map(function (str) {\n      return toValue(str, measurement, popperOffsets, referenceOffsets);\n    });\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach(function (op, index) {\n    op.forEach(function (frag, index2) {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nfunction offset(data, _ref) {\n  var offset = _ref.offset;\n  var placement = data.placement,\n      _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var basePlacement = placement.split('-')[0];\n\n  var offsets = void 0;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction preventOverflow(data, options) {\n  var boundariesElement = options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  // NOTE: DOM access here\n  // resets the popper's position so that the document size can be calculated excluding\n  // the size of the popper element itself\n  var transformProp = getSupportedPropertyName('transform');\n  var popperStyles = data.instance.popper.style; // assignment to help minification\n  var top = popperStyles.top,\n      left = popperStyles.left,\n      transform = popperStyles[transformProp];\n\n  popperStyles.top = '';\n  popperStyles.left = '';\n  popperStyles[transformProp] = '';\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, boundariesElement, data.positionFixed);\n\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  popperStyles.top = top;\n  popperStyles.left = left;\n  popperStyles[transformProp] = transform;\n\n  options.boundaries = boundaries;\n\n  var order = options.priority;\n  var popper = data.offsets.popper;\n\n  var check = {\n    primary: function primary(placement) {\n      var value = popper[placement];\n      if (popper[placement] < boundaries[placement] && !options.escapeWithReference) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return defineProperty({}, placement, value);\n    },\n    secondary: function secondary(placement) {\n      var mainSide = placement === 'right' ? 'left' : 'top';\n      var value = popper[mainSide];\n      if (popper[placement] > boundaries[placement] && !options.escapeWithReference) {\n        value = Math.min(popper[mainSide], boundaries[placement] - (placement === 'right' ? popper.width : popper.height));\n      }\n      return defineProperty({}, mainSide, value);\n    }\n  };\n\n  order.forEach(function (placement) {\n    var side = ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = _extends({}, popper, check[side](placement));\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction shift(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    var _data$offsets = data.offsets,\n        reference = _data$offsets.reference,\n        popper = _data$offsets.popper;\n\n    var isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    var side = isVertical ? 'left' : 'top';\n    var measurement = isVertical ? 'width' : 'height';\n\n    var shiftOffsets = {\n      start: defineProperty({}, side, reference[side]),\n      end: defineProperty({}, side, reference[side] + reference[measurement] - popper[measurement])\n    };\n\n    data.offsets.popper = _extends({}, popper, shiftOffsets[shiftvariation]);\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  var refRect = data.offsets.reference;\n  var bound = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'preventOverflow';\n  }).boundaries;\n\n  if (refRect.bottom < bound.top || refRect.left > bound.right || refRect.top > bound.bottom || refRect.right < bound.left) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction inner(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  var subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] = reference[basePlacement] - (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nvar modifiers = {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unit-less, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the `height`.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > You can read more on this at this [issue](https://github.com/FezVrasta/popper.js/issues/373).\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * A scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper. This makes sure the popper always has a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier. Can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent'\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near each other\n   * without leaving any gap between the two. Especially useful when the arrow is\n   * enabled and you want to ensure that it points to its reference element.\n   * It cares only about the first axis. You can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjunction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]'\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations)\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position.\n     * The popper will never be placed outside of the defined boundaries\n     * (except if `keepTogether` is enabled)\n     */\n    boundariesElement: 'viewport',\n    /**\n     * @prop {Boolean} flipVariations=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the reference element overlaps its boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariations: false,\n    /**\n     * @prop {Boolean} flipVariationsByContent=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the popper element overlaps its reference boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariationsByContent: false\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right'\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define your own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: undefined\n  }\n};\n\n/**\n * The `dataObject` is an object containing all the information used by Popper.js.\n * This object is passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overridden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass an object with the same\n * structure of the `options` object, as the 3rd argument. For example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nvar Defaults = {\n  /**\n   * Popper's placement.\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Set this to true if you want popper to position it self in 'fixed' mode\n   * @prop {Boolean} positionFixed=false\n   */\n  positionFixed: false,\n\n  /**\n   * Whether events (resize, scroll) are initially enabled.\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: function onCreate() {},\n\n  /**\n   * Callback called when the popper is updated. This callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: function onUpdate() {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js.\n   * @prop {modifiers}\n   */\n  modifiers: modifiers\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n\n// Utils\n// Methods\nvar Popper = function () {\n  /**\n   * Creates a new Popper.js instance.\n   * @class Popper\n   * @param {Element|referenceObject} reference - The reference element used to position the popper\n   * @param {Element} popper - The HTML / XML element used as the popper\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  function Popper(reference, popper) {\n    var _this = this;\n\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    classCallCheck(this, Popper);\n\n    this.scheduleUpdate = function () {\n      return requestAnimationFrame(_this.update);\n    };\n\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = _extends({}, Popper.Defaults, options);\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: []\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys(_extends({}, Popper.Defaults.modifiers, options.modifiers)).forEach(function (name) {\n      _this.options.modifiers[name] = _extends({}, Popper.Defaults.modifiers[name] || {}, options.modifiers ? options.modifiers[name] : {});\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers).map(function (name) {\n      return _extends({\n        name: name\n      }, _this.options.modifiers[name]);\n    })\n    // sort the modifiers by order\n    .sort(function (a, b) {\n      return a.order - b.order;\n    });\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(function (modifierOptions) {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(_this.reference, _this.popper, _this.options, modifierOptions, _this.state);\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    var eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n\n\n  createClass(Popper, [{\n    key: 'update',\n    value: function update$$1() {\n      return update.call(this);\n    }\n  }, {\n    key: 'destroy',\n    value: function destroy$$1() {\n      return destroy.call(this);\n    }\n  }, {\n    key: 'enableEventListeners',\n    value: function enableEventListeners$$1() {\n      return enableEventListeners.call(this);\n    }\n  }, {\n    key: 'disableEventListeners',\n    value: function disableEventListeners$$1() {\n      return disableEventListeners.call(this);\n    }\n\n    /**\n     * Schedules an update. It will run on the next UI update available.\n     * @method scheduleUpdate\n     * @memberof Popper\n     */\n\n\n    /**\n     * Collection of utilities useful when writing custom modifiers.\n     * Starting from version 1.7, this method is available only if you\n     * include `popper-utils.js` before `popper.js`.\n     *\n     * **DEPRECATION**: This way to access PopperUtils is deprecated\n     * and will be removed in v2! Use the PopperUtils module directly instead.\n     * Due to the high instability of the methods contained in Utils, we can't\n     * guarantee them to follow semver. Use them at your own risk!\n     * @static\n     * @private\n     * @type {Object}\n     * @deprecated since version 1.8\n     * @member Utils\n     * @memberof Popper\n     */\n\n  }]);\n  return Popper;\n}();\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10.\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n\n\nPopper.Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\nPopper.placements = placements;\nPopper.Defaults = Defaults;\n\nexport default Popper;\n//# sourceMappingURL=popper.js.map\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPRIGHT = 'dropright'\nconst CLASS_NAME_DROPLEFT = 'dropleft'\nconst CLASS_NAME_MENURIGHT = 'dropdown-menu-right'\nconst CLASS_NAME_NAVBAR = 'navbar'\nconst CLASS_NAME_POSITION_STATIC = 'position-static'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = 'top-start'\nconst PLACEMENT_TOPEND = 'top-end'\nconst PLACEMENT_BOTTOM = 'bottom-start'\nconst PLACEMENT_BOTTOMEND = 'bottom-end'\nconst PLACEMENT_RIGHT = 'right-start'\nconst PLACEMENT_LEFT = 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'scrollParent',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element = element\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Disable totally Popper.js for Dropdown in Navbar\n    if (!this._inNavbar) {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper.js (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        parent.classList.add(CLASS_NAME_POSITION_STATIC)\n      }\n\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._element, EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n    let placement = PLACEMENT_BOTTOM\n\n    // Handle dropup\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      placement = this._menu.classList.contains(CLASS_NAME_MENURIGHT) ?\n        PLACEMENT_TOPEND :\n        PLACEMENT_TOP\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPRIGHT)) {\n      placement = PLACEMENT_RIGHT\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPLEFT)) {\n      placement = PLACEMENT_LEFT\n    } else if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n      placement = PLACEMENT_BOTTOMEND\n    }\n\n    return placement\n  }\n\n  _detectNavbar() {\n    return Boolean(this._element.closest(`.${CLASS_NAME_NAVBAR}`))\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...(this._config.offset(data.offsets, this._element) || {})\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper.js if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON ||\n      (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown.getParentFromElement(toggles[i])\n      const context = Data.getData(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && ((event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName)) ||\n          (event.type === 'keyup' && event.key === TAB_KEY)) &&\n          dropdownMenu.contains(event.target)) {\n        continue\n      }\n\n      const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.key === ARROW_UP_KEY && index > 0) { // Up\n      index--\n    }\n\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) { // Down\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  event.stopPropagation()\n  Dropdown.dropdownInterface(this, 'toggle')\n})\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => e.stopPropagation())\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Dropdown.jQueryInterface\n    $.fn[NAME].Constructor = Dropdown\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Dropdown.jQueryInterface\n    }\n  }\n})\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true,\n  show: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean',\n  show: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config = this._getConfig(config)\n    this._element = element\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      event => this.hide(event)\n    )\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._element = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (transition) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, TRANSITION_END, transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const animate = this._element.classList.contains(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE :\n      ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        this._triggerBackdropTransition()\n      })\n\n      if (animate) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, TRANSITION_END, callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (this._element.classList.contains(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, TRANSITION_END, callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _triggerBackdropTransition() {\n    if (this._config.backdrop === 'static') {\n      const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n      if (hideEvent.defaultPrevented) {\n        return\n      }\n\n      const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n      if (!isModalOverflowing) {\n        this._element.style.overflowY = 'hidden'\n      }\n\n      this._element.classList.add(CLASS_NAME_STATIC)\n      const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n      EventHandler.off(this._element, TRANSITION_END)\n      EventHandler.one(this._element, TRANSITION_END, () => {\n        this._element.classList.remove(CLASS_NAME_STATIC)\n        if (!isModalOverflowing) {\n          EventHandler.one(this._element, TRANSITION_END, () => {\n            this._element.style.overflowY = ''\n          })\n          emulateTransitionEnd(this._element, modalTransitionDuration)\n        }\n      })\n      emulateTransitionEnd(this._element, modalTransitionDuration)\n      this._element.focus()\n    } else {\n      this.hide()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n      // Adjust fixed content padding\n      SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n        .forEach(element => {\n          const actualPadding = element.style.paddingRight\n          const calculatedPadding = window.getComputedStyle(element)['padding-right']\n          Manipulator.setDataAttribute(element, 'padding-right', actualPadding)\n          element.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n        })\n\n      // Adjust sticky content margin\n      SelectorEngine.find(SELECTOR_STICKY_CONTENT)\n        .forEach(element => {\n          const actualMargin = element.style.marginRight\n          const calculatedMargin = window.getComputedStyle(element)['margin-right']\n          Manipulator.setDataAttribute(element, 'margin-right', actualMargin)\n          element.style.marginRight = `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`\n        })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = window.getComputedStyle(document.body)['padding-right']\n\n      Manipulator.setDataAttribute(document.body, 'padding-right', actualPadding)\n      document.body.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n      .forEach(element => {\n        const padding = Manipulator.getDataAttribute(element, 'padding-right')\n        if (typeof padding !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'padding-right')\n          element.style.paddingRight = padding\n        }\n      })\n\n    // Restore sticky content and navbar-toggler margin\n    SelectorEngine.find(`${SELECTOR_STICKY_CONTENT}`)\n      .forEach(element => {\n        const margin = Manipulator.getDataAttribute(element, 'margin-right')\n        if (typeof margin !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'margin-right')\n          element.style.marginRight = margin\n        }\n      })\n\n    // Restore body padding\n    const padding = Manipulator.getDataAttribute(document.body, 'padding-right')\n    if (typeof padding === 'undefined') {\n      document.body.style.paddingRight = ''\n    } else {\n      Manipulator.removeDataAttribute(document.body, 'padding-right')\n      document.body.style.paddingRight = padding\n    }\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.getData(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.show(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Modal.jQueryInterface\n    $.fn[NAME].Constructor = Modal\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Modal.jQueryInterface\n    }\n  }\n})\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (allowlistKeys.indexOf(elName) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'allowList', 'sanitizeFn']\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(number|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacement: '(string|array)',\n  boundary: '(string|element)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: 'right',\n  BOTTOM: 'bottom',\n  LEFT: 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"tooltip-arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: 0,\n  container: false,\n  fallbackPlacement: 'flip',\n  boundary: 'scrollParent',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper.js (https://popper.js.org)')\n    }\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.element = element\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n    Data.setData(element, this.constructor.DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = Data.getData(event.delegateTarget, dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.delegateTarget,\n          this._getDelegateConfig()\n        )\n        Data.setData(event.delegateTarget, dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    Data.removeData(this.element, this.constructor.DATA_KEY)\n\n    EventHandler.off(this.element, this.constructor.EVENT_KEY)\n    EventHandler.off(this.element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config = null\n    this.tip = null\n  }\n\n  show() {\n    if (this.element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (this.isWithContent() && this._isEnabled) {\n      const showEvent = EventHandler.trigger(this.element, this.constructor.Event.SHOW)\n      const shadowRoot = findShadowRoot(this.element)\n      const isInTheDom = shadowRoot === null ?\n        this.element.ownerDocument.documentElement.contains(this.element) :\n        shadowRoot.contains(this.element)\n\n      if (showEvent.defaultPrevented || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        tip.classList.add(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this.element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this._addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      Data.setData(tip, this.constructor.DATA_KEY, this)\n\n      if (!this.element.ownerDocument.documentElement.contains(this.tip)) {\n        container.appendChild(tip)\n      }\n\n      EventHandler.trigger(this.element, this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, this._getPopperConfig(attachment))\n\n      tip.classList.add(CLASS_NAME_SHOW)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children).forEach(element => {\n          EventHandler.on(element, 'mouseover', noop())\n        })\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n\n        const prevHoverState = this._hoverState\n        this._hoverState = null\n\n        EventHandler.trigger(this.element, this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n        const transitionDuration = getTransitionDurationFromElement(this.tip)\n        EventHandler.one(this.tip, TRANSITION_END, complete)\n        emulateTransitionEnd(this.tip, transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this.element, this.constructor.Event.HIDDEN)\n      this._popper.destroy()\n    }\n\n    const hideEvent = EventHandler.trigger(this.element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, TRANSITION_END, complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.allowList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this.element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          behavior: this.config.fallbackPlacement\n        },\n        arrow: {\n          element: `.${this.constructor.NAME}-arrow`\n        },\n        preventOverflow: {\n          boundariesElement: this.config.boundary\n        }\n      },\n      onCreate: data => {\n        if (data.originalPlacement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      },\n      onUpdate: data => this._handlePopperPlacementChange(data)\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...(this.config.offset(data.offsets, this.element) || {})\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this.element,\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this.element,\n          eventIn,\n          this.config.selector,\n          event => this._enter(event)\n        )\n        EventHandler.on(this.element,\n          eventOut,\n          this.config.selector,\n          event => this._leave(event)\n        )\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this.element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this.element.closest(`.${CLASS_NAME_MODAL}`),\n      'hide.bs.modal',\n      this._hideModalHandler\n    )\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) ||\n        context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this.element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    this.tip = popperData.instance.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tooltip.jQueryInterface\n    $.fn[NAME].Constructor = Tooltip\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tooltip.jQueryInterface\n    }\n  }\n})\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.setData(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Popover.jQueryInterface\n    $.fn[NAME].Constructor = Popover\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Popover.jQueryInterface\n    }\n  }\n})\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._element = null\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset +\n      scrollHeight -\n      this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = ScrollSpy.jQueryInterface\n    $.fn[NAME].Constructor = ScrollSpy\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return ScrollSpy.jQueryInterface\n    }\n  }\n})\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab {\n  constructor(element) {\n    this._element = element\n\n    Data.setData(this._element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    let hideEvent = null\n\n    if (previous) {\n      hideEvent = EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      })\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented ||\n      (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(\n      this._element,\n      listElement\n    )\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback &&\n      (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(\n      element,\n      active,\n      callback\n    )\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, TRANSITION_END, complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.getData(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.getData(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tab.jQueryInterface\n    $.fn[NAME].Constructor = Tab\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tab.jQueryInterface\n    }\n  }\n})\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._element = null\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Toast.jQueryInterface\n    $.fn[NAME].Constructor = Toast\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Toast.jQueryInterface\n    }\n  }\n})\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"], "names": ["MAX_UID", "MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "window", "getComputedStyle", "transitionDuration", "transitionDelay", "floatTransitionDuration", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "nodeType", "emulateTransitionEnd", "duration", "called", "durationPadding", "emulatedDuration", "listener", "removeEventListener", "addEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "Error", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "hasAttribute", "onDOMContentLoaded", "callback", "readyState", "mapData", "storeData", "id", "set", "key", "data", "bs<PERSON><PERSON>", "get", "keyProperties", "delete", "Data", "setData", "instance", "getData", "removeData", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "getUidEvent", "uid", "getEvent", "bootstrapHandler", "fn", "handler", "event", "<PERSON><PERSON><PERSON><PERSON>", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "len", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "replace", "custom", "isNative", "indexOf", "add<PERSON><PERSON><PERSON>", "handlers", "previousFn", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "on", "one", "inNamespace", "isNamespace", "char<PERSON>t", "elementEvent", "slice", "keyHandlers", "trigger", "args", "$", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "SELECTOR_DISMISS", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "CLASSNAME_ALERT", "CLASSNAME_FADE", "CLASSNAME_SHOW", "<PERSON><PERSON>", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "dispose", "closest", "classList", "remove", "contains", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "jQueryInterface", "each", "handle<PERSON><PERSON><PERSON>", "alertInstance", "getInstance", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "setAttribute", "button", "normalizeData", "val", "Number", "normalizeDataKey", "chr", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "NODE_TEXT", "SelectorEngine", "matches", "find", "concat", "Element", "prototype", "findOne", "children", "filter", "child", "parents", "ancestor", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "DIRECTION_NEXT", "DIRECTION_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_SLIDE", "CLASS_NAME_RIGHT", "CLASS_NAME_LEFT", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "PointerType", "TOUCH", "PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "direction", "_handleSwipe", "absDeltax", "abs", "_keydown", "_addTouchEventListeners", "start", "pointerType", "clientX", "touches", "move", "end", "clearTimeout", "itemImg", "e", "add", "tagName", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "isGoingToWrap", "delta", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "from", "_setActiveIndicatorElement", "indicators", "nextIndicator", "elementInterval", "parseInt", "defaultInterval", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "slideEvent", "carouselInterface", "action", "TypeError", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "startEvent", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "complete", "capitalizedDimension", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "selectorElements", "_extends", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_CLICK", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DISABLED", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPRIGHT", "CLASS_NAME_DROPLEFT", "CLASS_NAME_MENURIGHT", "CLASS_NAME_NAVBAR", "CLASS_NAME_POSITION_STATIC", "SELECTOR_FORM_CHILD", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "flip", "boundary", "reference", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "isActive", "clearMenus", "getParentFromElement", "showEvent", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "focus", "hideEvent", "destroy", "update", "scheduleUpdate", "stopPropagation", "constructor", "_getPlacement", "parentDropdown", "placement", "_getOffset", "offsets", "modifiers", "enabled", "preventOverflow", "boundariesElement", "applyStyle", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "dataApiKeydownHandler", "items", "backdrop", "EVENT_HIDE_PREVENTED", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "CLASS_NAME_SCROLLBAR_MEASURER", "CLASS_NAME_BACKDROP", "CLASS_NAME_OPEN", "CLASS_NAME_FADE", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_DISMISS", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "transition", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_triggerBackdropTransition", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "animate", "createElement", "className", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "scrollHeight", "clientHeight", "overflowY", "modalTransitionDuration", "paddingLeft", "paddingRight", "round", "right", "innerWidth", "_getScrollbarWidth", "actualPadding", "calculatedPadding", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "nodeValue", "regExp", "attrRegex", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "el", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacement", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_MODAL", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "_fixTransition", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "defaultBsConfig", "behavior", "arrow", "onCreate", "originalPlacement", "_handlePopperPlacementChange", "onUpdate", "triggers", "eventIn", "eventOut", "_fixTitle", "titleType", "dataAttributes", "dataAttr", "tabClass", "map", "token", "tClass", "popperData", "popper", "initConfigAnimation", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "method", "EVENT_ACTIVATE", "EVENT_SCROLL", "CLASS_NAME_DROPDOWN_ITEM", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "isActiveTarget", "queries", "link", "join", "listGroup", "navItem", "node", "spy", "CLASS_NAME_DROPDOWN_MENU", "SELECTOR_ACTIVE_UL", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "listElement", "itemSelector", "activeElements", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "CLASS_NAME_HIDE", "CLASS_NAME_SHOWING", "autohide", "Toast", "_clearTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;EAEA,IAAMA,OAAO,GAAG,OAAhB;EACA,IAAMC,uBAAuB,GAAG,IAAhC;EACA,IAAMC,cAAc,GAAG,eAAvB;;EAGA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAAC,GAAG,EAAI;EACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;EACrC,gBAAUD,GAAV;EACD;;EAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;EACD,CAND;EAQA;EACA;EACA;EACA;EACA;;;EAEA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAAC,MAAM,EAAI;EACvB,KAAG;EACDA,IAAAA,MAAM,IAAIC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBd,OAA3B,CAAV;EACD,GAFD,QAESe,QAAQ,CAACC,cAAT,CAAwBL,MAAxB,CAFT;;EAIA,SAAOA,MAAP;EACD,CAND;;EAQA,IAAMM,WAAW,GAAG,SAAdA,WAAc,CAAAC,OAAO,EAAI;EAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,aAArB,CAAf;;EAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;EACjC,QAAME,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAjB;EAEAD,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACC,IAAT,EAA/B,GAAiD,IAA5D;EACD;;EAED,SAAOH,QAAP;EACD,CAVD;;EAYA,IAAMI,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAAL,OAAO,EAAI;EACxC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;;EAEA,MAAIC,QAAJ,EAAc;EACZ,WAAOJ,QAAQ,CAACS,aAAT,CAAuBL,QAAvB,IAAmCA,QAAnC,GAA8C,IAArD;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,IAAMM,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAAP,OAAO,EAAI;EACxC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;EAEA,SAAOC,QAAQ,GAAGJ,QAAQ,CAACS,aAAT,CAAuBL,QAAvB,CAAH,GAAsC,IAArD;EACD,CAJD;;EAMA,IAAMO,gCAAgC,GAAG,SAAnCA,gCAAmC,CAAAR,OAAO,EAAI;EAClD,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,CAAP;EACD,GAHiD;;;EAAA,8BAS9CS,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,CAT8C;EAAA,MAOhDW,kBAPgD,yBAOhDA,kBAPgD;EAAA,MAQhDC,eARgD,yBAQhDA,eARgD;;EAWlD,MAAMC,uBAAuB,GAAGC,UAAU,CAACH,kBAAD,CAA1C;EACA,MAAMI,oBAAoB,GAAGD,UAAU,CAACF,eAAD,CAAvC,CAZkD;;EAelD,MAAI,CAACC,uBAAD,IAA4B,CAACE,oBAAjC,EAAuD;EACrD,WAAO,CAAP;EACD,GAjBiD;;;EAoBlDJ,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACK,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;EACAJ,EAAAA,eAAe,GAAGA,eAAe,CAACI,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAlB;EAEA,SAAO,CAACF,UAAU,CAACH,kBAAD,CAAV,GAAiCG,UAAU,CAACF,eAAD,CAA5C,IAAiE7B,uBAAxE;EACD,CAxBD;;EA0BA,IAAMkC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAAAjB,OAAO,EAAI;EACtCA,EAAAA,OAAO,CAACkB,aAAR,CAAsB,IAAIC,KAAJ,CAAUnC,cAAV,CAAtB;EACD,CAFD;;EAIA,IAAMoC,SAAS,GAAG,SAAZA,SAAY,CAAAlC,GAAG;EAAA,SAAI,CAACA,GAAG,CAAC,CAAD,CAAH,IAAUA,GAAX,EAAgBmC,QAApB;EAAA,CAArB;;EAEA,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAACtB,OAAD,EAAUuB,QAAV,EAAuB;EAClD,MAAIC,MAAM,GAAG,KAAb;EACA,MAAMC,eAAe,GAAG,CAAxB;EACA,MAAMC,gBAAgB,GAAGH,QAAQ,GAAGE,eAApC;;EACA,WAASE,QAAT,GAAoB;EAClBH,IAAAA,MAAM,GAAG,IAAT;EACAxB,IAAAA,OAAO,CAAC4B,mBAAR,CAA4B5C,cAA5B,EAA4C2C,QAA5C;EACD;;EAED3B,EAAAA,OAAO,CAAC6B,gBAAR,CAAyB7C,cAAzB,EAAyC2C,QAAzC;EACAG,EAAAA,UAAU,CAAC,YAAM;EACf,QAAI,CAACN,MAAL,EAAa;EACXP,MAAAA,oBAAoB,CAACjB,OAAD,CAApB;EACD;EACF,GAJS,EAIP0B,gBAJO,CAAV;EAKD,CAfD;;EAiBA,IAAMK,eAAe,GAAG,SAAlBA,eAAkB,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,EAAwC;EAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiC,UAAAC,QAAQ,EAAI;EAC3C,QAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;EACA,QAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;EACA,QAAMG,SAAS,GAAGD,KAAK,IAAIpB,SAAS,CAACoB,KAAD,CAAlB,GAChB,SADgB,GAEhBvD,MAAM,CAACuD,KAAD,CAFR;;EAIA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;EAC9C,YAAM,IAAIG,KAAJ,CACDZ,aAAa,CAACa,WAAd,EAAH,yBACWP,QADX,2BACuCG,SADvC,sCAEsBF,aAFtB,SADI,CAAN;EAID;EACF,GAbD;EAcD,CAfD;;EAiBA,IAAMO,SAAS,GAAG,SAAZA,SAAY,CAAA9C,OAAO,EAAI;EAC3B,MAAI,CAACA,OAAL,EAAc;EACZ,WAAO,KAAP;EACD;;EAED,MAAIA,OAAO,CAAC+C,KAAR,IAAiB/C,OAAO,CAACgD,UAAzB,IAAuChD,OAAO,CAACgD,UAAR,CAAmBD,KAA9D,EAAqE;EACnE,QAAME,YAAY,GAAGvC,gBAAgB,CAACV,OAAD,CAArC;EACA,QAAMkD,eAAe,GAAGxC,gBAAgB,CAACV,OAAO,CAACgD,UAAT,CAAxC;EAEA,WAAOC,YAAY,CAACE,OAAb,KAAyB,MAAzB,IACLD,eAAe,CAACC,OAAhB,KAA4B,MADvB,IAELF,YAAY,CAACG,UAAb,KAA4B,QAF9B;EAGD;;EAED,SAAO,KAAP;EACD,CAfD;;EAiBA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAAArD,OAAO,EAAI;EAChC,MAAI,CAACH,QAAQ,CAACyD,eAAT,CAAyBC,YAA9B,EAA4C;EAC1C,WAAO,IAAP;EACD,GAH+B;;;EAMhC,MAAI,OAAOvD,OAAO,CAACwD,WAAf,KAA+B,UAAnC,EAA+C;EAC7C,QAAMC,IAAI,GAAGzD,OAAO,CAACwD,WAAR,EAAb;EACA,WAAOC,IAAI,YAAYC,UAAhB,GAA6BD,IAA7B,GAAoC,IAA3C;EACD;;EAED,MAAIzD,OAAO,YAAY0D,UAAvB,EAAmC;EACjC,WAAO1D,OAAP;EACD,GAb+B;;;EAgBhC,MAAI,CAACA,OAAO,CAACgD,UAAb,EAAyB;EACvB,WAAO,IAAP;EACD;;EAED,SAAOK,cAAc,CAACrD,OAAO,CAACgD,UAAT,CAArB;EACD,CArBD;;EAuBA,IAAMW,IAAI,GAAG,SAAPA,IAAO;EAAA,SAAM,YAAY,EAAlB;EAAA,CAAb;;EAEA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAA5D,OAAO;EAAA,SAAIA,OAAO,CAAC6D,YAAZ;EAAA,CAAtB;;EAEA,IAAMC,SAAS,GAAG,SAAZA,SAAY,GAAM;EAAA,gBACHrD,MADG;EAAA,MACdsD,MADc,WACdA,MADc;;EAGtB,MAAIA,MAAM,IAAI,CAAClE,QAAQ,CAACmE,IAAT,CAAcC,YAAd,CAA2B,gBAA3B,CAAf,EAA6D;EAC3D,WAAOF,MAAP;EACD;;EAED,SAAO,IAAP;EACD,CARD;;EAUA,IAAMG,kBAAkB,GAAG,SAArBA,kBAAqB,CAAAC,QAAQ,EAAI;EACrC,MAAItE,QAAQ,CAACuE,UAAT,KAAwB,SAA5B,EAAuC;EACrCvE,IAAAA,QAAQ,CAACgC,gBAAT,CAA0B,kBAA1B,EAA8CsC,QAA9C;EACD,GAFD,MAEO;EACLA,IAAAA,QAAQ;EACT;EACF,CAND;;ECtLA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EAEA,IAAME,OAAO,GAAI,YAAM;EACrB,MAAMC,SAAS,GAAG,EAAlB;EACA,MAAIC,EAAE,GAAG,CAAT;EACA,SAAO;EACLC,IAAAA,GADK,eACDxE,OADC,EACQyE,GADR,EACaC,IADb,EACmB;EACtB,UAAI,OAAO1E,OAAO,CAAC2E,KAAf,KAAyB,WAA7B,EAA0C;EACxC3E,QAAAA,OAAO,CAAC2E,KAAR,GAAgB;EACdF,UAAAA,GAAG,EAAHA,GADc;EAEdF,UAAAA,EAAE,EAAFA;EAFc,SAAhB;EAIAA,QAAAA,EAAE;EACH;;EAEDD,MAAAA,SAAS,CAACtE,OAAO,CAAC2E,KAAR,CAAcJ,EAAf,CAAT,GAA8BG,IAA9B;EACD,KAXI;EAYLE,IAAAA,GAZK,eAYD5E,OAZC,EAYQyE,GAZR,EAYa;EAChB,UAAI,CAACzE,OAAD,IAAY,OAAOA,OAAO,CAAC2E,KAAf,KAAyB,WAAzC,EAAsD;EACpD,eAAO,IAAP;EACD;;EAED,UAAME,aAAa,GAAG7E,OAAO,CAAC2E,KAA9B;;EACA,UAAIE,aAAa,CAACJ,GAAd,KAAsBA,GAA1B,EAA+B;EAC7B,eAAOH,SAAS,CAACO,aAAa,CAACN,EAAf,CAAhB;EACD;;EAED,aAAO,IAAP;EACD,KAvBI;EAwBLO,IAAAA,MAxBK,mBAwBE9E,OAxBF,EAwBWyE,GAxBX,EAwBgB;EACnB,UAAI,OAAOzE,OAAO,CAAC2E,KAAf,KAAyB,WAA7B,EAA0C;EACxC;EACD;;EAED,UAAME,aAAa,GAAG7E,OAAO,CAAC2E,KAA9B;;EACA,UAAIE,aAAa,CAACJ,GAAd,KAAsBA,GAA1B,EAA+B;EAC7B,eAAOH,SAAS,CAACO,aAAa,CAACN,EAAf,CAAhB;EACA,eAAOvE,OAAO,CAAC2E,KAAf;EACD;EACF;EAlCI,GAAP;EAoCD,CAvCe,EAAhB;;EAyCA,IAAMI,IAAI,GAAG;EACXC,EAAAA,OADW,mBACHC,QADG,EACOR,GADP,EACYC,IADZ,EACkB;EAC3BL,IAAAA,OAAO,CAACG,GAAR,CAAYS,QAAZ,EAAsBR,GAAtB,EAA2BC,IAA3B;EACD,GAHU;EAIXQ,EAAAA,OAJW,mBAIHD,QAJG,EAIOR,GAJP,EAIY;EACrB,WAAOJ,OAAO,CAACO,GAAR,CAAYK,QAAZ,EAAsBR,GAAtB,CAAP;EACD,GANU;EAOXU,EAAAA,UAPW,sBAOAF,QAPA,EAOUR,GAPV,EAOe;EACxBJ,IAAAA,OAAO,CAACS,MAAR,CAAeG,QAAf,EAAyBR,GAAzB;EACD;EATU,CAAb;;ECtDA;EACA;EACA;EACA;EACA;EACA;EAIA;EACA;EACA;EACA;EACA;;EAEA,IAAMW,cAAc,GAAG,oBAAvB;EACA,IAAMC,cAAc,GAAG,MAAvB;EACA,IAAMC,aAAa,GAAG,QAAtB;EACA,IAAMC,aAAa,GAAG,EAAtB;;EACA,IAAIC,QAAQ,GAAG,CAAf;EACA,IAAMC,YAAY,GAAG;EACnBC,EAAAA,UAAU,EAAE,WADO;EAEnBC,EAAAA,UAAU,EAAE;EAFO,CAArB;EAIA,IAAMC,YAAY,GAAG,CACnB,OADmB,EAEnB,UAFmB,EAGnB,SAHmB,EAInB,WAJmB,EAKnB,aALmB,EAMnB,YANmB,EAOnB,gBAPmB,EAQnB,WARmB,EASnB,UATmB,EAUnB,WAVmB,EAWnB,aAXmB,EAYnB,WAZmB,EAanB,SAbmB,EAcnB,UAdmB,EAenB,OAfmB,EAgBnB,mBAhBmB,EAiBnB,YAjBmB,EAkBnB,WAlBmB,EAmBnB,UAnBmB,EAoBnB,aApBmB,EAqBnB,aArBmB,EAsBnB,aAtBmB,EAuBnB,WAvBmB,EAwBnB,cAxBmB,EAyBnB,eAzBmB,EA0BnB,cA1BmB,EA2BnB,eA3BmB,EA4BnB,YA5BmB,EA6BnB,OA7BmB,EA8BnB,MA9BmB,EA+BnB,QA/BmB,EAgCnB,OAhCmB,EAiCnB,QAjCmB,EAkCnB,QAlCmB,EAmCnB,SAnCmB,EAoCnB,UApCmB,EAqCnB,MArCmB,EAsCnB,QAtCmB,EAuCnB,cAvCmB,EAwCnB,QAxCmB,EAyCnB,MAzCmB,EA0CnB,kBA1CmB,EA2CnB,kBA3CmB,EA4CnB,OA5CmB,EA6CnB,OA7CmB,EA8CnB,QA9CmB,CAArB;EAiDA;EACA;EACA;EACA;EACA;;EAEA,SAASC,WAAT,CAAqB7F,OAArB,EAA8B8F,GAA9B,EAAmC;EACjC,SAAQA,GAAG,IAAOA,GAAP,UAAeN,QAAQ,EAA3B,IAAoCxF,OAAO,CAACwF,QAA5C,IAAwDA,QAAQ,EAAvE;EACD;;EAED,SAASO,QAAT,CAAkB/F,OAAlB,EAA2B;EACzB,MAAM8F,GAAG,GAAGD,WAAW,CAAC7F,OAAD,CAAvB;EAEAA,EAAAA,OAAO,CAACwF,QAAR,GAAmBM,GAAnB;EACAP,EAAAA,aAAa,CAACO,GAAD,CAAb,GAAqBP,aAAa,CAACO,GAAD,CAAb,IAAsB,EAA3C;EAEA,SAAOP,aAAa,CAACO,GAAD,CAApB;EACD;;EAED,SAASE,gBAAT,CAA0BhG,OAA1B,EAAmCiG,EAAnC,EAAuC;EACrC,SAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;EAC7BA,IAAAA,KAAK,CAACC,cAAN,GAAuBpG,OAAvB;;EAEA,QAAIkG,OAAO,CAACG,MAAZ,EAAoB;EAClBC,MAAAA,YAAY,CAACC,GAAb,CAAiBvG,OAAjB,EAA0BmG,KAAK,CAACK,IAAhC,EAAsCP,EAAtC;EACD;;EAED,WAAOA,EAAE,CAACQ,KAAH,CAASzG,OAAT,EAAkB,CAACmG,KAAD,CAAlB,CAAP;EACD,GARD;EASD;;EAED,SAASO,0BAAT,CAAoC1G,OAApC,EAA6CC,QAA7C,EAAuDgG,EAAvD,EAA2D;EACzD,SAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;EAC7B,QAAMQ,WAAW,GAAG3G,OAAO,CAAC4G,gBAAR,CAAyB3G,QAAzB,CAApB;;EAEA,aAAW4G,MAAX,GAAsBV,KAAtB,CAAWU,MAAX,EAA6BA,MAAM,IAAIA,MAAM,KAAK,IAAlD,EAAwDA,MAAM,GAAGA,MAAM,CAAC7D,UAAxE,EAAoF;EAClF,WAAK,IAAI8D,CAAC,GAAGH,WAAW,CAACI,MAAzB,EAAiCD,CAAC,EAAlC,GAAuC;EACrC,YAAIH,WAAW,CAACG,CAAD,CAAX,KAAmBD,MAAvB,EAA+B;EAC7BV,UAAAA,KAAK,CAACC,cAAN,GAAuBS,MAAvB;;EAEA,cAAIX,OAAO,CAACG,MAAZ,EAAoB;EAClBC,YAAAA,YAAY,CAACC,GAAb,CAAiBvG,OAAjB,EAA0BmG,KAAK,CAACK,IAAhC,EAAsCP,EAAtC;EACD;;EAED,iBAAOA,EAAE,CAACQ,KAAH,CAASI,MAAT,EAAiB,CAACV,KAAD,CAAjB,CAAP;EACD;EACF;EACF,KAf4B;;;EAkB7B,WAAO,IAAP;EACD,GAnBD;EAoBD;;EAED,SAASa,WAAT,CAAqBC,MAArB,EAA6Bf,OAA7B,EAAsCgB,kBAAtC,EAAiE;EAAA,MAA3BA,kBAA2B;EAA3BA,IAAAA,kBAA2B,GAAN,IAAM;EAAA;;EAC/D,MAAMC,YAAY,GAAGhF,MAAM,CAACC,IAAP,CAAY6E,MAAZ,CAArB;;EAEA,OAAK,IAAIH,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGD,YAAY,CAACJ,MAAnC,EAA2CD,CAAC,GAAGM,GAA/C,EAAoDN,CAAC,EAArD,EAAyD;EACvD,QAAMX,KAAK,GAAGc,MAAM,CAACE,YAAY,CAACL,CAAD,CAAb,CAApB;;EAEA,QAAIX,KAAK,CAACkB,eAAN,KAA0BnB,OAA1B,IAAqCC,KAAK,CAACe,kBAAN,KAA6BA,kBAAtE,EAA0F;EACxF,aAAOf,KAAP;EACD;EACF;;EAED,SAAO,IAAP;EACD;;EAED,SAASmB,eAAT,CAAyBC,iBAAzB,EAA4CrB,OAA5C,EAAqDsB,YAArD,EAAmE;EACjE,MAAMC,UAAU,GAAG,OAAOvB,OAAP,KAAmB,QAAtC;EACA,MAAMmB,eAAe,GAAGI,UAAU,GAAGD,YAAH,GAAkBtB,OAApD,CAFiE;;EAKjE,MAAIwB,SAAS,GAAGH,iBAAiB,CAACI,OAAlB,CAA0BtC,cAA1B,EAA0C,EAA1C,CAAhB;EACA,MAAMuC,MAAM,GAAGnC,YAAY,CAACiC,SAAD,CAA3B;;EAEA,MAAIE,MAAJ,EAAY;EACVF,IAAAA,SAAS,GAAGE,MAAZ;EACD;;EAED,MAAMC,QAAQ,GAAGjC,YAAY,CAACkC,OAAb,CAAqBJ,SAArB,IAAkC,CAAC,CAApD;;EAEA,MAAI,CAACG,QAAL,EAAe;EACbH,IAAAA,SAAS,GAAGH,iBAAZ;EACD;;EAED,SAAO,CAACE,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,CAAP;EACD;;EAED,SAASK,UAAT,CAAoB/H,OAApB,EAA6BuH,iBAA7B,EAAgDrB,OAAhD,EAAyDsB,YAAzD,EAAuEnB,MAAvE,EAA+E;EAC7E,MAAI,OAAOkB,iBAAP,KAA6B,QAA7B,IAAyC,CAACvH,OAA9C,EAAuD;EACrD;EACD;;EAED,MAAI,CAACkG,OAAL,EAAc;EACZA,IAAAA,OAAO,GAAGsB,YAAV;EACAA,IAAAA,YAAY,GAAG,IAAf;EACD;;EAR4E,yBAU5BF,eAAe,CAACC,iBAAD,EAAoBrB,OAApB,EAA6BsB,YAA7B,CAVa;EAAA,MAUtEC,UAVsE;EAAA,MAU1DJ,eAV0D;EAAA,MAUzCK,SAVyC;;EAW7E,MAAMT,MAAM,GAAGlB,QAAQ,CAAC/F,OAAD,CAAvB;EACA,MAAMgI,QAAQ,GAAGf,MAAM,CAACS,SAAD,CAAN,KAAsBT,MAAM,CAACS,SAAD,CAAN,GAAoB,EAA1C,CAAjB;EACA,MAAMO,UAAU,GAAGjB,WAAW,CAACgB,QAAD,EAAWX,eAAX,EAA4BI,UAAU,GAAGvB,OAAH,GAAa,IAAnD,CAA9B;;EAEA,MAAI+B,UAAJ,EAAgB;EACdA,IAAAA,UAAU,CAAC5B,MAAX,GAAoB4B,UAAU,CAAC5B,MAAX,IAAqBA,MAAzC;EAEA;EACD;;EAED,MAAMP,GAAG,GAAGD,WAAW,CAACwB,eAAD,EAAkBE,iBAAiB,CAACI,OAAlB,CAA0BvC,cAA1B,EAA0C,EAA1C,CAAlB,CAAvB;EACA,MAAMa,EAAE,GAAGwB,UAAU,GACnBf,0BAA0B,CAAC1G,OAAD,EAAUkG,OAAV,EAAmBsB,YAAnB,CADP,GAEnBxB,gBAAgB,CAAChG,OAAD,EAAUkG,OAAV,CAFlB;EAIAD,EAAAA,EAAE,CAACiB,kBAAH,GAAwBO,UAAU,GAAGvB,OAAH,GAAa,IAA/C;EACAD,EAAAA,EAAE,CAACoB,eAAH,GAAqBA,eAArB;EACApB,EAAAA,EAAE,CAACI,MAAH,GAAYA,MAAZ;EACAJ,EAAAA,EAAE,CAACT,QAAH,GAAcM,GAAd;EACAkC,EAAAA,QAAQ,CAAClC,GAAD,CAAR,GAAgBG,EAAhB;EAEAjG,EAAAA,OAAO,CAAC6B,gBAAR,CAAyB6F,SAAzB,EAAoCzB,EAApC,EAAwCwB,UAAxC;EACD;;EAED,SAASS,aAAT,CAAuBlI,OAAvB,EAAgCiH,MAAhC,EAAwCS,SAAxC,EAAmDxB,OAAnD,EAA4DgB,kBAA5D,EAAgF;EAC9E,MAAMjB,EAAE,GAAGe,WAAW,CAACC,MAAM,CAACS,SAAD,CAAP,EAAoBxB,OAApB,EAA6BgB,kBAA7B,CAAtB;;EAEA,MAAI,CAACjB,EAAL,EAAS;EACP;EACD;;EAEDjG,EAAAA,OAAO,CAAC4B,mBAAR,CAA4B8F,SAA5B,EAAuCzB,EAAvC,EAA2CkC,OAAO,CAACjB,kBAAD,CAAlD;EACA,SAAOD,MAAM,CAACS,SAAD,CAAN,CAAkBzB,EAAE,CAACT,QAArB,CAAP;EACD;;EAED,SAAS4C,wBAAT,CAAkCpI,OAAlC,EAA2CiH,MAA3C,EAAmDS,SAAnD,EAA8DW,SAA9D,EAAyE;EACvE,MAAMC,iBAAiB,GAAGrB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;EAEAvF,EAAAA,MAAM,CAACC,IAAP,CAAYkG,iBAAZ,EAA+BjG,OAA/B,CAAuC,UAAAkG,UAAU,EAAI;EACnD,QAAIA,UAAU,CAACT,OAAX,CAAmBO,SAAnB,IAAgC,CAAC,CAArC,EAAwC;EACtC,UAAMlC,KAAK,GAAGmC,iBAAiB,CAACC,UAAD,CAA/B;EAEAL,MAAAA,aAAa,CAAClI,OAAD,EAAUiH,MAAV,EAAkBS,SAAlB,EAA6BvB,KAAK,CAACkB,eAAnC,EAAoDlB,KAAK,CAACe,kBAA1D,CAAb;EACD;EACF,GAND;EAOD;;EAED,IAAMZ,YAAY,GAAG;EACnBkC,EAAAA,EADmB,cAChBxI,OADgB,EACPmG,KADO,EACAD,OADA,EACSsB,YADT,EACuB;EACxCO,IAAAA,UAAU,CAAC/H,OAAD,EAAUmG,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC,KAAxC,CAAV;EACD,GAHkB;EAKnBiB,EAAAA,GALmB,eAKfzI,OALe,EAKNmG,KALM,EAKCD,OALD,EAKUsB,YALV,EAKwB;EACzCO,IAAAA,UAAU,CAAC/H,OAAD,EAAUmG,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC,IAAxC,CAAV;EACD,GAPkB;EASnBjB,EAAAA,GATmB,eASfvG,OATe,EASNuH,iBATM,EASarB,OATb,EASsBsB,YATtB,EASoC;EACrD,QAAI,OAAOD,iBAAP,KAA6B,QAA7B,IAAyC,CAACvH,OAA9C,EAAuD;EACrD;EACD;;EAHoD,4BAKJsH,eAAe,CAACC,iBAAD,EAAoBrB,OAApB,EAA6BsB,YAA7B,CALX;EAAA,QAK9CC,UAL8C;EAAA,QAKlCJ,eALkC;EAAA,QAKjBK,SALiB;;EAMrD,QAAMgB,WAAW,GAAGhB,SAAS,KAAKH,iBAAlC;EACA,QAAMN,MAAM,GAAGlB,QAAQ,CAAC/F,OAAD,CAAvB;EACA,QAAM2I,WAAW,GAAGpB,iBAAiB,CAACqB,MAAlB,CAAyB,CAAzB,MAAgC,GAApD;;EAEA,QAAI,OAAOvB,eAAP,KAA2B,WAA/B,EAA4C;EAC1C;EACA,UAAI,CAACJ,MAAD,IAAW,CAACA,MAAM,CAACS,SAAD,CAAtB,EAAmC;EACjC;EACD;;EAEDQ,MAAAA,aAAa,CAAClI,OAAD,EAAUiH,MAAV,EAAkBS,SAAlB,EAA6BL,eAA7B,EAA8CI,UAAU,GAAGvB,OAAH,GAAa,IAArE,CAAb;EACA;EACD;;EAED,QAAIyC,WAAJ,EAAiB;EACfxG,MAAAA,MAAM,CAACC,IAAP,CAAY6E,MAAZ,EAAoB5E,OAApB,CAA4B,UAAAwG,YAAY,EAAI;EAC1CT,QAAAA,wBAAwB,CAACpI,OAAD,EAAUiH,MAAV,EAAkB4B,YAAlB,EAAgCtB,iBAAiB,CAACuB,KAAlB,CAAwB,CAAxB,CAAhC,CAAxB;EACD,OAFD;EAGD;;EAED,QAAMR,iBAAiB,GAAGrB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;EACAvF,IAAAA,MAAM,CAACC,IAAP,CAAYkG,iBAAZ,EAA+BjG,OAA/B,CAAuC,UAAA0G,WAAW,EAAI;EACpD,UAAMR,UAAU,GAAGQ,WAAW,CAACpB,OAAZ,CAAoBrC,aAApB,EAAmC,EAAnC,CAAnB;;EAEA,UAAI,CAACoD,WAAD,IAAgBnB,iBAAiB,CAACO,OAAlB,CAA0BS,UAA1B,IAAwC,CAAC,CAA7D,EAAgE;EAC9D,YAAMpC,KAAK,GAAGmC,iBAAiB,CAACS,WAAD,CAA/B;EAEAb,QAAAA,aAAa,CAAClI,OAAD,EAAUiH,MAAV,EAAkBS,SAAlB,EAA6BvB,KAAK,CAACkB,eAAnC,EAAoDlB,KAAK,CAACe,kBAA1D,CAAb;EACD;EACF,KARD;EASD,GA7CkB;EA+CnB8B,EAAAA,OA/CmB,mBA+CXhJ,OA/CW,EA+CFmG,KA/CE,EA+CK8C,IA/CL,EA+CW;EAC5B,QAAI,OAAO9C,KAAP,KAAiB,QAAjB,IAA6B,CAACnG,OAAlC,EAA2C;EACzC,aAAO,IAAP;EACD;;EAED,QAAMkJ,CAAC,GAAGpF,SAAS,EAAnB;EACA,QAAM4D,SAAS,GAAGvB,KAAK,CAACwB,OAAN,CAActC,cAAd,EAA8B,EAA9B,CAAlB;EACA,QAAMqD,WAAW,GAAGvC,KAAK,KAAKuB,SAA9B;EACA,QAAMG,QAAQ,GAAGjC,YAAY,CAACkC,OAAb,CAAqBJ,SAArB,IAAkC,CAAC,CAApD;EAEA,QAAIyB,WAAJ;EACA,QAAIC,OAAO,GAAG,IAAd;EACA,QAAIC,cAAc,GAAG,IAArB;EACA,QAAIC,gBAAgB,GAAG,KAAvB;EACA,QAAIC,GAAG,GAAG,IAAV;;EAEA,QAAIb,WAAW,IAAIQ,CAAnB,EAAsB;EACpBC,MAAAA,WAAW,GAAGD,CAAC,CAAC/H,KAAF,CAAQgF,KAAR,EAAe8C,IAAf,CAAd;EAEAC,MAAAA,CAAC,CAAClJ,OAAD,CAAD,CAAWgJ,OAAX,CAAmBG,WAAnB;EACAC,MAAAA,OAAO,GAAG,CAACD,WAAW,CAACK,oBAAZ,EAAX;EACAH,MAAAA,cAAc,GAAG,CAACF,WAAW,CAACM,6BAAZ,EAAlB;EACAH,MAAAA,gBAAgB,GAAGH,WAAW,CAACO,kBAAZ,EAAnB;EACD;;EAED,QAAI7B,QAAJ,EAAc;EACZ0B,MAAAA,GAAG,GAAG1J,QAAQ,CAAC8J,WAAT,CAAqB,YAArB,CAAN;EACAJ,MAAAA,GAAG,CAACK,SAAJ,CAAclC,SAAd,EAAyB0B,OAAzB,EAAkC,IAAlC;EACD,KAHD,MAGO;EACLG,MAAAA,GAAG,GAAG,IAAIM,WAAJ,CAAgB1D,KAAhB,EAAuB;EAC3BiD,QAAAA,OAAO,EAAPA,OAD2B;EAE3BU,QAAAA,UAAU,EAAE;EAFe,OAAvB,CAAN;EAID,KAjC2B;;;EAoC5B,QAAI,OAAOb,IAAP,KAAgB,WAApB,EAAiC;EAC/B9G,MAAAA,MAAM,CAACC,IAAP,CAAY6G,IAAZ,EAAkB5G,OAAlB,CAA0B,UAAAoC,GAAG,EAAI;EAC/BtC,QAAAA,MAAM,CAAC4H,cAAP,CAAsBR,GAAtB,EAA2B9E,GAA3B,EAAgC;EAC9BG,UAAAA,GAD8B,iBACxB;EACJ,mBAAOqE,IAAI,CAACxE,GAAD,CAAX;EACD;EAH6B,SAAhC;EAKD,OAND;EAOD;;EAED,QAAI6E,gBAAJ,EAAsB;EACpBC,MAAAA,GAAG,CAACS,cAAJ;EACD;;EAED,QAAIX,cAAJ,EAAoB;EAClBrJ,MAAAA,OAAO,CAACkB,aAAR,CAAsBqI,GAAtB;EACD;;EAED,QAAIA,GAAG,CAACD,gBAAJ,IAAwB,OAAOH,WAAP,KAAuB,WAAnD,EAAgE;EAC9DA,MAAAA,WAAW,CAACa,cAAZ;EACD;;EAED,WAAOT,GAAP;EACD;EA1GkB,CAArB;;EC1MA;EACA;EACA;EACA;EACA;;EAEA,IAAMU,IAAI,GAAG,OAAb;EACA,IAAMC,OAAO,GAAG,cAAhB;EACA,IAAMC,QAAQ,GAAG,UAAjB;EACA,IAAMC,SAAS,SAAOD,QAAtB;EACA,IAAME,YAAY,GAAG,WAArB;EAEA,IAAMC,gBAAgB,GAAG,wBAAzB;EAEA,IAAMC,WAAW,aAAWH,SAA5B;EACA,IAAMI,YAAY,cAAYJ,SAA9B;EACA,IAAMK,oBAAoB,aAAWL,SAAX,GAAuBC,YAAjD;EAEA,IAAMK,eAAe,GAAG,OAAxB;EACA,IAAMC,cAAc,GAAG,MAAvB;EACA,IAAMC,cAAc,GAAG,MAAvB;EAEA;EACA;EACA;EACA;EACA;;MAEMC;EACJ,iBAAY7K,OAAZ,EAAqB;EACnB,SAAK8K,QAAL,GAAgB9K,OAAhB;;EAEA,QAAI,KAAK8K,QAAT,EAAmB;EACjB/F,MAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,QAAtB,EAAgC,IAAhC;EACD;EACF;;;;;EAQD;WAEAY,QAAA,eAAM/K,OAAN,EAAe;EACb,QAAMgL,WAAW,GAAGhL,OAAO,GAAG,KAAKiL,eAAL,CAAqBjL,OAArB,CAAH,GAAmC,KAAK8K,QAAnE;;EACA,QAAMI,WAAW,GAAG,KAAKC,kBAAL,CAAwBH,WAAxB,CAApB;;EAEA,QAAIE,WAAW,KAAK,IAAhB,IAAwBA,WAAW,CAAC5B,gBAAxC,EAA0D;EACxD;EACD;;EAED,SAAK8B,cAAL,CAAoBJ,WAApB;EACD;;WAEDK,UAAA,mBAAU;EACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,QAA/B;EACA,SAAKW,QAAL,GAAgB,IAAhB;EACD;;;WAIDG,kBAAA,yBAAgBjL,OAAhB,EAAyB;EACvB,WAAOO,sBAAsB,CAACP,OAAD,CAAtB,IAAmCA,OAAO,CAACsL,OAAR,OAAoBZ,eAApB,CAA1C;EACD;;WAEDS,qBAAA,4BAAmBnL,OAAnB,EAA4B;EAC1B,WAAOsG,YAAY,CAAC0C,OAAb,CAAqBhJ,OAArB,EAA8BuK,WAA9B,CAAP;EACD;;WAEDa,iBAAA,wBAAepL,OAAf,EAAwB;EAAA;;EACtBA,IAAAA,OAAO,CAACuL,SAAR,CAAkBC,MAAlB,CAAyBZ,cAAzB;;EAEA,QAAI,CAAC5K,OAAO,CAACuL,SAAR,CAAkBE,QAAlB,CAA2Bd,cAA3B,CAAL,EAAiD;EAC/C,WAAKe,eAAL,CAAqB1L,OAArB;;EACA;EACD;;EAED,QAAMW,kBAAkB,GAAGH,gCAAgC,CAACR,OAAD,CAA3D;EAEAsG,IAAAA,YAAY,CAACmC,GAAb,CAAiBzI,OAAjB,EAA0BhB,cAA1B,EAA0C;EAAA,aAAM,KAAI,CAAC0M,eAAL,CAAqB1L,OAArB,CAAN;EAAA,KAA1C;EACAsB,IAAAA,oBAAoB,CAACtB,OAAD,EAAUW,kBAAV,CAApB;EACD;;WAED+K,kBAAA,yBAAgB1L,OAAhB,EAAyB;EACvB,QAAIA,OAAO,CAACgD,UAAZ,EAAwB;EACtBhD,MAAAA,OAAO,CAACgD,UAAR,CAAmB2I,WAAnB,CAA+B3L,OAA/B;EACD;;EAEDsG,IAAAA,YAAY,CAAC0C,OAAb,CAAqBhJ,OAArB,EAA8BwK,YAA9B;EACD;;;UAIMoB,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,QAAnB,CAAX;;EAEA,UAAI,CAACzF,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAImG,KAAJ,CAAU,IAAV,CAAP;EACD;;EAED,UAAI5I,MAAM,KAAK,OAAf,EAAwB;EACtByC,QAAAA,IAAI,CAACzC,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAVM,CAAP;EAWD;;UAEM6J,gBAAP,uBAAqBC,aAArB,EAAoC;EAClC,WAAO,UAAU5F,KAAV,EAAiB;EACtB,UAAIA,KAAJ,EAAW;EACTA,QAAAA,KAAK,CAAC6D,cAAN;EACD;;EAED+B,MAAAA,aAAa,CAAChB,KAAd,CAAoB,IAApB;EACD,KAND;EAOD;;UAEMiB,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,QAAtB,CAAP;EACD;;;;0BAlFoB;EACnB,aAAOD,OAAP;EACD;;;;;EAmFH;EACA;EACA;EACA;EACA;;;EACA5D,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,oBAA1B,EAAgDH,gBAAhD,EAAkEO,KAAK,CAACiB,aAAN,CAAoB,IAAIjB,KAAJ,EAApB,CAAlE;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA3G,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,IAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,IAAL,IAAaY,KAAK,CAACe,eAAnB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,IAAL,EAAWiC,WAAX,GAAyBrB,KAAzB;;EACA3B,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,IAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,IAAL,IAAagC,kBAAb;EACA,aAAOpB,KAAK,CAACe,eAAb;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;ECjJA;EACA;EACA;EACA;EACA;;EAEA,IAAM3B,MAAI,GAAG,QAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,WAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAM+B,iBAAiB,GAAG,QAA1B;EAEA,IAAMC,oBAAoB,GAAG,wBAA7B;EAEA,IAAM5B,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EAEA;EACA;EACA;EACA;EACA;;MAEMiC;EACJ,kBAAYtM,OAAZ,EAAqB;EACnB,SAAK8K,QAAL,GAAgB9K,OAAhB;EACA+E,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;EACD;;;;;EAQD;WAEAoC,SAAA,kBAAS;EACP;EACA,SAAKzB,QAAL,CAAc0B,YAAd,CAA2B,cAA3B,EAA2C,KAAK1B,QAAL,CAAcS,SAAd,CAAwBgB,MAAxB,CAA+BH,iBAA/B,CAA3C;EACD;;WAEDf,UAAA,mBAAU;EACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EACA,SAAKW,QAAL,GAAgB,IAAhB;EACD;;;WAIMc,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;EAEA,UAAI,CAACzF,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI4H,MAAJ,CAAW,IAAX,CAAP;EACD;;EAED,UAAIrK,MAAM,KAAK,QAAf,EAAyB;EACvByC,QAAAA,IAAI,CAACzC,MAAD,CAAJ;EACD;EACF,KAVM,CAAP;EAWD;;WAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BAlCoB;EACnB,aAAOD,SAAP;EACD;;;;;EAmCH;EACA;EACA;EACA;EACA;;;EAEA5D,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4B,oBAAhD,EAAsE,UAAAlG,KAAK,EAAI;EAC7EA,EAAAA,KAAK,CAAC6D,cAAN;EAEA,MAAMyC,MAAM,GAAGtG,KAAK,CAACU,MAAN,CAAayE,OAAb,CAAqBe,oBAArB,CAAf;EAEA,MAAI3H,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAauH,MAAb,EAAqBtC,UAArB,CAAX;;EACA,MAAI,CAACzF,IAAL,EAAW;EACTA,IAAAA,IAAI,GAAG,IAAI4H,MAAJ,CAAWG,MAAX,CAAP;EACD;;EAED/H,EAAAA,IAAI,CAAC6H,MAAL;EACD,CAXD;EAaA;EACA;EACA;EACA;EACA;EACA;;EAEArI,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAaqC,MAAM,CAACV,eAApB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBI,MAAzB;;EAEApD,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOK,MAAM,CAACV,eAAd;EACD,KAHD;EAID;EACF,CAbiB,CAAlB;;EC1GA;EACA;EACA;EACA;EACA;EACA;EAEA,SAASc,aAAT,CAAuBC,GAAvB,EAA4B;EAC1B,MAAIA,GAAG,KAAK,MAAZ,EAAoB;EAClB,WAAO,IAAP;EACD;;EAED,MAAIA,GAAG,KAAK,OAAZ,EAAqB;EACnB,WAAO,KAAP;EACD;;EAED,MAAIA,GAAG,KAAKC,MAAM,CAACD,GAAD,CAAN,CAAYvN,QAAZ,EAAZ,EAAoC;EAClC,WAAOwN,MAAM,CAACD,GAAD,CAAb;EACD;;EAED,MAAIA,GAAG,KAAK,EAAR,IAAcA,GAAG,KAAK,MAA1B,EAAkC;EAChC,WAAO,IAAP;EACD;;EAED,SAAOA,GAAP;EACD;;EAED,SAASE,gBAAT,CAA0BpI,GAA1B,EAA+B;EAC7B,SAAOA,GAAG,CAACkD,OAAJ,CAAY,QAAZ,EAAsB,UAAAmF,GAAG;EAAA,iBAAQA,GAAG,CAACvN,WAAJ,EAAR;EAAA,GAAzB,CAAP;EACD;;EAED,IAAMwN,WAAW,GAAG;EAClBC,EAAAA,gBADkB,4BACDhN,OADC,EACQyE,GADR,EACajC,KADb,EACoB;EACpCxC,IAAAA,OAAO,CAACwM,YAAR,WAA6BK,gBAAgB,CAACpI,GAAD,CAA7C,EAAsDjC,KAAtD;EACD,GAHiB;EAKlByK,EAAAA,mBALkB,+BAKEjN,OALF,EAKWyE,GALX,EAKgB;EAChCzE,IAAAA,OAAO,CAACkN,eAAR,WAAgCL,gBAAgB,CAACpI,GAAD,CAAhD;EACD,GAPiB;EASlB0I,EAAAA,iBATkB,6BASAnN,OATA,EASS;EACzB,QAAI,CAACA,OAAL,EAAc;EACZ,aAAO,EAAP;EACD;;EAED,QAAMoN,UAAU,gBACXpN,OAAO,CAACqN,OADG,CAAhB;;EAIAlL,IAAAA,MAAM,CAACC,IAAP,CAAYgL,UAAZ,EAAwB/K,OAAxB,CAAgC,UAAAoC,GAAG,EAAI;EACrC2I,MAAAA,UAAU,CAAC3I,GAAD,CAAV,GAAkBiI,aAAa,CAACU,UAAU,CAAC3I,GAAD,CAAX,CAA/B;EACD,KAFD;EAIA,WAAO2I,UAAP;EACD,GAvBiB;EAyBlBE,EAAAA,gBAzBkB,4BAyBDtN,OAzBC,EAyBQyE,GAzBR,EAyBa;EAC7B,WAAOiI,aAAa,CAAC1M,OAAO,CAACE,YAAR,WAA6B2M,gBAAgB,CAACpI,GAAD,CAA7C,CAAD,CAApB;EACD,GA3BiB;EA6BlB8I,EAAAA,MA7BkB,kBA6BXvN,OA7BW,EA6BF;EACd,QAAMwN,IAAI,GAAGxN,OAAO,CAACyN,qBAAR,EAAb;EAEA,WAAO;EACLC,MAAAA,GAAG,EAAEF,IAAI,CAACE,GAAL,GAAW7N,QAAQ,CAACmE,IAAT,CAAc2J,SADzB;EAELC,MAAAA,IAAI,EAAEJ,IAAI,CAACI,IAAL,GAAY/N,QAAQ,CAACmE,IAAT,CAAc6J;EAF3B,KAAP;EAID,GApCiB;EAsClBC,EAAAA,QAtCkB,oBAsCT9N,OAtCS,EAsCA;EAChB,WAAO;EACL0N,MAAAA,GAAG,EAAE1N,OAAO,CAAC+N,SADR;EAELH,MAAAA,IAAI,EAAE5N,OAAO,CAACgO;EAFT,KAAP;EAID;EA3CiB,CAApB;;EC/BA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EAEA,IAAMC,SAAS,GAAG,CAAlB;EAEA,IAAMC,cAAc,GAAG;EACrBC,EAAAA,OADqB,mBACbnO,OADa,EACJC,QADI,EACM;EACzB,WAAOD,OAAO,CAACmO,OAAR,CAAgBlO,QAAhB,CAAP;EACD,GAHoB;EAKrBmO,EAAAA,IALqB,gBAKhBnO,QALgB,EAKND,OALM,EAK8B;EAAA;;EAAA,QAApCA,OAAoC;EAApCA,MAAAA,OAAoC,GAA1BH,QAAQ,CAACyD,eAAiB;EAAA;;EACjD,WAAO,YAAG+K,MAAH,aAAaC,OAAO,CAACC,SAAR,CAAkB3H,gBAAlB,CAAmCvH,IAAnC,CAAwCW,OAAxC,EAAiDC,QAAjD,CAAb,CAAP;EACD,GAPoB;EASrBuO,EAAAA,OATqB,mBASbvO,QATa,EASHD,OATG,EASiC;EAAA,QAApCA,OAAoC;EAApCA,MAAAA,OAAoC,GAA1BH,QAAQ,CAACyD,eAAiB;EAAA;;EACpD,WAAOgL,OAAO,CAACC,SAAR,CAAkBjO,aAAlB,CAAgCjB,IAAhC,CAAqCW,OAArC,EAA8CC,QAA9C,CAAP;EACD,GAXoB;EAarBwO,EAAAA,QAbqB,oBAaZzO,OAbY,EAaHC,QAbG,EAaO;EAAA;;EAC1B,QAAMwO,QAAQ,GAAG,aAAGJ,MAAH,cAAarO,OAAO,CAACyO,QAArB,CAAjB;;EAEA,WAAOA,QAAQ,CAACC,MAAT,CAAgB,UAAAC,KAAK;EAAA,aAAIA,KAAK,CAACR,OAAN,CAAclO,QAAd,CAAJ;EAAA,KAArB,CAAP;EACD,GAjBoB;EAmBrB2O,EAAAA,OAnBqB,mBAmBb5O,OAnBa,EAmBJC,QAnBI,EAmBM;EACzB,QAAM2O,OAAO,GAAG,EAAhB;EAEA,QAAIC,QAAQ,GAAG7O,OAAO,CAACgD,UAAvB;;EAEA,WAAO6L,QAAQ,IAAIA,QAAQ,CAACxN,QAAT,KAAsByN,IAAI,CAACC,YAAvC,IAAuDF,QAAQ,CAACxN,QAAT,KAAsB4M,SAApF,EAA+F;EAC7F,UAAI,KAAKE,OAAL,CAAaU,QAAb,EAAuB5O,QAAvB,CAAJ,EAAsC;EACpC2O,QAAAA,OAAO,CAACI,IAAR,CAAaH,QAAb;EACD;;EAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAAC7L,UAApB;EACD;;EAED,WAAO4L,OAAP;EACD,GAjCoB;EAmCrBK,EAAAA,IAnCqB,gBAmChBjP,OAnCgB,EAmCPC,QAnCO,EAmCG;EACtB,QAAIiP,QAAQ,GAAGlP,OAAO,CAACmP,sBAAvB;;EAEA,WAAOD,QAAP,EAAiB;EACf,UAAIA,QAAQ,CAACf,OAAT,CAAiBlO,QAAjB,CAAJ,EAAgC;EAC9B,eAAO,CAACiP,QAAD,CAAP;EACD;;EAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACC,sBAApB;EACD;;EAED,WAAO,EAAP;EACD,GA/CoB;EAiDrBC,EAAAA,IAjDqB,gBAiDhBpP,OAjDgB,EAiDPC,QAjDO,EAiDG;EACtB,QAAImP,IAAI,GAAGpP,OAAO,CAACqP,kBAAnB;;EAEA,WAAOD,IAAP,EAAa;EACX,UAAI,KAAKjB,OAAL,CAAaiB,IAAb,EAAmBnP,QAAnB,CAAJ,EAAkC;EAChC,eAAO,CAACmP,IAAD,CAAP;EACD;;EAEDA,MAAAA,IAAI,GAAGA,IAAI,CAACC,kBAAZ;EACD;;EAED,WAAO,EAAP;EACD;EA7DoB,CAAvB;;ECSA;EACA;EACA;EACA;EACA;;EAEA,IAAMpF,MAAI,GAAG,UAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,aAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAMiF,cAAc,GAAG,WAAvB;EACA,IAAMC,eAAe,GAAG,YAAxB;EACA,IAAMC,sBAAsB,GAAG,GAA/B;;EACA,IAAMC,eAAe,GAAG,EAAxB;EAEA,IAAMC,OAAO,GAAG;EACdC,EAAAA,QAAQ,EAAE,IADI;EAEdC,EAAAA,QAAQ,EAAE,IAFI;EAGdC,EAAAA,KAAK,EAAE,KAHO;EAIdC,EAAAA,KAAK,EAAE,OAJO;EAKdC,EAAAA,IAAI,EAAE,IALQ;EAMdC,EAAAA,KAAK,EAAE;EANO,CAAhB;EASA,IAAMC,WAAW,GAAG;EAClBN,EAAAA,QAAQ,EAAE,kBADQ;EAElBC,EAAAA,QAAQ,EAAE,SAFQ;EAGlBC,EAAAA,KAAK,EAAE,kBAHW;EAIlBC,EAAAA,KAAK,EAAE,kBAJW;EAKlBC,EAAAA,IAAI,EAAE,SALY;EAMlBC,EAAAA,KAAK,EAAE;EANW,CAApB;EASA,IAAME,cAAc,GAAG,MAAvB;EACA,IAAMC,cAAc,GAAG,MAAvB;EACA,IAAMC,cAAc,GAAG,MAAvB;EACA,IAAMC,eAAe,GAAG,OAAxB;EAEA,IAAMC,WAAW,aAAWlG,WAA5B;EACA,IAAMmG,UAAU,YAAUnG,WAA1B;EACA,IAAMoG,aAAa,eAAapG,WAAhC;EACA,IAAMqG,gBAAgB,kBAAgBrG,WAAtC;EACA,IAAMsG,gBAAgB,kBAAgBtG,WAAtC;EACA,IAAMuG,gBAAgB,kBAAgBvG,WAAtC;EACA,IAAMwG,eAAe,iBAAexG,WAApC;EACA,IAAMyG,cAAc,gBAAczG,WAAlC;EACA,IAAM0G,iBAAiB,mBAAiB1G,WAAxC;EACA,IAAM2G,eAAe,iBAAe3G,WAApC;EACA,IAAM4G,gBAAgB,iBAAe5G,WAArC;EACA,IAAM6G,mBAAmB,YAAU7G,WAAV,GAAsBC,cAA/C;EACA,IAAMI,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EAEA,IAAM6G,mBAAmB,GAAG,UAA5B;EACA,IAAM9E,mBAAiB,GAAG,QAA1B;EACA,IAAM+E,gBAAgB,GAAG,OAAzB;EACA,IAAMC,gBAAgB,GAAG,qBAAzB;EACA,IAAMC,eAAe,GAAG,oBAAxB;EACA,IAAMC,eAAe,GAAG,oBAAxB;EACA,IAAMC,eAAe,GAAG,oBAAxB;EACA,IAAMC,wBAAwB,GAAG,eAAjC;EAEA,IAAMC,eAAe,GAAG,SAAxB;EACA,IAAMC,oBAAoB,GAAG,uBAA7B;EACA,IAAMC,aAAa,GAAG,gBAAtB;EACA,IAAMC,iBAAiB,GAAG,oBAA1B;EACA,IAAMC,kBAAkB,GAAG,0CAA3B;EACA,IAAMC,mBAAmB,GAAG,sBAA5B;EACA,IAAMC,mBAAmB,GAAG,+BAA5B;EACA,IAAMC,kBAAkB,GAAG,wBAA3B;EAEA,IAAMC,WAAW,GAAG;EAClBC,EAAAA,KAAK,EAAE,OADW;EAElBC,EAAAA,GAAG,EAAE;EAFa,CAApB;EAKA;EACA;EACA;EACA;EACA;;MACMC;EACJ,oBAAYpS,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,SAAKoQ,MAAL,GAAc,IAAd;EACA,SAAKC,SAAL,GAAiB,IAAjB;EACA,SAAKC,cAAL,GAAsB,IAAtB;EACA,SAAKC,SAAL,GAAiB,KAAjB;EACA,SAAKC,UAAL,GAAkB,KAAlB;EACA,SAAKC,YAAL,GAAoB,IAApB;EACA,SAAKC,WAAL,GAAmB,CAAnB;EACA,SAAKC,WAAL,GAAmB,CAAnB;EAEA,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;EACA,SAAK6I,QAAL,GAAgB9K,OAAhB;EACA,SAAK+S,kBAAL,GAA0B7E,cAAc,CAACM,OAAf,CAAuBsD,mBAAvB,EAA4C,KAAKhH,QAAjD,CAA1B;EACA,SAAKkI,eAAL,GAAuB,kBAAkBnT,QAAQ,CAACyD,eAA3B,IAA8C2P,SAAS,CAACC,cAAV,GAA2B,CAAhG;EACA,SAAKC,aAAL,GAAqBhL,OAAO,CAAC1H,MAAM,CAAC2S,YAAR,CAA5B;;EAEA,SAAKC,kBAAL;;EACAtO,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;EACD;;;;;EAYD;WAEAiF,OAAA,gBAAO;EACL,QAAI,CAAC,KAAKqD,UAAV,EAAsB;EACpB,WAAKa,MAAL,CAAYpD,cAAZ;EACD;EACF;;WAEDqD,kBAAA,2BAAkB;EAChB;EACA;EACA,QAAI,CAAC1T,QAAQ,CAAC2T,MAAV,IAAoB1Q,SAAS,CAAC,KAAKgI,QAAN,CAAjC,EAAkD;EAChD,WAAKsE,IAAL;EACD;EACF;;WAEDH,OAAA,gBAAO;EACL,QAAI,CAAC,KAAKwD,UAAV,EAAsB;EACpB,WAAKa,MAAL,CAAYnD,cAAZ;EACD;EACF;;WAEDL,QAAA,eAAM3J,KAAN,EAAa;EACX,QAAI,CAACA,KAAL,EAAY;EACV,WAAKqM,SAAL,GAAiB,IAAjB;EACD;;EAED,QAAItE,cAAc,CAACM,OAAf,CAAuBqD,kBAAvB,EAA2C,KAAK/G,QAAhD,CAAJ,EAA+D;EAC7D7J,MAAAA,oBAAoB,CAAC,KAAK6J,QAAN,CAApB;EACA,WAAK2I,KAAL,CAAW,IAAX;EACD;;EAEDC,IAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;EACA,SAAKA,SAAL,GAAiB,IAAjB;EACD;;WAEDmB,QAAA,eAAMtN,KAAN,EAAa;EACX,QAAI,CAACA,KAAL,EAAY;EACV,WAAKqM,SAAL,GAAiB,KAAjB;EACD;;EAED,QAAI,KAAKF,SAAT,EAAoB;EAClBoB,MAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;EACA,WAAKA,SAAL,GAAiB,IAAjB;EACD;;EAED,QAAI,KAAKO,OAAL,IAAgB,KAAKA,OAAL,CAAalD,QAA7B,IAAyC,CAAC,KAAK6C,SAAnD,EAA8D;EAC5D,WAAKmB,eAAL;;EAEA,WAAKrB,SAAL,GAAiBsB,WAAW,CAC1B,CAAC/T,QAAQ,CAACgU,eAAT,GAA2B,KAAKN,eAAhC,GAAkD,KAAKnE,IAAxD,EAA8D0E,IAA9D,CAAmE,IAAnE,CAD0B,EAE1B,KAAKjB,OAAL,CAAalD,QAFa,CAA5B;EAID;EACF;;WAEDoE,KAAA,YAAGC,KAAH,EAAU;EAAA;;EACR,SAAKzB,cAAL,GAAsBrE,cAAc,CAACM,OAAf,CAAuBkD,oBAAvB,EAA6C,KAAK5G,QAAlD,CAAtB;;EACA,QAAMmJ,WAAW,GAAG,KAAKC,aAAL,CAAmB,KAAK3B,cAAxB,CAApB;;EAEA,QAAIyB,KAAK,GAAG,KAAK3B,MAAL,CAAYtL,MAAZ,GAAqB,CAA7B,IAAkCiN,KAAK,GAAG,CAA9C,EAAiD;EAC/C;EACD;;EAED,QAAI,KAAKvB,UAAT,EAAqB;EACnBnM,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgCyF,UAAhC,EAA4C;EAAA,eAAM,KAAI,CAACwD,EAAL,CAAQC,KAAR,CAAN;EAAA,OAA5C;EACA;EACD;;EAED,QAAIC,WAAW,KAAKD,KAApB,EAA2B;EACzB,WAAKlE,KAAL;EACA,WAAK2D,KAAL;EACA;EACD;;EAED,QAAMU,SAAS,GAAGH,KAAK,GAAGC,WAAR,GAChB/D,cADgB,GAEhBC,cAFF;;EAIA,SAAKmD,MAAL,CAAYa,SAAZ,EAAuB,KAAK9B,MAAL,CAAY2B,KAAZ,CAAvB;EACD;;WAED3I,UAAA,mBAAU;EACR/E,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgCV,WAAhC;EACArF,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EAEA,SAAKkI,MAAL,GAAc,IAAd;EACA,SAAKQ,OAAL,GAAe,IAAf;EACA,SAAK/H,QAAL,GAAgB,IAAhB;EACA,SAAKwH,SAAL,GAAiB,IAAjB;EACA,SAAKE,SAAL,GAAiB,IAAjB;EACA,SAAKC,UAAL,GAAkB,IAAlB;EACA,SAAKF,cAAL,GAAsB,IAAtB;EACA,SAAKQ,kBAAL,GAA0B,IAA1B;EACD;;;WAIDD,aAAA,oBAAW7Q,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACDyN,OADC,EAEDzN,MAFC,CAAN;EAIAF,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAegO,WAAf,CAAf;EACA,WAAOhO,MAAP;EACD;;WAEDmS,eAAA,wBAAe;EACb,QAAMC,SAAS,GAAG3U,IAAI,CAAC4U,GAAL,CAAS,KAAK1B,WAAd,CAAlB;;EAEA,QAAIyB,SAAS,IAAI5E,eAAjB,EAAkC;EAChC;EACD;;EAED,QAAM0E,SAAS,GAAGE,SAAS,GAAG,KAAKzB,WAAnC;EAEA,SAAKA,WAAL,GAAmB,CAAnB,CATa;;EAYb,QAAIuB,SAAS,GAAG,CAAhB,EAAmB;EACjB,WAAKlF,IAAL;EACD,KAdY;;;EAiBb,QAAIkF,SAAS,GAAG,CAAhB,EAAmB;EACjB,WAAK/E,IAAL;EACD;EACF;;WAEDiE,qBAAA,8BAAqB;EAAA;;EACnB,QAAI,KAAKR,OAAL,CAAajD,QAAjB,EAA2B;EACzBtJ,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B0F,aAA/B,EAA8C,UAAArK,KAAK;EAAA,eAAI,MAAI,CAACoO,QAAL,CAAcpO,KAAd,CAAJ;EAAA,OAAnD;EACD;;EAED,QAAI,KAAK0M,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;EAClCxJ,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B2F,gBAA/B,EAAiD,UAAAtK,KAAK;EAAA,eAAI,MAAI,CAAC2J,KAAL,CAAW3J,KAAX,CAAJ;EAAA,OAAtD;EACAG,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B4F,gBAA/B,EAAiD,UAAAvK,KAAK;EAAA,eAAI,MAAI,CAACsN,KAAL,CAAWtN,KAAX,CAAJ;EAAA,OAAtD;EACD;;EAED,QAAI,KAAK0M,OAAL,CAAa7C,KAAb,IAAsB,KAAKgD,eAA/B,EAAgD;EAC9C,WAAKwB,uBAAL;EACD;EACF;;WAEDA,0BAAA,mCAA0B;EAAA;;EACxB,QAAMC,KAAK,GAAG,SAARA,KAAQ,CAAAtO,KAAK,EAAI;EACrB,UAAI,MAAI,CAACgN,aAAL,IAAsBlB,WAAW,CAAC9L,KAAK,CAACuO,WAAN,CAAkB7R,WAAlB,EAAD,CAArC,EAAwE;EACtE,QAAA,MAAI,CAAC8P,WAAL,GAAmBxM,KAAK,CAACwO,OAAzB;EACD,OAFD,MAEO,IAAI,CAAC,MAAI,CAACxB,aAAV,EAAyB;EAC9B,QAAA,MAAI,CAACR,WAAL,GAAmBxM,KAAK,CAACyO,OAAN,CAAc,CAAd,EAAiBD,OAApC;EACD;EACF,KAND;;EAQA,QAAME,IAAI,GAAG,SAAPA,IAAO,CAAA1O,KAAK,EAAI;EACpB;EACA,UAAIA,KAAK,CAACyO,OAAN,IAAiBzO,KAAK,CAACyO,OAAN,CAAc7N,MAAd,GAAuB,CAA5C,EAA+C;EAC7C,QAAA,MAAI,CAAC6L,WAAL,GAAmB,CAAnB;EACD,OAFD,MAEO;EACL,QAAA,MAAI,CAACA,WAAL,GAAmBzM,KAAK,CAACyO,OAAN,CAAc,CAAd,EAAiBD,OAAjB,GAA2B,MAAI,CAAChC,WAAnD;EACD;EACF,KAPD;;EASA,QAAMmC,GAAG,GAAG,SAANA,GAAM,CAAA3O,KAAK,EAAI;EACnB,UAAI,MAAI,CAACgN,aAAL,IAAsBlB,WAAW,CAAC9L,KAAK,CAACuO,WAAN,CAAkB7R,WAAlB,EAAD,CAArC,EAAwE;EACtE,QAAA,MAAI,CAAC+P,WAAL,GAAmBzM,KAAK,CAACwO,OAAN,GAAgB,MAAI,CAAChC,WAAxC;EACD;;EAED,MAAA,MAAI,CAACyB,YAAL;;EACA,UAAI,MAAI,CAACvB,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EAEA,QAAA,MAAI,CAACA,KAAL;;EACA,YAAI,MAAI,CAAC4C,YAAT,EAAuB;EACrBqC,UAAAA,YAAY,CAAC,MAAI,CAACrC,YAAN,CAAZ;EACD;;EAED,QAAA,MAAI,CAACA,YAAL,GAAoB5Q,UAAU,CAAC,UAAAqE,KAAK;EAAA,iBAAI,MAAI,CAACsN,KAAL,CAAWtN,KAAX,CAAJ;EAAA,SAAN,EAA6BqJ,sBAAsB,GAAG,MAAI,CAACqD,OAAL,CAAalD,QAAnE,CAA9B;EACD;EACF,KAtBD;;EAwBAzB,IAAAA,cAAc,CAACE,IAAf,CAAoBwD,iBAApB,EAAuC,KAAK9G,QAA5C,EAAsDzI,OAAtD,CAA8D,UAAA2S,OAAO,EAAI;EACvE1O,MAAAA,YAAY,CAACkC,EAAb,CAAgBwM,OAAhB,EAAyBhE,gBAAzB,EAA2C,UAAAiE,CAAC;EAAA,eAAIA,CAAC,CAACjL,cAAF,EAAJ;EAAA,OAA5C;EACD,KAFD;;EAIA,QAAI,KAAKmJ,aAAT,EAAwB;EACtB7M,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+BgG,iBAA/B,EAAkD,UAAA3K,KAAK;EAAA,eAAIsO,KAAK,CAACtO,KAAD,CAAT;EAAA,OAAvD;EACAG,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+BiG,eAA/B,EAAgD,UAAA5K,KAAK;EAAA,eAAI2O,GAAG,CAAC3O,KAAD,CAAP;EAAA,OAArD;;EAEA,WAAK2E,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4B1D,wBAA5B;EACD,KALD,MAKO;EACLlL,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B6F,gBAA/B,EAAiD,UAAAxK,KAAK;EAAA,eAAIsO,KAAK,CAACtO,KAAD,CAAT;EAAA,OAAtD;EACAG,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B8F,eAA/B,EAAgD,UAAAzK,KAAK;EAAA,eAAI0O,IAAI,CAAC1O,KAAD,CAAR;EAAA,OAArD;EACAG,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B+F,cAA/B,EAA+C,UAAA1K,KAAK;EAAA,eAAI2O,GAAG,CAAC3O,KAAD,CAAP;EAAA,OAApD;EACD;EACF;;WAEDoO,WAAA,kBAASpO,KAAT,EAAgB;EACd,QAAI,kBAAkBxD,IAAlB,CAAuBwD,KAAK,CAACU,MAAN,CAAasO,OAApC,CAAJ,EAAkD;EAChD;EACD;;EAED,YAAQhP,KAAK,CAAC1B,GAAd;EACE,WAAK6K,cAAL;EACEnJ,QAAAA,KAAK,CAAC6D,cAAN;EACA,aAAKiF,IAAL;EACA;;EACF,WAAKM,eAAL;EACEpJ,QAAAA,KAAK,CAAC6D,cAAN;EACA,aAAKoF,IAAL;EACA;EARJ;EAWD;;WAED8E,gBAAA,uBAAclU,OAAd,EAAuB;EACrB,SAAKqS,MAAL,GAAcrS,OAAO,IAAIA,OAAO,CAACgD,UAAnB,GACZkL,cAAc,CAACE,IAAf,CAAoBuD,aAApB,EAAmC3R,OAAO,CAACgD,UAA3C,CADY,GAEZ,EAFF;EAIA,WAAO,KAAKqP,MAAL,CAAYvK,OAAZ,CAAoB9H,OAApB,CAAP;EACD;;WAEDoV,sBAAA,6BAAoBjB,SAApB,EAA+BkB,aAA/B,EAA8C;EAC5C,QAAMC,eAAe,GAAGnB,SAAS,KAAKjE,cAAtC;EACA,QAAMqF,eAAe,GAAGpB,SAAS,KAAKhE,cAAtC;;EACA,QAAM8D,WAAW,GAAG,KAAKC,aAAL,CAAmBmB,aAAnB,CAApB;;EACA,QAAMG,aAAa,GAAG,KAAKnD,MAAL,CAAYtL,MAAZ,GAAqB,CAA3C;EACA,QAAM0O,aAAa,GAAIF,eAAe,IAAItB,WAAW,KAAK,CAApC,IACGqB,eAAe,IAAIrB,WAAW,KAAKuB,aAD5D;;EAGA,QAAIC,aAAa,IAAI,CAAC,KAAK5C,OAAL,CAAa9C,IAAnC,EAAyC;EACvC,aAAOsF,aAAP;EACD;;EAED,QAAMK,KAAK,GAAGvB,SAAS,KAAKhE,cAAd,GAA+B,CAAC,CAAhC,GAAoC,CAAlD;EACA,QAAMwF,SAAS,GAAG,CAAC1B,WAAW,GAAGyB,KAAf,IAAwB,KAAKrD,MAAL,CAAYtL,MAAtD;EAEA,WAAO4O,SAAS,KAAK,CAAC,CAAf,GACL,KAAKtD,MAAL,CAAY,KAAKA,MAAL,CAAYtL,MAAZ,GAAqB,CAAjC,CADK,GAEL,KAAKsL,MAAL,CAAYsD,SAAZ,CAFF;EAGD;;WAEDC,qBAAA,4BAAmBC,aAAnB,EAAkCC,kBAAlC,EAAsD;EACpD,QAAMC,WAAW,GAAG,KAAK7B,aAAL,CAAmB2B,aAAnB,CAApB;;EACA,QAAMG,SAAS,GAAG,KAAK9B,aAAL,CAAmBhG,cAAc,CAACM,OAAf,CAAuBkD,oBAAvB,EAA6C,KAAK5G,QAAlD,CAAnB,CAAlB;;EAEA,WAAOxE,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCwF,WAApC,EAAiD;EACtDuF,MAAAA,aAAa,EAAbA,aADsD;EAEtD1B,MAAAA,SAAS,EAAE2B,kBAF2C;EAGtDG,MAAAA,IAAI,EAAED,SAHgD;EAItDjC,MAAAA,EAAE,EAAEgC;EAJkD,KAAjD,CAAP;EAMD;;WAEDG,6BAAA,oCAA2BlW,OAA3B,EAAoC;EAClC,QAAI,KAAK+S,kBAAT,EAA6B;EAC3B,UAAMoD,UAAU,GAAGjI,cAAc,CAACE,IAAf,CAAoBqD,eAApB,EAAqC,KAAKsB,kBAA1C,CAAnB;;EACA,WAAK,IAAIjM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqP,UAAU,CAACpP,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;EAC1CqP,QAAAA,UAAU,CAACrP,CAAD,CAAV,CAAcyE,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B;EACD;;EAED,UAAMgK,aAAa,GAAG,KAAKrD,kBAAL,CAAwBtE,QAAxB,CACpB,KAAKyF,aAAL,CAAmBlU,OAAnB,CADoB,CAAtB;;EAIA,UAAIoW,aAAJ,EAAmB;EACjBA,QAAAA,aAAa,CAAC7K,SAAd,CAAwB2J,GAAxB,CAA4B9I,mBAA5B;EACD;EACF;EACF;;WAEDuH,kBAAA,2BAAkB;EAChB,QAAM3T,OAAO,GAAG,KAAKuS,cAAL,IAAuBrE,cAAc,CAACM,OAAf,CAAuBkD,oBAAvB,EAA6C,KAAK5G,QAAlD,CAAvC;;EAEA,QAAI,CAAC9K,OAAL,EAAc;EACZ;EACD;;EAED,QAAMqW,eAAe,GAAGC,QAAQ,CAACtW,OAAO,CAACE,YAAR,CAAqB,eAArB,CAAD,EAAwC,EAAxC,CAAhC;;EAEA,QAAImW,eAAJ,EAAqB;EACnB,WAAKxD,OAAL,CAAa0D,eAAb,GAA+B,KAAK1D,OAAL,CAAa0D,eAAb,IAAgC,KAAK1D,OAAL,CAAalD,QAA5E;EACA,WAAKkD,OAAL,CAAalD,QAAb,GAAwB0G,eAAxB;EACD,KAHD,MAGO;EACL,WAAKxD,OAAL,CAAalD,QAAb,GAAwB,KAAKkD,OAAL,CAAa0D,eAAb,IAAgC,KAAK1D,OAAL,CAAalD,QAArE;EACD;EACF;;WAED2D,SAAA,gBAAOa,SAAP,EAAkBnU,OAAlB,EAA2B;EAAA;;EACzB,QAAMqV,aAAa,GAAGnH,cAAc,CAACM,OAAf,CAAuBkD,oBAAvB,EAA6C,KAAK5G,QAAlD,CAAtB;;EACA,QAAM0L,kBAAkB,GAAG,KAAKtC,aAAL,CAAmBmB,aAAnB,CAA3B;;EACA,QAAMoB,WAAW,GAAGzW,OAAO,IAAKqV,aAAa,IAC3C,KAAKD,mBAAL,CAAyBjB,SAAzB,EAAoCkB,aAApC,CADF;;EAGA,QAAMqB,gBAAgB,GAAG,KAAKxC,aAAL,CAAmBuC,WAAnB,CAAzB;;EACA,QAAME,SAAS,GAAGxO,OAAO,CAAC,KAAKmK,SAAN,CAAzB;EAEA,QAAIsE,oBAAJ;EACA,QAAIC,cAAJ;EACA,QAAIf,kBAAJ;;EAEA,QAAI3B,SAAS,KAAKjE,cAAlB,EAAkC;EAChC0G,MAAAA,oBAAoB,GAAGvF,eAAvB;EACAwF,MAAAA,cAAc,GAAGvF,eAAjB;EACAwE,MAAAA,kBAAkB,GAAG1F,cAArB;EACD,KAJD,MAIO;EACLwG,MAAAA,oBAAoB,GAAGxF,gBAAvB;EACAyF,MAAAA,cAAc,GAAGtF,eAAjB;EACAuE,MAAAA,kBAAkB,GAAGzF,eAArB;EACD;;EAED,QAAIoG,WAAW,IAAIA,WAAW,CAAClL,SAAZ,CAAsBE,QAAtB,CAA+BW,mBAA/B,CAAnB,EAAsE;EACpE,WAAKqG,UAAL,GAAkB,KAAlB;EACA;EACD;;EAED,QAAMqE,UAAU,GAAG,KAAKlB,kBAAL,CAAwBa,WAAxB,EAAqCX,kBAArC,CAAnB;;EACA,QAAIgB,UAAU,CAACxN,gBAAf,EAAiC;EAC/B;EACD;;EAED,QAAI,CAAC+L,aAAD,IAAkB,CAACoB,WAAvB,EAAoC;EAClC;EACA;EACD;;EAED,SAAKhE,UAAL,GAAkB,IAAlB;;EAEA,QAAIkE,SAAJ,EAAe;EACb,WAAK7G,KAAL;EACD;;EAED,SAAKoG,0BAAL,CAAgCO,WAAhC;;EACA,SAAKlE,cAAL,GAAsBkE,WAAtB;;EAEA,QAAI,KAAK3L,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC0F,gBAAjC,CAAJ,EAAwD;EACtDsF,MAAAA,WAAW,CAAClL,SAAZ,CAAsB2J,GAAtB,CAA0B2B,cAA1B;EAEAjT,MAAAA,MAAM,CAAC6S,WAAD,CAAN;EAEApB,MAAAA,aAAa,CAAC9J,SAAd,CAAwB2J,GAAxB,CAA4B0B,oBAA5B;EACAH,MAAAA,WAAW,CAAClL,SAAZ,CAAsB2J,GAAtB,CAA0B0B,oBAA1B;EAEA,UAAMjW,kBAAkB,GAAGH,gCAAgC,CAAC6U,aAAD,CAA3D;EAEA/O,MAAAA,YAAY,CAACmC,GAAb,CAAiB4M,aAAjB,EAAgCrW,cAAhC,EAAgD,YAAM;EACpDyX,QAAAA,WAAW,CAAClL,SAAZ,CAAsBC,MAAtB,CAA6BoL,oBAA7B,EAAmDC,cAAnD;EACAJ,QAAAA,WAAW,CAAClL,SAAZ,CAAsB2J,GAAtB,CAA0B9I,mBAA1B;EAEAiJ,QAAAA,aAAa,CAAC9J,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B,EAAkDyK,cAAlD,EAAkED,oBAAlE;EAEA,QAAA,MAAI,CAACnE,UAAL,GAAkB,KAAlB;EAEA3Q,QAAAA,UAAU,CAAC,YAAM;EACfwE,UAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAC8B,QAA1B,EAAoCyF,UAApC,EAAgD;EAC9CsF,YAAAA,aAAa,EAAEY,WAD+B;EAE9CtC,YAAAA,SAAS,EAAE2B,kBAFmC;EAG9CG,YAAAA,IAAI,EAAEO,kBAHwC;EAI9CzC,YAAAA,EAAE,EAAE2C;EAJ0C,WAAhD;EAMD,SAPS,EAOP,CAPO,CAAV;EAQD,OAhBD;EAkBApV,MAAAA,oBAAoB,CAAC+T,aAAD,EAAgB1U,kBAAhB,CAApB;EACD,KA7BD,MA6BO;EACL0U,MAAAA,aAAa,CAAC9J,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B;EACAqK,MAAAA,WAAW,CAAClL,SAAZ,CAAsB2J,GAAtB,CAA0B9I,mBAA1B;EAEA,WAAKqG,UAAL,GAAkB,KAAlB;EACAnM,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyF,UAApC,EAAgD;EAC9CsF,QAAAA,aAAa,EAAEY,WAD+B;EAE9CtC,QAAAA,SAAS,EAAE2B,kBAFmC;EAG9CG,QAAAA,IAAI,EAAEO,kBAHwC;EAI9CzC,QAAAA,EAAE,EAAE2C;EAJ0C,OAAhD;EAMD;;EAED,QAAIC,SAAJ,EAAe;EACb,WAAKlD,KAAL;EACD;EACF;;;aAIMsD,oBAAP,2BAAyB/W,OAAzB,EAAkCiC,MAAlC,EAA0C;EACxC,QAAIyC,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAX;;EACA,QAAI0I,OAAO,gBACNnD,OADM,EAEN3C,WAAW,CAACI,iBAAZ,CAA8BnN,OAA9B,CAFM,CAAX;;EAKA,QAAI,OAAOiC,MAAP,KAAkB,QAAtB,EAAgC;EAC9B4Q,MAAAA,OAAO,gBACFA,OADE,EAEF5Q,MAFE,CAAP;EAID;;EAED,QAAM+U,MAAM,GAAG,OAAO/U,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC4Q,OAAO,CAAChD,KAA7D;;EAEA,QAAI,CAACnL,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAI0N,QAAJ,CAAapS,OAAb,EAAsB6S,OAAtB,CAAP;EACD;;EAED,QAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;EAC9ByC,MAAAA,IAAI,CAACqP,EAAL,CAAQ9R,MAAR;EACD,KAFD,MAEO,IAAI,OAAO+U,MAAP,KAAkB,QAAtB,EAAgC;EACrC,UAAI,OAAOtS,IAAI,CAACsS,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIC,SAAJ,wBAAkCD,MAAlC,QAAN;EACD;;EAEDtS,MAAAA,IAAI,CAACsS,MAAD,CAAJ;EACD,KANM,MAMA,IAAInE,OAAO,CAAClD,QAAR,IAAoBkD,OAAO,CAACqE,IAAhC,EAAsC;EAC3CxS,MAAAA,IAAI,CAACoL,KAAL;EACApL,MAAAA,IAAI,CAAC+O,KAAL;EACD;EACF;;aAEM7H,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3BuG,MAAAA,QAAQ,CAAC2E,iBAAT,CAA2B,IAA3B,EAAiC9U,MAAjC;EACD,KAFM,CAAP;EAGD;;aAEMkV,sBAAP,6BAA2BhR,KAA3B,EAAkC;EAChC,QAAMU,MAAM,GAAGtG,sBAAsB,CAAC,IAAD,CAArC;;EAEA,QAAI,CAACsG,MAAD,IAAW,CAACA,MAAM,CAAC0E,SAAP,CAAiBE,QAAjB,CAA0ByF,mBAA1B,CAAhB,EAAgE;EAC9D;EACD;;EAED,QAAMjP,MAAM,gBACP8K,WAAW,CAACI,iBAAZ,CAA8BtG,MAA9B,CADO,EAEPkG,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAFO,CAAZ;;EAIA,QAAMiK,UAAU,GAAG,KAAKlX,YAAL,CAAkB,eAAlB,CAAnB;;EAEA,QAAIkX,UAAJ,EAAgB;EACdnV,MAAAA,MAAM,CAAC0N,QAAP,GAAkB,KAAlB;EACD;;EAEDyC,IAAAA,QAAQ,CAAC2E,iBAAT,CAA2BlQ,MAA3B,EAAmC5E,MAAnC;;EAEA,QAAImV,UAAJ,EAAgB;EACdrS,MAAAA,IAAI,CAACG,OAAL,CAAa2B,MAAb,EAAqBsD,UAArB,EAA+B4J,EAA/B,CAAkCqD,UAAlC;EACD;;EAEDjR,IAAAA,KAAK,CAAC6D,cAAN;EACD;;aAEMgC,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BAldoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOwF,OAAP;EACD;;;;;EA+cH;EACA;EACA;EACA;EACA;;;EAEApJ,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgDsH,mBAAhD,EAAqEK,QAAQ,CAAC+E,mBAA9E;EAEA7Q,YAAY,CAACkC,EAAb,CAAgB/H,MAAhB,EAAwBwQ,mBAAxB,EAA6C,YAAM;EACjD,MAAMoG,SAAS,GAAGnJ,cAAc,CAACE,IAAf,CAAoB4D,kBAApB,CAAlB;;EAEA,OAAK,IAAIlL,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGiQ,SAAS,CAACtQ,MAAhC,EAAwCD,CAAC,GAAGM,GAA5C,EAAiDN,CAAC,EAAlD,EAAsD;EACpDsL,IAAAA,QAAQ,CAAC2E,iBAAT,CAA2BM,SAAS,CAACvQ,CAAD,CAApC,EAAyC/B,IAAI,CAACG,OAAL,CAAamS,SAAS,CAACvQ,CAAD,CAAtB,EAA2BqD,UAA3B,CAAzC;EACD;EACF,CAND;EAQA;EACA;EACA;EACA;EACA;EACA;;EAEAjG,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAamI,QAAQ,CAACxG,eAAtB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBkG,QAAzB;;EACAlJ,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOmG,QAAQ,CAACxG,eAAhB;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;ECrlBA;EACA;EACA;EACA;EACA;;EAEA,IAAM3B,MAAI,GAAG,UAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,aAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAMqF,SAAO,GAAG;EACdnD,EAAAA,MAAM,EAAE,IADM;EAEd+K,EAAAA,MAAM,EAAE;EAFM,CAAhB;EAKA,IAAMrH,aAAW,GAAG;EAClB1D,EAAAA,MAAM,EAAE,SADU;EAElB+K,EAAAA,MAAM,EAAE;EAFU,CAApB;EAKA,IAAMC,UAAU,YAAUnN,WAA1B;EACA,IAAMoN,WAAW,aAAWpN,WAA5B;EACA,IAAMqN,UAAU,YAAUrN,WAA1B;EACA,IAAMsN,YAAY,cAAYtN,WAA9B;EACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EAEA,IAAMsN,eAAe,GAAG,MAAxB;EACA,IAAMC,mBAAmB,GAAG,UAA5B;EACA,IAAMC,qBAAqB,GAAG,YAA9B;EACA,IAAMC,oBAAoB,GAAG,WAA7B;EAEA,IAAMC,KAAK,GAAG,OAAd;EACA,IAAMC,MAAM,GAAG,QAAf;EAEA,IAAMC,gBAAgB,GAAG,oBAAzB;EACA,IAAM5L,sBAAoB,GAAG,0BAA7B;EAEA;EACA;EACA;EACA;EACA;;MAEM6L;EACJ,oBAAYlY,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,SAAKkW,gBAAL,GAAwB,KAAxB;EACA,SAAKrN,QAAL,GAAgB9K,OAAhB;EACA,SAAK6S,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;EACA,SAAKmW,aAAL,GAAqBlK,cAAc,CAACE,IAAf,CAChB/B,sBAAH,iBAAkCrM,OAAO,CAACuE,EAA1C,aACG8H,sBADH,wBACyCrM,OAAO,CAACuE,EADjD,SADmB,CAArB;EAKA,QAAM8T,UAAU,GAAGnK,cAAc,CAACE,IAAf,CAAoB/B,sBAApB,CAAnB;;EAEA,SAAK,IAAIvF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGiR,UAAU,CAACtR,MAAjC,EAAyCD,CAAC,GAAGM,GAA7C,EAAkDN,CAAC,EAAnD,EAAuD;EACrD,UAAMwR,IAAI,GAAGD,UAAU,CAACvR,CAAD,CAAvB;EACA,UAAM7G,QAAQ,GAAGI,sBAAsB,CAACiY,IAAD,CAAvC;EACA,UAAMC,aAAa,GAAGrK,cAAc,CAACE,IAAf,CAAoBnO,QAApB,EACnByO,MADmB,CACZ,UAAA8J,SAAS;EAAA,eAAIA,SAAS,KAAKxY,OAAlB;EAAA,OADG,CAAtB;;EAGA,UAAIC,QAAQ,KAAK,IAAb,IAAqBsY,aAAa,CAACxR,MAAvC,EAA+C;EAC7C,aAAK0R,SAAL,GAAiBxY,QAAjB;;EACA,aAAKmY,aAAL,CAAmBpJ,IAAnB,CAAwBsJ,IAAxB;EACD;EACF;;EAED,SAAKI,OAAL,GAAe,KAAK7F,OAAL,CAAayE,MAAb,GAAsB,KAAKqB,UAAL,EAAtB,GAA0C,IAAzD;;EAEA,QAAI,CAAC,KAAK9F,OAAL,CAAayE,MAAlB,EAA0B;EACxB,WAAKsB,yBAAL,CAA+B,KAAK9N,QAApC,EAA8C,KAAKsN,aAAnD;EACD;;EAED,QAAI,KAAKvF,OAAL,CAAatG,MAAjB,EAAyB;EACvB,WAAKA,MAAL;EACD;;EAEDxH,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;EACD;;;;;EAYD;WAEAoC,SAAA,kBAAS;EACP,QAAI,KAAKzB,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,eAAjC,CAAJ,EAAuD;EACrD,WAAKkB,IAAL;EACD,KAFD,MAEO;EACL,WAAKC,IAAL;EACD;EACF;;WAEDA,OAAA,gBAAO;EAAA;;EACL,QAAI,KAAKX,gBAAL,IACF,KAAKrN,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,eAAjC,CADF,EACqD;EACnD;EACD;;EAED,QAAIoB,OAAJ;EACA,QAAIC,WAAJ;;EAEA,QAAI,KAAKN,OAAT,EAAkB;EAChBK,MAAAA,OAAO,GAAG7K,cAAc,CAACE,IAAf,CAAoB6J,gBAApB,EAAsC,KAAKS,OAA3C,EACPhK,MADO,CACA,UAAA4J,IAAI,EAAI;EACd,YAAI,OAAO,KAAI,CAACzF,OAAL,CAAayE,MAApB,KAA+B,QAAnC,EAA6C;EAC3C,iBAAOgB,IAAI,CAACpY,YAAL,CAAkB,aAAlB,MAAqC,KAAI,CAAC2S,OAAL,CAAayE,MAAzD;EACD;;EAED,eAAOgB,IAAI,CAAC/M,SAAL,CAAeE,QAAf,CAAwBmM,mBAAxB,CAAP;EACD,OAPO,CAAV;;EASA,UAAImB,OAAO,CAAChS,MAAR,KAAmB,CAAvB,EAA0B;EACxBgS,QAAAA,OAAO,GAAG,IAAV;EACD;EACF;;EAED,QAAME,SAAS,GAAG/K,cAAc,CAACM,OAAf,CAAuB,KAAKiK,SAA5B,CAAlB;;EACA,QAAIM,OAAJ,EAAa;EACX,UAAMG,cAAc,GAAGH,OAAO,CAACrK,MAAR,CAAe,UAAA4J,IAAI;EAAA,eAAIW,SAAS,KAAKX,IAAlB;EAAA,OAAnB,CAAvB;EACAU,MAAAA,WAAW,GAAGE,cAAc,CAAC,CAAD,CAAd,GAAoBnU,IAAI,CAACG,OAAL,CAAagU,cAAc,CAAC,CAAD,CAA3B,EAAgC/O,UAAhC,CAApB,GAAgE,IAA9E;;EAEA,UAAI6O,WAAW,IAAIA,WAAW,CAACb,gBAA/B,EAAiD;EAC/C;EACD;EACF;;EAED,QAAMgB,UAAU,GAAG7S,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyM,UAApC,CAAnB;;EACA,QAAI4B,UAAU,CAAC7P,gBAAf,EAAiC;EAC/B;EACD;;EAED,QAAIyP,OAAJ,EAAa;EACXA,MAAAA,OAAO,CAAC1W,OAAR,CAAgB,UAAA+W,UAAU,EAAI;EAC5B,YAAIH,SAAS,KAAKG,UAAlB,EAA8B;EAC5BlB,UAAAA,QAAQ,CAACmB,iBAAT,CAA2BD,UAA3B,EAAuC,MAAvC;EACD;;EAED,YAAI,CAACJ,WAAL,EAAkB;EAChBjU,UAAAA,IAAI,CAACC,OAAL,CAAaoU,UAAb,EAAyBjP,UAAzB,EAAmC,IAAnC;EACD;EACF,OARD;EASD;;EAED,QAAMmP,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAKzO,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BoM,mBAA/B;;EACA,SAAK9M,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4B2C,qBAA5B;;EAEA,SAAK/M,QAAL,CAAc/H,KAAd,CAAoBuW,SAApB,IAAiC,CAAjC;;EAEA,QAAI,KAAKlB,aAAL,CAAmBrR,MAAvB,EAA+B;EAC7B,WAAKqR,aAAL,CAAmB/V,OAAnB,CAA2B,UAAArC,OAAO,EAAI;EACpCA,QAAAA,OAAO,CAACuL,SAAR,CAAkBC,MAAlB,CAAyBsM,oBAAzB;EACA9X,QAAAA,OAAO,CAACwM,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD,OAHD;EAID;;EAED,SAAKgN,gBAAL,CAAsB,IAAtB;;EAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,KAAI,CAAC3O,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BqM,qBAA/B;;EACA,MAAA,KAAI,CAAC/M,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4B0C,mBAA5B,EAAiDD,eAAjD;;EAEA,MAAA,KAAI,CAAC7M,QAAL,CAAc/H,KAAd,CAAoBuW,SAApB,IAAiC,EAAjC;;EAEA,MAAA,KAAI,CAACE,gBAAL,CAAsB,KAAtB;;EAEAlT,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAI,CAAC8B,QAA1B,EAAoC0M,WAApC;EACD,KATD;;EAWA,QAAMkC,oBAAoB,GAAGJ,SAAS,CAAC,CAAD,CAAT,CAAazW,WAAb,KAA6ByW,SAAS,CAACxQ,KAAV,CAAgB,CAAhB,CAA1D;EACA,QAAM6Q,UAAU,cAAYD,oBAA5B;EACA,QAAM/Y,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKsK,QAAN,CAA3D;EAEAxE,IAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgDya,QAAhD;EAEAnY,IAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBnK,kBAAhB,CAApB;EACA,SAAKmK,QAAL,CAAc/H,KAAd,CAAoBuW,SAApB,IAAoC,KAAKxO,QAAL,CAAc6O,UAAd,CAApC;EACD;;WAEDd,OAAA,gBAAO;EAAA;;EACL,QAAI,KAAKV,gBAAL,IACF,CAAC,KAAKrN,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,eAAjC,CADH,EACsD;EACpD;EACD;;EAED,QAAMwB,UAAU,GAAG7S,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoC2M,UAApC,CAAnB;;EACA,QAAI0B,UAAU,CAAC7P,gBAAf,EAAiC;EAC/B;EACD;;EAED,QAAMgQ,SAAS,GAAG,KAAKC,aAAL,EAAlB;;EAEA,SAAKzO,QAAL,CAAc/H,KAAd,CAAoBuW,SAApB,IAAoC,KAAKxO,QAAL,CAAc2C,qBAAd,GAAsC6L,SAAtC,CAApC;EAEA1V,IAAAA,MAAM,CAAC,KAAKkH,QAAN,CAAN;;EAEA,SAAKA,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4B2C,qBAA5B;;EACA,SAAK/M,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BoM,mBAA/B,EAAoDD,eAApD;;EAEA,QAAMiC,kBAAkB,GAAG,KAAKxB,aAAL,CAAmBrR,MAA9C;;EACA,QAAI6S,kBAAkB,GAAG,CAAzB,EAA4B;EAC1B,WAAK,IAAI9S,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8S,kBAApB,EAAwC9S,CAAC,EAAzC,EAA6C;EAC3C,YAAMkC,OAAO,GAAG,KAAKoP,aAAL,CAAmBtR,CAAnB,CAAhB;EACA,YAAMwR,IAAI,GAAG/X,sBAAsB,CAACyI,OAAD,CAAnC;;EAEA,YAAIsP,IAAI,IAAI,CAACA,IAAI,CAAC/M,SAAL,CAAeE,QAAf,CAAwBkM,eAAxB,CAAb,EAAuD;EACrD3O,UAAAA,OAAO,CAACuC,SAAR,CAAkB2J,GAAlB,CAAsB4C,oBAAtB;EACA9O,UAAAA,OAAO,CAACwD,YAAR,CAAqB,eAArB,EAAsC,KAAtC;EACD;EACF;EACF;;EAED,SAAKgN,gBAAL,CAAsB,IAAtB;;EAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,MAAI,CAACD,gBAAL,CAAsB,KAAtB;;EACA,MAAA,MAAI,CAAC1O,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BqM,qBAA/B;;EACA,MAAA,MAAI,CAAC/M,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4B0C,mBAA5B;;EACAtR,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAC8B,QAA1B,EAAoC4M,YAApC;EACD,KALD;;EAOA,SAAK5M,QAAL,CAAc/H,KAAd,CAAoBuW,SAApB,IAAiC,EAAjC;EACA,QAAM3Y,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKsK,QAAN,CAA3D;EAEAxE,IAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgDya,QAAhD;EACAnY,IAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBnK,kBAAhB,CAApB;EACD;;WAED6Y,mBAAA,0BAAiBK,eAAjB,EAAkC;EAChC,SAAK1B,gBAAL,GAAwB0B,eAAxB;EACD;;WAEDxO,UAAA,mBAAU;EACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EAEA,SAAK0I,OAAL,GAAe,IAAf;EACA,SAAK6F,OAAL,GAAe,IAAf;EACA,SAAK5N,QAAL,GAAgB,IAAhB;EACA,SAAKsN,aAAL,GAAqB,IAArB;EACA,SAAKD,gBAAL,GAAwB,IAAxB;EACD;;;WAIDrF,aAAA,oBAAW7Q,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACDyN,SADC,EAEDzN,MAFC,CAAN;EAIAA,IAAAA,MAAM,CAACsK,MAAP,GAAgBpE,OAAO,CAAClG,MAAM,CAACsK,MAAR,CAAvB,CALiB;;EAMjBxK,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAegO,aAAf,CAAf;EACA,WAAOhO,MAAP;EACD;;WAEDsX,gBAAA,yBAAgB;EACd,WAAO,KAAKzO,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCsM,KAAjC,IAA0CA,KAA1C,GAAkDC,MAAzD;EACD;;WAEDW,aAAA,sBAAa;EAAA;;EAAA,QACLrB,MADK,GACM,KAAKzE,OADX,CACLyE,MADK;;EAGX,QAAIlW,SAAS,CAACkW,MAAD,CAAb,EAAuB;EACrB;EACA,UAAI,OAAOA,MAAM,CAACwC,MAAd,KAAyB,WAAzB,IAAwC,OAAOxC,MAAM,CAAC,CAAD,CAAb,KAAqB,WAAjE,EAA8E;EAC5EA,QAAAA,MAAM,GAAGA,MAAM,CAAC,CAAD,CAAf;EACD;EACF,KALD,MAKO;EACLA,MAAAA,MAAM,GAAGpJ,cAAc,CAACM,OAAf,CAAuB8I,MAAvB,CAAT;EACD;;EAED,QAAMrX,QAAQ,GAAMoM,sBAAN,uBAA2CiL,MAA3C,QAAd;EAEApJ,IAAAA,cAAc,CAACE,IAAf,CAAoBnO,QAApB,EAA8BqX,MAA9B,EACGjV,OADH,CACW,UAAArC,OAAO,EAAI;EAClB,UAAM+Z,QAAQ,GAAGxZ,sBAAsB,CAACP,OAAD,CAAvC;;EAEA,MAAA,MAAI,CAAC4Y,yBAAL,CACEmB,QADF,EAEE,CAAC/Z,OAAD,CAFF;EAID,KARH;EAUA,WAAOsX,MAAP;EACD;;WAEDsB,4BAAA,mCAA0B5Y,OAA1B,EAAmCga,YAAnC,EAAiD;EAC/C,QAAI,CAACha,OAAD,IAAY,CAACga,YAAY,CAACjT,MAA9B,EAAsC;EACpC;EACD;;EAED,QAAMkT,MAAM,GAAGja,OAAO,CAACuL,SAAR,CAAkBE,QAAlB,CAA2BkM,eAA3B,CAAf;EAEAqC,IAAAA,YAAY,CAAC3X,OAAb,CAAqB,UAAAiW,IAAI,EAAI;EAC3B,UAAI2B,MAAJ,EAAY;EACV3B,QAAAA,IAAI,CAAC/M,SAAL,CAAeC,MAAf,CAAsBsM,oBAAtB;EACD,OAFD,MAEO;EACLQ,QAAAA,IAAI,CAAC/M,SAAL,CAAe2J,GAAf,CAAmB4C,oBAAnB;EACD;;EAEDQ,MAAAA,IAAI,CAAC9L,YAAL,CAAkB,eAAlB,EAAmCyN,MAAnC;EACD,KARD;EASD;;;aAIMZ,oBAAP,2BAAyBrZ,OAAzB,EAAkCiC,MAAlC,EAA0C;EACxC,QAAIyC,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAX;;EACA,QAAM0I,OAAO,gBACRnD,SADQ,EAER3C,WAAW,CAACI,iBAAZ,CAA8BnN,OAA9B,CAFQ,EAGP,OAAOiC,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHzC,CAAb;;EAMA,QAAI,CAACyC,IAAD,IAASmO,OAAO,CAACtG,MAAjB,IAA2B,OAAOtK,MAAP,KAAkB,QAA7C,IAAyD,YAAYU,IAAZ,CAAiBV,MAAjB,CAA7D,EAAuF;EACrF4Q,MAAAA,OAAO,CAACtG,MAAR,GAAiB,KAAjB;EACD;;EAED,QAAI,CAAC7H,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAIwT,QAAJ,CAAalY,OAAb,EAAsB6S,OAAtB,CAAP;EACD;;EAED,QAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,UAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,MAAAA,IAAI,CAACzC,MAAD,CAAJ;EACD;EACF;;aAEM2J,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3BqM,MAAAA,QAAQ,CAACmB,iBAAT,CAA2B,IAA3B,EAAiCpX,MAAjC;EACD,KAFM,CAAP;EAGD;;aAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BAzQoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOwF,SAAP;EACD;;;;;EAsQH;EACA;EACA;EACA;EACA;;;EAEApJ,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUlG,KAAV,EAAiB;EACrF;EACA,MAAIA,KAAK,CAACU,MAAN,CAAasO,OAAb,KAAyB,GAA7B,EAAkC;EAChChP,IAAAA,KAAK,CAAC6D,cAAN;EACD;;EAED,MAAMkQ,WAAW,GAAGnN,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAApB;EACA,MAAMlN,QAAQ,GAAGI,sBAAsB,CAAC,IAAD,CAAvC;EACA,MAAM8Z,gBAAgB,GAAGjM,cAAc,CAACE,IAAf,CAAoBnO,QAApB,CAAzB;EAEAka,EAAAA,gBAAgB,CAAC9X,OAAjB,CAAyB,UAAArC,OAAO,EAAI;EAClC,QAAM0E,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAb;EACA,QAAIlI,MAAJ;;EACA,QAAIyC,IAAJ,EAAU;EACR;EACA,UAAIA,IAAI,CAACgU,OAAL,KAAiB,IAAjB,IAAyB,OAAOwB,WAAW,CAAC5C,MAAnB,KAA8B,QAA3D,EAAqE;EACnE5S,QAAAA,IAAI,CAACmO,OAAL,CAAayE,MAAb,GAAsB4C,WAAW,CAAC5C,MAAlC;EACA5S,QAAAA,IAAI,CAACgU,OAAL,GAAehU,IAAI,CAACiU,UAAL,EAAf;EACD;;EAED1W,MAAAA,MAAM,GAAG,QAAT;EACD,KARD,MAQO;EACLA,MAAAA,MAAM,GAAGiY,WAAT;EACD;;EAEDhC,IAAAA,QAAQ,CAACmB,iBAAT,CAA2BrZ,OAA3B,EAAoCiC,MAApC;EACD,GAhBD;EAiBD,CA3BD;EA6BA;EACA;EACA;EACA;EACA;EACA;;EAEAiC,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAaiO,QAAQ,CAACtM,eAAtB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBgM,QAAzB;;EACAhP,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOiM,QAAQ,CAACtM,eAAhB;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;EClaA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,SAAS,GAAG,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,OAAO,SAAS,KAAK,WAAW,CAAC;AACrH;EACA,IAAI,eAAe,GAAG,YAAY;EAClC,EAAE,IAAI,qBAAqB,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EAC7D,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,qBAAqB,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;EAC5D,IAAI,IAAI,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;EACjF,MAAM,OAAO,CAAC,CAAC;EACf,KAAK;EACL,GAAG;EACH,EAAE,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,CAAC;AACJ;EACA,SAAS,iBAAiB,CAAC,EAAE,EAAE;EAC/B,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC;EACrB,EAAE,OAAO,YAAY;EACrB,IAAI,IAAI,MAAM,EAAE;EAChB,MAAM,OAAO;EACb,KAAK;EACL,IAAI,MAAM,GAAG,IAAI,CAAC;EAClB,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,YAAY;EAC9C,MAAM,MAAM,GAAG,KAAK,CAAC;EACrB,MAAM,EAAE,EAAE,CAAC;EACX,KAAK,CAAC,CAAC;EACP,GAAG,CAAC;EACJ,CAAC;AACD;EACA,SAAS,YAAY,CAAC,EAAE,EAAE;EAC1B,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC;EACxB,EAAE,OAAO,YAAY;EACrB,IAAI,IAAI,CAAC,SAAS,EAAE;EACpB,MAAM,SAAS,GAAG,IAAI,CAAC;EACvB,MAAM,UAAU,CAAC,YAAY;EAC7B,QAAQ,SAAS,GAAG,KAAK,CAAC;EAC1B,QAAQ,EAAE,EAAE,CAAC;EACb,OAAO,EAAE,eAAe,CAAC,CAAC;EAC1B,KAAK;EACL,GAAG,CAAC;EACJ,CAAC;AACD;EACA,IAAI,kBAAkB,GAAG,SAAS,IAAI,MAAM,CAAC,OAAO,CAAC;AACrD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,QAAQ,GAAG,kBAAkB,GAAG,iBAAiB,GAAG,YAAY,CAAC;AACrE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,UAAU,CAAC,eAAe,EAAE;EACrC,EAAE,IAAI,OAAO,GAAG,EAAE,CAAC;EACnB,EAAE,OAAO,eAAe,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,mBAAmB,CAAC;EAC3F,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,wBAAwB,CAAC,OAAO,EAAE,QAAQ,EAAE;EACrD,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;EAC9B,IAAI,OAAO,EAAE,CAAC;EACd,GAAG;EACH;EACA,EAAE,IAAI,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC;EACjD,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;EACnD,EAAE,OAAO,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;EACxC,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,aAAa,CAAC,OAAO,EAAE;EAChC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,MAAM,EAAE;EACnC,IAAI,OAAO,OAAO,CAAC;EACnB,GAAG;EACH,EAAE,OAAO,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;EAC5C,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,eAAe,CAAC,OAAO,EAAE;EAClC;EACA,EAAE,IAAI,CAAC,OAAO,EAAE;EAChB,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC;EACzB,GAAG;AACH;EACA,EAAE,QAAQ,OAAO,CAAC,QAAQ;EAC1B,IAAI,KAAK,MAAM,CAAC;EAChB,IAAI,KAAK,MAAM;EACf,MAAM,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC;EACxC,IAAI,KAAK,WAAW;EACpB,MAAM,OAAO,OAAO,CAAC,IAAI,CAAC;EAC1B,GAAG;AACH;EACA;AACA;EACA,EAAE,IAAI,qBAAqB,GAAG,wBAAwB,CAAC,OAAO,CAAC;EAC/D,MAAM,QAAQ,GAAG,qBAAqB,CAAC,QAAQ;EAC/C,MAAM,SAAS,GAAG,qBAAqB,CAAC,SAAS;EACjD,MAAM,SAAS,GAAG,qBAAqB,CAAC,SAAS,CAAC;AAClD;EACA,EAAE,IAAI,uBAAuB,CAAC,IAAI,CAAC,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC,EAAE;EACtE,IAAI,OAAO,OAAO,CAAC;EACnB,GAAG;AACH;EACA,EAAE,OAAO,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;EACjD,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,gBAAgB,CAAC,SAAS,EAAE;EACrC,EAAE,OAAO,SAAS,IAAI,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC,aAAa,GAAG,SAAS,CAAC;EACpF,CAAC;AACD;EACA,IAAI,MAAM,GAAG,SAAS,IAAI,CAAC,EAAE,MAAM,CAAC,oBAAoB,IAAI,QAAQ,CAAC,YAAY,CAAC,CAAC;EACnF,IAAI,MAAM,GAAG,SAAS,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAC9D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,IAAI,CAAC,OAAO,EAAE;EACvB,EAAE,IAAI,OAAO,KAAK,EAAE,EAAE;EACtB,IAAI,OAAO,MAAM,CAAC;EAClB,GAAG;EACH,EAAE,IAAI,OAAO,KAAK,EAAE,EAAE;EACtB,IAAI,OAAO,MAAM,CAAC;EAClB,GAAG;EACH,EAAE,OAAO,MAAM,IAAI,MAAM,CAAC;EAC1B,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,eAAe,CAAC,OAAO,EAAE;EAClC,EAAE,IAAI,CAAC,OAAO,EAAE;EAChB,IAAI,OAAO,QAAQ,CAAC,eAAe,CAAC;EACpC,GAAG;AACH;EACA,EAAE,IAAI,cAAc,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;AACvD;EACA;EACA,EAAE,IAAI,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC;EAClD;EACA,EAAE,OAAO,YAAY,KAAK,cAAc,IAAI,OAAO,CAAC,kBAAkB,EAAE;EACxE,IAAI,YAAY,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,kBAAkB,EAAE,YAAY,CAAC;EACvE,GAAG;AACH;EACA,EAAE,IAAI,QAAQ,GAAG,YAAY,IAAI,YAAY,CAAC,QAAQ,CAAC;AACvD;EACA,EAAE,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,MAAM,EAAE;EAC/D,IAAI,OAAO,OAAO,GAAG,OAAO,CAAC,aAAa,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC;EACtF,GAAG;AACH;EACA;EACA;EACA,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,wBAAwB,CAAC,YAAY,EAAE,UAAU,CAAC,KAAK,QAAQ,EAAE;EACtI,IAAI,OAAO,eAAe,CAAC,YAAY,CAAC,CAAC;EACzC,GAAG;AACH;EACA,EAAE,OAAO,YAAY,CAAC;EACtB,CAAC;AACD;EACA,SAAS,iBAAiB,CAAC,OAAO,EAAE;EACpC,EAAE,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AAClC;EACA,EAAE,IAAI,QAAQ,KAAK,MAAM,EAAE;EAC3B,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;EACH,EAAE,OAAO,QAAQ,KAAK,MAAM,IAAI,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,OAAO,CAAC;EACvF,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,OAAO,CAAC,IAAI,EAAE;EACvB,EAAE,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;EAChC,IAAI,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;EACpC,GAAG;AACH;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,EAAE;EACpD;EACA,EAAE,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;EAC1E,IAAI,OAAO,QAAQ,CAAC,eAAe,CAAC;EACpC,GAAG;AACH;EACA;EACA,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,uBAAuB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC;EAC5F,EAAE,IAAI,KAAK,GAAG,KAAK,GAAG,QAAQ,GAAG,QAAQ,CAAC;EAC1C,EAAE,IAAI,GAAG,GAAG,KAAK,GAAG,QAAQ,GAAG,QAAQ,CAAC;AACxC;EACA;EACA,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;EACrC,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;EAC3B,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;EACvB,EAAE,IAAI,uBAAuB,GAAG,KAAK,CAAC,uBAAuB,CAAC;AAC9D;EACA;AACA;EACA,EAAE,IAAI,QAAQ,KAAK,uBAAuB,IAAI,QAAQ,KAAK,uBAAuB,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;EAC3G,IAAI,IAAI,iBAAiB,CAAC,uBAAuB,CAAC,EAAE;EACpD,MAAM,OAAO,uBAAuB,CAAC;EACrC,KAAK;AACL;EACA,IAAI,OAAO,eAAe,CAAC,uBAAuB,CAAC,CAAC;EACpD,GAAG;AACH;EACA;EACA,EAAE,IAAI,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;EACvC,EAAE,IAAI,YAAY,CAAC,IAAI,EAAE;EACzB,IAAI,OAAO,sBAAsB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;EAC/D,GAAG,MAAM;EACT,IAAI,OAAO,sBAAsB,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;EACpE,GAAG;EACH,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,SAAS,CAAC,OAAO,EAAE;EAC5B,EAAE,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACvF;EACA,EAAE,IAAI,SAAS,GAAG,IAAI,KAAK,KAAK,GAAG,WAAW,GAAG,YAAY,CAAC;EAC9D,EAAE,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AAClC;EACA,EAAE,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,MAAM,EAAE;EAClD,IAAI,IAAI,IAAI,GAAG,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC;EACrD,IAAI,IAAI,gBAAgB,GAAG,OAAO,CAAC,aAAa,CAAC,gBAAgB,IAAI,IAAI,CAAC;EAC1E,IAAI,OAAO,gBAAgB,CAAC,SAAS,CAAC,CAAC;EACvC,GAAG;AACH;EACA,EAAE,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC;EAC5B,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE;EACtC,EAAE,IAAI,QAAQ,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAC3F;EACA,EAAE,IAAI,SAAS,GAAG,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;EAC5C,EAAE,IAAI,UAAU,GAAG,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;EAC9C,EAAE,IAAI,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EACnC,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS,GAAG,QAAQ,CAAC;EACnC,EAAE,IAAI,CAAC,MAAM,IAAI,SAAS,GAAG,QAAQ,CAAC;EACtC,EAAE,IAAI,CAAC,IAAI,IAAI,UAAU,GAAG,QAAQ,CAAC;EACrC,EAAE,IAAI,CAAC,KAAK,IAAI,UAAU,GAAG,QAAQ,CAAC;EACtC,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA,SAAS,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE;EACtC,EAAE,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,MAAM,GAAG,KAAK,CAAC;EAC5C,EAAE,IAAI,KAAK,GAAG,KAAK,KAAK,MAAM,GAAG,OAAO,GAAG,QAAQ,CAAC;AACpD;EACA,EAAE,OAAO,UAAU,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC;EACzG,CAAC;AACD;EACA,SAAS,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE;EAClD,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,IAAI,IAAI,KAAK,QAAQ,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,IAAI,IAAI,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC/U,CAAC;AACD;EACA,SAAS,cAAc,CAAC,QAAQ,EAAE;EAClC,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;EAC3B,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC,eAAe,CAAC;EACtC,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;AACzD;EACA,EAAE,OAAO;EACT,IAAI,MAAM,EAAE,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC;EACxD,IAAI,KAAK,EAAE,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC;EACtD,GAAG,CAAC;EACJ,CAAC;AACD;EACA,IAAI,cAAc,GAAG,UAAU,QAAQ,EAAE,WAAW,EAAE;EACtD,EAAE,IAAI,EAAE,QAAQ,YAAY,WAAW,CAAC,EAAE;EAC1C,IAAI,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC;EAC7D,GAAG;EACH,CAAC,CAAC;AACF;EACA,IAAI,WAAW,GAAG,YAAY;EAC9B,EAAE,SAAS,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE;EAC3C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC3C,MAAM,IAAI,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;EAChC,MAAM,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,IAAI,KAAK,CAAC;EAC7D,MAAM,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;EACrC,MAAM,IAAI,OAAO,IAAI,UAAU,EAAE,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;EAC5D,MAAM,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;EAChE,KAAK;EACL,GAAG;AACH;EACA,EAAE,OAAO,UAAU,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE;EACzD,IAAI,IAAI,UAAU,EAAE,gBAAgB,CAAC,WAAW,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;EACxE,IAAI,IAAI,WAAW,EAAE,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;EAChE,IAAI,OAAO,WAAW,CAAC;EACvB,GAAG,CAAC;EACJ,CAAC,EAAE,CAAC;AACJ;AACA;AACA;AACA;AACA;EACA,IAAI,cAAc,GAAG,UAAU,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;EAChD,EAAE,IAAI,GAAG,IAAI,GAAG,EAAE;EAClB,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE;EACpC,MAAM,KAAK,EAAE,KAAK;EAClB,MAAM,UAAU,EAAE,IAAI;EACtB,MAAM,YAAY,EAAE,IAAI;EACxB,MAAM,QAAQ,EAAE,IAAI;EACpB,KAAK,CAAC,CAAC;EACP,GAAG,MAAM;EACT,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;EACrB,GAAG;AACH;EACA,EAAE,OAAO,GAAG,CAAC;EACb,CAAC,CAAC;AACF;EACA,IAAIwO,UAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,UAAU,MAAM,EAAE;EAClD,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC7C,IAAI,IAAI,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAC9B;EACA,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;EAC5B,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;EAC7D,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;EAClC,OAAO;EACP,KAAK;EACL,GAAG;AACH;EACA,EAAE,OAAO,MAAM,CAAC;EAChB,CAAC,CAAC;AACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,aAAa,CAAC,OAAO,EAAE;EAChC,EAAE,OAAOA,UAAQ,CAAC,EAAE,EAAE,OAAO,EAAE;EAC/B,IAAI,KAAK,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK;EACvC,IAAI,MAAM,EAAE,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;EACxC,GAAG,CAAC,CAAC;EACL,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,qBAAqB,CAAC,OAAO,EAAE;EACxC,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC;AAChB;EACA;EACA;EACA;EACA,EAAE,IAAI;EACN,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE;EAClB,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;EAC7C,MAAM,IAAI,SAAS,GAAG,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;EAChD,MAAM,IAAI,UAAU,GAAG,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;EAClD,MAAM,IAAI,CAAC,GAAG,IAAI,SAAS,CAAC;EAC5B,MAAM,IAAI,CAAC,IAAI,IAAI,UAAU,CAAC;EAC9B,MAAM,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC;EAC/B,MAAM,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC;EAC/B,KAAK,MAAM;EACX,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;EAC7C,KAAK;EACL,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE;AAChB;EACA,EAAE,IAAI,MAAM,GAAG;EACf,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI;EACnB,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG;EACjB,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI;EACjC,IAAI,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG;EAClC,GAAG,CAAC;AACJ;EACA;EACA,EAAE,IAAI,KAAK,GAAG,OAAO,CAAC,QAAQ,KAAK,MAAM,GAAG,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;EACvF,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,OAAO,CAAC,WAAW,IAAI,MAAM,CAAC,KAAK,CAAC;EACjE,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,OAAO,CAAC,YAAY,IAAI,MAAM,CAAC,MAAM,CAAC;AACrE;EACA,EAAE,IAAI,cAAc,GAAG,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;EACnD,EAAE,IAAI,aAAa,GAAG,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC;AACpD;EACA;EACA;EACA,EAAE,IAAI,cAAc,IAAI,aAAa,EAAE;EACvC,IAAI,IAAI,MAAM,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;EACnD,IAAI,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;EAClD,IAAI,aAAa,IAAI,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AACjD;EACA,IAAI,MAAM,CAAC,KAAK,IAAI,cAAc,CAAC;EACnC,IAAI,MAAM,CAAC,MAAM,IAAI,aAAa,CAAC;EACnC,GAAG;AACH;EACA,EAAE,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC;EAC/B,CAAC;AACD;EACA,SAAS,oCAAoC,CAAC,QAAQ,EAAE,MAAM,EAAE;EAChE,EAAE,IAAI,aAAa,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAChG;EACA,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;EACxB,EAAE,IAAI,MAAM,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC;EAC1C,EAAE,IAAI,YAAY,GAAG,qBAAqB,CAAC,QAAQ,CAAC,CAAC;EACrD,EAAE,IAAI,UAAU,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC;EACjD,EAAE,IAAI,YAAY,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;AAC/C;EACA,EAAE,IAAI,MAAM,GAAG,wBAAwB,CAAC,MAAM,CAAC,CAAC;EAChD,EAAE,IAAI,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;EACzD,EAAE,IAAI,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;AAC3D;EACA;EACA,EAAE,IAAI,aAAa,IAAI,MAAM,EAAE;EAC/B,IAAI,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;EACjD,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;EACnD,GAAG;EACH,EAAE,IAAI,OAAO,GAAG,aAAa,CAAC;EAC9B,IAAI,GAAG,EAAE,YAAY,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,cAAc;EAC3D,IAAI,IAAI,EAAE,YAAY,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,eAAe;EAC/D,IAAI,KAAK,EAAE,YAAY,CAAC,KAAK;EAC7B,IAAI,MAAM,EAAE,YAAY,CAAC,MAAM;EAC/B,GAAG,CAAC,CAAC;EACL,EAAE,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;EACxB,EAAE,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;AACzB;EACA;EACA;EACA;EACA;EACA,EAAE,IAAI,CAAC,MAAM,IAAI,MAAM,EAAE;EACzB,IAAI,IAAI,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;EACjD,IAAI,IAAI,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACnD;EACA,IAAI,OAAO,CAAC,GAAG,IAAI,cAAc,GAAG,SAAS,CAAC;EAC9C,IAAI,OAAO,CAAC,MAAM,IAAI,cAAc,GAAG,SAAS,CAAC;EACjD,IAAI,OAAO,CAAC,IAAI,IAAI,eAAe,GAAG,UAAU,CAAC;EACjD,IAAI,OAAO,CAAC,KAAK,IAAI,eAAe,GAAG,UAAU,CAAC;AAClD;EACA;EACA,IAAI,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;EAClC,IAAI,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;EACpC,GAAG;AACH;EACA,EAAE,IAAI,MAAM,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,MAAM,KAAK,YAAY,IAAI,YAAY,CAAC,QAAQ,KAAK,MAAM,EAAE;EAC9H,IAAI,OAAO,GAAG,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;EAC7C,GAAG;AACH;EACA,EAAE,OAAO,OAAO,CAAC;EACjB,CAAC;AACD;EACA,SAAS,6CAA6C,CAAC,OAAO,EAAE;EAChE,EAAE,IAAI,aAAa,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAChG;EACA,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC;EACnD,EAAE,IAAI,cAAc,GAAG,oCAAoC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;EAC3E,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC;EACjE,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;AACpE;EACA,EAAE,IAAI,SAAS,GAAG,CAAC,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EACvD,EAAE,IAAI,UAAU,GAAG,CAAC,aAAa,GAAG,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;AAChE;EACA,EAAE,IAAI,MAAM,GAAG;EACf,IAAI,GAAG,EAAE,SAAS,GAAG,cAAc,CAAC,GAAG,GAAG,cAAc,CAAC,SAAS;EAClE,IAAI,IAAI,EAAE,UAAU,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,UAAU;EACtE,IAAI,KAAK,EAAE,KAAK;EAChB,IAAI,MAAM,EAAE,MAAM;EAClB,GAAG,CAAC;AACJ;EACA,EAAE,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC;EAC/B,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,OAAO,CAAC,OAAO,EAAE;EAC1B,EAAE,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;EAClC,EAAE,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,MAAM,EAAE;EAClD,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;EACH,EAAE,IAAI,wBAAwB,CAAC,OAAO,EAAE,UAAU,CAAC,KAAK,OAAO,EAAE;EACjE,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;EACH,EAAE,IAAI,UAAU,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;EAC1C,EAAE,IAAI,CAAC,UAAU,EAAE;EACnB,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;EACH,EAAE,OAAO,OAAO,CAAC,UAAU,CAAC,CAAC;EAC7B,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA,SAAS,4BAA4B,CAAC,OAAO,EAAE;EAC/C;EACA,EAAE,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,EAAE,EAAE;EACpD,IAAI,OAAO,QAAQ,CAAC,eAAe,CAAC;EACpC,GAAG;EACH,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,aAAa,CAAC;EACjC,EAAE,OAAO,EAAE,IAAI,wBAAwB,CAAC,EAAE,EAAE,WAAW,CAAC,KAAK,MAAM,EAAE;EACrE,IAAI,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC;EAC1B,GAAG;EACH,EAAE,OAAO,EAAE,IAAI,QAAQ,CAAC,eAAe,CAAC;EACxC,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,iBAAiB,EAAE;EACtE,EAAE,IAAI,aAAa,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAChG;EACA;AACA;EACA,EAAE,IAAI,UAAU,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;EACvC,EAAE,IAAI,YAAY,GAAG,aAAa,GAAG,4BAA4B,CAAC,MAAM,CAAC,GAAG,sBAAsB,CAAC,MAAM,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;AACxI;EACA;EACA,EAAE,IAAI,iBAAiB,KAAK,UAAU,EAAE;EACxC,IAAI,UAAU,GAAG,6CAA6C,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;EAC5F,GAAG,MAAM;EACT;EACA,IAAI,IAAI,cAAc,GAAG,KAAK,CAAC,CAAC;EAChC,IAAI,IAAI,iBAAiB,KAAK,cAAc,EAAE;EAC9C,MAAM,cAAc,GAAG,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC;EACjE,MAAM,IAAI,cAAc,CAAC,QAAQ,KAAK,MAAM,EAAE;EAC9C,QAAQ,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC;EAC9D,OAAO;EACP,KAAK,MAAM,IAAI,iBAAiB,KAAK,QAAQ,EAAE;EAC/C,MAAM,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC;EAC5D,KAAK,MAAM;EACX,MAAM,cAAc,GAAG,iBAAiB,CAAC;EACzC,KAAK;AACL;EACA,IAAI,IAAI,OAAO,GAAG,oCAAoC,CAAC,cAAc,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;AACpG;EACA;EACA,IAAI,IAAI,cAAc,CAAC,QAAQ,KAAK,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;EACtE,MAAM,IAAI,eAAe,GAAG,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC;EAChE,UAAU,MAAM,GAAG,eAAe,CAAC,MAAM;EACzC,UAAU,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC;AACxC;EACA,MAAM,UAAU,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC;EACxD,MAAM,UAAU,CAAC,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;EAC/C,MAAM,UAAU,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC;EAC3D,MAAM,UAAU,CAAC,KAAK,GAAG,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;EAC9C,KAAK,MAAM;EACX;EACA,MAAM,UAAU,GAAG,OAAO,CAAC;EAC3B,KAAK;EACL,GAAG;AACH;EACA;EACA,EAAE,OAAO,GAAG,OAAO,IAAI,CAAC,CAAC;EACzB,EAAE,IAAI,eAAe,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC;EACpD,EAAE,UAAU,CAAC,IAAI,IAAI,eAAe,GAAG,OAAO,GAAG,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;EACnE,EAAE,UAAU,CAAC,GAAG,IAAI,eAAe,GAAG,OAAO,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;EACjE,EAAE,UAAU,CAAC,KAAK,IAAI,eAAe,GAAG,OAAO,GAAG,OAAO,CAAC,KAAK,IAAI,CAAC,CAAC;EACrE,EAAE,UAAU,CAAC,MAAM,IAAI,eAAe,GAAG,OAAO,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;AACvE;EACA,EAAE,OAAO,UAAU,CAAC;EACpB,CAAC;AACD;EACA,SAAS,OAAO,CAAC,IAAI,EAAE;EACvB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;EACxB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC3B;EACA,EAAE,OAAO,KAAK,GAAG,MAAM,CAAC;EACxB,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,oBAAoB,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE;EACxF,EAAE,IAAI,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACtF;EACA,EAAE,IAAI,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;EACxC,IAAI,OAAO,SAAS,CAAC;EACrB,GAAG;AACH;EACA,EAAE,IAAI,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;AAChF;EACA,EAAE,IAAI,KAAK,GAAG;EACd,IAAI,GAAG,EAAE;EACT,MAAM,KAAK,EAAE,UAAU,CAAC,KAAK;EAC7B,MAAM,MAAM,EAAE,OAAO,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG;EAC1C,KAAK;EACL,IAAI,KAAK,EAAE;EACX,MAAM,KAAK,EAAE,UAAU,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK;EAC7C,MAAM,MAAM,EAAE,UAAU,CAAC,MAAM;EAC/B,KAAK;EACL,IAAI,MAAM,EAAE;EACZ,MAAM,KAAK,EAAE,UAAU,CAAC,KAAK;EAC7B,MAAM,MAAM,EAAE,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;EAChD,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,KAAK,EAAE,OAAO,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI;EAC3C,MAAM,MAAM,EAAE,UAAU,CAAC,MAAM;EAC/B,KAAK;EACL,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;EAC1D,IAAI,OAAOA,UAAQ,CAAC;EACpB,MAAM,GAAG,EAAE,GAAG;EACd,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE;EACnB,MAAM,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;EAC/B,KAAK,CAAC,CAAC;EACP,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;EAC1B,IAAI,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;EAC3B,GAAG,CAAC,CAAC;AACL;EACA,EAAE,IAAI,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC,UAAU,KAAK,EAAE;EAC1D,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK;EAC3B,QAAQ,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;EAC9B,IAAI,OAAO,KAAK,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC;EACxE,GAAG,CAAC,CAAC;AACL;EACA,EAAE,IAAI,iBAAiB,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAC/F;EACA,EAAE,IAAI,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C;EACA,EAAE,OAAO,iBAAiB,IAAI,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,EAAE,CAAC,CAAC;EAChE,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE;EACvD,EAAE,IAAI,aAAa,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAC/F;EACA,EAAE,IAAI,kBAAkB,GAAG,aAAa,GAAG,4BAA4B,CAAC,MAAM,CAAC,GAAG,sBAAsB,CAAC,MAAM,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;EAC9I,EAAE,OAAO,oCAAoC,CAAC,SAAS,EAAE,kBAAkB,EAAE,aAAa,CAAC,CAAC;EAC5F,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,aAAa,CAAC,OAAO,EAAE;EAChC,EAAE,IAAI,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,WAAW,CAAC;EACjD,EAAE,IAAI,MAAM,GAAG,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;EAChD,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,CAAC,CAAC;EACnF,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;EACnF,EAAE,IAAI,MAAM,GAAG;EACf,IAAI,KAAK,EAAE,OAAO,CAAC,WAAW,GAAG,CAAC;EAClC,IAAI,MAAM,EAAE,OAAO,CAAC,YAAY,GAAG,CAAC;EACpC,GAAG,CAAC;EACJ,EAAE,OAAO,MAAM,CAAC;EAChB,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,oBAAoB,CAAC,SAAS,EAAE;EACzC,EAAE,IAAI,IAAI,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;EAC5E,EAAE,OAAO,SAAS,CAAC,OAAO,CAAC,wBAAwB,EAAE,UAAU,OAAO,EAAE;EACxE,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;EACzB,GAAG,CAAC,CAAC;EACL,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,gBAAgB,CAAC,MAAM,EAAE,gBAAgB,EAAE,SAAS,EAAE;EAC/D,EAAE,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC;EACA;EACA,EAAE,IAAI,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;AACzC;EACA;EACA,EAAE,IAAI,aAAa,GAAG;EACtB,IAAI,KAAK,EAAE,UAAU,CAAC,KAAK;EAC3B,IAAI,MAAM,EAAE,UAAU,CAAC,MAAM;EAC7B,GAAG,CAAC;AACJ;EACA;EACA,EAAE,IAAI,OAAO,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;EAC5D,EAAE,IAAI,QAAQ,GAAG,OAAO,GAAG,KAAK,GAAG,MAAM,CAAC;EAC1C,EAAE,IAAI,aAAa,GAAG,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC;EAC/C,EAAE,IAAI,WAAW,GAAG,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAC;EACjD,EAAE,IAAI,oBAAoB,GAAG,CAAC,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAC;AAC3D;EACA,EAAE,aAAa,CAAC,QAAQ,CAAC,GAAG,gBAAgB,CAAC,QAAQ,CAAC,GAAG,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;EACzH,EAAE,IAAI,SAAS,KAAK,aAAa,EAAE;EACnC,IAAI,aAAa,CAAC,aAAa,CAAC,GAAG,gBAAgB,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,oBAAoB,CAAC,CAAC;EACtG,GAAG,MAAM;EACT,IAAI,aAAa,CAAC,aAAa,CAAC,GAAG,gBAAgB,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,CAAC;EACzF,GAAG;AACH;EACA,EAAE,OAAO,aAAa,CAAC;EACvB,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE;EAC1B;EACA,EAAE,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE;EAC5B,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAC3B,GAAG;AACH;EACA;EACA,EAAE,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE;EACrC;EACA,EAAE,IAAI,KAAK,CAAC,SAAS,CAAC,SAAS,EAAE;EACjC,IAAI,OAAO,GAAG,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE;EACxC,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC;EACjC,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA;EACA,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,UAAU,GAAG,EAAE;EACvC,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC;EAC/B,GAAG,CAAC,CAAC;EACL,EAAE,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;EAC5B,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE;EAC7C,EAAE,IAAI,cAAc,GAAG,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;AAC/G;EACA,EAAE,cAAc,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;EAC7C,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE;EAC9B;EACA,MAAM,OAAO,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;EAC5E,KAAK;EACL,IAAI,IAAI,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,EAAE,CAAC;EACjD,IAAI,IAAI,QAAQ,CAAC,OAAO,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE;EAC5C;EACA;EACA;EACA,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;EAC/D,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACrE;EACA,MAAM,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;EAChC,KAAK;EACL,GAAG,CAAC,CAAC;AACL;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,MAAM,GAAG;EAClB;EACA,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;EAC9B,IAAI,OAAO;EACX,GAAG;AACH;EACA,EAAE,IAAI,IAAI,GAAG;EACb,IAAI,QAAQ,EAAE,IAAI;EAClB,IAAI,MAAM,EAAE,EAAE;EACd,IAAI,WAAW,EAAE,EAAE;EACnB,IAAI,UAAU,EAAE,EAAE;EAClB,IAAI,OAAO,EAAE,KAAK;EAClB,IAAI,OAAO,EAAE,EAAE;EACf,GAAG,CAAC;AACJ;EACA;EACA,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;AACpH;EACA;EACA;EACA;EACA,EAAE,IAAI,CAAC,SAAS,GAAG,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACzM;EACA;EACA,EAAE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;AAC1C;EACA,EAAE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;AAClD;EACA;EACA,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAC9F;EACA,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,OAAO,GAAG,UAAU,CAAC;AACnF;EACA;EACA,EAAE,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AAC5C;EACA;EACA;EACA,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;EAC7B,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;EAChC,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;EAChC,GAAG,MAAM;EACT,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;EAChC,GAAG;EACH,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,iBAAiB,CAAC,SAAS,EAAE,YAAY,EAAE;EACpD,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE;EACxC,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;EACxB,QAAQ,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;EAC/B,IAAI,OAAO,OAAO,IAAI,IAAI,KAAK,YAAY,CAAC;EAC5C,GAAG,CAAC,CAAC;EACL,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,wBAAwB,CAAC,QAAQ,EAAE;EAC5C,EAAE,IAAI,QAAQ,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;EACrD,EAAE,IAAI,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACvE;EACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;EAC5C,IAAI,IAAI,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC7B,IAAI,IAAI,OAAO,GAAG,MAAM,GAAG,EAAE,GAAG,MAAM,GAAG,SAAS,GAAG,QAAQ,CAAC;EAC9D,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,WAAW,EAAE;EAC7D,MAAM,OAAO,OAAO,CAAC;EACrB,KAAK;EACL,GAAG;EACH,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,OAAO,GAAG;EACnB,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;AAChC;EACA;EACA,EAAE,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,EAAE;EACvD,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;EAC/C,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;EACpC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;EAC/B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC;EAChC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;EACjC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;EAClC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC;EACtC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC;EAClE,GAAG;AACH;EACA,EAAE,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC/B;EACA;EACA;EACA,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;EACpC,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EACpD,GAAG;EACH,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,SAAS,CAAC,OAAO,EAAE;EAC5B,EAAE,IAAI,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;EAC5C,EAAE,OAAO,aAAa,GAAG,aAAa,CAAC,WAAW,GAAG,MAAM,CAAC;EAC5D,CAAC;AACD;EACA,SAAS,qBAAqB,CAAC,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE;EAC7E,EAAE,IAAI,MAAM,GAAG,YAAY,CAAC,QAAQ,KAAK,MAAM,CAAC;EAChD,EAAE,IAAI,MAAM,GAAG,MAAM,GAAG,YAAY,CAAC,aAAa,CAAC,WAAW,GAAG,YAAY,CAAC;EAC9E,EAAE,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AAC9D;EACA,EAAE,IAAI,CAAC,MAAM,EAAE;EACf,IAAI,qBAAqB,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;EAC9F,GAAG;EACH,EAAE,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EAC7B,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,mBAAmB,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE;EACrE;EACA,EAAE,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;EAClC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AACxF;EACA;EACA,EAAE,IAAI,aAAa,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC;EACjD,EAAE,qBAAqB,CAAC,aAAa,EAAE,QAAQ,EAAE,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;EACzF,EAAE,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;EACtC,EAAE,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;AAC7B;EACA,EAAE,OAAO,KAAK,CAAC;EACf,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,oBAAoB,GAAG;EAChC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;EACjC,IAAI,IAAI,CAAC,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;EACpG,GAAG;EACH,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,oBAAoB,CAAC,SAAS,EAAE,KAAK,EAAE;EAChD;EACA,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;AACxE;EACA;EACA,EAAE,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;EAChD,IAAI,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;EAC5D,GAAG,CAAC,CAAC;AACL;EACA;EACA,EAAE,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;EAC3B,EAAE,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC;EAC3B,EAAE,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;EAC7B,EAAE,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC;EAC9B,EAAE,OAAO,KAAK,CAAC;EACf,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,qBAAqB,GAAG;EACjC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;EAChC,IAAI,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;EAC9C,IAAI,IAAI,CAAC,KAAK,GAAG,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;EAClE,GAAG;EACH,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,SAAS,CAAC,CAAC,EAAE;EACtB,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE;EACpC,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;EAC9C,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;EAClB;EACA,IAAI,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;EAC/G,MAAM,IAAI,GAAG,IAAI,CAAC;EAClB,KAAK;EACL,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;EAC9C,GAAG,CAAC,CAAC;EACL,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,aAAa,CAAC,OAAO,EAAE,UAAU,EAAE;EAC5C,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;EAClD,IAAI,IAAI,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;EACjC,IAAI,IAAI,KAAK,KAAK,KAAK,EAAE;EACzB,MAAM,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;EACnD,KAAK,MAAM;EACX,MAAM,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;EACpC,KAAK;EACL,GAAG,CAAC,CAAC;EACL,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,UAAU,CAAC,IAAI,EAAE;EAC1B;EACA;EACA;EACA;EACA,EAAE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAC/C;EACA;EACA;EACA,EAAE,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AACvD;EACA;EACA,EAAE,IAAI,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE;EACjE,IAAI,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;EACnD,GAAG;AACH;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,gBAAgB,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE;EAC9E;EACA,EAAE,IAAI,gBAAgB,GAAG,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;AAC9F;EACA;EACA;EACA;EACA,EAAE,IAAI,SAAS,GAAG,oBAAoB,CAAC,OAAO,CAAC,SAAS,EAAE,gBAAgB,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACzK;EACA,EAAE,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;AAChD;EACA;EACA;EACA,EAAE,SAAS,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,aAAa,GAAG,OAAO,GAAG,UAAU,EAAE,CAAC,CAAC;AAChF;EACA,EAAE,OAAO,OAAO,CAAC;EACjB,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,iBAAiB,CAAC,IAAI,EAAE,WAAW,EAAE;EAC9C,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO;EAClC,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM;EACnC,MAAM,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;EAC1C,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;EACxB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACzB;EACA,EAAE,IAAI,OAAO,GAAG,SAAS,OAAO,CAAC,CAAC,EAAE;EACpC,IAAI,OAAO,CAAC,CAAC;EACb,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,cAAc,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;EAC9C,EAAE,IAAI,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACxC;EACA,EAAE,IAAI,UAAU,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;EACpE,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;EACvD,EAAE,IAAI,eAAe,GAAG,cAAc,GAAG,CAAC,KAAK,WAAW,GAAG,CAAC,CAAC;EAC/D,EAAE,IAAI,YAAY,GAAG,cAAc,GAAG,CAAC,KAAK,CAAC,IAAI,WAAW,GAAG,CAAC,KAAK,CAAC,CAAC;AACvE;EACA,EAAE,IAAI,mBAAmB,GAAG,CAAC,WAAW,GAAG,OAAO,GAAG,UAAU,IAAI,WAAW,IAAI,eAAe,GAAG,KAAK,GAAG,KAAK,CAAC;EAClH,EAAE,IAAI,iBAAiB,GAAG,CAAC,WAAW,GAAG,OAAO,GAAG,KAAK,CAAC;AACzD;EACA,EAAE,OAAO;EACT,IAAI,IAAI,EAAE,mBAAmB,CAAC,YAAY,IAAI,CAAC,WAAW,IAAI,WAAW,GAAG,MAAM,CAAC,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC;EAC1G,IAAI,GAAG,EAAE,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC;EACtC,IAAI,MAAM,EAAE,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC;EAC5C,IAAI,KAAK,EAAE,mBAAmB,CAAC,MAAM,CAAC,KAAK,CAAC;EAC5C,GAAG,CAAC;EACJ,CAAC;AACD;EACA,IAAI,SAAS,GAAG,SAAS,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAClE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE;EACrC,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;EACnB,MAAM,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;EACpB,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;AACnC;EACA;AACA;EACA,EAAE,IAAI,2BAA2B,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,UAAU,QAAQ,EAAE;EACtF,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,YAAY,CAAC;EAC1C,GAAG,CAAC,CAAC,eAAe,CAAC;EACrB,EAAE,IAAI,2BAA2B,KAAK,SAAS,EAAE;EACjD,IAAI,OAAO,CAAC,IAAI,CAAC,+HAA+H,CAAC,CAAC;EAClJ,GAAG;EACH,EAAE,IAAI,eAAe,GAAG,2BAA2B,KAAK,SAAS,GAAG,2BAA2B,GAAG,OAAO,CAAC,eAAe,CAAC;AAC1H;EACA,EAAE,IAAI,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;EAC3D,EAAE,IAAI,gBAAgB,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;AAC7D;EACA;EACA,EAAE,IAAI,MAAM,GAAG;EACf,IAAI,QAAQ,EAAE,MAAM,CAAC,QAAQ;EAC7B,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,OAAO,GAAG,iBAAiB,CAAC,IAAI,EAAE,MAAM,CAAC,gBAAgB,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACnF;EACA,EAAE,IAAI,KAAK,GAAG,CAAC,KAAK,QAAQ,GAAG,KAAK,GAAG,QAAQ,CAAC;EAChD,EAAE,IAAI,KAAK,GAAG,CAAC,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;AAC/C;EACA;EACA;EACA;EACA,EAAE,IAAI,gBAAgB,GAAG,wBAAwB,CAAC,WAAW,CAAC,CAAC;AAC/D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC;EACnB,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC;EACnB,EAAE,IAAI,KAAK,KAAK,QAAQ,EAAE;EAC1B;EACA;EACA,IAAI,IAAI,YAAY,CAAC,QAAQ,KAAK,MAAM,EAAE;EAC1C,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;EACxD,KAAK,MAAM;EACX,MAAM,GAAG,GAAG,CAAC,gBAAgB,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;EACtD,KAAK;EACL,GAAG,MAAM;EACT,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;EACtB,GAAG;EACH,EAAE,IAAI,KAAK,KAAK,OAAO,EAAE;EACzB,IAAI,IAAI,YAAY,CAAC,QAAQ,KAAK,MAAM,EAAE;EAC1C,MAAM,IAAI,GAAG,CAAC,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC;EACvD,KAAK,MAAM;EACX,MAAM,IAAI,GAAG,CAAC,gBAAgB,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;EACrD,KAAK;EACL,GAAG,MAAM;EACT,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;EACxB,GAAG;EACH,EAAE,IAAI,eAAe,IAAI,gBAAgB,EAAE;EAC3C,IAAI,MAAM,CAAC,gBAAgB,CAAC,GAAG,cAAc,GAAG,IAAI,GAAG,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC;EAC/E,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;EACtB,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;EACtB,IAAI,MAAM,CAAC,UAAU,GAAG,WAAW,CAAC;EACpC,GAAG,MAAM;EACT;EACA,IAAI,IAAI,SAAS,GAAG,KAAK,KAAK,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAChD,IAAI,IAAI,UAAU,GAAG,KAAK,KAAK,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAChD,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC;EACpC,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,UAAU,CAAC;EACtC,IAAI,MAAM,CAAC,UAAU,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;EAC7C,GAAG;AACH;EACA;EACA,EAAE,IAAI,UAAU,GAAG;EACnB,IAAI,aAAa,EAAE,IAAI,CAAC,SAAS;EACjC,GAAG,CAAC;AACJ;EACA;EACA,EAAE,IAAI,CAAC,UAAU,GAAGA,UAAQ,CAAC,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;EAC9D,EAAE,IAAI,CAAC,MAAM,GAAGA,UAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;EAClD,EAAE,IAAI,CAAC,WAAW,GAAGA,UAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACxE;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,kBAAkB,CAAC,SAAS,EAAE,cAAc,EAAE,aAAa,EAAE;EACtE,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,UAAU,IAAI,EAAE;EACnD,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;EACzB,IAAI,OAAO,IAAI,KAAK,cAAc,CAAC;EACnC,GAAG,CAAC,CAAC;AACL;EACA,EAAE,IAAI,UAAU,GAAG,CAAC,CAAC,UAAU,IAAI,SAAS,CAAC,IAAI,CAAC,UAAU,QAAQ,EAAE;EACtE,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,aAAa,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;EACpG,GAAG,CAAC,CAAC;AACL;EACA,EAAE,IAAI,CAAC,UAAU,EAAE;EACnB,IAAI,IAAI,WAAW,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,CAAC;EACjD,IAAI,IAAI,SAAS,GAAG,GAAG,GAAG,aAAa,GAAG,GAAG,CAAC;EAC9C,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,2BAA2B,GAAG,WAAW,GAAG,2DAA2D,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC;EAC1J,GAAG;EACH,EAAE,OAAO,UAAU,CAAC;EACpB,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE;EAC9B,EAAE,IAAI,mBAAmB,CAAC;AAC1B;EACA;EACA,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE,cAAc,CAAC,EAAE;EAC7E,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;EACA,EAAE,IAAI,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC;AACrC;EACA;EACA,EAAE,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;EACxC,IAAI,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;AACpE;EACA;EACA,IAAI,IAAI,CAAC,YAAY,EAAE;EACvB,MAAM,OAAO,IAAI,CAAC;EAClB,KAAK;EACL,GAAG,MAAM;EACT;EACA;EACA,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;EACtD,MAAM,OAAO,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;EACpF,MAAM,OAAO,IAAI,CAAC;EAClB,KAAK;EACL,GAAG;AACH;EACA,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO;EAClC,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM;EACnC,MAAM,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;AAC1C;EACA,EAAE,IAAI,UAAU,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/D;EACA,EAAE,IAAI,GAAG,GAAG,UAAU,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC5C,EAAE,IAAI,eAAe,GAAG,UAAU,GAAG,KAAK,GAAG,MAAM,CAAC;EACpD,EAAE,IAAI,IAAI,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;EAC3C,EAAE,IAAI,OAAO,GAAG,UAAU,GAAG,MAAM,GAAG,KAAK,CAAC;EAC5C,EAAE,IAAI,MAAM,GAAG,UAAU,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC/C,EAAE,IAAI,gBAAgB,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1D;EACA;EACA;EACA;EACA;AACA;EACA;EACA,EAAE,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE;EAC3D,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,CAAC;EACvF,GAAG;EACH;EACA,EAAE,IAAI,SAAS,CAAC,IAAI,CAAC,GAAG,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE;EAC3D,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,GAAG,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;EACrF,GAAG;EACH,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3D;EACA;EACA,EAAE,IAAI,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,CAAC;AAC3E;EACA;EACA;EACA,EAAE,IAAI,GAAG,GAAG,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;EAC3D,EAAE,IAAI,gBAAgB,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,GAAG,eAAe,CAAC,CAAC,CAAC;EACrE,EAAE,IAAI,gBAAgB,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,GAAG,eAAe,GAAG,OAAO,CAAC,CAAC,CAAC;EAC/E,EAAE,IAAI,SAAS,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,gBAAgB,GAAG,gBAAgB,CAAC;AAC3F;EACA;EACA,EAAE,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,gBAAgB,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/E;EACA,EAAE,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;EACnC,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,mBAAmB,GAAG,EAAE,EAAE,cAAc,CAAC,mBAAmB,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,mBAAmB,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,mBAAmB,CAAC,CAAC;AAC3L;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,oBAAoB,CAAC,SAAS,EAAE;EACzC,EAAE,IAAI,SAAS,KAAK,KAAK,EAAE;EAC3B,IAAI,OAAO,OAAO,CAAC;EACnB,GAAG,MAAM,IAAI,SAAS,KAAK,OAAO,EAAE;EACpC,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;EACH,EAAE,OAAO,SAAS,CAAC;EACnB,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,UAAU,GAAG,CAAC,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;AAClM;EACA;EACA,IAAI,eAAe,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1C;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,SAAS,CAAC,SAAS,EAAE;EAC9B,EAAE,IAAI,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAC1F;EACA,EAAE,IAAI,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;EACjD,EAAE,IAAI,GAAG,GAAG,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;EACrF,EAAE,OAAO,OAAO,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC;EACvC,CAAC;AACD;EACA,IAAI,SAAS,GAAG;EAChB,EAAE,IAAI,EAAE,MAAM;EACd,EAAE,SAAS,EAAE,WAAW;EACxB,EAAE,gBAAgB,EAAE,kBAAkB;EACtC,CAAC,CAAC;AACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE;EAC7B;EACA,EAAE,IAAI,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE;EAC3D,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;EACA,EAAE,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,iBAAiB,EAAE;EACjE;EACA,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;EACA,EAAE,IAAI,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAChJ;EACA,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,EAAE,IAAI,iBAAiB,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;EAC1D,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AACrD;EACA,EAAE,IAAI,SAAS,GAAG,EAAE,CAAC;AACrB;EACA,EAAE,QAAQ,OAAO,CAAC,QAAQ;EAC1B,IAAI,KAAK,SAAS,CAAC,IAAI;EACvB,MAAM,SAAS,GAAG,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;EACjD,MAAM,MAAM;EACZ,IAAI,KAAK,SAAS,CAAC,SAAS;EAC5B,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC;EACvC,MAAM,MAAM;EACZ,IAAI,KAAK,SAAS,CAAC,gBAAgB;EACnC,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;EAC7C,MAAM,MAAM;EACZ,IAAI;EACJ,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC;EACnC,GAAG;AACH;EACA,EAAE,SAAS,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,KAAK,EAAE;EAC3C,IAAI,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,KAAK,GAAG,CAAC,EAAE;EAC9D,MAAM,OAAO,IAAI,CAAC;EAClB,KAAK;AACL;EACA,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C,IAAI,iBAAiB,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;AACxD;EACA,IAAI,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;EAC5C,IAAI,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;AAC5C;EACA;EACA,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;EAC3B,IAAI,IAAI,WAAW,GAAG,SAAS,KAAK,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,SAAS,KAAK,OAAO,IAAI,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,SAAS,KAAK,KAAK,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,SAAS,KAAK,QAAQ,IAAI,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AACjV;EACA,IAAI,IAAI,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;EAC3E,IAAI,IAAI,cAAc,GAAG,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;EAC9E,IAAI,IAAI,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;EACxE,IAAI,IAAI,eAAe,GAAG,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AACjF;EACA,IAAI,IAAI,mBAAmB,GAAG,SAAS,KAAK,MAAM,IAAI,aAAa,IAAI,SAAS,KAAK,OAAO,IAAI,cAAc,IAAI,SAAS,KAAK,KAAK,IAAI,YAAY,IAAI,SAAS,KAAK,QAAQ,IAAI,eAAe,CAAC;AACnM;EACA;EACA,IAAI,IAAI,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AACjE;EACA;EACA,IAAI,IAAI,qBAAqB,GAAG,CAAC,CAAC,OAAO,CAAC,cAAc,KAAK,UAAU,IAAI,SAAS,KAAK,OAAO,IAAI,aAAa,IAAI,UAAU,IAAI,SAAS,KAAK,KAAK,IAAI,cAAc,IAAI,CAAC,UAAU,IAAI,SAAS,KAAK,OAAO,IAAI,YAAY,IAAI,CAAC,UAAU,IAAI,SAAS,KAAK,KAAK,IAAI,eAAe,CAAC,CAAC;AAC3R;EACA;EACA,IAAI,IAAI,yBAAyB,GAAG,CAAC,CAAC,OAAO,CAAC,uBAAuB,KAAK,UAAU,IAAI,SAAS,KAAK,OAAO,IAAI,cAAc,IAAI,UAAU,IAAI,SAAS,KAAK,KAAK,IAAI,aAAa,IAAI,CAAC,UAAU,IAAI,SAAS,KAAK,OAAO,IAAI,eAAe,IAAI,CAAC,UAAU,IAAI,SAAS,KAAK,KAAK,IAAI,YAAY,CAAC,CAAC;AACxS;EACA,IAAI,IAAI,gBAAgB,GAAG,qBAAqB,IAAI,yBAAyB,CAAC;AAC9E;EACA,IAAI,IAAI,WAAW,IAAI,mBAAmB,IAAI,gBAAgB,EAAE;EAChE;EACA,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AAC1B;EACA,MAAM,IAAI,WAAW,IAAI,mBAAmB,EAAE;EAC9C,QAAQ,SAAS,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;EACzC,OAAO;AACP;EACA,MAAM,IAAI,gBAAgB,EAAE;EAC5B,QAAQ,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;EACpD,OAAO;AACP;EACA,MAAM,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,EAAE,CAAC,CAAC;AACtE;EACA;EACA;EACA,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,GAAGA,UAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AAC9I;EACA,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;EACjE,KAAK;EACL,GAAG,CAAC,CAAC;EACL,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,YAAY,CAAC,IAAI,EAAE;EAC5B,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO;EAClC,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM;EACnC,MAAM,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;AAC1C;EACA,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;EACzB,EAAE,IAAI,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;EAC/D,EAAE,IAAI,IAAI,GAAG,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC;EAC7C,EAAE,IAAI,MAAM,GAAG,UAAU,GAAG,MAAM,GAAG,KAAK,CAAC;EAC3C,EAAE,IAAI,WAAW,GAAG,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC;AACpD;EACA,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE;EAC/C,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;EACjF,GAAG;EACH,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;EAC/C,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;EACzD,GAAG;AACH;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,OAAO,CAAC,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,EAAE;EACpE;EACA,EAAE,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;EACrD,EAAE,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EACxB,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACtB;EACA;EACA,EAAE,IAAI,CAAC,KAAK,EAAE;EACd,IAAI,OAAO,GAAG,CAAC;EACf,GAAG;AACH;EACA,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;EAC/B,IAAI,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC;EACzB,IAAI,QAAQ,IAAI;EAChB,MAAM,KAAK,IAAI;EACf,QAAQ,OAAO,GAAG,aAAa,CAAC;EAChC,QAAQ,MAAM;EACd,MAAM,KAAK,GAAG,CAAC;EACf,MAAM,KAAK,IAAI,CAAC;EAChB,MAAM;EACN,QAAQ,OAAO,GAAG,gBAAgB,CAAC;EACnC,KAAK;AACL;EACA,IAAI,IAAI,IAAI,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;EACtC,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC;EAC3C,GAAG,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;EAC7C;EACA,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC;EACtB,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;EACvB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,MAAM,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;EACtF,KAAK,MAAM;EACX,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,EAAE,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC;EACpF,KAAK;EACL,IAAI,OAAO,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC;EAC9B,GAAG,MAAM;EACT;EACA;EACA,IAAI,OAAO,KAAK,CAAC;EACjB,GAAG;EACH,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,WAAW,CAAC,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,aAAa,EAAE;EAC7E,EAAE,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvB;EACA;EACA;EACA;EACA,EAAE,IAAI,SAAS,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;AAClE;EACA;EACA;EACA,EAAE,IAAI,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;EAC9D,IAAI,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;EACvB,GAAG,CAAC,CAAC;AACL;EACA;EACA;EACA,EAAE,IAAI,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,IAAI,EAAE;EAClE,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;EACtC,GAAG,CAAC,CAAC,CAAC;AACN;EACA,EAAE,IAAI,SAAS,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;EACpE,IAAI,OAAO,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;EACjG,GAAG;AACH;EACA;EACA;EACA,EAAE,IAAI,UAAU,GAAG,aAAa,CAAC;EACjC,EAAE,IAAI,GAAG,GAAG,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC3M;EACA;EACA,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE;EACrC;EACA,IAAI,IAAI,WAAW,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,SAAS,GAAG,SAAS,IAAI,QAAQ,GAAG,OAAO,CAAC;EAClF,IAAI,IAAI,iBAAiB,GAAG,KAAK,CAAC;EAClC,IAAI,OAAO,EAAE;EACb;EACA;EACA,KAAK,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;EAC5B,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;EAClE,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EAC5B,QAAQ,iBAAiB,GAAG,IAAI,CAAC;EACjC,QAAQ,OAAO,CAAC,CAAC;EACjB,OAAO,MAAM,IAAI,iBAAiB,EAAE;EACpC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;EAC7B,QAAQ,iBAAiB,GAAG,KAAK,CAAC;EAClC,QAAQ,OAAO,CAAC,CAAC;EACjB,OAAO,MAAM;EACb,QAAQ,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3B,OAAO;EACP,KAAK,EAAE,EAAE,CAAC;EACV;EACA,KAAK,GAAG,CAAC,UAAU,GAAG,EAAE;EACxB,MAAM,OAAO,OAAO,CAAC,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;EACxE,KAAK,CAAC,CAAC;EACP,GAAG,CAAC,CAAC;AACL;EACA;EACA,EAAE,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE;EACnC,IAAI,EAAE,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,MAAM,EAAE;EACvC,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;EAC3B,QAAQ,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EACnE,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG,CAAC,CAAC;EACL,EAAE,OAAO,OAAO,CAAC;EACjB,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE;EAC5B,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;EAC3B,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS;EAChC,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO;EAClC,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM;EACnC,MAAM,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;AAC1C;EACA,EAAE,IAAI,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C;EACA,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC;EACvB,EAAE,IAAI,SAAS,CAAC,CAAC,MAAM,CAAC,EAAE;EAC1B,IAAI,OAAO,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;EAC3B,GAAG,MAAM;EACT,IAAI,OAAO,GAAG,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;EACpE,GAAG;AACH;EACA,EAAE,IAAI,aAAa,KAAK,MAAM,EAAE;EAChC,IAAI,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;EAC7B,IAAI,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;EAC9B,GAAG,MAAM,IAAI,aAAa,KAAK,OAAO,EAAE;EACxC,IAAI,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;EAC7B,IAAI,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;EAC9B,GAAG,MAAM,IAAI,aAAa,KAAK,KAAK,EAAE;EACtC,IAAI,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;EAC9B,IAAI,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;EAC7B,GAAG,MAAM,IAAI,aAAa,KAAK,QAAQ,EAAE;EACzC,IAAI,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;EAC9B,IAAI,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;EAC7B,GAAG;AACH;EACA,EAAE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;EACvB,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE;EACxC,EAAE,IAAI,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC7F;EACA;EACA;EACA;EACA,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAK,iBAAiB,EAAE;EACrD,IAAI,iBAAiB,GAAG,eAAe,CAAC,iBAAiB,CAAC,CAAC;EAC3D,GAAG;AACH;EACA;EACA;EACA;EACA,EAAE,IAAI,aAAa,GAAG,wBAAwB,CAAC,WAAW,CAAC,CAAC;EAC5D,EAAE,IAAI,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC;EAChD,EAAE,IAAI,GAAG,GAAG,YAAY,CAAC,GAAG;EAC5B,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI;EAC9B,MAAM,SAAS,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC;AAC9C;EACA,EAAE,YAAY,CAAC,GAAG,GAAG,EAAE,CAAC;EACxB,EAAE,YAAY,CAAC,IAAI,GAAG,EAAE,CAAC;EACzB,EAAE,YAAY,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;AACnC;EACA,EAAE,IAAI,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,EAAE,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AACxI;EACA;EACA;EACA,EAAE,YAAY,CAAC,GAAG,GAAG,GAAG,CAAC;EACzB,EAAE,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;EAC3B,EAAE,YAAY,CAAC,aAAa,CAAC,GAAG,SAAS,CAAC;AAC1C;EACA,EAAE,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;AAClC;EACA,EAAE,IAAI,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAC;EAC/B,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;AACnC;EACA,EAAE,IAAI,KAAK,GAAG;EACd,IAAI,OAAO,EAAE,SAAS,OAAO,CAAC,SAAS,EAAE;EACzC,MAAM,IAAI,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;EACpC,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;EACrF,QAAQ,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;EACnE,OAAO;EACP,MAAM,OAAO,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;EAClD,KAAK;EACL,IAAI,SAAS,EAAE,SAAS,SAAS,CAAC,SAAS,EAAE;EAC7C,MAAM,IAAI,QAAQ,GAAG,SAAS,KAAK,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC;EAC5D,MAAM,IAAI,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;EACnC,MAAM,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;EACrF,QAAQ,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,IAAI,SAAS,KAAK,OAAO,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;EAC3H,OAAO;EACP,MAAM,OAAO,cAAc,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;EACjD,KAAK;EACL,GAAG,CAAC;AACJ;EACA,EAAE,KAAK,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;EACrC,IAAI,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS,GAAG,WAAW,CAAC;EACnF,IAAI,MAAM,GAAGA,UAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;EAC1D,GAAG,CAAC,CAAC;AACL;EACA,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AAC/B;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,KAAK,CAAC,IAAI,EAAE;EACrB,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;EACjC,EAAE,IAAI,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,EAAE,IAAI,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C;EACA;EACA,EAAE,IAAI,cAAc,EAAE;EACtB,IAAI,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO;EACpC,QAAQ,SAAS,GAAG,aAAa,CAAC,SAAS;EAC3C,QAAQ,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;AACtC;EACA,IAAI,IAAI,UAAU,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;EACrE,IAAI,IAAI,IAAI,GAAG,UAAU,GAAG,MAAM,GAAG,KAAK,CAAC;EAC3C,IAAI,IAAI,WAAW,GAAG,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC;AACtD;EACA,IAAI,IAAI,YAAY,GAAG;EACvB,MAAM,KAAK,EAAE,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;EACtD,MAAM,GAAG,EAAE,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;EACnG,KAAK,CAAC;AACN;EACA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAGA,UAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC;EAC7E,GAAG;AACH;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,IAAI,CAAC,IAAI,EAAE;EACpB,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,EAAE,iBAAiB,CAAC,EAAE;EAC/E,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;EACA,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;EACvC,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,UAAU,QAAQ,EAAE;EAChE,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,iBAAiB,CAAC;EAC/C,GAAG,CAAC,CAAC,UAAU,CAAC;AAChB;EACA,EAAE,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE;EAC5H;EACA,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;EAC5B,MAAM,OAAO,IAAI,CAAC;EAClB,KAAK;AACL;EACA,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;EACrB,IAAI,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,GAAG,EAAE,CAAC;EAChD,GAAG,MAAM;EACT;EACA,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE;EAC7B,MAAM,OAAO,IAAI,CAAC;EAClB,KAAK;AACL;EACA,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;EACtB,IAAI,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,GAAG,KAAK,CAAC;EACnD,GAAG;AACH;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS,KAAK,CAAC,IAAI,EAAE;EACrB,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;EACjC,EAAE,IAAI,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO;EAClC,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM;EACnC,MAAM,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;AAC1C;EACA,EAAE,IAAI,OAAO,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;AAChE;EACA,EAAE,IAAI,cAAc,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;AACrE;EACA,EAAE,MAAM,CAAC,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,aAAa,CAAC,IAAI,cAAc,GAAG,MAAM,CAAC,OAAO,GAAG,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5H;EACA,EAAE,IAAI,CAAC,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;EACnD,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;AAC9C;EACA,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;AACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,SAAS,GAAG;EAChB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,KAAK,EAAE;EACT;EACA,IAAI,KAAK,EAAE,GAAG;EACd;EACA,IAAI,OAAO,EAAE,IAAI;EACjB;EACA,IAAI,EAAE,EAAE,KAAK;EACb,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,MAAM,EAAE;EACV;EACA,IAAI,KAAK,EAAE,GAAG;EACd;EACA,IAAI,OAAO,EAAE,IAAI;EACjB;EACA,IAAI,EAAE,EAAE,MAAM;EACd;EACA;EACA;EACA,IAAI,MAAM,EAAE,CAAC;EACb,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,eAAe,EAAE;EACnB;EACA,IAAI,KAAK,EAAE,GAAG;EACd;EACA,IAAI,OAAO,EAAE,IAAI;EACjB;EACA,IAAI,EAAE,EAAE,eAAe;EACvB;EACA;EACA;EACA;EACA;EACA,IAAI,QAAQ,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC;EAChD;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,OAAO,EAAE,CAAC;EACd;EACA;EACA;EACA;EACA;EACA,IAAI,iBAAiB,EAAE,cAAc;EACrC,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,YAAY,EAAE;EAChB;EACA,IAAI,KAAK,EAAE,GAAG;EACd;EACA,IAAI,OAAO,EAAE,IAAI;EACjB;EACA,IAAI,EAAE,EAAE,YAAY;EACpB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,KAAK,EAAE;EACT;EACA,IAAI,KAAK,EAAE,GAAG;EACd;EACA,IAAI,OAAO,EAAE,IAAI;EACjB;EACA,IAAI,EAAE,EAAE,KAAK;EACb;EACA,IAAI,OAAO,EAAE,WAAW;EACxB,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,IAAI,EAAE;EACR;EACA,IAAI,KAAK,EAAE,GAAG;EACd;EACA,IAAI,OAAO,EAAE,IAAI;EACjB;EACA,IAAI,EAAE,EAAE,IAAI;EACZ;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,QAAQ,EAAE,MAAM;EACpB;EACA;EACA;EACA;EACA,IAAI,OAAO,EAAE,CAAC;EACd;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,iBAAiB,EAAE,UAAU;EACjC;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,cAAc,EAAE,KAAK;EACzB;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,uBAAuB,EAAE,KAAK;EAClC,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,KAAK,EAAE;EACT;EACA,IAAI,KAAK,EAAE,GAAG;EACd;EACA,IAAI,OAAO,EAAE,KAAK;EAClB;EACA,IAAI,EAAE,EAAE,KAAK;EACb,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,IAAI,EAAE;EACR;EACA,IAAI,KAAK,EAAE,GAAG;EACd;EACA,IAAI,OAAO,EAAE,IAAI;EACjB;EACA,IAAI,EAAE,EAAE,IAAI;EACZ,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,YAAY,EAAE;EAChB;EACA,IAAI,KAAK,EAAE,GAAG;EACd;EACA,IAAI,OAAO,EAAE,IAAI;EACjB;EACA,IAAI,EAAE,EAAE,YAAY;EACpB;EACA;EACA;EACA;EACA;EACA,IAAI,eAAe,EAAE,IAAI;EACzB;EACA;EACA;EACA;EACA;EACA,IAAI,CAAC,EAAE,QAAQ;EACf;EACA;EACA;EACA;EACA;EACA,IAAI,CAAC,EAAE,OAAO;EACd,GAAG;AACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,UAAU,EAAE;EACd;EACA,IAAI,KAAK,EAAE,GAAG;EACd;EACA,IAAI,OAAO,EAAE,IAAI;EACjB;EACA,IAAI,EAAE,EAAE,UAAU;EAClB;EACA,IAAI,MAAM,EAAE,gBAAgB;EAC5B;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,eAAe,EAAE,SAAS;EAC9B,GAAG;EACH,CAAC,CAAC;AACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,QAAQ,GAAG;EACf;EACA;EACA;EACA;EACA,EAAE,SAAS,EAAE,QAAQ;AACrB;EACA;EACA;EACA;EACA;EACA,EAAE,aAAa,EAAE,KAAK;AACtB;EACA;EACA;EACA;EACA;EACA,EAAE,aAAa,EAAE,IAAI;AACrB;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,eAAe,EAAE,KAAK;AACxB;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,QAAQ,EAAE,SAAS,QAAQ,GAAG,EAAE;AAClC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,QAAQ,EAAE,SAAS,QAAQ,GAAG,EAAE;AAClC;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,EAAE,SAAS;EACtB,CAAC,CAAC;AACF;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA;EACA;AACA;EACA;EACA;EACA,IAAI,MAAM,GAAG,YAAY;EACzB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,EAAE,SAAS,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;EACrC,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC;AACrB;EACA,IAAI,IAAI,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;EACzF,IAAI,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACjC;EACA,IAAI,IAAI,CAAC,cAAc,GAAG,YAAY;EACtC,MAAM,OAAO,qBAAqB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;EACjD,KAAK,CAAC;AACN;EACA;EACA,IAAI,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACnD;EACA;EACA,IAAI,IAAI,CAAC,OAAO,GAAGA,UAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC1D;EACA;EACA,IAAI,IAAI,CAAC,KAAK,GAAG;EACjB,MAAM,WAAW,EAAE,KAAK;EACxB,MAAM,SAAS,EAAE,KAAK;EACtB,MAAM,aAAa,EAAE,EAAE;EACvB,KAAK,CAAC;AACN;EACA;EACA,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;EAC9E,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;AAC/D;EACA;EACA,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC;EAChC,IAAI,MAAM,CAAC,IAAI,CAACA,UAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;EACpG,MAAM,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAGA,UAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;EAC5I,KAAK,CAAC,CAAC;AACP;EACA;EACA,IAAI,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;EAC7E,MAAM,OAAOA,UAAQ,CAAC;EACtB,QAAQ,IAAI,EAAE,IAAI;EAClB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;EACxC,KAAK,CAAC;EACN;EACA,KAAK,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;EAC1B,MAAM,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;EAC/B,KAAK,CAAC,CAAC;AACP;EACA;EACA;EACA;EACA;EACA,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,eAAe,EAAE;EACtD,MAAM,IAAI,eAAe,CAAC,OAAO,IAAI,UAAU,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE;EACzE,QAAQ,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,eAAe,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;EAC3G,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA;EACA,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;AAClB;EACA,IAAI,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;EACnD,IAAI,IAAI,aAAa,EAAE;EACvB;EACA,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;EAClC,KAAK;AACL;EACA,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;EAC7C,GAAG;AACH;EACA;EACA;AACA;AACA;EACA,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC;EACvB,IAAI,GAAG,EAAE,QAAQ;EACjB,IAAI,KAAK,EAAE,SAAS,SAAS,GAAG;EAChC,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC/B,KAAK;EACL,GAAG,EAAE;EACL,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,KAAK,EAAE,SAAS,UAAU,GAAG;EACjC,MAAM,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAChC,KAAK;EACL,GAAG,EAAE;EACL,IAAI,GAAG,EAAE,sBAAsB;EAC/B,IAAI,KAAK,EAAE,SAAS,uBAAuB,GAAG;EAC9C,MAAM,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC7C,KAAK;EACL,GAAG,EAAE;EACL,IAAI,GAAG,EAAE,uBAAuB;EAChC,IAAI,KAAK,EAAE,SAAS,wBAAwB,GAAG;EAC/C,MAAM,OAAO,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC9C,KAAK;AACL;EACA;EACA;EACA;EACA;EACA;AACA;AACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA,GAAG,CAAC,CAAC,CAAC;EACN,EAAE,OAAO,MAAM,CAAC;EAChB,CAAC,EAAE,CAAC;AACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;AACA;EACA,MAAM,CAAC,KAAK,GAAG,CAAC,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,GAAG,MAAM,EAAE,WAAW,CAAC;EAC7E,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;EAC/B,MAAM,CAAC,QAAQ,GAAG,QAAQ;;EC9hF1B;EACA;EACA;EACA;EACA;;EAEA,IAAMnQ,MAAI,GAAG,UAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,aAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAMgQ,UAAU,GAAG,QAAnB;EACA,IAAMC,SAAS,GAAG,OAAlB;EACA,IAAMC,OAAO,GAAG,KAAhB;EACA,IAAMC,YAAY,GAAG,SAArB;EACA,IAAMC,cAAc,GAAG,WAAvB;EACA,IAAMC,kBAAkB,GAAG,CAA3B;;EAEA,IAAMC,cAAc,GAAG,IAAIjY,MAAJ,CAAc8X,YAAd,SAA8BC,cAA9B,SAAgDJ,UAAhD,CAAvB;EAEA,IAAM5C,YAAU,YAAUrN,WAA1B;EACA,IAAMsN,cAAY,cAAYtN,WAA9B;EACA,IAAMmN,YAAU,YAAUnN,WAA1B;EACA,IAAMoN,aAAW,aAAWpN,WAA5B;EACA,IAAMwQ,WAAW,aAAWxQ,WAA5B;EACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EACA,IAAMwQ,sBAAsB,eAAazQ,WAAb,GAAyBC,cAArD;EACA,IAAMyQ,oBAAoB,aAAW1Q,WAAX,GAAuBC,cAAjD;EAEA,IAAM0Q,mBAAmB,GAAG,UAA5B;EACA,IAAMpD,iBAAe,GAAG,MAAxB;EACA,IAAMqD,iBAAiB,GAAG,QAA1B;EACA,IAAMC,oBAAoB,GAAG,WAA7B;EACA,IAAMC,mBAAmB,GAAG,UAA5B;EACA,IAAMC,oBAAoB,GAAG,qBAA7B;EACA,IAAMC,iBAAiB,GAAG,QAA1B;EACA,IAAMC,0BAA0B,GAAG,iBAAnC;EAEA,IAAMhP,sBAAoB,GAAG,0BAA7B;EACA,IAAMiP,mBAAmB,GAAG,gBAA5B;EACA,IAAMC,aAAa,GAAG,gBAAtB;EACA,IAAMC,mBAAmB,GAAG,aAA5B;EACA,IAAMC,sBAAsB,GAAG,6DAA/B;EAEA,IAAMC,aAAa,GAAG,WAAtB;EACA,IAAMC,gBAAgB,GAAG,SAAzB;EACA,IAAMC,gBAAgB,GAAG,cAAzB;EACA,IAAMC,mBAAmB,GAAG,YAA5B;EACA,IAAMC,eAAe,GAAG,aAAxB;EACA,IAAMC,cAAc,GAAG,YAAvB;EAEA,IAAMrM,SAAO,GAAG;EACdnC,EAAAA,MAAM,EAAE,CADM;EAEdyO,EAAAA,IAAI,EAAE,IAFQ;EAGdC,EAAAA,QAAQ,EAAE,cAHI;EAIdC,EAAAA,SAAS,EAAE,QAJG;EAKd/Y,EAAAA,OAAO,EAAE,SALK;EAMdgZ,EAAAA,YAAY,EAAE;EANA,CAAhB;EASA,IAAMlM,aAAW,GAAG;EAClB1C,EAAAA,MAAM,EAAE,0BADU;EAElByO,EAAAA,IAAI,EAAE,SAFY;EAGlBC,EAAAA,QAAQ,EAAE,kBAHQ;EAIlBC,EAAAA,SAAS,EAAE,kBAJO;EAKlB/Y,EAAAA,OAAO,EAAE,QALS;EAMlBgZ,EAAAA,YAAY,EAAE;EANI,CAApB;EASA;EACA;EACA;EACA;EACA;;MAEMC;EACJ,oBAAYpc,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,SAAK6I,QAAL,GAAgB9K,OAAhB;EACA,SAAKqc,OAAL,GAAe,IAAf;EACA,SAAKxJ,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;EACA,SAAKqa,KAAL,GAAa,KAAKC,eAAL,EAAb;EACA,SAAKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EAEA,SAAKpJ,kBAAL;;EACAtO,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;EACD;;;;;EAgBD;WAEAoC,SAAA,kBAAS;EACP,QAAI,KAAKzB,QAAL,CAAc4R,QAAd,IAA0B,KAAK5R,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCsP,mBAAjC,CAA9B,EAAqF;EACnF;EACD;;EAED,QAAM4B,QAAQ,GAAG,KAAK7R,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,iBAAjC,CAAjB;;EAEAyE,IAAAA,QAAQ,CAACQ,UAAT;;EAEA,QAAID,QAAJ,EAAc;EACZ;EACD;;EAED,SAAK7D,IAAL;EACD;;WAEDA,OAAA,gBAAO;EACL,QAAI,KAAKhO,QAAL,CAAc4R,QAAd,IAA0B,KAAK5R,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCsP,mBAAjC,CAA1B,IAAmF,KAAKuB,KAAL,CAAW/Q,SAAX,CAAqBE,QAArB,CAA8BkM,iBAA9B,CAAvF,EAAuI;EACrI;EACD;;EAED,QAAML,MAAM,GAAG8E,QAAQ,CAACS,oBAAT,CAA8B,KAAK/R,QAAnC,CAAf;EACA,QAAM+K,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAK/K;EADA,KAAtB;EAIA,QAAMgS,SAAS,GAAGxW,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyM,YAApC,EAAgD1B,aAAhD,CAAlB;;EAEA,QAAIiH,SAAS,CAACxT,gBAAd,EAAgC;EAC9B;EACD,KAdI;;;EAiBL,QAAI,CAAC,KAAKkT,SAAV,EAAqB;EACnB,UAAI,OAAOO,MAAP,KAAkB,WAAtB,EAAmC;EACjC,cAAM,IAAI9F,SAAJ,CAAc,kEAAd,CAAN;EACD;;EAED,UAAI+F,gBAAgB,GAAG,KAAKlS,QAA5B;;EAEA,UAAI,KAAK+H,OAAL,CAAaqJ,SAAb,KAA2B,QAA/B,EAAyC;EACvCc,QAAAA,gBAAgB,GAAG1F,MAAnB;EACD,OAFD,MAEO,IAAIlW,SAAS,CAAC,KAAKyR,OAAL,CAAaqJ,SAAd,CAAb,EAAuC;EAC5Cc,QAAAA,gBAAgB,GAAG,KAAKnK,OAAL,CAAaqJ,SAAhC,CAD4C;;EAI5C,YAAI,OAAO,KAAKrJ,OAAL,CAAaqJ,SAAb,CAAuBpC,MAA9B,KAAyC,WAA7C,EAA0D;EACxDkD,UAAAA,gBAAgB,GAAG,KAAKnK,OAAL,CAAaqJ,SAAb,CAAuB,CAAvB,CAAnB;EACD;EACF,OAhBkB;EAmBnB;EACA;;;EACA,UAAI,KAAKrJ,OAAL,CAAaoJ,QAAb,KAA0B,cAA9B,EAA8C;EAC5C3E,QAAAA,MAAM,CAAC/L,SAAP,CAAiB2J,GAAjB,CAAqBmG,0BAArB;EACD;;EAED,WAAKgB,OAAL,GAAe,IAAIU,MAAJ,CAAWC,gBAAX,EAA6B,KAAKV,KAAlC,EAAyC,KAAKW,gBAAL,EAAzC,CAAf;EACD,KA3CI;EA8CL;EACA;EACA;;;EACA,QAAI,kBAAkBpd,QAAQ,CAACyD,eAA3B,IACF,CAACgU,MAAM,CAAChM,OAAP,CAAekQ,mBAAf,CADH,EACwC;EAAA;;EACtC,kBAAGnN,MAAH,aAAaxO,QAAQ,CAACmE,IAAT,CAAcyK,QAA3B,EACGpM,OADH,CACW,UAAAiW,IAAI;EAAA,eAAIhS,YAAY,CAACkC,EAAb,CAAgB8P,IAAhB,EAAsB,WAAtB,EAAmC,IAAnC,EAAyC3U,IAAI,EAA7C,CAAJ;EAAA,OADf;EAED;;EAED,SAAKmH,QAAL,CAAcoS,KAAd;;EACA,SAAKpS,QAAL,CAAc0B,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;EAEA,SAAK8P,KAAL,CAAW/Q,SAAX,CAAqBgB,MAArB,CAA4BoL,iBAA5B;;EACA,SAAK7M,QAAL,CAAcS,SAAd,CAAwBgB,MAAxB,CAA+BoL,iBAA/B;;EACArR,IAAAA,YAAY,CAAC0C,OAAb,CAAqBsO,MAArB,EAA6BE,aAA7B,EAA0C3B,aAA1C;EACD;;WAEDgD,OAAA,gBAAO;EACL,QAAI,KAAK/N,QAAL,CAAc4R,QAAd,IAA0B,KAAK5R,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCsP,mBAAjC,CAA1B,IAAmF,CAAC,KAAKuB,KAAL,CAAW/Q,SAAX,CAAqBE,QAArB,CAA8BkM,iBAA9B,CAAxF,EAAwI;EACtI;EACD;;EAED,QAAML,MAAM,GAAG8E,QAAQ,CAACS,oBAAT,CAA8B,KAAK/R,QAAnC,CAAf;EACA,QAAM+K,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAK/K;EADA,KAAtB;EAIA,QAAMqS,SAAS,GAAG7W,YAAY,CAAC0C,OAAb,CAAqBsO,MAArB,EAA6BG,YAA7B,EAAyC5B,aAAzC,CAAlB;;EAEA,QAAIsH,SAAS,CAAC7T,gBAAd,EAAgC;EAC9B;EACD;;EAED,QAAI,KAAK+S,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAae,OAAb;EACD;;EAED,SAAKd,KAAL,CAAW/Q,SAAX,CAAqBgB,MAArB,CAA4BoL,iBAA5B;;EACA,SAAK7M,QAAL,CAAcS,SAAd,CAAwBgB,MAAxB,CAA+BoL,iBAA/B;;EACArR,IAAAA,YAAY,CAAC0C,OAAb,CAAqBsO,MAArB,EAA6BI,cAA7B,EAA2C7B,aAA3C;EACD;;WAEDxK,UAAA,mBAAU;EACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EACA7D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgCV,WAAhC;EACA,SAAKU,QAAL,GAAgB,IAAhB;EACA,SAAKwR,KAAL,GAAa,IAAb;;EACA,QAAI,KAAKD,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAae,OAAb;;EACA,WAAKf,OAAL,GAAe,IAAf;EACD;EACF;;WAEDgB,SAAA,kBAAS;EACP,SAAKb,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EACA,QAAI,KAAKJ,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAaiB,cAAb;EACD;EACF;;;WAIDjK,qBAAA,8BAAqB;EAAA;;EACnB/M,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B8P,WAA/B,EAA4C,UAAAzU,KAAK,EAAI;EACnDA,MAAAA,KAAK,CAAC6D,cAAN;EACA7D,MAAAA,KAAK,CAACoX,eAAN;;EACA,MAAA,KAAI,CAAChR,MAAL;EACD,KAJD;EAKD;;WAEDuG,aAAA,oBAAW7Q,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACD,KAAKub,WAAL,CAAiB9N,OADhB,EAED3C,WAAW,CAACI,iBAAZ,CAA8B,KAAKrC,QAAnC,CAFC,EAGD7I,MAHC,CAAN;EAMAF,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAe,KAAKub,WAAL,CAAiBvN,WAAhC,CAAf;EAEA,WAAOhO,MAAP;EACD;;WAEDsa,kBAAA,2BAAkB;EAChB,WAAOrO,cAAc,CAACkB,IAAf,CAAoB,KAAKtE,QAAzB,EAAmCyQ,aAAnC,EAAkD,CAAlD,CAAP;EACD;;WAEDkC,gBAAA,yBAAgB;EACd,QAAMC,cAAc,GAAG,KAAK5S,QAAL,CAAc9H,UAArC;EACA,QAAI2a,SAAS,GAAG/B,gBAAhB,CAFc;;EAKd,QAAI8B,cAAc,CAACnS,SAAf,CAAyBE,QAAzB,CAAkCuP,iBAAlC,CAAJ,EAA0D;EACxD2C,MAAAA,SAAS,GAAG,KAAKrB,KAAL,CAAW/Q,SAAX,CAAqBE,QAArB,CAA8B0P,oBAA9B,IACVQ,gBADU,GAEVD,aAFF;EAGD,KAJD,MAIO,IAAIgC,cAAc,CAACnS,SAAf,CAAyBE,QAAzB,CAAkCwP,oBAAlC,CAAJ,EAA6D;EAClE0C,MAAAA,SAAS,GAAG7B,eAAZ;EACD,KAFM,MAEA,IAAI4B,cAAc,CAACnS,SAAf,CAAyBE,QAAzB,CAAkCyP,mBAAlC,CAAJ,EAA4D;EACjEyC,MAAAA,SAAS,GAAG5B,cAAZ;EACD,KAFM,MAEA,IAAI,KAAKO,KAAL,CAAW/Q,SAAX,CAAqBE,QAArB,CAA8B0P,oBAA9B,CAAJ,EAAyD;EAC9DwC,MAAAA,SAAS,GAAG9B,mBAAZ;EACD;;EAED,WAAO8B,SAAP;EACD;;WAEDlB,gBAAA,yBAAgB;EACd,WAAOtU,OAAO,CAAC,KAAK2C,QAAL,CAAcQ,OAAd,OAA0B8P,iBAA1B,CAAD,CAAd;EACD;;WAEDwC,aAAA,sBAAa;EAAA;;EACX,QAAMrQ,MAAM,GAAG,EAAf;;EAEA,QAAI,OAAO,KAAKsF,OAAL,CAAatF,MAApB,KAA+B,UAAnC,EAA+C;EAC7CA,MAAAA,MAAM,CAACtH,EAAP,GAAY,UAAAvB,IAAI,EAAI;EAClBA,QAAAA,IAAI,CAACmZ,OAAL,gBACKnZ,IAAI,CAACmZ,OADV,EAEM,MAAI,CAAChL,OAAL,CAAatF,MAAb,CAAoB7I,IAAI,CAACmZ,OAAzB,EAAkC,MAAI,CAAC/S,QAAvC,KAAoD,EAF1D;EAKA,eAAOpG,IAAP;EACD,OAPD;EAQD,KATD,MASO;EACL6I,MAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKsF,OAAL,CAAatF,MAA7B;EACD;;EAED,WAAOA,MAAP;EACD;;WAED0P,mBAAA,4BAAmB;EACjB,QAAMd,YAAY,GAAG;EACnBwB,MAAAA,SAAS,EAAE,KAAKF,aAAL,EADQ;EAEnBK,MAAAA,SAAS,EAAE;EACTvQ,QAAAA,MAAM,EAAE,KAAKqQ,UAAL,EADC;EAET5B,QAAAA,IAAI,EAAE;EACJ+B,UAAAA,OAAO,EAAE,KAAKlL,OAAL,CAAamJ;EADlB,SAFG;EAKTgC,QAAAA,eAAe,EAAE;EACfC,UAAAA,iBAAiB,EAAE,KAAKpL,OAAL,CAAaoJ;EADjB;EALR;EAFQ,KAArB,CADiB;;EAejB,QAAI,KAAKpJ,OAAL,CAAa1P,OAAb,KAAyB,QAA7B,EAAuC;EACrCgZ,MAAAA,YAAY,CAAC2B,SAAb,CAAuBI,UAAvB,GAAoC;EAClCH,QAAAA,OAAO,EAAE;EADyB,OAApC;EAGD;;EAED,wBACK5B,YADL,EAEK,KAAKtJ,OAAL,CAAasJ,YAFlB;EAID;;;aAIMgC,oBAAP,2BAAyBne,OAAzB,EAAkCiC,MAAlC,EAA0C;EACxC,QAAIyC,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAX;;EACA,QAAM0I,OAAO,GAAG,OAAO5Q,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;EAEA,QAAI,CAACyC,IAAL,EAAW;EACTA,MAAAA,IAAI,GAAG,IAAI0X,QAAJ,CAAapc,OAAb,EAAsB6S,OAAtB,CAAP;EACD;;EAED,QAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,UAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,cAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,MAAAA,IAAI,CAACzC,MAAD,CAAJ;EACD;EACF;;aAEM2J,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3BuQ,MAAAA,QAAQ,CAAC+B,iBAAT,CAA2B,IAA3B,EAAiClc,MAAjC;EACD,KAFM,CAAP;EAGD;;aAEM2a,aAAP,oBAAkBzW,KAAlB,EAAyB;EACvB,QAAIA,KAAK,KAAKA,KAAK,CAACsG,MAAN,KAAiBiO,kBAAjB,IACXvU,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAAC1B,GAAN,KAAc8V,OADlC,CAAT,EACsD;EACpD;EACD;;EAED,QAAM6D,OAAO,GAAGlQ,cAAc,CAACE,IAAf,CAAoB/B,sBAApB,CAAhB;;EAEA,SAAK,IAAIvF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGgX,OAAO,CAACrX,MAA9B,EAAsCD,CAAC,GAAGM,GAA1C,EAA+CN,CAAC,EAAhD,EAAoD;EAClD,UAAMwQ,MAAM,GAAG8E,QAAQ,CAACS,oBAAT,CAA8BuB,OAAO,CAACtX,CAAD,CAArC,CAAf;EACA,UAAMuX,OAAO,GAAGtZ,IAAI,CAACG,OAAL,CAAakZ,OAAO,CAACtX,CAAD,CAApB,EAAyBqD,UAAzB,CAAhB;EACA,UAAM0L,aAAa,GAAG;EACpBA,QAAAA,aAAa,EAAEuI,OAAO,CAACtX,CAAD;EADF,OAAtB;;EAIA,UAAIX,KAAK,IAAIA,KAAK,CAACK,IAAN,KAAe,OAA5B,EAAqC;EACnCqP,QAAAA,aAAa,CAACyI,UAAd,GAA2BnY,KAA3B;EACD;;EAED,UAAI,CAACkY,OAAL,EAAc;EACZ;EACD;;EAED,UAAME,YAAY,GAAGF,OAAO,CAAC/B,KAA7B;;EACA,UAAI,CAAC8B,OAAO,CAACtX,CAAD,CAAP,CAAWyE,SAAX,CAAqBE,QAArB,CAA8BkM,iBAA9B,CAAL,EAAqD;EACnD;EACD;;EAED,UAAIxR,KAAK,KAAMA,KAAK,CAACK,IAAN,KAAe,OAAf,IACX,kBAAkB7D,IAAlB,CAAuBwD,KAAK,CAACU,MAAN,CAAasO,OAApC,CADU,IAEThP,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAAC1B,GAAN,KAAc8V,OAFpC,CAAL,IAGAgE,YAAY,CAAC9S,QAAb,CAAsBtF,KAAK,CAACU,MAA5B,CAHJ,EAGyC;EACvC;EACD;;EAED,UAAMsW,SAAS,GAAG7W,YAAY,CAAC0C,OAAb,CAAqBsO,MAArB,EAA6BG,YAA7B,EAAyC5B,aAAzC,CAAlB;;EACA,UAAIsH,SAAS,CAAC7T,gBAAd,EAAgC;EAC9B;EACD,OA9BiD;EAiClD;;;EACA,UAAI,kBAAkBzJ,QAAQ,CAACyD,eAA/B,EAAgD;EAAA;;EAC9C,qBAAG+K,MAAH,cAAaxO,QAAQ,CAACmE,IAAT,CAAcyK,QAA3B,EACGpM,OADH,CACW,UAAAiW,IAAI;EAAA,iBAAIhS,YAAY,CAACC,GAAb,CAAiB+R,IAAjB,EAAuB,WAAvB,EAAoC,IAApC,EAA0C3U,IAAI,EAA9C,CAAJ;EAAA,SADf;EAED;;EAEDya,MAAAA,OAAO,CAACtX,CAAD,CAAP,CAAW0F,YAAX,CAAwB,eAAxB,EAAyC,OAAzC;;EAEA,UAAI6R,OAAO,CAAChC,OAAZ,EAAqB;EACnBgC,QAAAA,OAAO,CAAChC,OAAR,CAAgBe,OAAhB;EACD;;EAEDmB,MAAAA,YAAY,CAAChT,SAAb,CAAuBC,MAAvB,CAA8BmM,iBAA9B;EACAyG,MAAAA,OAAO,CAACtX,CAAD,CAAP,CAAWyE,SAAX,CAAqBC,MAArB,CAA4BmM,iBAA5B;EACArR,MAAAA,YAAY,CAAC0C,OAAb,CAAqBsO,MAArB,EAA6BI,cAA7B,EAA2C7B,aAA3C;EACD;EACF;;aAEMgH,uBAAP,8BAA4B7c,OAA5B,EAAqC;EACnC,WAAOO,sBAAsB,CAACP,OAAD,CAAtB,IAAmCA,OAAO,CAACgD,UAAlD;EACD;;aAEMwb,wBAAP,+BAA6BrY,KAA7B,EAAoC;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EACA,QAAI,kBAAkBxD,IAAlB,CAAuBwD,KAAK,CAACU,MAAN,CAAasO,OAApC,IACFhP,KAAK,CAAC1B,GAAN,KAAc6V,SAAd,IAA4BnU,KAAK,CAAC1B,GAAN,KAAc4V,UAAd,KAC1BlU,KAAK,CAAC1B,GAAN,KAAcgW,cAAd,IAAgCtU,KAAK,CAAC1B,GAAN,KAAc+V,YAA/C,IACCrU,KAAK,CAACU,MAAN,CAAayE,OAAb,CAAqBiQ,aAArB,CAF0B,CAD1B,GAIF,CAACZ,cAAc,CAAChY,IAAf,CAAoBwD,KAAK,CAAC1B,GAA1B,CAJH,EAImC;EACjC;EACD;;EAED0B,IAAAA,KAAK,CAAC6D,cAAN;EACA7D,IAAAA,KAAK,CAACoX,eAAN;;EAEA,QAAI,KAAKb,QAAL,IAAiB,KAAKnR,SAAL,CAAeE,QAAf,CAAwBsP,mBAAxB,CAArB,EAAmE;EACjE;EACD;;EAED,QAAMzD,MAAM,GAAG8E,QAAQ,CAACS,oBAAT,CAA8B,IAA9B,CAAf;EACA,QAAMF,QAAQ,GAAG,KAAKpR,SAAL,CAAeE,QAAf,CAAwBkM,iBAAxB,CAAjB;;EAEA,QAAIxR,KAAK,CAAC1B,GAAN,KAAc4V,UAAlB,EAA8B;EAC5B,UAAM5N,MAAM,GAAG,KAAK0B,OAAL,CAAa9B,sBAAb,IAAqC,IAArC,GAA4C6B,cAAc,CAACe,IAAf,CAAoB,IAApB,EAA0B5C,sBAA1B,EAAgD,CAAhD,CAA3D;EACAI,MAAAA,MAAM,CAACyQ,KAAP;EACAd,MAAAA,QAAQ,CAACQ,UAAT;EACA;EACD;;EAED,QAAI,CAACD,QAAD,IAAaxW,KAAK,CAAC1B,GAAN,KAAc6V,SAA/B,EAA0C;EACxC8B,MAAAA,QAAQ,CAACQ,UAAT;EACA;EACD;;EAED,QAAM6B,KAAK,GAAGvQ,cAAc,CAACE,IAAf,CAAoBqN,sBAApB,EAA4CnE,MAA5C,EAAoD5I,MAApD,CAA2D5L,SAA3D,CAAd;;EAEA,QAAI,CAAC2b,KAAK,CAAC1X,MAAX,EAAmB;EACjB;EACD;;EAED,QAAIiN,KAAK,GAAGyK,KAAK,CAAC3W,OAAN,CAAc3B,KAAK,CAACU,MAApB,CAAZ;;EAEA,QAAIV,KAAK,CAAC1B,GAAN,KAAc+V,YAAd,IAA8BxG,KAAK,GAAG,CAA1C,EAA6C;EAAE;EAC7CA,MAAAA,KAAK;EACN;;EAED,QAAI7N,KAAK,CAAC1B,GAAN,KAAcgW,cAAd,IAAgCzG,KAAK,GAAGyK,KAAK,CAAC1X,MAAN,GAAe,CAA3D,EAA8D;EAAE;EAC9DiN,MAAAA,KAAK;EACN,KApDiC;;;EAuDlCA,IAAAA,KAAK,GAAGA,KAAK,KAAK,CAAC,CAAX,GAAe,CAAf,GAAmBA,KAA3B;EAEAyK,IAAAA,KAAK,CAACzK,KAAD,CAAL,CAAakJ,KAAb;EACD;;aAEMlR,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BA9XoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOwF,SAAP;EACD;;;0BAEwB;EACvB,aAAOO,aAAP;EACD;;;;;EAuXH;EACA;EACA;EACA;EACA;;;EAEA3J,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0Bgb,sBAA1B,EAAkDxO,sBAAlD,EAAwE+P,QAAQ,CAACoC,qBAAjF;EACAlY,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0Bgb,sBAA1B,EAAkDU,aAAlD,EAAiEa,QAAQ,CAACoC,qBAA1E;EACAlY,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD2R,QAAQ,CAACQ,UAAzD;EACAtW,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0Bib,oBAA1B,EAAgDsB,QAAQ,CAACQ,UAAzD;EACAtW,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUlG,KAAV,EAAiB;EACrFA,EAAAA,KAAK,CAAC6D,cAAN;EACA7D,EAAAA,KAAK,CAACoX,eAAN;EACAnB,EAAAA,QAAQ,CAAC+B,iBAAT,CAA2B,IAA3B,EAAiC,QAAjC;EACD,CAJD;EAKA7X,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD6Q,mBAAhD,EAAqE,UAAArG,CAAC;EAAA,SAAIA,CAAC,CAACsI,eAAF,EAAJ;EAAA,CAAtE;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEArZ,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAamS,QAAQ,CAACxQ,eAAtB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBkQ,QAAzB;;EACAlT,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOmQ,QAAQ,CAACxQ,eAAhB;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;EClfA;EACA;EACA;EACA;EACA;;EAEA,IAAM3B,MAAI,GAAG,OAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,UAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EACA,IAAMgQ,YAAU,GAAG,QAAnB;EAEA,IAAM3K,SAAO,GAAG;EACdgP,EAAAA,QAAQ,EAAE,IADI;EAEd9O,EAAAA,QAAQ,EAAE,IAFI;EAGdsN,EAAAA,KAAK,EAAE,IAHO;EAIdpE,EAAAA,IAAI,EAAE;EAJQ,CAAhB;EAOA,IAAM7I,aAAW,GAAG;EAClByO,EAAAA,QAAQ,EAAE,kBADQ;EAElB9O,EAAAA,QAAQ,EAAE,SAFQ;EAGlBsN,EAAAA,KAAK,EAAE,SAHW;EAIlBpE,EAAAA,IAAI,EAAE;EAJY,CAApB;EAOA,IAAMrB,YAAU,YAAUrN,WAA1B;EACA,IAAMuU,oBAAoB,qBAAmBvU,WAA7C;EACA,IAAMsN,cAAY,cAAYtN,WAA9B;EACA,IAAMmN,YAAU,YAAUnN,WAA1B;EACA,IAAMoN,aAAW,aAAWpN,WAA5B;EACA,IAAMwU,aAAa,eAAaxU,WAAhC;EACA,IAAMyU,YAAY,cAAYzU,WAA9B;EACA,IAAM0U,mBAAmB,qBAAmB1U,WAA5C;EACA,IAAM2U,qBAAqB,uBAAqB3U,WAAhD;EACA,IAAM4U,qBAAqB,uBAAqB5U,WAAhD;EACA,IAAM6U,uBAAuB,yBAAuB7U,WAApD;EACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EAEA,IAAM6U,6BAA6B,GAAG,yBAAtC;EACA,IAAMC,mBAAmB,GAAG,gBAA5B;EACA,IAAMC,eAAe,GAAG,YAAxB;EACA,IAAMC,eAAe,GAAG,MAAxB;EACA,IAAM1H,iBAAe,GAAG,MAAxB;EACA,IAAM2H,iBAAiB,GAAG,cAA1B;EAEA,IAAMC,eAAe,GAAG,eAAxB;EACA,IAAMC,mBAAmB,GAAG,aAA5B;EACA,IAAMnT,sBAAoB,GAAG,uBAA7B;EACA,IAAMoT,qBAAqB,GAAG,wBAA9B;EACA,IAAMC,sBAAsB,GAAG,mDAA/B;EACA,IAAMC,uBAAuB,GAAG,aAAhC;EAEA;EACA;EACA;EACA;EACA;;MAEMC;EACJ,iBAAY5f,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,SAAK4Q,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;EACA,SAAK6I,QAAL,GAAgB9K,OAAhB;EACA,SAAK6f,OAAL,GAAe3R,cAAc,CAACM,OAAf,CAAuB+Q,eAAvB,EAAwCvf,OAAxC,CAAf;EACA,SAAK8f,SAAL,GAAiB,IAAjB;EACA,SAAKC,QAAL,GAAgB,KAAhB;EACA,SAAKC,kBAAL,GAA0B,KAA1B;EACA,SAAKC,oBAAL,GAA4B,KAA5B;EACA,SAAK9H,gBAAL,GAAwB,KAAxB;EACA,SAAK+H,eAAL,GAAuB,CAAvB;EACAnb,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;EACD;;;;;EAYD;WAEAoC,SAAA,gBAAOsJ,aAAP,EAAsB;EACpB,WAAO,KAAKkK,QAAL,GAAgB,KAAKlH,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAUjD,aAAV,CAArC;EACD;;WAEDiD,OAAA,cAAKjD,aAAL,EAAoB;EAAA;;EAClB,QAAI,KAAKkK,QAAL,IAAiB,KAAK5H,gBAA1B,EAA4C;EAC1C;EACD;;EAED,QAAI,KAAKrN,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC4T,eAAjC,CAAJ,EAAuD;EACrD,WAAKlH,gBAAL,GAAwB,IAAxB;EACD;;EAED,QAAM2E,SAAS,GAAGxW,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyM,YAApC,EAAgD;EAChE1B,MAAAA,aAAa,EAAbA;EADgE,KAAhD,CAAlB;;EAIA,QAAI,KAAKkK,QAAL,IAAiBjD,SAAS,CAACxT,gBAA/B,EAAiD;EAC/C;EACD;;EAED,SAAKyW,QAAL,GAAgB,IAAhB;;EAEA,SAAKI,eAAL;;EACA,SAAKC,aAAL;;EAEA,SAAKC,aAAL;;EAEA,SAAKC,eAAL;;EACA,SAAKC,eAAL;;EAEAja,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EACEgU,mBADF,EAEEW,qBAFF,EAGE,UAAAtZ,KAAK;EAAA,aAAI,KAAI,CAAC0S,IAAL,CAAU1S,KAAV,CAAJ;EAAA,KAHP;EAMAG,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKqX,OAArB,EAA8BZ,uBAA9B,EAAuD,YAAM;EAC3D3Y,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAI,CAACqC,QAAtB,EAAgCkU,qBAAhC,EAAuD,UAAA7Y,KAAK,EAAI;EAC9D,YAAIA,KAAK,CAACU,MAAN,KAAiB,KAAI,CAACiE,QAA1B,EAAoC;EAClC,UAAA,KAAI,CAACmV,oBAAL,GAA4B,IAA5B;EACD;EACF,OAJD;EAKD,KAND;;EAQA,SAAKO,aAAL,CAAmB;EAAA,aAAM,KAAI,CAACC,YAAL,CAAkB5K,aAAlB,CAAN;EAAA,KAAnB;EACD;;WAEDgD,OAAA,cAAK1S,KAAL,EAAY;EAAA;;EACV,QAAIA,KAAJ,EAAW;EACTA,MAAAA,KAAK,CAAC6D,cAAN;EACD;;EAED,QAAI,CAAC,KAAK+V,QAAN,IAAkB,KAAK5H,gBAA3B,EAA6C;EAC3C;EACD;;EAED,QAAMgF,SAAS,GAAG7W,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoC2M,YAApC,CAAlB;;EAEA,QAAI0F,SAAS,CAAC7T,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKyW,QAAL,GAAgB,KAAhB;;EACA,QAAMW,UAAU,GAAG,KAAK5V,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC4T,eAAjC,CAAnB;;EAEA,QAAIqB,UAAJ,EAAgB;EACd,WAAKvI,gBAAL,GAAwB,IAAxB;EACD;;EAED,SAAKmI,eAAL;;EACA,SAAKC,eAAL;;EAEAja,IAAAA,YAAY,CAACC,GAAb,CAAiB1G,QAAjB,EAA2B+e,aAA3B;;EAEA,SAAK9T,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BmM,iBAA/B;;EAEArR,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgCgU,mBAAhC;EACAxY,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKsZ,OAAtB,EAA+BZ,uBAA/B;;EAEA,QAAIyB,UAAJ,EAAgB;EACd,UAAM/f,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKsK,QAAN,CAA3D;EAEAxE,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgD,UAAAmH,KAAK;EAAA,eAAI,MAAI,CAACwa,UAAL,CAAgBxa,KAAhB,CAAJ;EAAA,OAArD;EACA7E,MAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBnK,kBAAhB,CAApB;EACD,KALD,MAKO;EACL,WAAKggB,UAAL;EACD;EACF;;WAEDtV,UAAA,mBAAU;EACR,KAAC5K,MAAD,EAAS,KAAKqK,QAAd,EAAwB,KAAK+U,OAA7B,EACGxd,OADH,CACW,UAAAue,WAAW;EAAA,aAAIta,YAAY,CAACC,GAAb,CAAiBqa,WAAjB,EAA8BxW,WAA9B,CAAJ;EAAA,KADtB;EAGA;EACJ;EACA;EACA;EACA;;EACI9D,IAAAA,YAAY,CAACC,GAAb,CAAiB1G,QAAjB,EAA2B+e,aAA3B;EAEA7Z,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EAEA,SAAK0I,OAAL,GAAe,IAAf;EACA,SAAK/H,QAAL,GAAgB,IAAhB;EACA,SAAK+U,OAAL,GAAe,IAAf;EACA,SAAKC,SAAL,GAAiB,IAAjB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,kBAAL,GAA0B,IAA1B;EACA,SAAKC,oBAAL,GAA4B,IAA5B;EACA,SAAK9H,gBAAL,GAAwB,IAAxB;EACA,SAAK+H,eAAL,GAAuB,IAAvB;EACD;;WAEDW,eAAA,wBAAe;EACb,SAAKR,aAAL;EACD;;;WAIDvN,aAAA,oBAAW7Q,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACDyN,SADC,EAEDzN,MAFC,CAAN;EAIAF,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAegO,aAAf,CAAf;EACA,WAAOhO,MAAP;EACD;;WAEDwe,eAAA,sBAAa5K,aAAb,EAA4B;EAAA;;EAC1B,QAAM6K,UAAU,GAAG,KAAK5V,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC4T,eAAjC,CAAnB;;EACA,QAAMyB,SAAS,GAAG5S,cAAc,CAACM,OAAf,CAAuBgR,mBAAvB,EAA4C,KAAKK,OAAjD,CAAlB;;EAEA,QAAI,CAAC,KAAK/U,QAAL,CAAc9H,UAAf,IACA,KAAK8H,QAAL,CAAc9H,UAAd,CAAyB3B,QAAzB,KAAsCyN,IAAI,CAACC,YAD/C,EAC6D;EAC3D;EACAlP,MAAAA,QAAQ,CAACmE,IAAT,CAAc+c,WAAd,CAA0B,KAAKjW,QAA/B;EACD;;EAED,SAAKA,QAAL,CAAc/H,KAAd,CAAoBI,OAApB,GAA8B,OAA9B;;EACA,SAAK2H,QAAL,CAAcoC,eAAd,CAA8B,aAA9B;;EACA,SAAKpC,QAAL,CAAc0B,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;EACA,SAAK1B,QAAL,CAAc0B,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;EACA,SAAK1B,QAAL,CAAc6C,SAAd,GAA0B,CAA1B;;EAEA,QAAImT,SAAJ,EAAe;EACbA,MAAAA,SAAS,CAACnT,SAAV,GAAsB,CAAtB;EACD;;EAED,QAAI+S,UAAJ,EAAgB;EACd9c,MAAAA,MAAM,CAAC,KAAKkH,QAAN,CAAN;EACD;;EAED,SAAKA,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4ByC,iBAA5B;;EAEA,QAAI,KAAK9E,OAAL,CAAaqK,KAAjB,EAAwB;EACtB,WAAK8D,aAAL;EACD;;EAED,QAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,GAAM;EAC/B,UAAI,MAAI,CAACpO,OAAL,CAAaqK,KAAjB,EAAwB;EACtB,QAAA,MAAI,CAACpS,QAAL,CAAcoS,KAAd;EACD;;EAED,MAAA,MAAI,CAAC/E,gBAAL,GAAwB,KAAxB;EACA7R,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAC8B,QAA1B,EAAoC0M,aAApC,EAAiD;EAC/C3B,QAAAA,aAAa,EAAbA;EAD+C,OAAjD;EAGD,KATD;;EAWA,QAAI6K,UAAJ,EAAgB;EACd,UAAM/f,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKqf,OAAN,CAA3D;EAEAvZ,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKoX,OAAtB,EAA+B7gB,cAA/B,EAA+CiiB,kBAA/C;EACA3f,MAAAA,oBAAoB,CAAC,KAAKue,OAAN,EAAelf,kBAAf,CAApB;EACD,KALD,MAKO;EACLsgB,MAAAA,kBAAkB;EACnB;EACF;;WAEDD,gBAAA,yBAAgB;EAAA;;EACd1a,IAAAA,YAAY,CAACC,GAAb,CAAiB1G,QAAjB,EAA2B+e,aAA3B,EADc;;EAEdtY,IAAAA,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B+e,aAA1B,EAAyC,UAAAzY,KAAK,EAAI;EAChD,UAAItG,QAAQ,KAAKsG,KAAK,CAACU,MAAnB,IACA,MAAI,CAACiE,QAAL,KAAkB3E,KAAK,CAACU,MADxB,IAEA,CAAC,MAAI,CAACiE,QAAL,CAAcW,QAAd,CAAuBtF,KAAK,CAACU,MAA7B,CAFL,EAE2C;EACzC,QAAA,MAAI,CAACiE,QAAL,CAAcoS,KAAd;EACD;EACF,KAND;EAOD;;WAEDoD,kBAAA,2BAAkB;EAAA;;EAChB,QAAI,KAAKP,QAAT,EAAmB;EACjBzZ,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+BiU,qBAA/B,EAAsD,UAAA5Y,KAAK,EAAI;EAC7D,YAAI,MAAI,CAAC0M,OAAL,CAAajD,QAAb,IAAyBzJ,KAAK,CAAC1B,GAAN,KAAc4V,YAA3C,EAAuD;EACrDlU,UAAAA,KAAK,CAAC6D,cAAN;;EACA,UAAA,MAAI,CAAC6O,IAAL;EACD,SAHD,MAGO,IAAI,CAAC,MAAI,CAAChG,OAAL,CAAajD,QAAd,IAA0BzJ,KAAK,CAAC1B,GAAN,KAAc4V,YAA5C,EAAwD;EAC7D,UAAA,MAAI,CAAC6G,0BAAL;EACD;EACF,OAPD;EAQD,KATD,MASO;EACL5a,MAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgCiU,qBAAhC;EACD;EACF;;WAEDwB,kBAAA,2BAAkB;EAAA;;EAChB,QAAI,KAAKR,QAAT,EAAmB;EACjBzZ,MAAAA,YAAY,CAACkC,EAAb,CAAgB/H,MAAhB,EAAwBoe,YAAxB,EAAsC;EAAA,eAAM,MAAI,CAACwB,aAAL,EAAN;EAAA,OAAtC;EACD,KAFD,MAEO;EACL/Z,MAAAA,YAAY,CAACC,GAAb,CAAiB9F,MAAjB,EAAyBoe,YAAzB;EACD;EACF;;WAED8B,aAAA,sBAAa;EAAA;;EACX,SAAK7V,QAAL,CAAc/H,KAAd,CAAoBI,OAApB,GAA8B,MAA9B;;EACA,SAAK2H,QAAL,CAAc0B,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;EACA,SAAK1B,QAAL,CAAcoC,eAAd,CAA8B,YAA9B;;EACA,SAAKpC,QAAL,CAAcoC,eAAd,CAA8B,MAA9B;;EACA,SAAKiL,gBAAL,GAAwB,KAAxB;;EACA,SAAKqI,aAAL,CAAmB,YAAM;EACvB3gB,MAAAA,QAAQ,CAACmE,IAAT,CAAcuH,SAAd,CAAwBC,MAAxB,CAA+B4T,eAA/B;;EACA,MAAA,MAAI,CAAC+B,iBAAL;;EACA,MAAA,MAAI,CAACC,eAAL;;EACA9a,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAC8B,QAA1B,EAAoC4M,cAApC;EACD,KALD;EAMD;;WAED2J,kBAAA,2BAAkB;EAChB,SAAKvB,SAAL,CAAe9c,UAAf,CAA0B2I,WAA1B,CAAsC,KAAKmU,SAA3C;;EACA,SAAKA,SAAL,GAAiB,IAAjB;EACD;;WAEDU,gBAAA,uBAAcrc,QAAd,EAAwB;EAAA;;EACtB,QAAMmd,OAAO,GAAG,KAAKxW,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC4T,eAAjC,IACdA,eADc,GAEd,EAFF;;EAIA,QAAI,KAAKU,QAAL,IAAiB,KAAKlN,OAAL,CAAa6L,QAAlC,EAA4C;EAC1C,WAAKoB,SAAL,GAAiBjgB,QAAQ,CAAC0hB,aAAT,CAAuB,KAAvB,CAAjB;EACA,WAAKzB,SAAL,CAAe0B,SAAf,GAA2BrC,mBAA3B;;EAEA,UAAImC,OAAJ,EAAa;EACX,aAAKxB,SAAL,CAAevU,SAAf,CAAyB2J,GAAzB,CAA6BoM,OAA7B;EACD;;EAEDzhB,MAAAA,QAAQ,CAACmE,IAAT,CAAc+c,WAAd,CAA0B,KAAKjB,SAA/B;EAEAxZ,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+BgU,mBAA/B,EAAoD,UAAA3Y,KAAK,EAAI;EAC3D,YAAI,MAAI,CAAC8Z,oBAAT,EAA+B;EAC7B,UAAA,MAAI,CAACA,oBAAL,GAA4B,KAA5B;EACA;EACD;;EAED,YAAI9Z,KAAK,CAACU,MAAN,KAAiBV,KAAK,CAACsb,aAA3B,EAA0C;EACxC;EACD;;EAED,QAAA,MAAI,CAACP,0BAAL;EACD,OAXD;;EAaA,UAAII,OAAJ,EAAa;EACX1d,QAAAA,MAAM,CAAC,KAAKkc,SAAN,CAAN;EACD;;EAED,WAAKA,SAAL,CAAevU,SAAf,CAAyB2J,GAAzB,CAA6ByC,iBAA7B;;EAEA,UAAI,CAAC2J,OAAL,EAAc;EACZnd,QAAAA,QAAQ;EACR;EACD;;EAED,UAAMud,0BAA0B,GAAGlhB,gCAAgC,CAAC,KAAKsf,SAAN,CAAnE;EAEAxZ,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqX,SAAtB,EAAiC9gB,cAAjC,EAAiDmF,QAAjD;EACA7C,MAAAA,oBAAoB,CAAC,KAAKwe,SAAN,EAAiB4B,0BAAjB,CAApB;EACD,KAtCD,MAsCO,IAAI,CAAC,KAAK3B,QAAN,IAAkB,KAAKD,SAA3B,EAAsC;EAC3C,WAAKA,SAAL,CAAevU,SAAf,CAAyBC,MAAzB,CAAgCmM,iBAAhC;;EAEA,UAAMgK,cAAc,GAAG,SAAjBA,cAAiB,GAAM;EAC3B,QAAA,MAAI,CAACN,eAAL;;EACAld,QAAAA,QAAQ;EACT,OAHD;;EAKA,UAAI,KAAK2G,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC4T,eAAjC,CAAJ,EAAuD;EACrD,YAAMqC,2BAA0B,GAAGlhB,gCAAgC,CAAC,KAAKsf,SAAN,CAAnE;;EACAxZ,QAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqX,SAAtB,EAAiC9gB,cAAjC,EAAiD2iB,cAAjD;EACArgB,QAAAA,oBAAoB,CAAC,KAAKwe,SAAN,EAAiB4B,2BAAjB,CAApB;EACD,OAJD,MAIO;EACLC,QAAAA,cAAc;EACf;EACF,KAfM,MAeA;EACLxd,MAAAA,QAAQ;EACT;EACF;;WAED+c,6BAAA,sCAA6B;EAAA;;EAC3B,QAAI,KAAKrO,OAAL,CAAa6L,QAAb,KAA0B,QAA9B,EAAwC;EACtC,UAAMvB,SAAS,GAAG7W,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoC6T,oBAApC,CAAlB;;EACA,UAAIxB,SAAS,CAAC7T,gBAAd,EAAgC;EAC9B;EACD;;EAED,UAAMsY,kBAAkB,GAAG,KAAK9W,QAAL,CAAc+W,YAAd,GAA6BhiB,QAAQ,CAACyD,eAAT,CAAyBwe,YAAjF;;EAEA,UAAI,CAACF,kBAAL,EAAyB;EACvB,aAAK9W,QAAL,CAAc/H,KAAd,CAAoBgf,SAApB,GAAgC,QAAhC;EACD;;EAED,WAAKjX,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4BoK,iBAA5B;;EACA,UAAM0C,uBAAuB,GAAGxhB,gCAAgC,CAAC,KAAKqf,OAAN,CAAhE;EACAvZ,MAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgC9L,cAAhC;EACAsH,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgD,YAAM;EACpD,QAAA,MAAI,CAAC8L,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B8T,iBAA/B;;EACA,YAAI,CAACsC,kBAAL,EAAyB;EACvBtb,UAAAA,YAAY,CAACmC,GAAb,CAAiB,MAAI,CAACqC,QAAtB,EAAgC9L,cAAhC,EAAgD,YAAM;EACpD,YAAA,MAAI,CAAC8L,QAAL,CAAc/H,KAAd,CAAoBgf,SAApB,GAAgC,EAAhC;EACD,WAFD;EAGAzgB,UAAAA,oBAAoB,CAAC,MAAI,CAACwJ,QAAN,EAAgBkX,uBAAhB,CAApB;EACD;EACF,OARD;EASA1gB,MAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBkX,uBAAhB,CAApB;;EACA,WAAKlX,QAAL,CAAcoS,KAAd;EACD,KA1BD,MA0BO;EACL,WAAKrE,IAAL;EACD;EACF;EAGD;EACA;;;WAEAwH,gBAAA,yBAAgB;EACd,QAAMuB,kBAAkB,GACtB,KAAK9W,QAAL,CAAc+W,YAAd,GAA6BhiB,QAAQ,CAACyD,eAAT,CAAyBwe,YADxD;;EAGA,QAAI,CAAC,KAAK9B,kBAAN,IAA4B4B,kBAAhC,EAAoD;EAClD,WAAK9W,QAAL,CAAc/H,KAAd,CAAoBkf,WAApB,GAAqC,KAAK/B,eAA1C;EACD;;EAED,QAAI,KAAKF,kBAAL,IAA2B,CAAC4B,kBAAhC,EAAoD;EAClD,WAAK9W,QAAL,CAAc/H,KAAd,CAAoBmf,YAApB,GAAsC,KAAKhC,eAA3C;EACD;EACF;;WAEDiB,oBAAA,6BAAoB;EAClB,SAAKrW,QAAL,CAAc/H,KAAd,CAAoBkf,WAApB,GAAkC,EAAlC;EACA,SAAKnX,QAAL,CAAc/H,KAAd,CAAoBmf,YAApB,GAAmC,EAAnC;EACD;;WAED/B,kBAAA,2BAAkB;EAChB,QAAM3S,IAAI,GAAG3N,QAAQ,CAACmE,IAAT,CAAcyJ,qBAAd,EAAb;EACA,SAAKuS,kBAAL,GAA0BtgB,IAAI,CAACyiB,KAAL,CAAW3U,IAAI,CAACI,IAAL,GAAYJ,IAAI,CAAC4U,KAA5B,IAAqC3hB,MAAM,CAAC4hB,UAAtE;EACA,SAAKnC,eAAL,GAAuB,KAAKoC,kBAAL,EAAvB;EACD;;WAEDlC,gBAAA,yBAAgB;EAAA;;EACd,QAAI,KAAKJ,kBAAT,EAA6B;EAC3B;EACA;EAEA;EACA9R,MAAAA,cAAc,CAACE,IAAf,CAAoBsR,sBAApB,EACGrd,OADH,CACW,UAAArC,OAAO,EAAI;EAClB,YAAMuiB,aAAa,GAAGviB,OAAO,CAAC+C,KAAR,CAAcmf,YAApC;EACA,YAAMM,iBAAiB,GAAG/hB,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,EAAiC,eAAjC,CAA1B;EACA+M,QAAAA,WAAW,CAACC,gBAAZ,CAA6BhN,OAA7B,EAAsC,eAAtC,EAAuDuiB,aAAvD;EACAviB,QAAAA,OAAO,CAAC+C,KAAR,CAAcmf,YAAd,GAAgCphB,UAAU,CAAC0hB,iBAAD,CAAV,GAAgC,OAAI,CAACtC,eAArE;EACD,OANH,EAL2B;;EAc3BhS,MAAAA,cAAc,CAACE,IAAf,CAAoBuR,uBAApB,EACGtd,OADH,CACW,UAAArC,OAAO,EAAI;EAClB,YAAMyiB,YAAY,GAAGziB,OAAO,CAAC+C,KAAR,CAAc2f,WAAnC;EACA,YAAMC,gBAAgB,GAAGliB,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,EAAiC,cAAjC,CAAzB;EACA+M,QAAAA,WAAW,CAACC,gBAAZ,CAA6BhN,OAA7B,EAAsC,cAAtC,EAAsDyiB,YAAtD;EACAziB,QAAAA,OAAO,CAAC+C,KAAR,CAAc2f,WAAd,GAA+B5hB,UAAU,CAAC6hB,gBAAD,CAAV,GAA+B,OAAI,CAACzC,eAAnE;EACD,OANH,EAd2B;;EAuB3B,UAAMqC,aAAa,GAAG1iB,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBmf,YAA1C;EACA,UAAMM,iBAAiB,GAAG/hB,MAAM,CAACC,gBAAP,CAAwBb,QAAQ,CAACmE,IAAjC,EAAuC,eAAvC,CAA1B;EAEA+I,MAAAA,WAAW,CAACC,gBAAZ,CAA6BnN,QAAQ,CAACmE,IAAtC,EAA4C,eAA5C,EAA6Due,aAA7D;EACA1iB,MAAAA,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBmf,YAApB,GAAsCphB,UAAU,CAAC0hB,iBAAD,CAAV,GAAgC,KAAKtC,eAA3E;EACD;;EAEDrgB,IAAAA,QAAQ,CAACmE,IAAT,CAAcuH,SAAd,CAAwB2J,GAAxB,CAA4BkK,eAA5B;EACD;;WAEDgC,kBAAA,2BAAkB;EAChB;EACAlT,IAAAA,cAAc,CAACE,IAAf,CAAoBsR,sBAApB,EACGrd,OADH,CACW,UAAArC,OAAO,EAAI;EAClB,UAAM4iB,OAAO,GAAG7V,WAAW,CAACO,gBAAZ,CAA6BtN,OAA7B,EAAsC,eAAtC,CAAhB;;EACA,UAAI,OAAO4iB,OAAP,KAAmB,WAAvB,EAAoC;EAClC7V,QAAAA,WAAW,CAACE,mBAAZ,CAAgCjN,OAAhC,EAAyC,eAAzC;EACAA,QAAAA,OAAO,CAAC+C,KAAR,CAAcmf,YAAd,GAA6BU,OAA7B;EACD;EACF,KAPH,EAFgB;;EAYhB1U,IAAAA,cAAc,CAACE,IAAf,MAAuBuR,uBAAvB,EACGtd,OADH,CACW,UAAArC,OAAO,EAAI;EAClB,UAAM6iB,MAAM,GAAG9V,WAAW,CAACO,gBAAZ,CAA6BtN,OAA7B,EAAsC,cAAtC,CAAf;;EACA,UAAI,OAAO6iB,MAAP,KAAkB,WAAtB,EAAmC;EACjC9V,QAAAA,WAAW,CAACE,mBAAZ,CAAgCjN,OAAhC,EAAyC,cAAzC;EACAA,QAAAA,OAAO,CAAC+C,KAAR,CAAc2f,WAAd,GAA4BG,MAA5B;EACD;EACF,KAPH,EAZgB;;EAsBhB,QAAMD,OAAO,GAAG7V,WAAW,CAACO,gBAAZ,CAA6BzN,QAAQ,CAACmE,IAAtC,EAA4C,eAA5C,CAAhB;;EACA,QAAI,OAAO4e,OAAP,KAAmB,WAAvB,EAAoC;EAClC/iB,MAAAA,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBmf,YAApB,GAAmC,EAAnC;EACD,KAFD,MAEO;EACLnV,MAAAA,WAAW,CAACE,mBAAZ,CAAgCpN,QAAQ,CAACmE,IAAzC,EAA+C,eAA/C;EACAnE,MAAAA,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBmf,YAApB,GAAmCU,OAAnC;EACD;EACF;;WAEDN,qBAAA,8BAAqB;EAAE;EACrB,QAAMQ,SAAS,GAAGjjB,QAAQ,CAAC0hB,aAAT,CAAuB,KAAvB,CAAlB;EACAuB,IAAAA,SAAS,CAACtB,SAAV,GAAsBtC,6BAAtB;EACArf,IAAAA,QAAQ,CAACmE,IAAT,CAAc+c,WAAd,CAA0B+B,SAA1B;EACA,QAAMC,cAAc,GAAGD,SAAS,CAACrV,qBAAV,GAAkCuV,KAAlC,GAA0CF,SAAS,CAACG,WAA3E;EACApjB,IAAAA,QAAQ,CAACmE,IAAT,CAAc2H,WAAd,CAA0BmX,SAA1B;EACA,WAAOC,cAAP;EACD;;;UAIMnX,kBAAP,yBAAuB3J,MAAvB,EAA+B4T,aAA/B,EAA8C;EAC5C,WAAO,KAAKhK,IAAL,CAAU,YAAY;EAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;EACA,UAAM0I,OAAO,gBACRnD,SADQ,EAER3C,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAFQ,EAGP,OAAOlL,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHzC,CAAb;;EAMA,UAAI,CAACyC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIkb,KAAJ,CAAU,IAAV,EAAgB/M,OAAhB,CAAP;EACD;;EAED,UAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ,CAAa4T,aAAb;EACD,OAND,MAMO,IAAIhD,OAAO,CAACiG,IAAZ,EAAkB;EACvBpU,QAAAA,IAAI,CAACoU,IAAL,CAAUjD,aAAV;EACD;EACF,KArBM,CAAP;EAsBD;;UAEM7J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BArdoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOwF,SAAP;EACD;;;;;EAkdH;EACA;EACA;EACA;EACA;;;EAEApJ,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUlG,KAAV,EAAiB;EAAA;;EACrF,MAAMU,MAAM,GAAGtG,sBAAsB,CAAC,IAAD,CAArC;;EAEA,MAAI,KAAK4U,OAAL,KAAiB,GAAjB,IAAwB,KAAKA,OAAL,KAAiB,MAA7C,EAAqD;EACnDhP,IAAAA,KAAK,CAAC6D,cAAN;EACD;;EAED1D,EAAAA,YAAY,CAACmC,GAAb,CAAiB5B,MAAjB,EAAyB0Q,YAAzB,EAAqC,UAAAuF,SAAS,EAAI;EAChD,QAAIA,SAAS,CAACxT,gBAAd,EAAgC;EAC9B;EACA;EACD;;EAEDhD,IAAAA,YAAY,CAACmC,GAAb,CAAiB5B,MAAjB,EAAyB6Q,cAAzB,EAAuC,YAAM;EAC3C,UAAI5U,SAAS,CAAC,OAAD,CAAb,EAAqB;EACnB,QAAA,OAAI,CAACoa,KAAL;EACD;EACF,KAJD;EAKD,GAXD;EAaA,MAAIxY,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa2B,MAAb,EAAqBsD,UAArB,CAAX;;EACA,MAAI,CAACzF,IAAL,EAAW;EACT,QAAMzC,MAAM,gBACP8K,WAAW,CAACI,iBAAZ,CAA8BtG,MAA9B,CADO,EAEPkG,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAFO,CAAZ;;EAKAzI,IAAAA,IAAI,GAAG,IAAIkb,KAAJ,CAAU/Y,MAAV,EAAkB5E,MAAlB,CAAP;EACD;;EAEDyC,EAAAA,IAAI,CAACoU,IAAL,CAAU,IAAV;EACD,CA/BD;EAiCA;EACA;EACA;EACA;EACA;EACA;;EAEA5U,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAa2V,KAAK,CAAChU,eAAnB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyB0T,KAAzB;;EACA1W,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAO2T,KAAK,CAAChU,eAAb;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;ECzmBA;EACA;EACA;EACA;EACA;EACA;EAEA,IAAMsX,QAAQ,GAAG,CACf,YADe,EAEf,MAFe,EAGf,MAHe,EAIf,UAJe,EAKf,UALe,EAMf,QANe,EAOf,KAPe,EAQf,YARe,CAAjB;EAWA,IAAMC,sBAAsB,GAAG,gBAA/B;EAEA;EACA;EACA;EACA;EACA;;EACA,IAAMC,gBAAgB,GAAG,6DAAzB;EAEA;EACA;EACA;EACA;EACA;;EACA,IAAMC,gBAAgB,GAAG,oIAAzB;;EAEA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACC,IAAD,EAAOC,oBAAP,EAAgC;EACvD,MAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAcnkB,WAAd,EAAjB;;EAEA,MAAIikB,oBAAoB,CAAC1b,OAArB,CAA6B2b,QAA7B,MAA2C,CAAC,CAAhD,EAAmD;EACjD,QAAIP,QAAQ,CAACpb,OAAT,CAAiB2b,QAAjB,MAA+B,CAAC,CAApC,EAAuC;EACrC,aAAOtb,OAAO,CAACob,IAAI,CAACI,SAAL,CAAerkB,KAAf,CAAqB8jB,gBAArB,KAA0CG,IAAI,CAACI,SAAL,CAAerkB,KAAf,CAAqB+jB,gBAArB,CAA3C,CAAd;EACD;;EAED,WAAO,IAAP;EACD;;EAED,MAAMO,MAAM,GAAGJ,oBAAoB,CAAC9U,MAArB,CAA4B,UAAAmV,SAAS;EAAA,WAAIA,SAAS,YAAYnhB,MAAzB;EAAA,GAArC,CAAf,CAXuD;;EAcvD,OAAK,IAAIoE,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGwc,MAAM,CAAC7c,MAA7B,EAAqCD,CAAC,GAAGM,GAAzC,EAA8CN,CAAC,EAA/C,EAAmD;EACjD,QAAI2c,QAAQ,CAACnkB,KAAT,CAAeskB,MAAM,CAAC9c,CAAD,CAArB,CAAJ,EAA+B;EAC7B,aAAO,IAAP;EACD;EACF;;EAED,SAAO,KAAP;EACD,CArBD;;EAuBO,IAAMgd,gBAAgB,GAAG;EAC9B;EACA,OAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCX,sBAAvC,CAFyB;EAG9BY,EAAAA,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;EAI9BC,EAAAA,IAAI,EAAE,EAJwB;EAK9BC,EAAAA,CAAC,EAAE,EAL2B;EAM9BC,EAAAA,EAAE,EAAE,EAN0B;EAO9BC,EAAAA,GAAG,EAAE,EAPyB;EAQ9BC,EAAAA,IAAI,EAAE,EARwB;EAS9BC,EAAAA,GAAG,EAAE,EATyB;EAU9BC,EAAAA,EAAE,EAAE,EAV0B;EAW9BC,EAAAA,EAAE,EAAE,EAX0B;EAY9BC,EAAAA,EAAE,EAAE,EAZ0B;EAa9BC,EAAAA,EAAE,EAAE,EAb0B;EAc9BC,EAAAA,EAAE,EAAE,EAd0B;EAe9BC,EAAAA,EAAE,EAAE,EAf0B;EAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;EAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;EAkB9B/d,EAAAA,CAAC,EAAE,EAlB2B;EAmB9Bge,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;EAoB9BC,EAAAA,EAAE,EAAE,EApB0B;EAqB9BC,EAAAA,EAAE,EAAE,EArB0B;EAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;EAuB9BC,EAAAA,GAAG,EAAE,EAvByB;EAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;EAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;EA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;EA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;EA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;EA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;EA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;EA+B9BC,EAAAA,EAAE,EAAE;EA/B0B,CAAzB;EAkCA,SAASC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyD;EAAA;;EAC9D,MAAI,CAACF,UAAU,CAAC7e,MAAhB,EAAwB;EACtB,WAAO6e,UAAP;EACD;;EAED,MAAIE,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;EAClD,WAAOA,UAAU,CAACF,UAAD,CAAjB;EACD;;EAED,MAAMG,SAAS,GAAG,IAAItlB,MAAM,CAACulB,SAAX,EAAlB;EACA,MAAMC,eAAe,GAAGF,SAAS,CAACG,eAAV,CAA0BN,UAA1B,EAAsC,WAAtC,CAAxB;EACA,MAAMO,aAAa,GAAGhkB,MAAM,CAACC,IAAP,CAAYyjB,SAAZ,CAAtB;;EACA,MAAMO,QAAQ,GAAG,YAAG/X,MAAH,aAAa4X,eAAe,CAACjiB,IAAhB,CAAqB4C,gBAArB,CAAsC,GAAtC,CAAb,CAAjB;;EAZ8D,6BAcrDE,CAdqD,EAc9CM,GAd8C;EAAA;;EAe5D,QAAMif,EAAE,GAAGD,QAAQ,CAACtf,CAAD,CAAnB;EACA,QAAMwf,MAAM,GAAGD,EAAE,CAAC3C,QAAH,CAAYnkB,WAAZ,EAAf;;EAEA,QAAI4mB,aAAa,CAACre,OAAd,CAAsBwe,MAAtB,MAAkC,CAAC,CAAvC,EAA0C;EACxCD,MAAAA,EAAE,CAACrjB,UAAH,CAAc2I,WAAd,CAA0B0a,EAA1B;EAEA;EACD;;EAED,QAAME,aAAa,GAAG,aAAGlY,MAAH,cAAagY,EAAE,CAACjZ,UAAhB,CAAtB;;EACA,QAAMoZ,iBAAiB,GAAG,GAAGnY,MAAH,CAAUwX,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACS,MAAD,CAAT,IAAqB,EAArD,CAA1B;EAEAC,IAAAA,aAAa,CAAClkB,OAAd,CAAsB,UAAAkhB,IAAI,EAAI;EAC5B,UAAI,CAACD,gBAAgB,CAACC,IAAD,EAAOiD,iBAAP,CAArB,EAAgD;EAC9CH,QAAAA,EAAE,CAACnZ,eAAH,CAAmBqW,IAAI,CAACG,QAAxB;EACD;EACF,KAJD;EA3B4D;;EAc9D,OAAK,IAAI5c,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGgf,QAAQ,CAACrf,MAA/B,EAAuCD,CAAC,GAAGM,GAA3C,EAAgDN,CAAC,EAAjD,EAAqD;EAAA,qBAA5CA,CAA4C;;EAAA,6BAOjD;EAWH;;EAED,SAAOmf,eAAe,CAACjiB,IAAhB,CAAqByiB,SAA5B;EACD;;ECjGD;EACA;EACA;EACA;EACA;;EAEA,IAAMxc,MAAI,GAAG,SAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,YAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAMuc,YAAY,GAAG,YAArB;EACA,IAAMC,kBAAkB,GAAG,IAAIjkB,MAAJ,aAAqBgkB,YAArB,WAAyC,GAAzC,CAA3B;EACA,IAAME,qBAAqB,GAAG,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAA9B;EAEA,IAAM3W,aAAW,GAAG;EAClB4W,EAAAA,SAAS,EAAE,SADO;EAElBC,EAAAA,QAAQ,EAAE,QAFQ;EAGlBC,EAAAA,KAAK,EAAE,2BAHW;EAIlB/d,EAAAA,OAAO,EAAE,QAJS;EAKlBge,EAAAA,KAAK,EAAE,iBALW;EAMlBC,EAAAA,IAAI,EAAE,SANY;EAOlBhnB,EAAAA,QAAQ,EAAE,kBAPQ;EAQlB0d,EAAAA,SAAS,EAAE,mBARO;EASlBpQ,EAAAA,MAAM,EAAE,0BATU;EAUlB0L,EAAAA,SAAS,EAAE,0BAVO;EAWlBiO,EAAAA,iBAAiB,EAAE,gBAXD;EAYlBjL,EAAAA,QAAQ,EAAE,kBAZQ;EAalBkL,EAAAA,QAAQ,EAAE,SAbQ;EAclBrB,EAAAA,UAAU,EAAE,iBAdM;EAelBD,EAAAA,SAAS,EAAE,QAfO;EAgBlB1J,EAAAA,YAAY,EAAE;EAhBI,CAApB;EAmBA,IAAMiL,aAAa,GAAG;EACpBC,EAAAA,IAAI,EAAE,MADc;EAEpBC,EAAAA,GAAG,EAAE,KAFe;EAGpBC,EAAAA,KAAK,EAAE,OAHa;EAIpBC,EAAAA,MAAM,EAAE,QAJY;EAKpBC,EAAAA,IAAI,EAAE;EALc,CAAtB;EAQA,IAAM/X,SAAO,GAAG;EACdmX,EAAAA,SAAS,EAAE,IADG;EAEdC,EAAAA,QAAQ,EAAE,yCACQ,mCADR,GAEQ,yCAJJ;EAKd9d,EAAAA,OAAO,EAAE,aALK;EAMd+d,EAAAA,KAAK,EAAE,EANO;EAOdC,EAAAA,KAAK,EAAE,CAPO;EAQdC,EAAAA,IAAI,EAAE,KARQ;EASdhnB,EAAAA,QAAQ,EAAE,KATI;EAUd0d,EAAAA,SAAS,EAAE,KAVG;EAWdpQ,EAAAA,MAAM,EAAE,CAXM;EAYd0L,EAAAA,SAAS,EAAE,KAZG;EAadiO,EAAAA,iBAAiB,EAAE,MAbL;EAcdjL,EAAAA,QAAQ,EAAE,cAdI;EAedkL,EAAAA,QAAQ,EAAE,IAfI;EAgBdrB,EAAAA,UAAU,EAAE,IAhBE;EAiBdD,EAAAA,SAAS,EAAE/B,gBAjBG;EAkBd3H,EAAAA,YAAY,EAAE;EAlBA,CAAhB;EAqBA,IAAMhb,OAAK,GAAG;EACZumB,EAAAA,IAAI,WAAStd,WADD;EAEZud,EAAAA,MAAM,aAAWvd,WAFL;EAGZwd,EAAAA,IAAI,WAASxd,WAHD;EAIZyd,EAAAA,KAAK,YAAUzd,WAJH;EAKZ0d,EAAAA,QAAQ,eAAa1d,WALT;EAMZ2d,EAAAA,KAAK,YAAU3d,WANH;EAOZ4d,EAAAA,OAAO,cAAY5d,WAPP;EAQZ6d,EAAAA,QAAQ,eAAa7d,WART;EASZ8d,EAAAA,UAAU,iBAAe9d,WATb;EAUZ+d,EAAAA,UAAU,iBAAe/d;EAVb,CAAd;EAaA,IAAMiV,iBAAe,GAAG,MAAxB;EACA,IAAM+I,gBAAgB,GAAG,OAAzB;EACA,IAAMzQ,iBAAe,GAAG,MAAxB;EAEA,IAAM0Q,gBAAgB,GAAG,MAAzB;EACA,IAAMC,eAAe,GAAG,KAAxB;EAEA,IAAMC,sBAAsB,GAAG,gBAA/B;EAEA,IAAMC,aAAa,GAAG,OAAtB;EACA,IAAMC,aAAa,GAAG,OAAtB;EACA,IAAMC,aAAa,GAAG,OAAtB;EACA,IAAMC,cAAc,GAAG,QAAvB;EAEA;EACA;EACA;EACA;EACA;;MAEMC;EACJ,mBAAY5oB,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,QAAI,OAAO8a,MAAP,KAAkB,WAAtB,EAAmC;EACjC,YAAM,IAAI9F,SAAJ,CAAc,iEAAd,CAAN;EACD,KAH0B;;;EAM3B,SAAK4R,UAAL,GAAkB,IAAlB;EACA,SAAKC,QAAL,GAAgB,CAAhB;EACA,SAAKC,WAAL,GAAmB,EAAnB;EACA,SAAKC,cAAL,GAAsB,EAAtB;EACA,SAAK3M,OAAL,GAAe,IAAf,CAV2B;;EAa3B,SAAKrc,OAAL,GAAeA,OAAf;EACA,SAAKiC,MAAL,GAAc,KAAK6Q,UAAL,CAAgB7Q,MAAhB,CAAd;EACA,SAAKgnB,GAAL,GAAW,IAAX;;EAEA,SAAKC,aAAL;;EACAnkB,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsB,KAAKwd,WAAL,CAAiBrT,QAAvC,EAAiD,IAAjD;EACD;;;;;EAgCD;WAEAgf,SAAA,kBAAS;EACP,SAAKN,UAAL,GAAkB,IAAlB;EACD;;WAEDO,UAAA,mBAAU;EACR,SAAKP,UAAL,GAAkB,KAAlB;EACD;;WAEDQ,gBAAA,yBAAgB;EACd,SAAKR,UAAL,GAAkB,CAAC,KAAKA,UAAxB;EACD;;WAEDtc,SAAA,gBAAOpG,KAAP,EAAc;EACZ,QAAI,CAAC,KAAK0iB,UAAV,EAAsB;EACpB;EACD;;EAED,QAAI1iB,KAAJ,EAAW;EACT,UAAMmjB,OAAO,GAAG,KAAK9L,WAAL,CAAiBrT,QAAjC;EACA,UAAIkU,OAAO,GAAGtZ,IAAI,CAACG,OAAL,CAAaiB,KAAK,CAACC,cAAnB,EAAmCkjB,OAAnC,CAAd;;EAEA,UAAI,CAACjL,OAAL,EAAc;EACZA,QAAAA,OAAO,GAAG,IAAI,KAAKb,WAAT,CACRrX,KAAK,CAACC,cADE,EAER,KAAKmjB,kBAAL,EAFQ,CAAV;EAIAxkB,QAAAA,IAAI,CAACC,OAAL,CAAamB,KAAK,CAACC,cAAnB,EAAmCkjB,OAAnC,EAA4CjL,OAA5C;EACD;;EAEDA,MAAAA,OAAO,CAAC2K,cAAR,CAAuBQ,KAAvB,GAA+B,CAACnL,OAAO,CAAC2K,cAAR,CAAuBQ,KAAvD;;EAEA,UAAInL,OAAO,CAACoL,oBAAR,EAAJ,EAAoC;EAClCpL,QAAAA,OAAO,CAACqL,MAAR,CAAe,IAAf,EAAqBrL,OAArB;EACD,OAFD,MAEO;EACLA,QAAAA,OAAO,CAACsL,MAAR,CAAe,IAAf,EAAqBtL,OAArB;EACD;EACF,KAnBD,MAmBO;EACL,UAAI,KAAKuL,aAAL,GAAqBre,SAArB,CAA+BE,QAA/B,CAAwCkM,iBAAxC,CAAJ,EAA8D;EAC5D,aAAKgS,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;EACA;EACD;;EAED,WAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;EACD;EACF;;WAEDre,UAAA,mBAAU;EACR0J,IAAAA,YAAY,CAAC,KAAK+T,QAAN,CAAZ;EAEA/jB,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKnF,OAArB,EAA8B,KAAKwd,WAAL,CAAiBrT,QAA/C;EAEA7D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKvG,OAAtB,EAA+B,KAAKwd,WAAL,CAAiBpT,SAAhD;EACA9D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKvG,OAAL,CAAasL,OAAb,OAAyB8c,gBAAzB,CAAjB,EAA+D,eAA/D,EAAgF,KAAKyB,iBAArF;;EAEA,QAAI,KAAKZ,GAAT,EAAc;EACZ,WAAKA,GAAL,CAASjmB,UAAT,CAAoB2I,WAApB,CAAgC,KAAKsd,GAArC;EACD;;EAED,SAAKJ,UAAL,GAAkB,IAAlB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,WAAL,GAAmB,IAAnB;EACA,SAAKC,cAAL,GAAsB,IAAtB;;EACA,QAAI,KAAK3M,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAae,OAAb;EACD;;EAED,SAAKf,OAAL,GAAe,IAAf;EACA,SAAKrc,OAAL,GAAe,IAAf;EACA,SAAKiC,MAAL,GAAc,IAAd;EACA,SAAKgnB,GAAL,GAAW,IAAX;EACD;;WAEDnQ,OAAA,gBAAO;EAAA;;EACL,QAAI,KAAK9Y,OAAL,CAAa+C,KAAb,CAAmBI,OAAnB,KAA+B,MAAnC,EAA2C;EACzC,YAAM,IAAIP,KAAJ,CAAU,qCAAV,CAAN;EACD;;EAED,QAAI,KAAKknB,aAAL,MAAwB,KAAKjB,UAAjC,EAA6C;EAC3C,UAAM/L,SAAS,GAAGxW,YAAY,CAAC0C,OAAb,CAAqB,KAAKhJ,OAA1B,EAAmC,KAAKwd,WAAL,CAAiBrc,KAAjB,CAAuBymB,IAA1D,CAAlB;EACA,UAAMmC,UAAU,GAAG1mB,cAAc,CAAC,KAAKrD,OAAN,CAAjC;EACA,UAAMgqB,UAAU,GAAGD,UAAU,KAAK,IAAf,GACjB,KAAK/pB,OAAL,CAAaiqB,aAAb,CAA2B3mB,eAA3B,CAA2CmI,QAA3C,CAAoD,KAAKzL,OAAzD,CADiB,GAEjB+pB,UAAU,CAACte,QAAX,CAAoB,KAAKzL,OAAzB,CAFF;;EAIA,UAAI8c,SAAS,CAACxT,gBAAV,IAA8B,CAAC0gB,UAAnC,EAA+C;EAC7C;EACD;;EAED,UAAMf,GAAG,GAAG,KAAKW,aAAL,EAAZ;EACA,UAAMM,KAAK,GAAG1qB,MAAM,CAAC,KAAKge,WAAL,CAAiBvT,IAAlB,CAApB;EAEAgf,MAAAA,GAAG,CAACzc,YAAJ,CAAiB,IAAjB,EAAuB0d,KAAvB;EACA,WAAKlqB,OAAL,CAAawM,YAAb,CAA0B,kBAA1B,EAA8C0d,KAA9C;EAEA,WAAKC,UAAL;;EAEA,UAAI,KAAKloB,MAAL,CAAY4kB,SAAhB,EAA2B;EACzBoC,QAAAA,GAAG,CAAC1d,SAAJ,CAAc2J,GAAd,CAAkBmK,iBAAlB;EACD;;EAED,UAAM1B,SAAS,GAAG,OAAO,KAAK1b,MAAL,CAAY0b,SAAnB,KAAiC,UAAjC,GAChB,KAAK1b,MAAL,CAAY0b,SAAZ,CAAsBte,IAAtB,CAA2B,IAA3B,EAAiC4pB,GAAjC,EAAsC,KAAKjpB,OAA3C,CADgB,GAEhB,KAAKiC,MAAL,CAAY0b,SAFd;;EAIA,UAAMyM,UAAU,GAAG,KAAKC,cAAL,CAAoB1M,SAApB,CAAnB;;EACA,WAAK2M,mBAAL,CAAyBF,UAAzB;;EAEA,UAAMnR,SAAS,GAAG,KAAKsR,aAAL,EAAlB;;EACAxlB,MAAAA,IAAI,CAACC,OAAL,CAAaikB,GAAb,EAAkB,KAAKzL,WAAL,CAAiBrT,QAAnC,EAA6C,IAA7C;;EAEA,UAAI,CAAC,KAAKnK,OAAL,CAAaiqB,aAAb,CAA2B3mB,eAA3B,CAA2CmI,QAA3C,CAAoD,KAAKwd,GAAzD,CAAL,EAAoE;EAClEhQ,QAAAA,SAAS,CAAC8H,WAAV,CAAsBkI,GAAtB;EACD;;EAED3iB,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAKhJ,OAA1B,EAAmC,KAAKwd,WAAL,CAAiBrc,KAAjB,CAAuB2mB,QAA1D;EAEA,WAAKzL,OAAL,GAAe,IAAIU,MAAJ,CAAW,KAAK/c,OAAhB,EAAyBipB,GAAzB,EAA8B,KAAKhM,gBAAL,CAAsBmN,UAAtB,CAA9B,CAAf;EAEAnB,MAAAA,GAAG,CAAC1d,SAAJ,CAAc2J,GAAd,CAAkByC,iBAAlB,EAzC2C;EA4C3C;EACA;EACA;;EACA,UAAI,kBAAkB9X,QAAQ,CAACyD,eAA/B,EAAgD;EAAA;;EAC9C,oBAAG+K,MAAH,aAAaxO,QAAQ,CAACmE,IAAT,CAAcyK,QAA3B,EAAqCpM,OAArC,CAA6C,UAAArC,OAAO,EAAI;EACtDsG,UAAAA,YAAY,CAACkC,EAAb,CAAgBxI,OAAhB,EAAyB,WAAzB,EAAsC2D,IAAI,EAA1C;EACD,SAFD;EAGD;;EAED,UAAM8V,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,YAAI,KAAI,CAACxX,MAAL,CAAY4kB,SAAhB,EAA2B;EACzB,UAAA,KAAI,CAAC2D,cAAL;EACD;;EAED,YAAMC,cAAc,GAAG,KAAI,CAAC1B,WAA5B;EACA,QAAA,KAAI,CAACA,WAAL,GAAmB,IAAnB;EAEAziB,QAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAI,CAAChJ,OAA1B,EAAmC,KAAI,CAACwd,WAAL,CAAiBrc,KAAjB,CAAuB0mB,KAA1D;;EAEA,YAAI4C,cAAc,KAAKnC,eAAvB,EAAwC;EACtC,UAAA,KAAI,CAACqB,MAAL,CAAY,IAAZ,EAAkB,KAAlB;EACD;EACF,OAbD;;EAeA,UAAI,KAAKV,GAAL,CAAS1d,SAAT,CAAmBE,QAAnB,CAA4B4T,iBAA5B,CAAJ,EAAkD;EAChD,YAAM1e,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKyoB,GAAN,CAA3D;EACA3iB,QAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKwgB,GAAtB,EAA2BjqB,cAA3B,EAA2Cya,QAA3C;EACAnY,QAAAA,oBAAoB,CAAC,KAAK2nB,GAAN,EAAWtoB,kBAAX,CAApB;EACD,OAJD,MAIO;EACL8Y,QAAAA,QAAQ;EACT;EACF;EACF;;WAEDZ,OAAA,gBAAO;EAAA;;EACL,QAAI,CAAC,KAAKwD,OAAV,EAAmB;EACjB;EACD;;EAED,QAAM4M,GAAG,GAAG,KAAKW,aAAL,EAAZ;;EACA,QAAMnQ,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,UAAI,MAAI,CAACsP,WAAL,KAAqBV,gBAArB,IAAyCY,GAAG,CAACjmB,UAAjD,EAA6D;EAC3DimB,QAAAA,GAAG,CAACjmB,UAAJ,CAAe2I,WAAf,CAA2Bsd,GAA3B;EACD;;EAED,MAAA,MAAI,CAACyB,cAAL;;EACA,MAAA,MAAI,CAAC1qB,OAAL,CAAakN,eAAb,CAA6B,kBAA7B;;EACA5G,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAChJ,OAA1B,EAAmC,MAAI,CAACwd,WAAL,CAAiBrc,KAAjB,CAAuBwmB,MAA1D;;EACA,MAAA,MAAI,CAACtL,OAAL,CAAae,OAAb;EACD,KATD;;EAWA,QAAMD,SAAS,GAAG7W,YAAY,CAAC0C,OAAb,CAAqB,KAAKhJ,OAA1B,EAAmC,KAAKwd,WAAL,CAAiBrc,KAAjB,CAAuBumB,IAA1D,CAAlB;;EACA,QAAIvK,SAAS,CAAC7T,gBAAd,EAAgC;EAC9B;EACD;;EAED2f,IAAAA,GAAG,CAAC1d,SAAJ,CAAcC,MAAd,CAAqBmM,iBAArB,EAtBK;EAyBL;;EACA,QAAI,kBAAkB9X,QAAQ,CAACyD,eAA/B,EAAgD;EAAA;;EAC9C,mBAAG+K,MAAH,cAAaxO,QAAQ,CAACmE,IAAT,CAAcyK,QAA3B,EACGpM,OADH,CACW,UAAArC,OAAO;EAAA,eAAIsG,YAAY,CAACC,GAAb,CAAiBvG,OAAjB,EAA0B,WAA1B,EAAuC2D,IAAvC,CAAJ;EAAA,OADlB;EAED;;EAED,SAAKqlB,cAAL,CAAoBN,aAApB,IAAqC,KAArC;EACA,SAAKM,cAAL,CAAoBP,aAApB,IAAqC,KAArC;EACA,SAAKO,cAAL,CAAoBR,aAApB,IAAqC,KAArC;;EAEA,QAAI,KAAKS,GAAL,CAAS1d,SAAT,CAAmBE,QAAnB,CAA4B4T,iBAA5B,CAAJ,EAAkD;EAChD,UAAM1e,kBAAkB,GAAGH,gCAAgC,CAACyoB,GAAD,CAA3D;EAEA3iB,MAAAA,YAAY,CAACmC,GAAb,CAAiBwgB,GAAjB,EAAsBjqB,cAAtB,EAAsCya,QAAtC;EACAnY,MAAAA,oBAAoB,CAAC2nB,GAAD,EAAMtoB,kBAAN,CAApB;EACD,KALD,MAKO;EACL8Y,MAAAA,QAAQ;EACT;;EAED,SAAKsP,WAAL,GAAmB,EAAnB;EACD;;WAED1L,SAAA,kBAAS;EACP,QAAI,KAAKhB,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAaiB,cAAb;EACD;EACF;;;WAIDwM,gBAAA,yBAAgB;EACd,WAAO3hB,OAAO,CAAC,KAAKwiB,QAAL,EAAD,CAAd;EACD;;WAEDf,gBAAA,yBAAgB;EACd,QAAI,KAAKX,GAAT,EAAc;EACZ,aAAO,KAAKA,GAAZ;EACD;;EAED,QAAMjpB,OAAO,GAAGH,QAAQ,CAAC0hB,aAAT,CAAuB,KAAvB,CAAhB;EACAvhB,IAAAA,OAAO,CAACymB,SAAR,GAAoB,KAAKxkB,MAAL,CAAY6kB,QAAhC;EAEA,SAAKmC,GAAL,GAAWjpB,OAAO,CAACyO,QAAR,CAAiB,CAAjB,CAAX;EACA,WAAO,KAAKwa,GAAZ;EACD;;WAEDkB,aAAA,sBAAa;EACX,QAAMlB,GAAG,GAAG,KAAKW,aAAL,EAAZ;EACA,SAAKgB,iBAAL,CAAuB1c,cAAc,CAACM,OAAf,CAAuB+Z,sBAAvB,EAA+CU,GAA/C,CAAvB,EAA4E,KAAK0B,QAAL,EAA5E;EACA1B,IAAAA,GAAG,CAAC1d,SAAJ,CAAcC,MAAd,CAAqB6T,iBAArB,EAAsC1H,iBAAtC;EACD;;WAEDiT,oBAAA,2BAAkB5qB,OAAlB,EAA2B6qB,OAA3B,EAAoC;EAClC,QAAI7qB,OAAO,KAAK,IAAhB,EAAsB;EACpB;EACD;;EAED,QAAI,OAAO6qB,OAAP,KAAmB,QAAnB,IAA+BzpB,SAAS,CAACypB,OAAD,CAA5C,EAAuD;EACrD,UAAIA,OAAO,CAAC/Q,MAAZ,EAAoB;EAClB+Q,QAAAA,OAAO,GAAGA,OAAO,CAAC,CAAD,CAAjB;EACD,OAHoD;;;EAMrD,UAAI,KAAK5oB,MAAL,CAAYglB,IAAhB,EAAsB;EACpB,YAAI4D,OAAO,CAAC7nB,UAAR,KAAuBhD,OAA3B,EAAoC;EAClCA,UAAAA,OAAO,CAACymB,SAAR,GAAoB,EAApB;EACAzmB,UAAAA,OAAO,CAAC+gB,WAAR,CAAoB8J,OAApB;EACD;EACF,OALD,MAKO;EACL7qB,QAAAA,OAAO,CAAC8qB,WAAR,GAAsBD,OAAO,CAACC,WAA9B;EACD;;EAED;EACD;;EAED,QAAI,KAAK7oB,MAAL,CAAYglB,IAAhB,EAAsB;EACpB,UAAI,KAAKhlB,MAAL,CAAYklB,QAAhB,EAA0B;EACxB0D,QAAAA,OAAO,GAAGlF,YAAY,CAACkF,OAAD,EAAU,KAAK5oB,MAAL,CAAY4jB,SAAtB,EAAiC,KAAK5jB,MAAL,CAAY6jB,UAA7C,CAAtB;EACD;;EAED9lB,MAAAA,OAAO,CAACymB,SAAR,GAAoBoE,OAApB;EACD,KAND,MAMO;EACL7qB,MAAAA,OAAO,CAAC8qB,WAAR,GAAsBD,OAAtB;EACD;EACF;;WAEDF,WAAA,oBAAW;EACT,QAAI5D,KAAK,GAAG,KAAK/mB,OAAL,CAAaE,YAAb,CAA0B,qBAA1B,CAAZ;;EAEA,QAAI,CAAC6mB,KAAL,EAAY;EACVA,MAAAA,KAAK,GAAG,OAAO,KAAK9kB,MAAL,CAAY8kB,KAAnB,KAA6B,UAA7B,GACN,KAAK9kB,MAAL,CAAY8kB,KAAZ,CAAkB1nB,IAAlB,CAAuB,KAAKW,OAA5B,CADM,GAEN,KAAKiC,MAAL,CAAY8kB,KAFd;EAGD;;EAED,WAAOA,KAAP;EACD;;;WAID9J,mBAAA,0BAAiBmN,UAAjB,EAA6B;EAAA;;EAC3B,QAAMW,eAAe,GAAG;EACtBpN,MAAAA,SAAS,EAAEyM,UADW;EAEtBtM,MAAAA,SAAS,EAAE;EACTvQ,QAAAA,MAAM,EAAE,KAAKqQ,UAAL,EADC;EAET5B,QAAAA,IAAI,EAAE;EACJgP,UAAAA,QAAQ,EAAE,KAAK/oB,MAAL,CAAYilB;EADlB,SAFG;EAKT+D,QAAAA,KAAK,EAAE;EACLjrB,UAAAA,OAAO,QAAM,KAAKwd,WAAL,CAAiBvT,IAAvB;EADF,SALE;EAQT+T,QAAAA,eAAe,EAAE;EACfC,UAAAA,iBAAiB,EAAE,KAAKhc,MAAL,CAAYga;EADhB;EARR,OAFW;EActBiP,MAAAA,QAAQ,EAAE,kBAAAxmB,IAAI,EAAI;EAChB,YAAIA,IAAI,CAACymB,iBAAL,KAA2BzmB,IAAI,CAACiZ,SAApC,EAA+C;EAC7C,UAAA,MAAI,CAACyN,4BAAL,CAAkC1mB,IAAlC;EACD;EACF,OAlBqB;EAmBtB2mB,MAAAA,QAAQ,EAAE,kBAAA3mB,IAAI;EAAA,eAAI,MAAI,CAAC0mB,4BAAL,CAAkC1mB,IAAlC,CAAJ;EAAA;EAnBQ,KAAxB;EAsBA,wBACKqmB,eADL,EAEK,KAAK9oB,MAAL,CAAYka,YAFjB;EAID;;WAEDmO,sBAAA,6BAAoBF,UAApB,EAAgC;EAC9B,SAAKR,aAAL,GAAqBre,SAArB,CAA+B2J,GAA/B,CAAsCwR,YAAtC,SAAsD0D,UAAtD;EACD;;WAEDxM,aAAA,sBAAa;EAAA;;EACX,QAAMrQ,MAAM,GAAG,EAAf;;EAEA,QAAI,OAAO,KAAKtL,MAAL,CAAYsL,MAAnB,KAA8B,UAAlC,EAA8C;EAC5CA,MAAAA,MAAM,CAACtH,EAAP,GAAY,UAAAvB,IAAI,EAAI;EAClBA,QAAAA,IAAI,CAACmZ,OAAL,gBACKnZ,IAAI,CAACmZ,OADV,EAEM,MAAI,CAAC5b,MAAL,CAAYsL,MAAZ,CAAmB7I,IAAI,CAACmZ,OAAxB,EAAiC,MAAI,CAAC7d,OAAtC,KAAkD,EAFxD;EAKA,eAAO0E,IAAP;EACD,OAPD;EAQD,KATD,MASO;EACL6I,MAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKtL,MAAL,CAAYsL,MAA5B;EACD;;EAED,WAAOA,MAAP;EACD;;WAEDgd,gBAAA,yBAAgB;EACd,QAAI,KAAKtoB,MAAL,CAAYgX,SAAZ,KAA0B,KAA9B,EAAqC;EACnC,aAAOpZ,QAAQ,CAACmE,IAAhB;EACD;;EAED,QAAI5C,SAAS,CAAC,KAAKa,MAAL,CAAYgX,SAAb,CAAb,EAAsC;EACpC,aAAO,KAAKhX,MAAL,CAAYgX,SAAnB;EACD;;EAED,WAAO/K,cAAc,CAACM,OAAf,CAAuB,KAAKvM,MAAL,CAAYgX,SAAnC,CAAP;EACD;;WAEDoR,iBAAA,wBAAe1M,SAAf,EAA0B;EACxB,WAAOyJ,aAAa,CAACzJ,SAAS,CAAC9a,WAAV,EAAD,CAApB;EACD;;WAEDqmB,gBAAA,yBAAgB;EAAA;;EACd,QAAMoC,QAAQ,GAAG,KAAKrpB,MAAL,CAAY+G,OAAZ,CAAoBhI,KAApB,CAA0B,GAA1B,CAAjB;EAEAsqB,IAAAA,QAAQ,CAACjpB,OAAT,CAAiB,UAAA2G,OAAO,EAAI;EAC1B,UAAIA,OAAO,KAAK,OAAhB,EAAyB;EACvB1C,QAAAA,YAAY,CAACkC,EAAb,CAAgB,MAAI,CAACxI,OAArB,EACE,MAAI,CAACwd,WAAL,CAAiBrc,KAAjB,CAAuB4mB,KADzB,EAEE,MAAI,CAAC9lB,MAAL,CAAYhC,QAFd,EAGE,UAAAkG,KAAK;EAAA,iBAAI,MAAI,CAACoG,MAAL,CAAYpG,KAAZ,CAAJ;EAAA,SAHP;EAKD,OAND,MAMO,IAAI6C,OAAO,KAAK2f,cAAhB,EAAgC;EACrC,YAAM4C,OAAO,GAAGviB,OAAO,KAAKwf,aAAZ,GACd,MAAI,CAAChL,WAAL,CAAiBrc,KAAjB,CAAuB+mB,UADT,GAEd,MAAI,CAAC1K,WAAL,CAAiBrc,KAAjB,CAAuB6mB,OAFzB;EAGA,YAAMwD,QAAQ,GAAGxiB,OAAO,KAAKwf,aAAZ,GACf,MAAI,CAAChL,WAAL,CAAiBrc,KAAjB,CAAuBgnB,UADR,GAEf,MAAI,CAAC3K,WAAL,CAAiBrc,KAAjB,CAAuB8mB,QAFzB;EAIA3hB,QAAAA,YAAY,CAACkC,EAAb,CAAgB,MAAI,CAACxI,OAArB,EACEurB,OADF,EAEE,MAAI,CAACtpB,MAAL,CAAYhC,QAFd,EAGE,UAAAkG,KAAK;EAAA,iBAAI,MAAI,CAACujB,MAAL,CAAYvjB,KAAZ,CAAJ;EAAA,SAHP;EAKAG,QAAAA,YAAY,CAACkC,EAAb,CAAgB,MAAI,CAACxI,OAArB,EACEwrB,QADF,EAEE,MAAI,CAACvpB,MAAL,CAAYhC,QAFd,EAGE,UAAAkG,KAAK;EAAA,iBAAI,MAAI,CAACwjB,MAAL,CAAYxjB,KAAZ,CAAJ;EAAA,SAHP;EAKD;EACF,KA1BD;;EA4BA,SAAK0jB,iBAAL,GAAyB,YAAM;EAC7B,UAAI,MAAI,CAAC7pB,OAAT,EAAkB;EAChB,QAAA,MAAI,CAAC6Y,IAAL;EACD;EACF,KAJD;;EAMAvS,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKxI,OAAL,CAAasL,OAAb,OAAyB8c,gBAAzB,CAAhB,EACE,eADF,EAEE,KAAKyB,iBAFP;;EAKA,QAAI,KAAK5nB,MAAL,CAAYhC,QAAhB,EAA0B;EACxB,WAAKgC,MAAL,gBACK,KAAKA,MADV;EAEE+G,QAAAA,OAAO,EAAE,QAFX;EAGE/I,QAAAA,QAAQ,EAAE;EAHZ;EAKD,KAND,MAMO;EACL,WAAKwrB,SAAL;EACD;EACF;;WAEDA,YAAA,qBAAY;EACV,QAAMC,SAAS,GAAG,OAAO,KAAK1rB,OAAL,CAAaE,YAAb,CAA0B,qBAA1B,CAAzB;;EAEA,QAAI,KAAKF,OAAL,CAAaE,YAAb,CAA0B,OAA1B,KAAsCwrB,SAAS,KAAK,QAAxD,EAAkE;EAChE,WAAK1rB,OAAL,CAAawM,YAAb,CACE,qBADF,EAEE,KAAKxM,OAAL,CAAaE,YAAb,CAA0B,OAA1B,KAAsC,EAFxC;EAKA,WAAKF,OAAL,CAAawM,YAAb,CAA0B,OAA1B,EAAmC,EAAnC;EACD;EACF;;WAEDkd,SAAA,gBAAOvjB,KAAP,EAAckY,OAAd,EAAuB;EACrB,QAAMiL,OAAO,GAAG,KAAK9L,WAAL,CAAiBrT,QAAjC;EACAkU,IAAAA,OAAO,GAAGA,OAAO,IAAItZ,IAAI,CAACG,OAAL,CAAaiB,KAAK,CAACC,cAAnB,EAAmCkjB,OAAnC,CAArB;;EAEA,QAAI,CAACjL,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAI,KAAKb,WAAT,CACRrX,KAAK,CAACC,cADE,EAER,KAAKmjB,kBAAL,EAFQ,CAAV;EAIAxkB,MAAAA,IAAI,CAACC,OAAL,CAAamB,KAAK,CAACC,cAAnB,EAAmCkjB,OAAnC,EAA4CjL,OAA5C;EACD;;EAED,QAAIlY,KAAJ,EAAW;EACTkY,MAAAA,OAAO,CAAC2K,cAAR,CACE7iB,KAAK,CAACK,IAAN,KAAe,SAAf,GAA2BiiB,aAA3B,GAA2CD,aAD7C,IAEI,IAFJ;EAGD;;EAED,QAAInK,OAAO,CAACuL,aAAR,GAAwBre,SAAxB,CAAkCE,QAAlC,CAA2CkM,iBAA3C,KACA0G,OAAO,CAAC0K,WAAR,KAAwBV,gBAD5B,EAC8C;EAC5ChK,MAAAA,OAAO,CAAC0K,WAAR,GAAsBV,gBAAtB;EACA;EACD;;EAEDtT,IAAAA,YAAY,CAACsJ,OAAO,CAACyK,QAAT,CAAZ;EAEAzK,IAAAA,OAAO,CAAC0K,WAAR,GAAsBV,gBAAtB;;EAEA,QAAI,CAAChK,OAAO,CAACpc,MAAR,CAAe+kB,KAAhB,IAAyB,CAAC3I,OAAO,CAACpc,MAAR,CAAe+kB,KAAf,CAAqBlO,IAAnD,EAAyD;EACvDuF,MAAAA,OAAO,CAACvF,IAAR;EACA;EACD;;EAEDuF,IAAAA,OAAO,CAACyK,QAAR,GAAmBhnB,UAAU,CAAC,YAAM;EAClC,UAAIuc,OAAO,CAAC0K,WAAR,KAAwBV,gBAA5B,EAA8C;EAC5ChK,QAAAA,OAAO,CAACvF,IAAR;EACD;EACF,KAJ4B,EAI1BuF,OAAO,CAACpc,MAAR,CAAe+kB,KAAf,CAAqBlO,IAJK,CAA7B;EAKD;;WAED6Q,SAAA,gBAAOxjB,KAAP,EAAckY,OAAd,EAAuB;EACrB,QAAMiL,OAAO,GAAG,KAAK9L,WAAL,CAAiBrT,QAAjC;EACAkU,IAAAA,OAAO,GAAGA,OAAO,IAAItZ,IAAI,CAACG,OAAL,CAAaiB,KAAK,CAACC,cAAnB,EAAmCkjB,OAAnC,CAArB;;EAEA,QAAI,CAACjL,OAAL,EAAc;EACZA,MAAAA,OAAO,GAAG,IAAI,KAAKb,WAAT,CACRrX,KAAK,CAACC,cADE,EAER,KAAKmjB,kBAAL,EAFQ,CAAV;EAIAxkB,MAAAA,IAAI,CAACC,OAAL,CAAamB,KAAK,CAACC,cAAnB,EAAmCkjB,OAAnC,EAA4CjL,OAA5C;EACD;;EAED,QAAIlY,KAAJ,EAAW;EACTkY,MAAAA,OAAO,CAAC2K,cAAR,CACE7iB,KAAK,CAACK,IAAN,KAAe,UAAf,GAA4BiiB,aAA5B,GAA4CD,aAD9C,IAEI,KAFJ;EAGD;;EAED,QAAInK,OAAO,CAACoL,oBAAR,EAAJ,EAAoC;EAClC;EACD;;EAED1U,IAAAA,YAAY,CAACsJ,OAAO,CAACyK,QAAT,CAAZ;EAEAzK,IAAAA,OAAO,CAAC0K,WAAR,GAAsBT,eAAtB;;EAEA,QAAI,CAACjK,OAAO,CAACpc,MAAR,CAAe+kB,KAAhB,IAAyB,CAAC3I,OAAO,CAACpc,MAAR,CAAe+kB,KAAf,CAAqBnO,IAAnD,EAAyD;EACvDwF,MAAAA,OAAO,CAACxF,IAAR;EACA;EACD;;EAEDwF,IAAAA,OAAO,CAACyK,QAAR,GAAmBhnB,UAAU,CAAC,YAAM;EAClC,UAAIuc,OAAO,CAAC0K,WAAR,KAAwBT,eAA5B,EAA6C;EAC3CjK,QAAAA,OAAO,CAACxF,IAAR;EACD;EACF,KAJ4B,EAI1BwF,OAAO,CAACpc,MAAR,CAAe+kB,KAAf,CAAqBnO,IAJK,CAA7B;EAKD;;WAED4Q,uBAAA,gCAAuB;EACrB,SAAK,IAAMzgB,OAAX,IAAsB,KAAKggB,cAA3B,EAA2C;EACzC,UAAI,KAAKA,cAAL,CAAoBhgB,OAApB,CAAJ,EAAkC;EAChC,eAAO,IAAP;EACD;EACF;;EAED,WAAO,KAAP;EACD;;WAED8J,aAAA,oBAAW7Q,MAAX,EAAmB;EACjB,QAAM0pB,cAAc,GAAG5e,WAAW,CAACI,iBAAZ,CAA8B,KAAKnN,OAAnC,CAAvB;EAEAmC,IAAAA,MAAM,CAACC,IAAP,CAAYupB,cAAZ,EAA4BtpB,OAA5B,CAAoC,UAAAupB,QAAQ,EAAI;EAC9C,UAAIhF,qBAAqB,CAAC9e,OAAtB,CAA8B8jB,QAA9B,MAA4C,CAAC,CAAjD,EAAoD;EAClD,eAAOD,cAAc,CAACC,QAAD,CAArB;EACD;EACF,KAJD;;EAMA,QAAI3pB,MAAM,IAAI,OAAOA,MAAM,CAACgX,SAAd,KAA4B,QAAtC,IAAkDhX,MAAM,CAACgX,SAAP,CAAiBa,MAAvE,EAA+E;EAC7E7X,MAAAA,MAAM,CAACgX,SAAP,GAAmBhX,MAAM,CAACgX,SAAP,CAAiB,CAAjB,CAAnB;EACD;;EAEDhX,IAAAA,MAAM,gBACD,KAAKub,WAAL,CAAiB9N,OADhB,EAEDic,cAFC,EAGA,OAAO1pB,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHhD,CAAN;;EAMA,QAAI,OAAOA,MAAM,CAAC+kB,KAAd,KAAwB,QAA5B,EAAsC;EACpC/kB,MAAAA,MAAM,CAAC+kB,KAAP,GAAe;EACblO,QAAAA,IAAI,EAAE7W,MAAM,CAAC+kB,KADA;EAEbnO,QAAAA,IAAI,EAAE5W,MAAM,CAAC+kB;EAFA,OAAf;EAID;;EAED,QAAI,OAAO/kB,MAAM,CAAC8kB,KAAd,KAAwB,QAA5B,EAAsC;EACpC9kB,MAAAA,MAAM,CAAC8kB,KAAP,GAAe9kB,MAAM,CAAC8kB,KAAP,CAAa3nB,QAAb,EAAf;EACD;;EAED,QAAI,OAAO6C,MAAM,CAAC4oB,OAAd,KAA0B,QAA9B,EAAwC;EACtC5oB,MAAAA,MAAM,CAAC4oB,OAAP,GAAiB5oB,MAAM,CAAC4oB,OAAP,CAAezrB,QAAf,EAAjB;EACD;;EAED2C,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAe,KAAKub,WAAL,CAAiBvN,WAAhC,CAAf;;EAEA,QAAIhO,MAAM,CAACklB,QAAX,EAAqB;EACnBllB,MAAAA,MAAM,CAAC6kB,QAAP,GAAkBnB,YAAY,CAAC1jB,MAAM,CAAC6kB,QAAR,EAAkB7kB,MAAM,CAAC4jB,SAAzB,EAAoC5jB,MAAM,CAAC6jB,UAA3C,CAA9B;EACD;;EAED,WAAO7jB,MAAP;EACD;;WAEDsnB,qBAAA,8BAAqB;EACnB,QAAMtnB,MAAM,GAAG,EAAf;;EAEA,QAAI,KAAKA,MAAT,EAAiB;EACf,WAAK,IAAMwC,GAAX,IAAkB,KAAKxC,MAAvB,EAA+B;EAC7B,YAAI,KAAKub,WAAL,CAAiB9N,OAAjB,CAAyBjL,GAAzB,MAAkC,KAAKxC,MAAL,CAAYwC,GAAZ,CAAtC,EAAwD;EACtDxC,UAAAA,MAAM,CAACwC,GAAD,CAAN,GAAc,KAAKxC,MAAL,CAAYwC,GAAZ,CAAd;EACD;EACF;EACF;;EAED,WAAOxC,MAAP;EACD;;WAEDyoB,iBAAA,0BAAiB;EACf,QAAMzB,GAAG,GAAG,KAAKW,aAAL,EAAZ;EACA,QAAMiC,QAAQ,GAAG5C,GAAG,CAAC/oB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgCqnB,kBAAhC,CAAjB;;EACA,QAAIkF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAC9kB,MAAT,GAAkB,CAA3C,EAA8C;EAC5C8kB,MAAAA,QAAQ,CAACC,GAAT,CAAa,UAAAC,KAAK;EAAA,eAAIA,KAAK,CAAC3rB,IAAN,EAAJ;EAAA,OAAlB,EACGiC,OADH,CACW,UAAA2pB,MAAM;EAAA,eAAI/C,GAAG,CAAC1d,SAAJ,CAAcC,MAAd,CAAqBwgB,MAArB,CAAJ;EAAA,OADjB;EAED;EACF;;WAEDZ,+BAAA,sCAA6Ba,UAA7B,EAAyC;EACvC,SAAKhD,GAAL,GAAWgD,UAAU,CAAChnB,QAAX,CAAoBinB,MAA/B;;EACA,SAAKxB,cAAL;;EACA,SAAKJ,mBAAL,CAAyB,KAAKD,cAAL,CAAoB4B,UAAU,CAACtO,SAA/B,CAAzB;EACD;;WAED6M,iBAAA,0BAAiB;EACf,QAAMvB,GAAG,GAAG,KAAKW,aAAL,EAAZ;EACA,QAAMuC,mBAAmB,GAAG,KAAKlqB,MAAL,CAAY4kB,SAAxC;;EACA,QAAIoC,GAAG,CAAC/oB,YAAJ,CAAiB,aAAjB,MAAoC,IAAxC,EAA8C;EAC5C;EACD;;EAED+oB,IAAAA,GAAG,CAAC1d,SAAJ,CAAcC,MAAd,CAAqB6T,iBAArB;EACA,SAAKpd,MAAL,CAAY4kB,SAAZ,GAAwB,KAAxB;EACA,SAAKhO,IAAL;EACA,SAAKC,IAAL;EACA,SAAK7W,MAAL,CAAY4kB,SAAZ,GAAwBsF,mBAAxB;EACD;;;YAIMvgB,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;EACA,UAAM0I,OAAO,GAAG,OAAO5Q,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAACyC,IAAD,IAAS,eAAe/B,IAAf,CAAoBV,MAApB,CAAb,EAA0C;EACxC;EACD;;EAED,UAAI,CAACyC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIkkB,OAAJ,CAAY,IAAZ,EAAkB/V,OAAlB,CAAP;EACD;;EAED,UAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ;EACD;EACF,KAnBM,CAAP;EAoBD;;YAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BAroBoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOwF,SAAP;EACD;;;0BAEiB;EAChB,aAAOzF,MAAP;EACD;;;0BAEqB;EACpB,aAAOE,UAAP;EACD;;;0BAEkB;EACjB,aAAOhJ,OAAP;EACD;;;0BAEsB;EACrB,aAAOiJ,WAAP;EACD;;;0BAEwB;EACvB,aAAO6F,aAAP;EACD;;;;;EA8mBH;EACA;EACA;EACA;EACA;EACA;;;EAEA/L,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAa2e,OAAO,CAAChd,eAArB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyB0c,OAAzB;;EACA1f,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAO2c,OAAO,CAAChd,eAAf;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;ECvxBA;EACA;EACA;EACA;EACA;;EAEA,IAAM3B,MAAI,GAAG,SAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,YAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAMuc,cAAY,GAAG,YAArB;EACA,IAAMC,oBAAkB,GAAG,IAAIjkB,MAAJ,aAAqBgkB,cAArB,WAAyC,GAAzC,CAA3B;;EAEA,IAAMhX,SAAO,gBACRkZ,OAAO,CAAClZ,OADA;EAEXiO,EAAAA,SAAS,EAAE,OAFA;EAGX3U,EAAAA,OAAO,EAAE,OAHE;EAIX6hB,EAAAA,OAAO,EAAE,EAJE;EAKX/D,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEE,kCAFF,GAGE;EARD,EAAb;;EAWA,IAAM7W,aAAW,gBACZ2Y,OAAO,CAAC3Y,WADI;EAEf4a,EAAAA,OAAO,EAAE;EAFM,EAAjB;;EAKA,IAAM1pB,OAAK,GAAG;EACZumB,EAAAA,IAAI,WAAStd,WADD;EAEZud,EAAAA,MAAM,aAAWvd,WAFL;EAGZwd,EAAAA,IAAI,WAASxd,WAHD;EAIZyd,EAAAA,KAAK,YAAUzd,WAJH;EAKZ0d,EAAAA,QAAQ,eAAa1d,WALT;EAMZ2d,EAAAA,KAAK,YAAU3d,WANH;EAOZ4d,EAAAA,OAAO,cAAY5d,WAPP;EAQZ6d,EAAAA,QAAQ,eAAa7d,WART;EASZ8d,EAAAA,UAAU,iBAAe9d,WATb;EAUZ+d,EAAAA,UAAU,iBAAe/d;EAVb,CAAd;EAaA,IAAMiV,iBAAe,GAAG,MAAxB;EACA,IAAM1H,iBAAe,GAAG,MAAxB;EAEA,IAAMyU,cAAc,GAAG,iBAAvB;EACA,IAAMC,gBAAgB,GAAG,eAAzB;EAEA;EACA;EACA;EACA;EACA;;MAEMC;;;;;;;;;EA+BJ;WAEAxC,gBAAA,yBAAgB;EACd,WAAO,KAAKa,QAAL,MAAmB,KAAK4B,WAAL,EAA1B;EACD;;WAEDpC,aAAA,sBAAa;EACX,QAAMlB,GAAG,GAAG,KAAKW,aAAL,EAAZ,CADW;;EAIX,SAAKgB,iBAAL,CAAuB1c,cAAc,CAACM,OAAf,CAAuB4d,cAAvB,EAAuCnD,GAAvC,CAAvB,EAAoE,KAAK0B,QAAL,EAApE;;EACA,QAAIE,OAAO,GAAG,KAAK0B,WAAL,EAAd;;EACA,QAAI,OAAO1B,OAAP,KAAmB,UAAvB,EAAmC;EACjCA,MAAAA,OAAO,GAAGA,OAAO,CAACxrB,IAAR,CAAa,KAAKW,OAAlB,CAAV;EACD;;EAED,SAAK4qB,iBAAL,CAAuB1c,cAAc,CAACM,OAAf,CAAuB6d,gBAAvB,EAAyCpD,GAAzC,CAAvB,EAAsE4B,OAAtE;EAEA5B,IAAAA,GAAG,CAAC1d,SAAJ,CAAcC,MAAd,CAAqB6T,iBAArB,EAAsC1H,iBAAtC;EACD;;;WAID2S,sBAAA,6BAAoBF,UAApB,EAAgC;EAC9B,SAAKR,aAAL,GAAqBre,SAArB,CAA+B2J,GAA/B,CAAsCwR,cAAtC,SAAsD0D,UAAtD;EACD;;WAEDmC,cAAA,uBAAc;EACZ,WAAO,KAAKvsB,OAAL,CAAaE,YAAb,CAA0B,cAA1B,KACL,KAAK+B,MAAL,CAAY4oB,OADd;EAED;;WAEDH,iBAAA,0BAAiB;EACf,QAAMzB,GAAG,GAAG,KAAKW,aAAL,EAAZ;EACA,QAAMiC,QAAQ,GAAG5C,GAAG,CAAC/oB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgCqnB,oBAAhC,CAAjB;;EACA,QAAIkF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAC9kB,MAAT,GAAkB,CAA3C,EAA8C;EAC5C8kB,MAAAA,QAAQ,CAACC,GAAT,CAAa,UAAAC,KAAK;EAAA,eAAIA,KAAK,CAAC3rB,IAAN,EAAJ;EAAA,OAAlB,EACGiC,OADH,CACW,UAAA2pB,MAAM;EAAA,eAAI/C,GAAG,CAAC1d,SAAJ,CAAcC,MAAd,CAAqBwgB,MAArB,CAAJ;EAAA,OADjB;EAED;EACF;;;YAIMpgB,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;EACA,UAAM0I,OAAO,GAAG,OAAO5Q,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;EAEA,UAAI,CAACyC,IAAD,IAAS,eAAe/B,IAAf,CAAoBV,MAApB,CAAb,EAA0C;EACxC;EACD;;EAED,UAAI,CAACyC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI4nB,OAAJ,CAAY,IAAZ,EAAkBzZ,OAAlB,CAAP;EACA9N,QAAAA,IAAI,CAACC,OAAL,CAAa,IAAb,EAAmBmF,UAAnB,EAA6BzF,IAA7B;EACD;;EAED,UAAI,OAAOzC,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ;EACD;EACF,KApBM,CAAP;EAqBD;;YAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;EAnGD;0BAEqB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOwF,SAAP;EACD;;;0BAEiB;EAChB,aAAOzF,MAAP;EACD;;;0BAEqB;EACpB,aAAOE,UAAP;EACD;;;0BAEkB;EACjB,aAAOhJ,OAAP;EACD;;;0BAEsB;EACrB,aAAOiJ,WAAP;EACD;;;0BAEwB;EACvB,aAAO6F,aAAP;EACD;;;;IA7BmB2Y;EAuGtB;EACA;EACA;EACA;EACA;EACA;;;EAEA1kB,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAaqiB,OAAO,CAAC1gB,eAArB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBogB,OAAzB;;EACApjB,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOqgB,OAAO,CAAC1gB,eAAf;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;EC5JA;EACA;EACA;EACA;EACA;;EAEA,IAAM3B,MAAI,GAAG,WAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,cAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAMqF,SAAO,GAAG;EACdnC,EAAAA,MAAM,EAAE,EADM;EAEdif,EAAAA,MAAM,EAAE,MAFM;EAGd3lB,EAAAA,MAAM,EAAE;EAHM,CAAhB;EAMA,IAAMoJ,aAAW,GAAG;EAClB1C,EAAAA,MAAM,EAAE,QADU;EAElBif,EAAAA,MAAM,EAAE,QAFU;EAGlB3lB,EAAAA,MAAM,EAAE;EAHU,CAApB;EAMA,IAAM4lB,cAAc,gBAAcriB,WAAlC;EACA,IAAMsiB,YAAY,cAAYtiB,WAA9B;EACA,IAAM6G,qBAAmB,YAAU7G,WAAV,GAAsBC,cAA/C;EAEA,IAAMsiB,wBAAwB,GAAG,eAAjC;EACA,IAAMvgB,mBAAiB,GAAG,QAA1B;EAEA,IAAMwgB,iBAAiB,GAAG,qBAA1B;EACA,IAAMC,uBAAuB,GAAG,mBAAhC;EACA,IAAMC,kBAAkB,GAAG,WAA3B;EACA,IAAMC,kBAAkB,GAAG,WAA3B;EACA,IAAMC,mBAAmB,GAAG,kBAA5B;EACA,IAAMC,iBAAiB,GAAG,WAA1B;EACA,IAAMC,wBAAwB,GAAG,kBAAjC;EAEA,IAAMC,aAAa,GAAG,QAAtB;EACA,IAAMC,eAAe,GAAG,UAAxB;EAEA;EACA;EACA;EACA;EACA;;MAEMC;EACJ,qBAAYrtB,OAAZ,EAAqBiC,MAArB,EAA6B;EAAA;;EAC3B,SAAK6I,QAAL,GAAgB9K,OAAhB;EACA,SAAKstB,cAAL,GAAsBttB,OAAO,CAACmV,OAAR,KAAoB,MAApB,GAA6B1U,MAA7B,GAAsCT,OAA5D;EACA,SAAK6S,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;EACA,SAAKwW,SAAL,GAAoB,KAAK5F,OAAL,CAAahM,MAAjC,SAA2CimB,kBAA3C,UAAkE,KAAKja,OAAL,CAAahM,MAA/E,SAAyFmmB,mBAAzF,UAAiH,KAAKna,OAAL,CAAahM,MAA9H,UAAyI8lB,wBAAzI;EACA,SAAKY,QAAL,GAAgB,EAAhB;EACA,SAAKC,QAAL,GAAgB,EAAhB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACA,SAAKC,aAAL,GAAqB,CAArB;EAEApnB,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAK8kB,cAArB,EAAqCZ,YAArC,EAAmD,UAAAvmB,KAAK;EAAA,aAAI,KAAI,CAACwnB,QAAL,CAAcxnB,KAAd,CAAJ;EAAA,KAAxD;EAEA,SAAKynB,OAAL;;EACA,SAAKD,QAAL;;EAEA5oB,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;EACD;;;;;EAYD;WAEAyjB,UAAA,mBAAU;EAAA;;EACR,QAAMC,UAAU,GAAG,KAAKP,cAAL,KAAwB,KAAKA,cAAL,CAAoB7sB,MAA5C,GACjB0sB,aADiB,GAEjBC,eAFF;EAIA,QAAMU,YAAY,GAAG,KAAKjb,OAAL,CAAa2Z,MAAb,KAAwB,MAAxB,GACnBqB,UADmB,GAEnB,KAAKhb,OAAL,CAAa2Z,MAFf;EAIA,QAAMuB,UAAU,GAAGD,YAAY,KAAKV,eAAjB,GACjB,KAAKY,aAAL,EADiB,GAEjB,CAFF;EAIA,SAAKT,QAAL,GAAgB,EAAhB;EACA,SAAKC,QAAL,GAAgB,EAAhB;EACA,SAAKE,aAAL,GAAqB,KAAKO,gBAAL,EAArB;EAEA,QAAMC,OAAO,GAAGhgB,cAAc,CAACE,IAAf,CAAoB,KAAKqK,SAAzB,CAAhB;EAEAyV,IAAAA,OAAO,CAACpC,GAAR,CAAY,UAAA9rB,OAAO,EAAI;EACrB,UAAMmuB,cAAc,GAAG9tB,sBAAsB,CAACL,OAAD,CAA7C;EACA,UAAM6G,MAAM,GAAGsnB,cAAc,GAAGjgB,cAAc,CAACM,OAAf,CAAuB2f,cAAvB,CAAH,GAA4C,IAAzE;;EAEA,UAAItnB,MAAJ,EAAY;EACV,YAAMunB,SAAS,GAAGvnB,MAAM,CAAC4G,qBAAP,EAAlB;;EACA,YAAI2gB,SAAS,CAACpL,KAAV,IAAmBoL,SAAS,CAACC,MAAjC,EAAyC;EACvC,iBAAO,CACLthB,WAAW,CAAC+gB,YAAD,CAAX,CAA0BjnB,MAA1B,EAAkC6G,GAAlC,GAAwCqgB,UADnC,EAELI,cAFK,CAAP;EAID;EACF;;EAED,aAAO,IAAP;EACD,KAfD,EAgBGzf,MAhBH,CAgBU,UAAA4f,IAAI;EAAA,aAAIA,IAAJ;EAAA,KAhBd,EAiBGC,IAjBH,CAiBQ,UAACxK,CAAD,EAAIE,CAAJ;EAAA,aAAUF,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAC,CAAC,CAAD,CAAlB;EAAA,KAjBR,EAkBG5hB,OAlBH,CAkBW,UAAAisB,IAAI,EAAI;EACf,MAAA,MAAI,CAACf,QAAL,CAAcve,IAAd,CAAmBsf,IAAI,CAAC,CAAD,CAAvB;;EACA,MAAA,MAAI,CAACd,QAAL,CAAcxe,IAAd,CAAmBsf,IAAI,CAAC,CAAD,CAAvB;EACD,KArBH;EAsBD;;WAEDjjB,UAAA,mBAAU;EACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EACA7D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK+mB,cAAtB,EAAsCljB,WAAtC;EAEA,SAAKU,QAAL,GAAgB,IAAhB;EACA,SAAKwiB,cAAL,GAAsB,IAAtB;EACA,SAAKza,OAAL,GAAe,IAAf;EACA,SAAK4F,SAAL,GAAiB,IAAjB;EACA,SAAK8U,QAAL,GAAgB,IAAhB;EACA,SAAKC,QAAL,GAAgB,IAAhB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACA,SAAKC,aAAL,GAAqB,IAArB;EACD;;;WAID5a,aAAA,oBAAW7Q,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACDyN,SADC,EAEA,OAAOzN,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAFhD,CAAN;;EAKA,QAAI,OAAOA,MAAM,CAAC4E,MAAd,KAAyB,QAAzB,IAAqCzF,SAAS,CAACa,MAAM,CAAC4E,MAAR,CAAlD,EAAmE;EAAA,UAC3DtC,EAD2D,GACpDtC,MAAM,CAAC4E,MAD6C,CAC3DtC,EAD2D;;EAEjE,UAAI,CAACA,EAAL,EAAS;EACPA,QAAAA,EAAE,GAAG/E,MAAM,CAACyK,MAAD,CAAX;EACAhI,QAAAA,MAAM,CAAC4E,MAAP,CAActC,EAAd,GAAmBA,EAAnB;EACD;;EAEDtC,MAAAA,MAAM,CAAC4E,MAAP,SAAoBtC,EAApB;EACD;;EAEDxC,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAegO,aAAf,CAAf;EAEA,WAAOhO,MAAP;EACD;;WAED+rB,gBAAA,yBAAgB;EACd,WAAO,KAAKV,cAAL,KAAwB7sB,MAAxB,GACL,KAAK6sB,cAAL,CAAoBkB,WADf,GAEL,KAAKlB,cAAL,CAAoB3f,SAFtB;EAGD;;WAEDsgB,mBAAA,4BAAmB;EACjB,WAAO,KAAKX,cAAL,CAAoBzL,YAApB,IAAoCniB,IAAI,CAAC+uB,GAAL,CACzC5uB,QAAQ,CAACmE,IAAT,CAAc6d,YAD2B,EAEzChiB,QAAQ,CAACyD,eAAT,CAAyBue,YAFgB,CAA3C;EAID;;WAED6M,mBAAA,4BAAmB;EACjB,WAAO,KAAKpB,cAAL,KAAwB7sB,MAAxB,GACLA,MAAM,CAACkuB,WADF,GAEL,KAAKrB,cAAL,CAAoB7f,qBAApB,GAA4C4gB,MAF9C;EAGD;;WAEDV,WAAA,oBAAW;EACT,QAAMhgB,SAAS,GAAG,KAAKqgB,aAAL,KAAuB,KAAKnb,OAAL,CAAatF,MAAtD;;EACA,QAAMsU,YAAY,GAAG,KAAKoM,gBAAL,EAArB;;EACA,QAAMW,SAAS,GAAG,KAAK/b,OAAL,CAAatF,MAAb,GAChBsU,YADgB,GAEhB,KAAK6M,gBAAL,EAFF;;EAIA,QAAI,KAAKhB,aAAL,KAAuB7L,YAA3B,EAAyC;EACvC,WAAK+L,OAAL;EACD;;EAED,QAAIjgB,SAAS,IAAIihB,SAAjB,EAA4B;EAC1B,UAAM/nB,MAAM,GAAG,KAAK2mB,QAAL,CAAc,KAAKA,QAAL,CAAczmB,MAAd,GAAuB,CAArC,CAAf;;EAEA,UAAI,KAAK0mB,aAAL,KAAuB5mB,MAA3B,EAAmC;EACjC,aAAKgoB,SAAL,CAAehoB,MAAf;EACD;;EAED;EACD;;EAED,QAAI,KAAK4mB,aAAL,IAAsB9f,SAAS,GAAG,KAAK4f,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;EAC9E,WAAKE,aAAL,GAAqB,IAArB;;EACA,WAAKqB,MAAL;;EACA;EACD;;EAED,SAAK,IAAIhoB,CAAC,GAAG,KAAKymB,QAAL,CAAcxmB,MAA3B,EAAmCD,CAAC,EAApC,GAAyC;EACvC,UAAMioB,cAAc,GAAG,KAAKtB,aAAL,KAAuB,KAAKD,QAAL,CAAc1mB,CAAd,CAAvB,IACnB6G,SAAS,IAAI,KAAK4f,QAAL,CAAczmB,CAAd,CADM,KAElB,OAAO,KAAKymB,QAAL,CAAczmB,CAAC,GAAG,CAAlB,CAAP,KAAgC,WAAhC,IACG6G,SAAS,GAAG,KAAK4f,QAAL,CAAczmB,CAAC,GAAG,CAAlB,CAHG,CAAvB;;EAKA,UAAIioB,cAAJ,EAAoB;EAClB,aAAKF,SAAL,CAAe,KAAKrB,QAAL,CAAc1mB,CAAd,CAAf;EACD;EACF;EACF;;WAED+nB,YAAA,mBAAUhoB,MAAV,EAAkB;EAChB,SAAK4mB,aAAL,GAAqB5mB,MAArB;;EAEA,SAAKioB,MAAL;;EAEA,QAAME,OAAO,GAAG,KAAKvW,SAAL,CAAezX,KAAf,CAAqB,GAArB,EACb8qB,GADa,CACT,UAAA7rB,QAAQ;EAAA,aAAOA,QAAP,uBAAgC4G,MAAhC,YAA4C5G,QAA5C,gBAA8D4G,MAA9D;EAAA,KADC,CAAhB;;EAGA,QAAMooB,IAAI,GAAG/gB,cAAc,CAACM,OAAf,CAAuBwgB,OAAO,CAACE,IAAR,CAAa,GAAb,CAAvB,CAAb;;EAEA,QAAID,IAAI,CAAC1jB,SAAL,CAAeE,QAAf,CAAwBkhB,wBAAxB,CAAJ,EAAuD;EACrDze,MAAAA,cAAc,CAACM,OAAf,CAAuB0e,wBAAvB,EAAiD+B,IAAI,CAAC3jB,OAAL,CAAa2hB,iBAAb,CAAjD,EACG1hB,SADH,CACa2J,GADb,CACiB9I,mBADjB;EAGA6iB,MAAAA,IAAI,CAAC1jB,SAAL,CAAe2J,GAAf,CAAmB9I,mBAAnB;EACD,KALD,MAKO;EACL;EACA6iB,MAAAA,IAAI,CAAC1jB,SAAL,CAAe2J,GAAf,CAAmB9I,mBAAnB;EAEA8B,MAAAA,cAAc,CAACU,OAAf,CAAuBqgB,IAAvB,EAA6BpC,uBAA7B,EACGxqB,OADH,CACW,UAAA8sB,SAAS,EAAI;EACpB;EACA;EACAjhB,QAAAA,cAAc,CAACe,IAAf,CAAoBkgB,SAApB,EAAkCrC,kBAAlC,UAAyDE,mBAAzD,EACG3qB,OADH,CACW,UAAAisB,IAAI;EAAA,iBAAIA,IAAI,CAAC/iB,SAAL,CAAe2J,GAAf,CAAmB9I,mBAAnB,CAAJ;EAAA,SADf,EAHoB;;EAOpB8B,QAAAA,cAAc,CAACe,IAAf,CAAoBkgB,SAApB,EAA+BpC,kBAA/B,EACG1qB,OADH,CACW,UAAA+sB,OAAO,EAAI;EAClBlhB,UAAAA,cAAc,CAACO,QAAf,CAAwB2gB,OAAxB,EAAiCtC,kBAAjC,EACGzqB,OADH,CACW,UAAAisB,IAAI;EAAA,mBAAIA,IAAI,CAAC/iB,SAAL,CAAe2J,GAAf,CAAmB9I,mBAAnB,CAAJ;EAAA,WADf;EAED,SAJH;EAKD,OAbH;EAcD;;EAED9F,IAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAKskB,cAA1B,EAA0Cb,cAA1C,EAA0D;EACxD5W,MAAAA,aAAa,EAAEhP;EADyC,KAA1D;EAGD;;WAEDioB,SAAA,kBAAS;EACP5gB,IAAAA,cAAc,CAACE,IAAf,CAAoB,KAAKqK,SAAzB,EACG/J,MADH,CACU,UAAA2gB,IAAI;EAAA,aAAIA,IAAI,CAAC9jB,SAAL,CAAeE,QAAf,CAAwBW,mBAAxB,CAAJ;EAAA,KADd,EAEG/J,OAFH,CAEW,UAAAgtB,IAAI;EAAA,aAAIA,IAAI,CAAC9jB,SAAL,CAAeC,MAAf,CAAsBY,mBAAtB,CAAJ;EAAA,KAFf;EAGD;;;cAIMR,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;EACA,UAAM0I,OAAO,GAAG,OAAO5Q,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAACyC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI2oB,SAAJ,CAAc,IAAd,EAAoBxa,OAApB,CAAP;EACD;;EAED,UAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ;EACD;EACF,KAfM,CAAP;EAgBD;;cAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BAzNoB;EACnB,aAAOD,SAAP;EACD;;;0BAEoB;EACnB,aAAOwF,SAAP;EACD;;;;;EAsNH;EACA;EACA;EACA;EACA;;;EAEApJ,YAAY,CAACkC,EAAb,CAAgB/H,MAAhB,EAAwBwQ,qBAAxB,EAA6C,YAAM;EACjD/C,EAAAA,cAAc,CAACE,IAAf,CAAoBwe,iBAApB,EACGvqB,OADH,CACW,UAAAitB,GAAG;EAAA,WAAI,IAAIjC,SAAJ,CAAciC,GAAd,EAAmBviB,WAAW,CAACI,iBAAZ,CAA8BmiB,GAA9B,CAAnB,CAAJ;EAAA,GADd;EAED,CAHD;EAKA;EACA;EACA;EACA;EACA;EACA;;EAEAprB,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAaojB,SAAS,CAACzhB,eAAvB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBmhB,SAAzB;;EACAnkB,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOohB,SAAS,CAACzhB,eAAjB;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;ECnTA;EACA;EACA;EACA;EACA;;EAEA,IAAM3B,MAAI,GAAG,KAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,QAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EACA,IAAME,cAAY,GAAG,WAArB;EAEA,IAAMoN,YAAU,YAAUrN,WAA1B;EACA,IAAMsN,cAAY,cAAYtN,WAA9B;EACA,IAAMmN,YAAU,YAAUnN,WAA1B;EACA,IAAMoN,aAAW,aAAWpN,WAA5B;EACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;EAEA,IAAMklB,wBAAwB,GAAG,eAAjC;EACA,IAAMnjB,mBAAiB,GAAG,QAA1B;EACA,IAAM2O,qBAAmB,GAAG,UAA5B;EACA,IAAMsE,iBAAe,GAAG,MAAxB;EACA,IAAM1H,iBAAe,GAAG,MAAxB;EAEA,IAAMsV,mBAAiB,GAAG,WAA1B;EACA,IAAMJ,yBAAuB,GAAG,mBAAhC;EACA,IAAMpb,iBAAe,GAAG,SAAxB;EACA,IAAM+d,kBAAkB,GAAG,uBAA3B;EACA,IAAMnjB,sBAAoB,GAAG,iEAA7B;EACA,IAAM6gB,0BAAwB,GAAG,kBAAjC;EACA,IAAMuC,8BAA8B,GAAG,iCAAvC;EAEA;EACA;EACA;EACA;EACA;;MAEMC;EACJ,eAAY1vB,OAAZ,EAAqB;EACnB,SAAK8K,QAAL,GAAgB9K,OAAhB;EAEA+E,IAAAA,IAAI,CAACC,OAAL,CAAa,KAAK8F,QAAlB,EAA4BX,UAA5B,EAAsC,IAAtC;EACD;;;;;EAQD;WAEA2O,OAAA,gBAAO;EAAA;;EACL,QAAK,KAAKhO,QAAL,CAAc9H,UAAd,IACH,KAAK8H,QAAL,CAAc9H,UAAd,CAAyB3B,QAAzB,KAAsCyN,IAAI,CAACC,YADxC,IAEH,KAAKjE,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCW,mBAAjC,CAFE,IAGF,KAAKtB,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCsP,qBAAjC,CAHF,EAGyD;EACvD;EACD;;EAED,QAAI7L,QAAJ;EACA,QAAMrI,MAAM,GAAGtG,sBAAsB,CAAC,KAAKuK,QAAN,CAArC;;EACA,QAAM6kB,WAAW,GAAG,KAAK7kB,QAAL,CAAcQ,OAAd,CAAsBuhB,yBAAtB,CAApB;;EAEA,QAAI8C,WAAJ,EAAiB;EACf,UAAMC,YAAY,GAAGD,WAAW,CAACjM,QAAZ,KAAyB,IAAzB,IAAiCiM,WAAW,CAACjM,QAAZ,KAAyB,IAA1D,GAAiE8L,kBAAjE,GAAsF/d,iBAA3G;EACAvC,MAAAA,QAAQ,GAAGhB,cAAc,CAACE,IAAf,CAAoBwhB,YAApB,EAAkCD,WAAlC,CAAX;EACAzgB,MAAAA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,CAACnI,MAAT,GAAkB,CAAnB,CAAnB;EACD;;EAED,QAAIoW,SAAS,GAAG,IAAhB;;EAEA,QAAIjO,QAAJ,EAAc;EACZiO,MAAAA,SAAS,GAAG7W,YAAY,CAAC0C,OAAb,CAAqBkG,QAArB,EAA+BuI,YAA/B,EAA2C;EACrD5B,QAAAA,aAAa,EAAE,KAAK/K;EADiC,OAA3C,CAAZ;EAGD;;EAED,QAAMgS,SAAS,GAAGxW,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyM,YAApC,EAAgD;EAChE1B,MAAAA,aAAa,EAAE3G;EADiD,KAAhD,CAAlB;;EAIA,QAAI4N,SAAS,CAACxT,gBAAV,IACD6T,SAAS,KAAK,IAAd,IAAsBA,SAAS,CAAC7T,gBADnC,EACsD;EACpD;EACD;;EAED,SAAKulB,SAAL,CACE,KAAK/jB,QADP,EAEE6kB,WAFF;;EAKA,QAAMlW,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrBnT,MAAAA,YAAY,CAAC0C,OAAb,CAAqBkG,QAArB,EAA+BwI,cAA/B,EAA6C;EAC3C7B,QAAAA,aAAa,EAAE,KAAI,CAAC/K;EADuB,OAA7C;EAGAxE,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAI,CAAC8B,QAA1B,EAAoC0M,aAApC,EAAiD;EAC/C3B,QAAAA,aAAa,EAAE3G;EADgC,OAAjD;EAGD,KAPD;;EASA,QAAIrI,MAAJ,EAAY;EACV,WAAKgoB,SAAL,CAAehoB,MAAf,EAAuBA,MAAM,CAAC7D,UAA9B,EAA0CyW,QAA1C;EACD,KAFD,MAEO;EACLA,MAAAA,QAAQ;EACT;EACF;;WAEDpO,UAAA,mBAAU;EACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EACA,SAAKW,QAAL,GAAgB,IAAhB;EACD;;;WAID+jB,YAAA,mBAAU7uB,OAAV,EAAmBiZ,SAAnB,EAA8B9U,QAA9B,EAAwC;EAAA;;EACtC,QAAM0rB,cAAc,GAAG5W,SAAS,KAAKA,SAAS,CAACyK,QAAV,KAAuB,IAAvB,IAA+BzK,SAAS,CAACyK,QAAV,KAAuB,IAA3D,CAAT,GACrBxV,cAAc,CAACE,IAAf,CAAoBohB,kBAApB,EAAwCvW,SAAxC,CADqB,GAErB/K,cAAc,CAACO,QAAf,CAAwBwK,SAAxB,EAAmCxH,iBAAnC,CAFF;EAIA,QAAMqe,MAAM,GAAGD,cAAc,CAAC,CAAD,CAA7B;EACA,QAAMhW,eAAe,GAAG1V,QAAQ,IAC7B2rB,MAAM,IAAIA,MAAM,CAACvkB,SAAP,CAAiBE,QAAjB,CAA0B4T,iBAA1B,CADb;;EAGA,QAAM5F,QAAQ,GAAG,SAAXA,QAAW;EAAA,aAAM,MAAI,CAACsW,mBAAL,CACrB/vB,OADqB,EAErB8vB,MAFqB,EAGrB3rB,QAHqB,CAAN;EAAA,KAAjB;;EAMA,QAAI2rB,MAAM,IAAIjW,eAAd,EAA+B;EAC7B,UAAMlZ,kBAAkB,GAAGH,gCAAgC,CAACsvB,MAAD,CAA3D;EACAA,MAAAA,MAAM,CAACvkB,SAAP,CAAiBC,MAAjB,CAAwBmM,iBAAxB;EAEArR,MAAAA,YAAY,CAACmC,GAAb,CAAiBqnB,MAAjB,EAAyB9wB,cAAzB,EAAyCya,QAAzC;EACAnY,MAAAA,oBAAoB,CAACwuB,MAAD,EAASnvB,kBAAT,CAApB;EACD,KAND,MAMO;EACL8Y,MAAAA,QAAQ;EACT;EACF;;WAEDsW,sBAAA,6BAAoB/vB,OAApB,EAA6B8vB,MAA7B,EAAqC3rB,QAArC,EAA+C;EAC7C,QAAI2rB,MAAJ,EAAY;EACVA,MAAAA,MAAM,CAACvkB,SAAP,CAAiBC,MAAjB,CAAwBY,mBAAxB;EAEA,UAAM4jB,aAAa,GAAG9hB,cAAc,CAACM,OAAf,CAAuBihB,8BAAvB,EAAuDK,MAAM,CAAC9sB,UAA9D,CAAtB;;EAEA,UAAIgtB,aAAJ,EAAmB;EACjBA,QAAAA,aAAa,CAACzkB,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B;EACD;;EAED,UAAI0jB,MAAM,CAAC5vB,YAAP,CAAoB,MAApB,MAAgC,KAApC,EAA2C;EACzC4vB,QAAAA,MAAM,CAACtjB,YAAP,CAAoB,eAApB,EAAqC,KAArC;EACD;EACF;;EAEDxM,IAAAA,OAAO,CAACuL,SAAR,CAAkB2J,GAAlB,CAAsB9I,mBAAtB;;EACA,QAAIpM,OAAO,CAACE,YAAR,CAAqB,MAArB,MAAiC,KAArC,EAA4C;EAC1CF,MAAAA,OAAO,CAACwM,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAED5I,IAAAA,MAAM,CAAC5D,OAAD,CAAN;;EAEA,QAAIA,OAAO,CAACuL,SAAR,CAAkBE,QAAlB,CAA2B4T,iBAA3B,CAAJ,EAAiD;EAC/Crf,MAAAA,OAAO,CAACuL,SAAR,CAAkB2J,GAAlB,CAAsByC,iBAAtB;EACD;;EAED,QAAI3X,OAAO,CAACgD,UAAR,IAAsBhD,OAAO,CAACgD,UAAR,CAAmBuI,SAAnB,CAA6BE,QAA7B,CAAsC8jB,wBAAtC,CAA1B,EAA2F;EACzF,UAAMU,eAAe,GAAGjwB,OAAO,CAACsL,OAAR,CAAgB2hB,mBAAhB,CAAxB;;EAEA,UAAIgD,eAAJ,EAAqB;EACnB/hB,QAAAA,cAAc,CAACE,IAAf,CAAoB8e,0BAApB,EACG7qB,OADH,CACW,UAAA6tB,QAAQ;EAAA,iBAAIA,QAAQ,CAAC3kB,SAAT,CAAmB2J,GAAnB,CAAuB9I,mBAAvB,CAAJ;EAAA,SADnB;EAED;;EAEDpM,MAAAA,OAAO,CAACwM,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAED,QAAIrI,QAAJ,EAAc;EACZA,MAAAA,QAAQ;EACT;EACF;;;QAIMyH,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3B,UAAMnH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,KAAgC,IAAIulB,GAAJ,CAAQ,IAAR,CAA7C;;EAEA,UAAI,OAAOztB,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ;EACD;EACF,KAVM,CAAP;EAWD;;QAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BA3JoB;EACnB,aAAOD,SAAP;EACD;;;;;EA4JH;EACA;EACA;EACA;EACA;;;EAEA5D,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUlG,KAAV,EAAiB;EACrFA,EAAAA,KAAK,CAAC6D,cAAN;EAEA,MAAMtF,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,KAAgC,IAAIulB,GAAJ,CAAQ,IAAR,CAA7C;EACAhrB,EAAAA,IAAI,CAACoU,IAAL;EACD,CALD;EAOA;EACA;EACA;EACA;EACA;EACA;;EAEA5U,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAaylB,GAAG,CAAC9jB,eAAjB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBwjB,GAAzB;;EACAxmB,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOyjB,GAAG,CAAC9jB,eAAX;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;ECjOA;EACA;EACA;EACA;EACA;;EAEA,IAAM3B,MAAI,GAAG,OAAb;EACA,IAAMC,SAAO,GAAG,cAAhB;EACA,IAAMC,UAAQ,GAAG,UAAjB;EACA,IAAMC,WAAS,SAAOD,UAAtB;EAEA,IAAM2U,qBAAmB,qBAAmB1U,WAA5C;EACA,IAAMqN,YAAU,YAAUrN,WAA1B;EACA,IAAMsN,cAAY,cAAYtN,WAA9B;EACA,IAAMmN,YAAU,YAAUnN,WAA1B;EACA,IAAMoN,aAAW,aAAWpN,WAA5B;EAEA,IAAMiV,iBAAe,GAAG,MAAxB;EACA,IAAM8Q,eAAe,GAAG,MAAxB;EACA,IAAMxY,iBAAe,GAAG,MAAxB;EACA,IAAMyY,kBAAkB,GAAG,SAA3B;EAEA,IAAMngB,aAAW,GAAG;EAClB4W,EAAAA,SAAS,EAAE,SADO;EAElBwJ,EAAAA,QAAQ,EAAE,SAFQ;EAGlBrJ,EAAAA,KAAK,EAAE;EAHW,CAApB;EAMA,IAAMtX,SAAO,GAAG;EACdmX,EAAAA,SAAS,EAAE,IADG;EAEdwJ,EAAAA,QAAQ,EAAE,IAFI;EAGdrJ,EAAAA,KAAK,EAAE;EAHO,CAAhB;EAMA,IAAMvH,uBAAqB,GAAG,wBAA9B;EAEA;EACA;EACA;EACA;EACA;;MAEM6Q;EACJ,iBAAYtwB,OAAZ,EAAqBiC,MAArB,EAA6B;EAC3B,SAAK6I,QAAL,GAAgB9K,OAAhB;EACA,SAAK6S,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;EACA,SAAK6mB,QAAL,GAAgB,IAAhB;;EACA,SAAKI,aAAL;;EACAnkB,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;EACD;;;;;EAgBD;WAEA2O,OAAA,gBAAO;EAAA;;EACL,QAAMgE,SAAS,GAAGxW,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyM,YAApC,CAAlB;;EAEA,QAAIuF,SAAS,CAACxT,gBAAd,EAAgC;EAC9B;EACD;;EAED,SAAKinB,aAAL;;EAEA,QAAI,KAAK1d,OAAL,CAAagU,SAAjB,EAA4B;EAC1B,WAAK/b,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4BmK,iBAA5B;EACD;;EAED,QAAM5F,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,KAAI,CAAC3O,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B4kB,kBAA/B;;EACA,MAAA,KAAI,CAACtlB,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4ByC,iBAA5B;;EAEArR,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAI,CAAC8B,QAA1B,EAAoC0M,aAApC;;EAEA,UAAI,KAAI,CAAC3E,OAAL,CAAawd,QAAjB,EAA2B;EACzB,QAAA,KAAI,CAACvH,QAAL,GAAgBhnB,UAAU,CAAC,YAAM;EAC/B,UAAA,KAAI,CAAC+W,IAAL;EACD,SAFyB,EAEvB,KAAI,CAAChG,OAAL,CAAamU,KAFU,CAA1B;EAGD;EACF,KAXD;;EAaA,SAAKlc,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B2kB,eAA/B;;EACAvsB,IAAAA,MAAM,CAAC,KAAKkH,QAAN,CAAN;;EACA,SAAKA,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4Bkb,kBAA5B;;EACA,QAAI,KAAKvd,OAAL,CAAagU,SAAjB,EAA4B;EAC1B,UAAMlmB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKsK,QAAN,CAA3D;EAEAxE,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgDya,QAAhD;EACAnY,MAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBnK,kBAAhB,CAApB;EACD,KALD,MAKO;EACL8Y,MAAAA,QAAQ;EACT;EACF;;WAEDZ,OAAA,gBAAO;EAAA;;EACL,QAAI,CAAC,KAAK/N,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,iBAAjC,CAAL,EAAwD;EACtD;EACD;;EAED,QAAMwF,SAAS,GAAG7W,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoC2M,YAApC,CAAlB;;EAEA,QAAI0F,SAAS,CAAC7T,gBAAd,EAAgC;EAC9B;EACD;;EAED,QAAMmQ,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,MAAI,CAAC3O,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4Bib,eAA5B;;EACA7pB,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAC8B,QAA1B,EAAoC4M,cAApC;EACD,KAHD;;EAKA,SAAK5M,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BmM,iBAA/B;;EACA,QAAI,KAAK9E,OAAL,CAAagU,SAAjB,EAA4B;EAC1B,UAAMlmB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKsK,QAAN,CAA3D;EAEAxE,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgDya,QAAhD;EACAnY,MAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBnK,kBAAhB,CAApB;EACD,KALD,MAKO;EACL8Y,MAAAA,QAAQ;EACT;EACF;;WAEDpO,UAAA,mBAAU;EACR,SAAKklB,aAAL;;EAEA,QAAI,KAAKzlB,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,iBAAjC,CAAJ,EAAuD;EACrD,WAAK7M,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BmM,iBAA/B;EACD;;EAEDrR,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgCgU,qBAAhC;EACA/Z,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;EAEA,SAAKW,QAAL,GAAgB,IAAhB;EACA,SAAK+H,OAAL,GAAe,IAAf;EACD;;;WAIDC,aAAA,oBAAW7Q,MAAX,EAAmB;EACjBA,IAAAA,MAAM,gBACDyN,SADC,EAED3C,WAAW,CAACI,iBAAZ,CAA8B,KAAKrC,QAAnC,CAFC,EAGA,OAAO7I,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHhD,CAAN;EAMAF,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAe,KAAKub,WAAL,CAAiBvN,WAAhC,CAAf;EAEA,WAAOhO,MAAP;EACD;;WAEDinB,gBAAA,yBAAgB;EAAA;;EACd5iB,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+BgU,qBAA/B,EAAoDW,uBAApD,EAA2E;EAAA,aAAM,MAAI,CAAC5G,IAAL,EAAN;EAAA,KAA3E;EACD;;WAED0X,gBAAA,yBAAgB;EACdxb,IAAAA,YAAY,CAAC,KAAK+T,QAAN,CAAZ;EACA,SAAKA,QAAL,GAAgB,IAAhB;EACD;;;UAIMld,kBAAP,yBAAuB3J,MAAvB,EAA+B;EAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;EAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;EACA,UAAM0I,OAAO,GAAG,OAAO5Q,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAACyC,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI4rB,KAAJ,CAAU,IAAV,EAAgBzd,OAAhB,CAAP;EACD;;EAED,UAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;EACD;;EAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAfM,CAAP;EAgBD;;UAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;EAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;EACD;;;;0BA5IoB;EACnB,aAAOD,SAAP;EACD;;;0BAEwB;EACvB,aAAO+F,aAAP;EACD;;;0BAEoB;EACnB,aAAOP,SAAP;EACD;;;;;EAqIH;EACA;EACA;EACA;EACA;EACA;;;EAEAxL,kBAAkB,CAAC,YAAM;EACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;EACA;;EACA,MAAIoF,CAAJ,EAAO;EACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;EACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAaqmB,KAAK,CAAC1kB,eAAnB;EACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBokB,KAAzB;;EACApnB,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;EAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;EACA,aAAOqkB,KAAK,CAAC1kB,eAAb;EACD,KAHD;EAID;EACF,CAZiB,CAAlB;;EC/NA;EACA;EACA;EACA;EACA;EACA;AAcA,kBAAe;EACbf,EAAAA,KAAK,EAALA,KADa;EAEbyB,EAAAA,MAAM,EAANA,MAFa;EAGb8F,EAAAA,QAAQ,EAARA,QAHa;EAIb8F,EAAAA,QAAQ,EAARA,QAJa;EAKbkE,EAAAA,QAAQ,EAARA,QALa;EAMbwD,EAAAA,KAAK,EAALA,KANa;EAOb0M,EAAAA,OAAO,EAAPA,OAPa;EAQbe,EAAAA,SAAS,EAATA,SARa;EASbqC,EAAAA,GAAG,EAAHA,GATa;EAUbY,EAAAA,KAAK,EAALA,KAVa;EAWb1H,EAAAA,OAAO,EAAPA;EAXa,CAAf;;;;;;;;"}