<?php
// Test script to check if cashier page redirects non-logged-in users to login page

// Function to make a GET request and follow redirects
function get_with_redirects($url, $max_redirects = 5)
{
    $redirects = 0;
    $final_url = $url;
    $all_headers = [];

    while ($redirects < $max_redirects) {
        $options = [
            'http' => [
                'method' => 'GET',
                'follow_location' => 0,  // Don't follow redirects automatically
            ]
        ];

        $context = stream_context_create($options);

        // Try to get the headers only
        $headers = get_headers($final_url, 1);
        if ($headers === false) {
            echo "Error: Could not get headers for $final_url\n";
            break;
        }

        $http_code = 0;
        if (isset($headers[0])) {
            preg_match('/HTTP\/[\d.]+\s+(\d+)/', $headers[0], $matches);
            if (isset($matches[1])) {
                $http_code = (int)$matches[1];
            }
        }

        $all_headers[] = [
            'url' => $final_url,
            'code' => $http_code,
            'headers' => $headers
        ];

        // Check if it's a redirect
        if ($http_code >= 300 && $http_code < 400) {
            // Extract the Location header
            if (isset($headers['Location'])) {
                $redirect_url = $headers['Location'];
                if (is_array($redirect_url)) {
                    $redirect_url = $redirect_url[0]; // Use the first location if multiple
                }

                // Handle relative URLs
                if (strpos($redirect_url, 'http') !== 0) {
                    // If it starts with /, it's relative to the domain root
                    if (strpos($redirect_url, '/') === 0) {
                        $parsed_url = parse_url($final_url);
                        $redirect_url = $parsed_url['scheme'] . '://' . $parsed_url['host'] . $redirect_url;
                    } else {
                        // It's relative to the current directory
                        $dir = dirname($final_url);
                        $redirect_url = $dir . '/' . $redirect_url;
                    }
                }

                $final_url = $redirect_url;
                $redirects++;
                echo "Redirected to: $final_url (HTTP $http_code)\n";
            } else {
                echo "Redirect without Location header. HTTP code: $http_code\n";
                break;
            }
        } else {
            echo "Final URL: $final_url (HTTP $http_code)\n";
            break;
        }
    }

    return [
        'final_url' => $final_url,
        'redirects' => $redirects,
        'all_headers' => $all_headers
    ];
}

// Start the test
echo "Testing cashier page redirect for non-logged-in users...\n";

// Try to access the cashier page directly
$cashier_url = 'http://localhost/finance/ecommerce_complete/pages/cashier/index.php';
$result = get_with_redirects($cashier_url);

// Check if we were redirected to the login page
if (strpos($result['final_url'], 'login.php') !== false) {
    echo "SUCCESS: Non-logged-in user was redirected to login page as expected.\n";
} else {
    echo "FAILURE: Non-logged-in user was not redirected to login page. Ended up at: " . $result['final_url'] . "\n";
}

echo "Test completed with " . $result['redirects'] . " redirect(s).\n";
