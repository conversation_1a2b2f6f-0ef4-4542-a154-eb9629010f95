{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "names": ["storeData", "id", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "_window$getComputedSt", "window", "getComputedStyle", "transitionDuration", "transitionDelay", "floatTransitionDuration", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "obj", "nodeType", "emulateTransitionEnd", "duration", "called", "emulatedDuration", "addEventListener", "listener", "removeEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "toString", "call", "match", "toLowerCase", "RegExp", "test", "Error", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "hasAttribute", "onDOMContentLoaded", "callback", "readyState", "mapData", "set", "key", "data", "bs<PERSON><PERSON>", "get", "keyProperties", "delete", "Data", "instance", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "handler", "delegationSelector", "uidEventList", "i", "len", "length", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "replace", "custom", "indexOf", "add<PERSON><PERSON><PERSON>", "oneOff", "_normalizeParams", "handlers", "previousFn", "fn", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "this", "<PERSON><PERSON><PERSON><PERSON>", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "on", "one", "_normalizeParams2", "inNamespace", "isNamespace", "char<PERSON>t", "elementEvent", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "removeNamespacedHandlers", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "$", "isNative", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "NAME", "<PERSON><PERSON>", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "dispose", "closest", "_this", "classList", "remove", "contains", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "jQueryInterface", "each", "handle<PERSON><PERSON><PERSON>", "alertInstance", "getInstance", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "<PERSON><PERSON>", "toggle", "setAttribute", "normalizeData", "val", "Number", "normalizeDataKey", "chr", "button", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "_extends", "dataset", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "SelectorEngine", "matches", "find", "_ref", "documentElement", "concat", "Element", "prototype", "findOne", "children", "_ref2", "filter", "child", "parents", "ancestor", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "EVENT_KEY", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "PointerType", "TOUCH", "PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "direction", "_handleSwipe", "absDeltax", "abs", "_this2", "_keydown", "_addTouchEventListeners", "_this3", "start", "pointerType", "clientX", "touches", "end", "clearTimeout", "itemImg", "e", "add", "move", "tagName", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "from", "_setActiveIndicatorElement", "indicators", "nextIndicator", "elementInterval", "parseInt", "defaultInterval", "directionalClassName", "orderClassName", "_this4", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "carouselInterface", "action", "TypeError", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "Collapse", "_isTransitioning", "_triggerArray", "SELECTOR_DATA_TOGGLE", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "REGEXP_KEYDOWN", "ARROW_UP_KEY", "flip", "boundary", "reference", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "isActive", "clearMenus", "getParentFromElement", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "focus", "destroy", "update", "scheduleUpdate", "stopPropagation", "constructor", "_getPlacement", "parentDropdown", "placement", "_getOffset", "offsets", "modifiers", "enabled", "preventOverflow", "boundariesElement", "applyStyle", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "dataApiKeydownHandler", "items", "backdrop", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "showEvent", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "transition", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_this5", "_triggerBackdropTransition", "_this6", "_this7", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "_this8", "animate", "createElement", "className", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "_this9", "isModalOverflowing", "scrollHeight", "clientHeight", "overflowY", "modalTransitionDuration", "paddingLeft", "paddingRight", "round", "right", "innerWidth", "_getScrollbarWidth", "_this10", "actualPadding", "calculatedPadding", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "_this11", "uriAttrs", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "DefaultAllowlist", "*", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "_loop", "el", "el<PERSON>ame", "nodeName", "attributeList", "allowedAttributes", "attr", "allowedAttributeList", "attrName", "nodeValue", "regExp", "attrRegex", "allowedAttribute", "innerHTML", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacement", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "DATA_KEY", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "findShadowRoot", "attachShadow", "getRootNode", "root", "ShadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "complete", "_fixTransition", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "behavior", "arrow", "onCreate", "originalPlacement", "_handlePopperPlacementChange", "onUpdate", "CLASS_PREFIX", "eventIn", "eventOut", "_fixTitle", "titleType", "dataAttributes", "dataAttr", "tabClass", "map", "token", "tClass", "popperData", "popper", "initConfigAnimation", "Popover", "_getContent", "method", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "queries", "link", "join", "listGroup", "SELECTOR_NAV_LINKS", "navItem", "node", "spy", "Tab", "listElement", "itemSelector", "hideEvent", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdown", "autohide", "Toast", "_clearTimeout"], "mappings": ";;;;;sxBAOA,ICOQA,EACFC,EDWAC,EAAS,SAAAC,GACb,GACEA,GAAUC,KAAKC,MArBH,IAqBSD,KAAKE,gBACnBC,SAASC,eAAeL,IAEjC,OAAOA,GAGHM,EAAc,SAAAC,GAClB,IAAIC,EAAWD,EAAQE,aAAa,eAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAME,EAAWH,EAAQE,aAAa,QAEtCD,EAAWE,GAAyB,MAAbA,EAAmBA,EAASC,OAAS,KAG9D,OAAOH,GAGHI,EAAyB,SAAAL,GAC7B,IAAMC,EAAWF,EAAYC,GAE7B,OAAIC,GACKJ,SAASS,cAAcL,GAAYA,EAGrC,MAGHM,EAAyB,SAAAP,GAC7B,IAAMC,EAAWF,EAAYC,GAE7B,OAAOC,EAAWJ,SAASS,cAAcL,GAAY,MAGjDO,EAAmC,SAAAR,GACvC,IAAKA,EACH,OAAO,EAFyC,IAAAS,EAS9CC,OAAOC,iBAAiBX,GAF1BY,EAPgDH,EAOhDG,mBACAC,EARgDJ,EAQhDI,gBAGIC,EAA0BC,WAAWH,GACrCI,EAAuBD,WAAWF,GAGxC,OAAKC,GAA4BE,GAKjCJ,EAAqBA,EAAmBK,MAAM,KAAK,GACnDJ,EAAkBA,EAAgBI,MAAM,KAAK,GA3Ef,KA6EtBF,WAAWH,GAAsBG,WAAWF,KAP3C,GAULK,EAAuB,SAAAlB,GAC3BA,EAAQmB,cAAc,IAAIC,MAhFL,mBAmFjBC,EAAY,SAAAC,GAAG,OAAKA,EAAI,IAAMA,GAAKC,UAEnCC,EAAuB,SAACxB,EAASyB,GACrC,IAAIC,GAAS,EAEPC,EAAmBF,EADD,EAOxBzB,EAAQ4B,iBA9Fa,iBAyFrB,SAASC,IACPH,GAAS,EACT1B,EAAQ8B,oBA3FW,gBA2FyBD,MAI9CE,YAAW,WACJL,GACHR,EAAqBlB,KAEtB2B,IAGCK,EAAkB,SAACC,EAAeC,EAAQC,GAC9CC,OAAOC,KAAKF,GAAaG,SAAQ,SAAAC,GAC/B,IArGWjB,EAqGLkB,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASpB,EAAUoB,GACnC,UAvGAnB,OADSA,EAyGFmB,GAvGT,GAAUnB,EAGL,GAAGqB,SAASC,KAAKtB,GAAKuB,MAAM,eAAe,GAAGC,cAsGnD,IAAK,IAAIC,OAAOP,GAAeQ,KAAKN,GAClC,MAAM,IAAIO,MACLhB,EAAciB,cAAdjB,aACQM,EADX,oBACuCG,EADpCT,wBAEmBO,EAFtB,UAOFW,EAAY,SAAAnD,GAChB,IAAKA,EACH,OAAO,EAGT,GAAIA,EAAQoD,OAASpD,EAAQqD,YAAcrD,EAAQqD,WAAWD,MAAO,CACnE,IAAME,EAAe3C,iBAAiBX,GAChCuD,EAAkB5C,iBAAiBX,EAAQqD,YAEjD,MAAgC,SAAzBC,EAAaE,SACU,SAA5BD,EAAgBC,SACY,WAA5BF,EAAaG,WAGjB,OAAO,GA0BHC,EAAO,WAAA,OAAM,cAEbC,EAAS,SAAA3D,GAAO,OAAIA,EAAQ4D,cAE5BC,EAAY,WAAM,IACdC,EAAWpD,OAAXoD,OAER,OAAIA,IAAWjE,SAASkE,KAAKC,aAAa,kBACjCF,EAGF,MAGHG,EAAqB,SAAAC,GACG,YAAxBrE,SAASsE,WACXtE,SAAS+B,iBAAiB,mBAAoBsC,GAE9CA,KC7KEE,GACE9E,EAAY,GACdC,EAAK,EACF,CACL8E,IADK,SACDrE,EAASsE,EAAKC,QACa,IAAlBvE,EAAQwE,QACjBxE,EAAQwE,MAAQ,CACdF,IAAAA,EACA/E,GAAAA,GAEFA,KAGFD,EAAUU,EAAQwE,MAAMjF,IAAMgF,GAEhCE,IAZK,SAYDzE,EAASsE,GACX,IAAKtE,QAAoC,IAAlBA,EAAQwE,MAC7B,OAAO,KAGT,IAAME,EAAgB1E,EAAQwE,MAC9B,OAAIE,EAAcJ,MAAQA,EACjBhF,EAAUoF,EAAcnF,IAG1B,MAEToF,OAxBK,SAwBE3E,EAASsE,GACd,QAA6B,IAAlBtE,EAAQwE,MAAnB,CAIA,IAAME,EAAgB1E,EAAQwE,MAC1BE,EAAcJ,MAAQA,WACjBhF,EAAUoF,EAAcnF,WACxBS,EAAQwE,WAMjBI,EAAO,SACHC,EAAUP,EAAKC,GACrBH,EAAQC,IAAIQ,EAAUP,EAAKC,IAFzBK,EAAO,SAIHC,EAAUP,GAChB,OAAOF,EAAQK,IAAII,EAAUP,IAL3BM,EAAO,SAOAC,EAAUP,GACnBF,EAAQO,OAAOE,EAAUP,IC/CvBQ,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GAClBC,EAAW,EACTC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,EAAe,CACnB,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,UASF,SAASC,EAAYvF,EAASwF,GAC5B,OAAQA,GAAUA,EAAP,KAAeN,KAAiBlF,EAAQkF,UAAYA,IAGjE,SAASO,EAASzF,GAChB,IAAMwF,EAAMD,EAAYvF,GAKxB,OAHAA,EAAQkF,SAAWM,EACnBP,EAAcO,GAAOP,EAAcO,IAAQ,GAEpCP,EAAcO,GAsCvB,SAASE,EAAYC,EAAQC,EAASC,QAA2B,IAA3BA,IAAAA,EAAqB,MAGzD,IAFA,IAAMC,EAAe1D,OAAOC,KAAKsD,GAExBI,EAAI,EAAGC,EAAMF,EAAaG,OAAQF,EAAIC,EAAKD,IAAK,CACvD,IAAMG,EAAQP,EAAOG,EAAaC,IAElC,GAAIG,EAAMC,kBAAoBP,GAAWM,EAAML,qBAAuBA,EACpE,OAAOK,EAIX,OAAO,KAGT,SAASE,EAAgBC,EAAmBT,EAASU,GACnD,IAAMC,EAAgC,iBAAZX,EACpBO,EAAkBI,EAAaD,EAAeV,EAGhDY,EAAYH,EAAkBI,QAAQ1B,EAAgB,IACpD2B,EAASvB,EAAaqB,GAY5B,OAVIE,IACFF,EAAYE,GAGGpB,EAAaqB,QAAQH,IAAc,IAGlDA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASI,EAAW5G,EAASqG,EAAmBT,EAASU,EAAcO,GACrE,GAAiC,iBAAtBR,GAAmCrG,EAA9C,CAIK4F,IACHA,EAAUU,EACVA,EAAe,MAP4D,IAAAQ,EAU5BV,EAAgBC,EAAmBT,EAASU,GAAtFC,EAVsEO,EAAA,GAU1DX,EAV0DW,EAAA,GAUzCN,EAVyCM,EAAA,GAWvEnB,EAASF,EAASzF,GAClB+G,EAAWpB,EAAOa,KAAeb,EAAOa,GAAa,IACrDQ,EAAatB,EAAYqB,EAAUZ,EAAiBI,EAAaX,EAAU,MAEjF,GAAIoB,EACFA,EAAWH,OAASG,EAAWH,QAAUA,MAD3C,CAMA,IAAMrB,EAAMD,EAAYY,EAAiBE,EAAkBI,QAAQ3B,EAAgB,KAC7EmC,EAAKV,EAhFb,SAAoCvG,EAASC,EAAUgH,GACrD,OAAO,SAASrB,EAAQM,GAGtB,IAFA,IAAMgB,EAAclH,EAAQmH,iBAAiBlH,GAElCmH,EAAWlB,EAAXkB,OAAkBA,GAAUA,IAAWC,KAAMD,EAASA,EAAO/D,WACtE,IAAK,IAAI0C,EAAImB,EAAYjB,OAAQF,KAC/B,GAAImB,EAAYnB,KAAOqB,EAOrB,OANAlB,EAAMoB,eAAiBF,EAEnBxB,EAAQiB,QACVU,EAAaC,IAAIxH,EAASkG,EAAMuB,KAAMR,GAGjCA,EAAGS,MAAMN,EAAQ,CAAClB,IAM/B,OAAO,MA8DPyB,CAA2B3H,EAAS4F,EAASU,GA7FjD,SAA0BtG,EAASiH,GACjC,OAAO,SAASrB,EAAQM,GAOtB,OANAA,EAAMoB,eAAiBtH,EAEnB4F,EAAQiB,QACVU,EAAaC,IAAIxH,EAASkG,EAAMuB,KAAMR,GAGjCA,EAAGS,MAAM1H,EAAS,CAACkG,KAsF1B0B,CAAiB5H,EAAS4F,GAE5BqB,EAAGpB,mBAAqBU,EAAaX,EAAU,KAC/CqB,EAAGd,gBAAkBA,EACrBc,EAAGJ,OAASA,EACZI,EAAG/B,SAAWM,EACduB,EAASvB,GAAOyB,EAEhBjH,EAAQ4B,iBAAiB4E,EAAWS,EAAIV,KAG1C,SAASsB,EAAc7H,EAAS2F,EAAQa,EAAWZ,EAASC,GAC1D,IAAMoB,EAAKvB,EAAYC,EAAOa,GAAYZ,EAASC,GAE9CoB,IAILjH,EAAQ8B,oBAAoB0E,EAAWS,EAAIa,QAAQjC,WAC5CF,EAAOa,GAAWS,EAAG/B,WAe9B,IAAMqC,EAAe,CACnBQ,GADmB,SAChB/H,EAASkG,EAAON,EAASU,GAC1BM,EAAW5G,EAASkG,EAAON,EAASU,GAAc,IAGpD0B,IALmB,SAKfhI,EAASkG,EAAON,EAASU,GAC3BM,EAAW5G,EAASkG,EAAON,EAASU,GAAc,IAGpDkB,IATmB,SASfxH,EAASqG,EAAmBT,EAASU,GACvC,GAAiC,iBAAtBD,GAAmCrG,EAA9C,CADqD,IAAAiI,EAKJ7B,EAAgBC,EAAmBT,EAASU,GAAtFC,EAL8C0B,EAAA,GAKlC9B,EALkC8B,EAAA,GAKjBzB,EALiByB,EAAA,GAM/CC,EAAc1B,IAAcH,EAC5BV,EAASF,EAASzF,GAClBmI,EAA8C,MAAhC9B,EAAkB+B,OAAO,GAE7C,QAA+B,IAApBjC,EAAX,CAUIgC,GACF/F,OAAOC,KAAKsD,GAAQrD,SAAQ,SAAA+F,IA1ClC,SAAkCrI,EAAS2F,EAAQa,EAAW8B,GAC5D,IAAMC,EAAoB5C,EAAOa,IAAc,GAE/CpE,OAAOC,KAAKkG,GAAmBjG,SAAQ,SAAAkG,GACrC,GAAIA,EAAW7B,QAAQ2B,IAAc,EAAG,CACtC,IAAMpC,EAAQqC,EAAkBC,GAEhCX,EAAc7H,EAAS2F,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,wBAoCrE4C,CAAyBzI,EAAS2F,EAAQ0C,EAAchC,EAAkBqC,MAAM,OAIpF,IAAMH,EAAoB5C,EAAOa,IAAc,GAC/CpE,OAAOC,KAAKkG,GAAmBjG,SAAQ,SAAAqG,GACrC,IAAMH,EAAaG,EAAYlC,QAAQzB,EAAe,IAEtD,IAAKkD,GAAe7B,EAAkBM,QAAQ6B,IAAe,EAAG,CAC9D,IAAMtC,EAAQqC,EAAkBI,GAEhCd,EAAc7H,EAAS2F,EAAQa,EAAWN,EAAMC,gBAAiBD,EAAML,4BAvB3E,CAEE,IAAKF,IAAWA,EAAOa,GACrB,OAGFqB,EAAc7H,EAAS2F,EAAQa,EAAWL,EAAiBI,EAAaX,EAAU,SAsBtFgD,QA/CmB,SA+CX5I,EAASkG,EAAO2C,GACtB,GAAqB,iBAAV3C,IAAuBlG,EAChC,OAAO,KAGT,IAKI8I,EALEC,EAAIlF,IACJ2C,EAAYN,EAAMO,QAAQ1B,EAAgB,IAC1CmD,EAAchC,IAAUM,EACxBwC,EAAW1D,EAAaqB,QAAQH,IAAc,EAGhDyC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EACnBC,EAAM,KA4CV,OA1CIlB,GAAea,IACjBD,EAAcC,EAAE3H,MAAM8E,EAAO2C,GAE7BE,EAAE/I,GAAS4I,QAAQE,GACnBG,GAAWH,EAAYO,uBACvBH,GAAkBJ,EAAYQ,gCAC9BH,EAAmBL,EAAYS,sBAG7BP,GACFI,EAAMvJ,SAAS2J,YAAY,eACvBC,UAAUjD,EAAWyC,GAAS,GAElCG,EAAM,IAAIM,YAAYxD,EAAO,CAC3B+C,QAAAA,EACAU,YAAY,SAKI,IAATd,GACTzG,OAAOC,KAAKwG,GAAMvG,SAAQ,SAAAgC,GACxBlC,OAAOwH,eAAeR,EAAK9E,EAAK,CAC9BG,IAD8B,WAE5B,OAAOoE,EAAKvE,SAMhB6E,GACFC,EAAIS,iBAGFX,GACFlJ,EAAQmB,cAAciI,GAGpBA,EAAID,uBAA2C,IAAhBL,GACjCA,EAAYe,iBAGPT,IC7SLU,EAAO,QAsBPC,EAAAA,WACJ,SAAAA,EAAY/J,GACVqH,KAAK2C,SAAWhK,EAEZqH,KAAK2C,UACPpF,EAAa5E,EAzBF,WAyBqBqH,iCAYpC4C,MAAA,SAAMjK,GACJ,IAAMkK,EAAclK,EAAUqH,KAAK8C,gBAAgBnK,GAAWqH,KAAK2C,SAC7DI,EAAc/C,KAAKgD,mBAAmBH,GAExB,OAAhBE,GAAwBA,EAAYjB,kBAIxC9B,KAAKiD,eAAeJ,MAGtBK,QAAA,WACE3F,EAAgByC,KAAK2C,SAjDR,YAkDb3C,KAAK2C,SAAW,QAKlBG,gBAAA,SAAgBnK,GACd,OAAOO,EAAuBP,IAAYA,EAAQwK,QAAR,aAG5CH,mBAAA,SAAmBrK,GACjB,OAAOuH,EAAaqB,QAAQ5I,EAtDf,qBAyDfsK,eAAA,SAAetK,GAAS,IAAAyK,EAAApD,KAGtB,GAFArH,EAAQ0K,UAAUC,OApDC,QAsDd3K,EAAQ0K,UAAUE,SAvDJ,QAuDnB,CAKA,IAAMhK,EAAqBJ,EAAiCR,GAE5DuH,EAAaS,IAAIhI,EH1FE,iBG0FuB,WAAA,OAAMyK,EAAKI,gBAAgB7K,MACrEwB,EAAqBxB,EAASY,QAP5ByG,KAAKwD,gBAAgB7K,MAUzB6K,gBAAA,SAAgB7K,GACVA,EAAQqD,YACVrD,EAAQqD,WAAWyH,YAAY9K,GAGjCuH,EAAaqB,QAAQ5I,EA3EP,sBAgFT+K,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACf,IAAIzG,EAAOK,EAAayC,KAzFb,YA2FN9C,IACHA,EAAO,IAAIwF,EAAM1C,OAGJ,UAAXnF,GACFqC,EAAKrC,GAAQmF,YAKZ4D,cAAP,SAAqBC,GACnB,OAAO,SAAUhF,GACXA,GACFA,EAAM2D,iBAGRqB,EAAcjB,MAAM5C,UAIjB8D,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAhHP,qDAgCb,MAjCY,qBAqBV+J,GAqGNxC,EAAaQ,GAAGlI,SAjHU,0BAJD,yBAqHyCkK,EAAMkB,cAAc,IAAIlB,IAS1F9F,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAG6C,GAChCf,EAAE9B,GAAG6C,GAAQC,EAAMgB,gBACnBhC,EAAE9B,GAAG6C,GAAMuB,YAActB,EACzBhB,EAAE9B,GAAG6C,GAAMwB,WAAa,WAEtB,OADAvC,EAAE9B,GAAG6C,GAAQsB,EACNrB,EAAMgB,qBCpJnB,IAkBMQ,EAAAA,WACJ,SAAAA,EAAYvL,GACVqH,KAAK2C,SAAWhK,EAChB4E,EAAa5E,EAnBA,YAmBmBqH,iCAWlCmE,OAAA,WAEEnE,KAAK2C,SAASyB,aAAa,eAAgBpE,KAAK2C,SAASU,UAAUc,OA5B7C,cA+BxBjB,QAAA,WACE3F,EAAgByC,KAAK2C,SApCR,aAqCb3C,KAAK2C,SAAW,QAKXe,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACf,IAAIzG,EAAOK,EAAayC,KA5Cb,aA8CN9C,IACHA,EAAO,IAAIgH,EAAOlE,OAGL,WAAXnF,GACFqC,EAAKrC,WAKJiJ,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAzDP,sDAyBb,MA1BY,qBAiBVuL,GC5BN,SAASG,EAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQC,OAAOD,GAAKhJ,WACfiJ,OAAOD,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASE,EAAiBvH,GACxB,OAAOA,EAAImC,QAAQ,UAAU,SAAAqF,GAAG,MAAA,IAAQA,EAAIhJ,iBD0D9CyE,EAAaQ,GAAGlI,SA3DU,2BAFG,0BA6DyC,SAAAqG,GACpEA,EAAM2D,iBAEN,IAAMkC,EAAS7F,EAAMkB,OAAOoD,QAhED,0BAkEvBjG,EAAOK,EAAamH,EAxET,aAyEVxH,IACHA,EAAO,IAAIgH,EAAOQ,IAGpBxH,EAAKiH,YAUPvH,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAF,OAC3B8B,EAAE9B,GAAF,OAAasE,EAAOR,gBACpBhC,EAAE9B,GAAF,OAAWoE,YAAcE,EAEzBxC,EAAE9B,GAAF,OAAWqE,WAAa,WAEtB,OADAvC,EAAE9B,GAAF,OAAamE,EACNG,EAAOR,qBCrFpB,IAAMiB,EAAc,CAClBC,iBADkB,SACDjM,EAASsE,EAAK7B,GAC7BzC,EAAQyL,aAAR,QAA6BI,EAAiBvH,GAAQ7B,IAGxDyJ,oBALkB,SAKElM,EAASsE,GAC3BtE,EAAQmM,gBAAR,QAAgCN,EAAiBvH,KAGnD8H,kBATkB,SASApM,GAChB,IAAKA,EACH,MAAO,GAGT,IAAMqM,EAAUC,EAAA,GACXtM,EAAQuM,SAOb,OAJAnK,OAAOC,KAAKgK,GAAY/J,SAAQ,SAAAgC,GAC9B+H,EAAW/H,GAAOoH,EAAcW,EAAW/H,OAGtC+H,GAGTG,iBAzBkB,SAyBDxM,EAASsE,GACxB,OAAOoH,EAAc1L,EAAQE,aAAR,QAA6B2L,EAAiBvH,MAGrEmI,OA7BkB,SA6BXzM,GACL,IAAM0M,EAAO1M,EAAQ2M,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAM/M,SAASkE,KAAK8I,UAC9BC,KAAMJ,EAAKI,KAAOjN,SAASkE,KAAKgJ,aAIpCC,SAtCkB,SAsCThN,GACP,MAAO,CACL4M,IAAK5M,EAAQiN,UACbH,KAAM9M,EAAQkN,cCzDdC,EAAiB,CACrBC,QADqB,SACbpN,EAASC,GACf,OAAOD,EAAQoN,QAAQnN,IAGzBoN,KALqB,SAKhBpN,EAAUD,GAAoC,IAAAsN,EACjD,YADiD,IAApCtN,IAAAA,EAAUH,SAAS0N,kBACzBD,EAAA,IAAGE,OAAH9F,MAAA4F,EAAaG,QAAQC,UAAUvG,iBAAiBvE,KAAK5C,EAASC,KAGvE0N,QATqB,SASb1N,EAAUD,GAChB,YADoD,IAApCA,IAAAA,EAAUH,SAAS0N,iBAC5BE,QAAQC,UAAUpN,cAAcsC,KAAK5C,EAASC,IAGvD2N,SAbqB,SAaZ5N,EAASC,GAAU,IAAA4N,EACpBD,GAAWC,EAAA,IAAGL,OAAH9F,MAAAmG,EAAa7N,EAAQ4N,UAEtC,OAAOA,EAASE,QAAO,SAAAC,GAAK,OAAIA,EAAMX,QAAQnN,OAGhD+N,QAnBqB,SAmBbhO,EAASC,GAKf,IAJA,IAAM+N,EAAU,GAEZC,EAAWjO,EAAQqD,WAEhB4K,GAAYA,EAAS1M,WAAa2M,KAAKC,cA1BhC,IA0BgDF,EAAS1M,UACjE8F,KAAK+F,QAAQa,EAAUhO,IACzB+N,EAAQI,KAAKH,GAGfA,EAAWA,EAAS5K,WAGtB,OAAO2K,GAGTK,KAnCqB,SAmChBrO,EAASC,GAGZ,IAFA,IAAIqO,EAAWtO,EAAQuO,uBAEhBD,GAAU,CACf,GAAIA,EAASlB,QAAQnN,GACnB,MAAO,CAACqO,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAjDqB,SAiDhBxO,EAASC,GAGZ,IAFA,IAAIuO,EAAOxO,EAAQyO,mBAEZD,GAAM,CACX,GAAInH,KAAK+F,QAAQoB,EAAMvO,GACrB,MAAO,CAACuO,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,KC7CL3E,EAAO,WAGP4E,EAAS,eAQTC,EAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,EAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAwCHE,EAAc,CAClBC,MAAO,QACPC,IAAK,OAQDC,EAAAA,WACJ,SAAAA,EAAYtP,EAASkC,GACnBmF,KAAKkI,OAAS,KACdlI,KAAKmI,UAAY,KACjBnI,KAAKoI,eAAiB,KACtBpI,KAAKqI,WAAY,EACjBrI,KAAKsI,YAAa,EAClBtI,KAAKuI,aAAe,KACpBvI,KAAKwI,YAAc,EACnBxI,KAAKyI,YAAc,EAEnBzI,KAAK0I,QAAU1I,KAAK2I,WAAW9N,GAC/BmF,KAAK2C,SAAWhK,EAChBqH,KAAK4I,mBAAqB9C,EAAeQ,QA3BjB,uBA2B8CtG,KAAK2C,UAC3E3C,KAAK6I,gBAAkB,iBAAkBrQ,SAAS0N,iBAAmB4C,UAAUC,eAAiB,EAChG/I,KAAKgJ,cAAgBvI,QAAQpH,OAAO4P,cAEpCjJ,KAAKkJ,qBACL3L,EAAa5E,EA5FA,cA4FmBqH,iCAelCmH,KAAA,WACOnH,KAAKsI,YACRtI,KAAKmJ,OAlFY,WAsFrBC,gBAAA,YAGO5Q,SAAS6Q,QAAUvN,EAAUkE,KAAK2C,WACrC3C,KAAKmH,UAITH,KAAA,WACOhH,KAAKsI,YACRtI,KAAKmJ,OA/FY,WAmGrBzB,MAAA,SAAM7I,GACCA,IACHmB,KAAKqI,WAAY,GAGfvC,EAAeQ,QAzEI,2CAyEwBtG,KAAK2C,YAClD9I,EAAqBmG,KAAK2C,UAC1B3C,KAAKsJ,OAAM,IAGbC,cAAcvJ,KAAKmI,WACnBnI,KAAKmI,UAAY,QAGnBmB,MAAA,SAAMzK,GACCA,IACHmB,KAAKqI,WAAY,GAGfrI,KAAKmI,YACPoB,cAAcvJ,KAAKmI,WACnBnI,KAAKmI,UAAY,MAGfnI,KAAK0I,SAAW1I,KAAK0I,QAAQnB,WAAavH,KAAKqI,YACjDrI,KAAKwJ,kBAELxJ,KAAKmI,UAAYsB,aACdjR,SAASkR,gBAAkB1J,KAAKoJ,gBAAkBpJ,KAAKmH,MAAMwC,KAAK3J,MACnEA,KAAK0I,QAAQnB,cAKnBqC,GAAA,SAAGC,GAAO,IAAAzG,EAAApD,KACRA,KAAKoI,eAAiBtC,EAAeQ,QA1GZ,wBA0G0CtG,KAAK2C,UACxE,IAAMmH,EAAc9J,KAAK+J,cAAc/J,KAAKoI,gBAE5C,KAAIyB,EAAQ7J,KAAKkI,OAAOtJ,OAAS,GAAKiL,EAAQ,GAI9C,GAAI7J,KAAKsI,WACPpI,EAAaS,IAAIX,KAAK2C,SAzIZ,oBAyIkC,WAAA,OAAMS,EAAKwG,GAAGC,UAD5D,CAKA,GAAIC,IAAgBD,EAGlB,OAFA7J,KAAK0H,aACL1H,KAAKsJ,QAIP,IAAMU,EAAYH,EAAQC,EAzJP,OACA,OA4JnB9J,KAAKmJ,OAAOa,EAAWhK,KAAKkI,OAAO2B,QAGrC3G,QAAA,WACEhD,EAAaC,IAAIH,KAAK2C,SAAU0E,GAChC9J,EAAgByC,KAAK2C,SA7LR,eA+Lb3C,KAAKkI,OAAS,KACdlI,KAAK0I,QAAU,KACf1I,KAAK2C,SAAW,KAChB3C,KAAKmI,UAAY,KACjBnI,KAAKqI,UAAY,KACjBrI,KAAKsI,WAAa,KAClBtI,KAAKoI,eAAiB,KACtBpI,KAAK4I,mBAAqB,QAK5BD,WAAA,SAAW9N,GAMT,OALAA,EAAMoK,EAAA,GACDqC,EACAzM,GAELF,EAAgB8H,EAAM5H,EAAQgN,GACvBhN,KAGToP,aAAA,WACE,IAAMC,EAAY7R,KAAK8R,IAAInK,KAAKyI,aAEhC,KAAIyB,GAhNgB,IAgNpB,CAIA,IAAMF,EAAYE,EAAYlK,KAAKyI,YAEnCzI,KAAKyI,YAAc,EAGfuB,EAAY,GACdhK,KAAKgH,OAIHgD,EAAY,GACdhK,KAAKmH,WAIT+B,mBAAA,WAAqB,IAAAkB,EAAApK,KACfA,KAAK0I,QAAQlB,UACftH,EAAaQ,GAAGV,KAAK2C,SA1MR,uBA0MiC,SAAA9D,GAAK,OAAIuL,EAAKC,SAASxL,MAG5C,UAAvBmB,KAAK0I,QAAQhB,QACfxH,EAAaQ,GAAGV,KAAK2C,SA7ML,0BA6MiC,SAAA9D,GAAK,OAAIuL,EAAK1C,MAAM7I,MACrEqB,EAAaQ,GAAGV,KAAK2C,SA7ML,0BA6MiC,SAAA9D,GAAK,OAAIuL,EAAKd,MAAMzK,OAGnEmB,KAAK0I,QAAQd,OAAS5H,KAAK6I,iBAC7B7I,KAAKsK,6BAITA,wBAAA,WAA0B,IAAAC,EAAAvK,KAClBwK,EAAQ,SAAA3L,GACR0L,EAAKvB,eAAiBlB,EAAYjJ,EAAM4L,YAAY5O,eACtD0O,EAAK/B,YAAc3J,EAAM6L,QACfH,EAAKvB,gBACfuB,EAAK/B,YAAc3J,EAAM8L,QAAQ,GAAGD,UAalCE,EAAM,SAAA/L,GACN0L,EAAKvB,eAAiBlB,EAAYjJ,EAAM4L,YAAY5O,iBACtD0O,EAAK9B,YAAc5J,EAAM6L,QAAUH,EAAK/B,aAG1C+B,EAAKN,eACsB,UAAvBM,EAAK7B,QAAQhB,QASf6C,EAAK7C,QACD6C,EAAKhC,cACPsC,aAAaN,EAAKhC,cAGpBgC,EAAKhC,aAAe7N,YAAW,SAAAmE,GAAK,OAAI0L,EAAKjB,MAAMzK,KAzR5B,IAyR6D0L,EAAK7B,QAAQnB,YAIrGzB,EAAeE,KAzOO,qBAyOiBhG,KAAK2C,UAAU1H,SAAQ,SAAA6P,GAC5D5K,EAAaQ,GAAGoK,EA1PA,yBA0P2B,SAAAC,GAAC,OAAIA,EAAEvI,uBAGhDxC,KAAKgJ,eACP9I,EAAaQ,GAAGV,KAAK2C,SAhQJ,2BAgQiC,SAAA9D,GAAK,OAAI2L,EAAM3L,MACjEqB,EAAaQ,GAAGV,KAAK2C,SAhQN,yBAgQiC,SAAA9D,GAAK,OAAI+L,EAAI/L,MAE7DmB,KAAK2C,SAASU,UAAU2H,IAtPG,mBAwP3B9K,EAAaQ,GAAGV,KAAK2C,SAxQL,0BAwQiC,SAAA9D,GAAK,OAAI2L,EAAM3L,MAChEqB,EAAaQ,GAAGV,KAAK2C,SAxQN,yBAwQiC,SAAA9D,GAAK,OA5C1C,SAAAA,GAEPA,EAAM8L,SAAW9L,EAAM8L,QAAQ/L,OAAS,EAC1C2L,EAAK9B,YAAc,EAEnB8B,EAAK9B,YAAc5J,EAAM8L,QAAQ,GAAGD,QAAUH,EAAK/B,YAuCIyC,CAAKpM,MAC9DqB,EAAaQ,GAAGV,KAAK2C,SAxQP,wBAwQiC,SAAA9D,GAAK,OAAI+L,EAAI/L,UAIhEwL,SAAA,SAASxL,GACP,IAAI,kBAAkBlD,KAAKkD,EAAMkB,OAAOmL,SAIxC,OAAQrM,EAAM5B,KACZ,IArTiB,YAsTf4B,EAAM2D,iBACNxC,KAAKgH,OACL,MACF,IAxTkB,aAyThBnI,EAAM2D,iBACNxC,KAAKmH,WAMX4C,cAAA,SAAcpR,GAKZ,OAJAqH,KAAKkI,OAASvP,GAAWA,EAAQqD,WAC/B8J,EAAeE,KA9QC,iBA8QmBrN,EAAQqD,YAC3C,GAEKgE,KAAKkI,OAAO5I,QAAQ3G,MAG7BwS,oBAAA,SAAoBnB,EAAWoB,GAC7B,IAAMC,EAnTa,SAmTKrB,EAClBsB,EAnTa,SAmTKtB,EAClBF,EAAc9J,KAAK+J,cAAcqB,GACjCG,EAAgBvL,KAAKkI,OAAOtJ,OAAS,EAI3C,IAHuB0M,GAAmC,IAAhBxB,GACjBuB,GAAmBvB,IAAgByB,KAEtCvL,KAAK0I,QAAQf,KACjC,OAAOyD,EAGT,IACMI,GAAa1B,GA9TA,SA6TLE,GAAgC,EAAI,IACRhK,KAAKkI,OAAOtJ,OAEtD,OAAsB,IAAf4M,EACLxL,KAAKkI,OAAOlI,KAAKkI,OAAOtJ,OAAS,GACjCoB,KAAKkI,OAAOsD,MAGhBC,mBAAA,SAAmBC,EAAeC,GAChC,IAAMC,EAAc5L,KAAK+J,cAAc2B,GACjCG,EAAY7L,KAAK+J,cAAcjE,EAAeQ,QA3S3B,wBA2SyDtG,KAAK2C,WAEvF,OAAOzC,EAAaqB,QAAQvB,KAAK2C,SArUpB,oBAqU2C,CACtD+I,cAAAA,EACA1B,UAAW2B,EACXG,KAAMD,EACNjC,GAAIgC,OAIRG,2BAAA,SAA2BpT,GACzB,GAAIqH,KAAK4I,mBAAoB,CAE3B,IADA,IAAMoD,EAAalG,EAAeE,KAxThB,UAwTsChG,KAAK4I,oBACpDlK,EAAI,EAAGA,EAAIsN,EAAWpN,OAAQF,IACrCsN,EAAWtN,GAAG2E,UAAUC,OAlUN,UAqUpB,IAAM2I,EAAgBjM,KAAK4I,mBAAmBrC,SAC5CvG,KAAK+J,cAAcpR,IAGjBsT,GACFA,EAAc5I,UAAU2H,IA1UN,cA+UxBxB,gBAAA,WACE,IAAM7Q,EAAUqH,KAAKoI,gBAAkBtC,EAAeQ,QAvU7B,wBAuU2DtG,KAAK2C,UAEzF,GAAKhK,EAAL,CAIA,IAAMuT,EAAkBC,SAASxT,EAAQE,aAAa,iBAAkB,IAEpEqT,GACFlM,KAAK0I,QAAQ0D,gBAAkBpM,KAAK0I,QAAQ0D,iBAAmBpM,KAAK0I,QAAQnB,SAC5EvH,KAAK0I,QAAQnB,SAAW2E,GAExBlM,KAAK0I,QAAQnB,SAAWvH,KAAK0I,QAAQ0D,iBAAmBpM,KAAK0I,QAAQnB,aAIzE4B,OAAA,SAAOa,EAAWrR,GAAS,IASrB0T,EACAC,EACAX,EAXqBY,EAAAvM,KACnBoL,EAAgBtF,EAAeQ,QAxVZ,wBAwV0CtG,KAAK2C,UAClE6J,EAAqBxM,KAAK+J,cAAcqB,GACxCqB,EAAc9T,GAAYyS,GAC9BpL,KAAKmL,oBAAoBnB,EAAWoB,GAEhCsB,EAAmB1M,KAAK+J,cAAc0C,GACtCE,EAAYlM,QAAQT,KAAKmI,WAgB/B,GA3YmB,SAiYf6B,GACFqC,EA3WkB,qBA4WlBC,EA3WkB,qBA4WlBX,EAlYiB,SAoYjBU,EAhXmB,sBAiXnBC,EA9WkB,qBA+WlBX,EArYkB,SAwYhBc,GAAeA,EAAYpJ,UAAUE,SAvXnB,UAwXpBvD,KAAKsI,YAAa,OAKpB,IADmBtI,KAAKyL,mBAAmBgB,EAAad,GACzC7J,kBAIVsJ,GAAkBqB,EAAvB,CAcA,GATAzM,KAAKsI,YAAa,EAEdqE,GACF3M,KAAK0H,QAGP1H,KAAK+L,2BAA2BU,GAChCzM,KAAKoI,eAAiBqE,EAElBzM,KAAK2C,SAASU,UAAUE,SA9YP,SA8YmC,CACtDkJ,EAAYpJ,UAAU2H,IAAIsB,GAE1BhQ,EAAOmQ,GAEPrB,EAAc/H,UAAU2H,IAAIqB,GAC5BI,EAAYpJ,UAAU2H,IAAIqB,GAE1B,IAAM9S,EAAqBJ,EAAiCiS,GAE5DlL,EAAaS,IAAIyK,EP/dA,iBO+d+B,WAC9CqB,EAAYpJ,UAAUC,OAAO+I,EAAsBC,GACnDG,EAAYpJ,UAAU2H,IA3ZJ,UA6ZlBI,EAAc/H,UAAUC,OA7ZN,SA6ZgCgJ,EAAgBD,GAElEE,EAAKjE,YAAa,EAElB5N,YAAW,WACTwF,EAAaqB,QAAQgL,EAAK5J,SAhbpB,mBAgb0C,CAC9C+I,cAAee,EACfzC,UAAW2B,EACXG,KAAMU,EACN5C,GAAI8C,MAEL,MAGLvS,EAAqBiR,EAAe7R,QAEpC6R,EAAc/H,UAAUC,OA7aJ,UA8apBmJ,EAAYpJ,UAAU2H,IA9aF,UAgbpBhL,KAAKsI,YAAa,EAClBpI,EAAaqB,QAAQvB,KAAK2C,SA/bhB,mBA+bsC,CAC9C+I,cAAee,EACfzC,UAAW2B,EACXG,KAAMU,EACN5C,GAAI8C,IAIJC,GACF3M,KAAKsJ,YAMFsD,kBAAP,SAAyBjU,EAASkC,GAChC,IAAIqC,EAAOK,EAAa5E,EAhfX,eAifT+P,EAAOzD,EAAA,GACNqC,EACA3C,EAAYI,kBAAkBpM,IAGb,iBAAXkC,IACT6N,EAAOzD,EAAA,GACFyD,EACA7N,IAIP,IAAMgS,EAA2B,iBAAXhS,EAAsBA,EAAS6N,EAAQjB,MAM7D,GAJKvK,IACHA,EAAO,IAAI+K,EAAStP,EAAS+P,IAGT,iBAAX7N,EACTqC,EAAK0M,GAAG/O,QACH,GAAsB,iBAAXgS,EAAqB,CACrC,QAA4B,IAAjB3P,EAAK2P,GACd,MAAM,IAAIC,UAAJ,oBAAkCD,EAAlC,KAGR3P,EAAK2P,UACInE,EAAQnB,UAAYmB,EAAQqE,OACrC7P,EAAKwK,QACLxK,EAAKoM,YAIF5F,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACfsE,EAAS2E,kBAAkB5M,KAAMnF,SAI9BmS,oBAAP,SAA2BnO,GACzB,IAAMkB,EAAS7G,EAAuB8G,MAEtC,GAAKD,GAAWA,EAAOsD,UAAUE,SA5eT,YA4exB,CAIA,IAAM1I,EAAMoK,EAAA,GACPN,EAAYI,kBAAkBhF,GAC9B4E,EAAYI,kBAAkB/E,OAE7BiN,EAAajN,KAAKnH,aAAa,iBAEjCoU,IACFpS,EAAO0M,UAAW,GAGpBU,EAAS2E,kBAAkB7M,EAAQlF,GAE/BoS,GACF1P,EAAawC,EA3iBF,eA2iBoB6J,GAAGqD,GAGpCpO,EAAM2D,qBAGDsB,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAljBP,wDAkGb,MAnGY,+CAuGZ,OAAO2O,QA5BLW,GAkfN/H,EAAaQ,GAAGlI,SAhhBU,6BAiBE,gCA+fyCyP,EAAS+E,qBAE9E9M,EAAaQ,GAAGrH,OAnhBS,6BAmhBoB,WAG3C,IAFA,IAAM6T,EAAYpH,EAAeE,KAjgBR,0BAmgBhBtH,EAAI,EAAGC,EAAMuO,EAAUtO,OAAQF,EAAIC,EAAKD,IAC/CuJ,EAAS2E,kBAAkBM,EAAUxO,GAAInB,EAAa2P,EAAUxO,GAlkBnD,mBA6kBjB9B,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAG6C,GAChCf,EAAE9B,GAAG6C,GAAQwF,EAASvE,gBACtBhC,EAAE9B,GAAG6C,GAAMuB,YAAciE,EACzBvG,EAAE9B,GAAG6C,GAAMwB,WAAa,WAEtB,OADAvC,EAAE9B,GAAG6C,GAAQsB,EACNkE,EAASvE,qBCxlBtB,IAAMjB,EAAO,WAMP6E,GAAU,CACdnD,QAAQ,EACRgJ,OAAQ,IAGJtF,GAAc,CAClB1D,OAAQ,UACRgJ,OAAQ,oBA0BJC,GAAAA,WACJ,SAAAA,EAAYzU,EAASkC,GACnBmF,KAAKqN,kBAAmB,EACxBrN,KAAK2C,SAAWhK,EAChBqH,KAAK0I,QAAU1I,KAAK2I,WAAW9N,GAC/BmF,KAAKsN,cAAgBxH,EAAeE,KAC/BuH,mCAA+B5U,EAAQT,GAAvCqV,6CACsC5U,EAAQT,GADjD,MAMF,IAFA,IAAMsV,EAAa1H,EAAeE,KAlBT,4BAoBhBtH,EAAI,EAAGC,EAAM6O,EAAW5O,OAAQF,EAAIC,EAAKD,IAAK,CACrD,IAAM+O,EAAOD,EAAW9O,GAClB9F,EAAWI,EAAuByU,GAClCC,EAAgB5H,EAAeE,KAAKpN,GACvC6N,QAAO,SAAAkH,GAAS,OAAIA,IAAchV,KAEpB,OAAbC,GAAqB8U,EAAc9O,SACrCoB,KAAK4N,UAAYhV,EACjBoH,KAAKsN,cAAcvG,KAAK0G,IAI5BzN,KAAK6N,QAAU7N,KAAK0I,QAAQyE,OAASnN,KAAK8N,aAAe,KAEpD9N,KAAK0I,QAAQyE,QAChBnN,KAAK+N,0BAA0B/N,KAAK2C,SAAU3C,KAAKsN,eAGjDtN,KAAK0I,QAAQvE,QACfnE,KAAKmE,SAGP5G,EAAa5E,EAvEA,cAuEmBqH,iCAelCmE,OAAA,WACMnE,KAAK2C,SAASU,UAAUE,SAnER,QAoElBvD,KAAKgO,OAELhO,KAAKiO,UAITA,KAAA,WAAO,IAAA7K,EAAApD,KACL,IAAIA,KAAKqN,mBACPrN,KAAK2C,SAASU,UAAUE,SA5EN,QA2EpB,CAKA,IAAI2K,EACAC,EAEAnO,KAAK6N,SAUgB,KATvBK,EAAUpI,EAAeE,KA5EN,qBA4E6BhG,KAAK6N,SAClDpH,QAAO,SAAAgH,GACN,MAAmC,iBAAxBrK,EAAKsF,QAAQyE,OACfM,EAAK5U,aAAa,iBAAmBuK,EAAKsF,QAAQyE,OAGpDM,EAAKpK,UAAUE,SAzFJ,gBA4FV3E,SACVsP,EAAU,MAId,IAAME,EAAYtI,EAAeQ,QAAQtG,KAAK4N,WAC9C,GAAIM,EAAS,CACX,IAAMG,EAAiBH,EAAQzH,QAAO,SAAAgH,GAAI,OAAIW,IAAcX,KAG5D,IAFAU,EAAcE,EAAe,GAAK9Q,EAAa8Q,EAAe,GAzHnD,eAyHmE,OAE3DF,EAAYd,iBAC7B,OAKJ,IADmBnN,EAAaqB,QAAQvB,KAAK2C,SAlHjC,oBAmHGb,iBAAf,CAIIoM,GACFA,EAAQjT,SAAQ,SAAAqT,GACVF,IAAcE,GAChBlB,EAASmB,kBAAkBD,EAAY,QAGpCH,GACH5Q,EAAa+Q,EA5IN,cA4I4B,SAKzC,IAAME,EAAYxO,KAAKyO,gBAEvBzO,KAAK2C,SAASU,UAAUC,OA9HA,YA+HxBtD,KAAK2C,SAASU,UAAU2H,IA9HE,cAgI1BhL,KAAK2C,SAAS5G,MAAMyS,GAAa,EAE7BxO,KAAKsN,cAAc1O,QACrBoB,KAAKsN,cAAcrS,SAAQ,SAAAtC,GACzBA,EAAQ0K,UAAUC,OAnIG,aAoIrB3K,EAAQyL,aAAa,iBAAiB,MAI1CpE,KAAK0O,kBAAiB,GAEtB,IAYMC,EAAU,UADaH,EAAU,GAAG3S,cAAgB2S,EAAUnN,MAAM,IAEpE9H,EAAqBJ,EAAiC6G,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,SRvMH,iBQwLF,WACfS,EAAKT,SAASU,UAAUC,OA5IA,cA6IxBF,EAAKT,SAASU,UAAU2H,IA9IF,WADJ,QAiJlB5H,EAAKT,SAAS5G,MAAMyS,GAAa,GAEjCpL,EAAKsL,kBAAiB,GAEtBxO,EAAaqB,QAAQ6B,EAAKT,SA1Jf,wBAmKbxI,EAAqB6F,KAAK2C,SAAUpJ,GACpCyG,KAAK2C,SAAS5G,MAAMyS,GAAgBxO,KAAK2C,SAASgM,GAAlD,UAGFX,KAAA,WAAO,IAAA5D,EAAApK,KACL,IAAIA,KAAKqN,kBACNrN,KAAK2C,SAASU,UAAUE,SApKP,UAwKDrD,EAAaqB,QAAQvB,KAAK2C,SA5KjC,oBA6KGb,iBAAf,CAIA,IAAM0M,EAAYxO,KAAKyO,gBAEvBzO,KAAK2C,SAAS5G,MAAMyS,GAAgBxO,KAAK2C,SAAS2C,wBAAwBkJ,GAA1E,KAEAlS,EAAO0D,KAAK2C,UAEZ3C,KAAK2C,SAASU,UAAU2H,IAjLE,cAkL1BhL,KAAK2C,SAASU,UAAUC,OAnLA,WADJ,QAsLpB,IAAMsL,EAAqB5O,KAAKsN,cAAc1O,OAC9C,GAAIgQ,EAAqB,EACvB,IAAK,IAAIlQ,EAAI,EAAGA,EAAIkQ,EAAoBlQ,IAAK,CAC3C,IAAM6C,EAAUvB,KAAKsN,cAAc5O,GAC7B+O,EAAOvU,EAAuBqI,GAEhCkM,IAASA,EAAKpK,UAAUE,SA5LZ,UA6LdhC,EAAQ8B,UAAU2H,IA1LC,aA2LnBzJ,EAAQ6C,aAAa,iBAAiB,IAK5CpE,KAAK0O,kBAAiB,GAStB1O,KAAK2C,SAAS5G,MAAMyS,GAAa,GACjC,IAAMjV,EAAqBJ,EAAiC6G,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,SR1PH,iBQgPF,WACfyH,EAAKsE,kBAAiB,GACtBtE,EAAKzH,SAASU,UAAUC,OArMA,cAsMxB8G,EAAKzH,SAASU,UAAU2H,IAvMF,YAwMtB9K,EAAaqB,QAAQ6I,EAAKzH,SA5Md,yBAmNdxI,EAAqB6F,KAAK2C,SAAUpJ,OAGtCmV,iBAAA,SAAiBG,GACf7O,KAAKqN,iBAAmBwB,KAG1B3L,QAAA,WACE3F,EAAgByC,KAAK2C,SA5OR,eA8Ob3C,KAAK0I,QAAU,KACf1I,KAAK6N,QAAU,KACf7N,KAAK2C,SAAW,KAChB3C,KAAKsN,cAAgB,KACrBtN,KAAKqN,iBAAmB,QAK1B1E,WAAA,SAAW9N,GAOT,OANAA,EAAMoK,EAAA,GACDqC,GACAzM,IAEEsJ,OAAS1D,QAAQ5F,EAAOsJ,QAC/BxJ,EAAgB8H,EAAM5H,EAAQgN,IACvBhN,KAGT4T,cAAA,WACE,OAAOzO,KAAK2C,SAASU,UAAUE,SAzOrB,SAAA,QACC,YA2ObuK,WAAA,WAAa,IAAAvD,EAAAvK,KACLmN,EAAWnN,KAAK0I,QAAhByE,OAEFnT,EAAUmT,QAEiB,IAAlBA,EAAO2B,aAA+C,IAAd3B,EAAO,KACxDA,EAASA,EAAO,IAGlBA,EAASrH,EAAeQ,QAAQ6G,GAGlC,IAAMvU,EAAc2U,yCAAqCJ,EAA3C,KAYd,OAVArH,EAAeE,KAAKpN,EAAUuU,GAC3BlS,SAAQ,SAAAtC,GACP,IAAMoW,EAAW7V,EAAuBP,GAExC4R,EAAKwD,0BACHgB,EACA,CAACpW,OAIAwU,KAGTY,0BAAA,SAA0BpV,EAASqW,GACjC,GAAKrW,GAAYqW,EAAapQ,OAA9B,CAIA,IAAMqQ,EAAStW,EAAQ0K,UAAUE,SAjRb,QAmRpByL,EAAa/T,SAAQ,SAAAwS,GACfwB,EACFxB,EAAKpK,UAAUC,OAlRM,aAoRrBmK,EAAKpK,UAAU2H,IApRM,aAuRvByC,EAAKrJ,aAAa,gBAAiB6K,UAMhCV,kBAAP,SAAyB5V,EAASkC,GAChC,IAAIqC,EAAOK,EAAa5E,EArTX,eAsTP+P,EAAOzD,EAAA,GACRqC,GACA3C,EAAYI,kBAAkBpM,GACX,iBAAXkC,GAAuBA,EAASA,EAAS,IAWtD,IARKqC,GAAQwL,EAAQvE,QAA4B,iBAAXtJ,GAAuB,YAAYc,KAAKd,KAC5E6N,EAAQvE,QAAS,GAGdjH,IACHA,EAAO,IAAIkQ,EAASzU,EAAS+P,IAGT,iBAAX7N,EAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,SAIF6I,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACfyJ,EAASmB,kBAAkBvO,KAAMnF,SAI9BiJ,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EApVP,wDA6Eb,MA9EY,+CAkFZ,OAAO2O,SA5CL8F,GAyTNlN,EAAaQ,GAAGlI,SA5UU,6BAWG,4BAiUyC,SAAUqG,GAEjD,MAAzBA,EAAMkB,OAAOmL,SACfrM,EAAM2D,iBAGR,IAAM0M,EAAcvK,EAAYI,kBAAkB/E,MAC5CpH,EAAWI,EAAuBgH,MACf8F,EAAeE,KAAKpN,GAE5BqC,SAAQ,SAAAtC,GACvB,IACIkC,EADEqC,EAAOK,EAAa5E,EAzWb,eA2WTuE,GAEmB,OAAjBA,EAAK2Q,SAAkD,iBAAvBqB,EAAY/B,SAC9CjQ,EAAKwL,QAAQyE,OAAS+B,EAAY/B,OAClCjQ,EAAK2Q,QAAU3Q,EAAK4Q,cAGtBjT,EAAS,UAETA,EAASqU,EAGX9B,GAASmB,kBAAkB5V,EAASkC,SAWxC+B,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAG6C,GAChCf,EAAE9B,GAAG6C,GAAQ2K,GAAS1J,gBACtBhC,EAAE9B,GAAG6C,GAAMuB,YAAcoJ,GACzB1L,EAAE9B,GAAG6C,GAAMwB,WAAa,WAEtB,OADAvC,EAAE9B,GAAG6C,GAAQsB,EACNqJ,GAAS1J,qBC/YtB,IAAMjB,GAAO,WAaP0M,GAAiB,IAAIzT,OAAU0T,4BAiC/B9H,GAAU,CACdlC,OAAQ,EACRiK,MAAM,EACNC,SAAU,eACVC,UAAW,SACXpT,QAAS,UACTqT,aAAc,MAGV3H,GAAc,CAClBzC,OAAQ,2BACRiK,KAAM,UACNC,SAAU,mBACVC,UAAW,mBACXpT,QAAS,SACTqT,aAAc,iBASVC,GAAAA,WACJ,SAAAA,EAAY9W,EAASkC,GACnBmF,KAAK2C,SAAWhK,EAChBqH,KAAK0P,QAAU,KACf1P,KAAK0I,QAAU1I,KAAK2I,WAAW9N,GAC/BmF,KAAK2P,MAAQ3P,KAAK4P,kBAClB5P,KAAK6P,UAAY7P,KAAK8P,gBAEtB9P,KAAKkJ,qBACL3L,EAAa5E,EA7EA,cA6EmBqH,iCAmBlCmE,OAAA,WACE,IAAInE,KAAK2C,SAASoN,WAAY/P,KAAK2C,SAASU,UAAUE,SA3E9B,YA2ExB,CAIA,IAAMyM,EAAWhQ,KAAK2C,SAASU,UAAUE,SA9ErB,QAgFpBkM,EAASQ,aAELD,GAIJhQ,KAAKiO,WAGPA,KAAA,WACE,KAAIjO,KAAK2C,SAASoN,UAAY/P,KAAK2C,SAASU,UAAUE,SA3F9B,aA2F+DvD,KAAK2P,MAAMtM,UAAUE,SA1FxF,SA0FpB,CAIA,IAAM4J,EAASsC,EAASS,qBAAqBlQ,KAAK2C,UAC5C+I,EAAgB,CACpBA,cAAe1L,KAAK2C,UAKtB,IAFkBzC,EAAaqB,QAAQvB,KAAK2C,SA3GhC,mBA2GsD+I,GAEpD5J,iBAAd,CAKA,IAAK9B,KAAK6P,UAAW,CACnB,QAAsB,IAAXM,EAAAA,QACT,MAAM,IAAIrD,UAAU,mEAGtB,IAAIsD,EAAmBpQ,KAAK2C,SAEG,WAA3B3C,KAAK0I,QAAQ6G,UACfa,EAAmBjD,EACVnT,EAAUgG,KAAK0I,QAAQ6G,aAChCa,EAAmBpQ,KAAK0I,QAAQ6G,eAGa,IAAlCvP,KAAK0I,QAAQ6G,UAAUT,SAChCsB,EAAmBpQ,KAAK0I,QAAQ6G,UAAU,KAOhB,iBAA1BvP,KAAK0I,QAAQ4G,UACfnC,EAAO9J,UAAU2H,IA1HU,mBA6H7BhL,KAAK0P,QAAU,IAAIS,EAAAA,QAAOC,EAAkBpQ,KAAK2P,MAAO3P,KAAKqQ,oBAQvB,IAAApK,EADxC,GAAI,iBAAkBzN,SAAS0N,kBAC5BiH,EAAOhK,QAhIc,gBAiItB8C,EAAA,IAAGE,OAAH9F,MAAA4F,EAAazN,SAASkE,KAAK6J,UACxBtL,SAAQ,SAAAwS,GAAI,OAAIvN,EAAaQ,GAAG+M,EAAM,YAAa,MT1BzC,kBS6BfzN,KAAK2C,SAAS2N,QACdtQ,KAAK2C,SAASyB,aAAa,iBAAiB,GAE5CpE,KAAK2P,MAAMtM,UAAUc,OAnJD,QAoJpBnE,KAAK2C,SAASU,UAAUc,OApJJ,QAqJpBjE,EAAaqB,QAAQ4L,EA5JR,oBA4J6BzB,QAG5CsC,KAAA,WACE,IAAIhO,KAAK2C,SAASoN,WAAY/P,KAAK2C,SAASU,UAAUE,SA1J9B,aA0JgEvD,KAAK2P,MAAMtM,UAAUE,SAzJzF,QAyJpB,CAIA,IAAM4J,EAASsC,EAASS,qBAAqBlQ,KAAK2C,UAC5C+I,EAAgB,CACpBA,cAAe1L,KAAK2C,UAGJzC,EAAaqB,QAAQ4L,EA5K3B,mBA4K+CzB,GAE7C5J,mBAIV9B,KAAK0P,SACP1P,KAAK0P,QAAQa,UAGfvQ,KAAK2P,MAAMtM,UAAUc,OA5KD,QA6KpBnE,KAAK2C,SAASU,UAAUc,OA7KJ,QA8KpBjE,EAAaqB,QAAQ4L,EAvLP,qBAuL6BzB,QAG7CxI,QAAA,WACE3F,EAAgByC,KAAK2C,SAzMR,eA0MbzC,EAAaC,IAAIH,KAAK2C,SAzMX,gBA0MX3C,KAAK2C,SAAW,KAChB3C,KAAK2P,MAAQ,KACT3P,KAAK0P,UACP1P,KAAK0P,QAAQa,UACbvQ,KAAK0P,QAAU,SAInBc,OAAA,WACExQ,KAAK6P,UAAY7P,KAAK8P,gBAClB9P,KAAK0P,SACP1P,KAAK0P,QAAQe,oBAMjBvH,mBAAA,WAAqB,IAAA9F,EAAApD,KACnBE,EAAaQ,GAAGV,KAAK2C,SA5MR,qBA4M+B,SAAA9D,GAC1CA,EAAM2D,iBACN3D,EAAM6R,kBACNtN,EAAKe,eAITwE,WAAA,SAAW9N,GAST,OARAA,EAAMoK,EAAA,GACDjF,KAAK2Q,YAAYrJ,QACjB3C,EAAYI,kBAAkB/E,KAAK2C,UACnC9H,GAGLF,EAAgB8H,GAAM5H,EAAQmF,KAAK2Q,YAAY9I,aAExChN,KAGT+U,gBAAA,WACE,OAAO9J,EAAeqB,KAAKnH,KAAK2C,SAhNd,kBAgNuC,MAG3DiO,cAAA,WACE,IAAMC,EAAiB7Q,KAAK2C,SAAS3G,WACjC8U,EA/MiB,eA8NrB,OAZID,EAAexN,UAAUE,SAjOP,UAkOpBuN,EAAY9Q,KAAK2P,MAAMtM,UAAUE,SA/NV,uBAWJ,UADH,YAwNPsN,EAAexN,UAAUE,SApOX,aAqOvBuN,EArNkB,cAsNTD,EAAexN,UAAUE,SArOZ,YAsOtBuN,EAtNiB,aAuNR9Q,KAAK2P,MAAMtM,UAAUE,SAtOP,yBAuOvBuN,EA1NsB,cA6NjBA,KAGThB,cAAA,WACE,OAAOrP,QAAQT,KAAK2C,SAASQ,QAAd,eAGjB4N,WAAA,WAAa,IAAA3G,EAAApK,KACLoF,EAAS,GAef,MAbmC,mBAAxBpF,KAAK0I,QAAQtD,OACtBA,EAAOxF,GAAK,SAAA1C,GAMV,OALAA,EAAK8T,QAAL/L,EAAA,GACK/H,EAAK8T,QACJ5G,EAAK1B,QAAQtD,OAAOlI,EAAK8T,QAAS5G,EAAKzH,WAAa,IAGnDzF,GAGTkI,EAAOA,OAASpF,KAAK0I,QAAQtD,OAGxBA,KAGTiL,iBAAA,WACE,IAAMb,EAAe,CACnBsB,UAAW9Q,KAAK4Q,gBAChBK,UAAW,CACT7L,OAAQpF,KAAK+Q,aACb1B,KAAM,CACJ6B,QAASlR,KAAK0I,QAAQ2G,MAExB8B,gBAAiB,CACfC,kBAAmBpR,KAAK0I,QAAQ4G,YAYtC,MAN6B,WAAzBtP,KAAK0I,QAAQvM,UACfqT,EAAayB,UAAUI,WAAa,CAClCH,SAAS,IAIbjM,EAAA,GACKuK,EACAxP,KAAK0I,QAAQ8G,iBAMb8B,kBAAP,SAAyB3Y,EAASkC,GAChC,IAAIqC,EAAOK,EAAa5E,EA7TX,eAoUb,GAJKuE,IACHA,EAAO,IAAIuS,EAAS9W,EAHY,iBAAXkC,EAAsBA,EAAS,OAMhC,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,SAIF6I,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACf8L,EAAS6B,kBAAkBtR,KAAMnF,SAI9BoV,WAAP,SAAkBpR,GAChB,IAAIA,GA3UmB,IA2UTA,EAAM6F,SACF,UAAf7F,EAAMuB,MA/UG,QA+UiBvB,EAAM5B,KAMnC,IAFA,IAAMsU,EAAUzL,EAAeE,KA1TN,4BA4ThBtH,EAAI,EAAGC,EAAM4S,EAAQ3S,OAAQF,EAAIC,EAAKD,IAAK,CAClD,IAAMyO,EAASsC,EAASS,qBAAqBqB,EAAQ7S,IAC/C8S,EAAUjU,EAAagU,EAAQ7S,GA7V1B,eA8VLgN,EAAgB,CACpBA,cAAe6F,EAAQ7S,IAOzB,GAJIG,GAAwB,UAAfA,EAAMuB,OACjBsL,EAAc+F,WAAa5S,GAGxB2S,EAAL,CAIA,IAAME,EAAeF,EAAQ7B,MAC7B,GAAK4B,EAAQ7S,GAAG2E,UAAUE,SApVR,QAwVlB,KAAI1E,IAA0B,UAAfA,EAAMuB,MACjB,kBAAkBzE,KAAKkD,EAAMkB,OAAOmL,UACpB,UAAfrM,EAAMuB,MA3WD,QA2WqBvB,EAAM5B,MACjCyU,EAAanO,SAAS1E,EAAMkB,SAKhC,IADkBG,EAAaqB,QAAQ4L,EAzW7B,mBAyWiDzB,GAC7C5J,iBAAd,CAMgD,IAAA0E,EAAhD,GAAI,iBAAkBhO,SAAS0N,iBAC7BM,EAAA,IAAGL,OAAH9F,MAAAmG,EAAahO,SAASkE,KAAK6J,UACxBtL,SAAQ,SAAAwS,GAAI,OAAIvN,EAAaC,IAAIsN,EAAM,YAAa,MTrP5C,kBSwPb8D,EAAQ7S,GAAG0F,aAAa,gBAAiB,SAErCoN,EAAQ9B,SACV8B,EAAQ9B,QAAQa,UAGlBmB,EAAarO,UAAUC,OAjXL,QAkXlBiO,EAAQ7S,GAAG2E,UAAUC,OAlXH,QAmXlBpD,EAAaqB,QAAQ4L,EA5XT,qBA4X+BzB,SAIxCwE,qBAAP,SAA4BvX,GAC1B,OAAOO,EAAuBP,IAAYA,EAAQqD,cAG7C2V,sBAAP,SAA6B9S,GAQ3B,KAAI,kBAAkBlD,KAAKkD,EAAMkB,OAAOmL,SArZ1B,UAsZZrM,EAAM5B,KAvZO,WAuZe4B,EAAM5B,MAnZjB,cAoZf4B,EAAM5B,KArZO,YAqZmB4B,EAAM5B,KACtC4B,EAAMkB,OAAOoD,QA5XC,oBA6XfgM,GAAexT,KAAKkD,EAAM5B,QAI7B4B,EAAM2D,iBACN3D,EAAM6R,mBAEF1Q,KAAK+P,WAAY/P,KAAKqD,UAAUE,SA/YZ,aA+YxB,CAIA,IAAM4J,EAASsC,EAASS,qBAAqBlQ,MACvCgQ,EAAWhQ,KAAKqD,UAAUE,SAnZZ,QAqZpB,GAxae,WAwaX1E,EAAM5B,IAIR,OAHe+C,KAAK+F,QA9YG,4BA8Y6B/F,KAAO8F,EAAekB,KAAKhH,KA9YxD,4BA8YoF,IACpGsQ,aACPb,EAASQ,aAIX,GAAKD,GA9aS,UA8aGnR,EAAM5B,IAAvB,CAKA,IAAM2U,EAAQ9L,EAAeE,KArZF,8DAqZ+BmH,GAAQ1G,OAAO3K,GAEzE,GAAK8V,EAAMhT,OAAX,CAIA,IAAIiL,EAAQ+H,EAAMtS,QAAQT,EAAMkB,QAvbf,YAybblB,EAAM5B,KAAwB4M,EAAQ,GACxCA,IAzbiB,cA4bfhL,EAAM5B,KAA0B4M,EAAQ+H,EAAMhT,OAAS,GACzDiL,IAMF+H,EAFA/H,GAAmB,IAAXA,EAAe,EAAIA,GAEdyG,cAvBXb,EAASQ,iBA0BNnM,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EA/cP,wDAmFb,MApFY,+CAwFZ,OAAO2O,uCAIP,OAAOO,SAvBL4H,GAqZNvP,EAAaQ,GAAGlI,SAtcY,+BAYC,2BA0b2CiX,GAASkC,uBACjFzR,EAAaQ,GAAGlI,SAvcY,+BAcN,iBAyb2CiX,GAASkC,uBAC1EzR,EAAaQ,GAAGlI,SAzcU,6BAycsBiX,GAASQ,YACzD/P,EAAaQ,GAAGlI,SAxcU,6BAwcsBiX,GAASQ,YACzD/P,EAAaQ,GAAGlI,SA3cU,6BAaG,4BA8byC,SAAUqG,GAC9EA,EAAM2D,iBACN3D,EAAM6R,kBACNjB,GAAS6B,kBAAkBtR,KAAM,aAEnCE,EAAaQ,GAAGlI,SAhdU,6BAcE,kBAkcyC,SAAAuS,GAAC,OAAIA,EAAE2F,qBAS5E9T,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAG6C,IAChCf,EAAE9B,GAAG6C,IAAQgN,GAAS/L,gBACtBhC,EAAE9B,GAAG6C,IAAMuB,YAAcyL,GACzB/N,EAAE9B,GAAG6C,IAAMwB,WAAa,WAEtB,OADAvC,EAAE9B,GAAG6C,IAAQsB,EACN0L,GAAS/L,qBCrftB,IAOM4D,GAAU,CACduK,UAAU,EACVrK,UAAU,EACV8I,OAAO,EACPrC,MAAM,GAGFpG,GAAc,CAClBgK,SAAU,mBACVrK,SAAU,UACV8I,MAAO,UACPrC,KAAM,WAoCF6D,GAAAA,WACJ,SAAAA,EAAYnZ,EAASkC,GACnBmF,KAAK0I,QAAU1I,KAAK2I,WAAW9N,GAC/BmF,KAAK2C,SAAWhK,EAChBqH,KAAK+R,QAAUjM,EAAeQ,QAjBV,gBAiBmC3N,GACvDqH,KAAKgS,UAAY,KACjBhS,KAAKiS,UAAW,EAChBjS,KAAKkS,oBAAqB,EAC1BlS,KAAKmS,sBAAuB,EAC5BnS,KAAKqN,kBAAmB,EACxBrN,KAAKoS,gBAAkB,EACvB7U,EAAa5E,EA/DA,WA+DmBqH,iCAelCmE,OAAA,SAAOuH,GACL,OAAO1L,KAAKiS,SAAWjS,KAAKgO,OAAShO,KAAKiO,KAAKvC,MAGjDuC,KAAA,SAAKvC,GAAe,IAAAtI,EAAApD,KAClB,IAAIA,KAAKiS,WAAYjS,KAAKqN,iBAA1B,CAIIrN,KAAK2C,SAASU,UAAUE,SApDR,UAqDlBvD,KAAKqN,kBAAmB,GAG1B,IAAMgF,EAAYnS,EAAaqB,QAAQvB,KAAK2C,SArEhC,gBAqEsD,CAChE+I,cAAAA,IAGE1L,KAAKiS,UAAYI,EAAUvQ,mBAI/B9B,KAAKiS,UAAW,EAEhBjS,KAAKsS,kBACLtS,KAAKuS,gBAELvS,KAAKwS,gBAELxS,KAAKyS,kBACLzS,KAAK0S,kBAELxS,EAAaQ,GAAGV,KAAK2C,SAnFA,yBAgBK,0BAsExB,SAAA9D,GAAK,OAAIuE,EAAK4K,KAAKnP,MAGrBqB,EAAaQ,GAAGV,KAAK+R,QAtFI,8BAsF8B,WACrD7R,EAAaS,IAAIyC,EAAKT,SAxFD,4BAwFkC,SAAA9D,GACjDA,EAAMkB,SAAWqD,EAAKT,WACxBS,EAAK+O,sBAAuB,SAKlCnS,KAAK2S,eAAc,WAAA,OAAMvP,EAAKwP,aAAalH,WAG7CsC,KAAA,SAAKnP,GAAO,IAAAuL,EAAApK,KAKV,IAJInB,GACFA,EAAM2D,iBAGHxC,KAAKiS,WAAYjS,KAAKqN,oBAITnN,EAAaqB,QAAQvB,KAAK2C,SApHhC,iBAsHEb,iBAAd,CAIA9B,KAAKiS,UAAW,EAChB,IAAMY,EAAa7S,KAAK2C,SAASU,UAAUE,SA3GvB,QA2HpB,GAdIsP,IACF7S,KAAKqN,kBAAmB,GAG1BrN,KAAKyS,kBACLzS,KAAK0S,kBAELxS,EAAaC,IAAI3H,SA/HF,oBAiIfwH,KAAK2C,SAASU,UAAUC,OArHJ,QAuHpBpD,EAAaC,IAAIH,KAAK2C,SAjID,0BAkIrBzC,EAAaC,IAAIH,KAAK+R,QA/HG,8BAiIrBc,EAAY,CACd,IAAMtZ,EAAqBJ,EAAiC6G,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,SVvLL,iBUuL+B,SAAA9D,GAAK,OAAIuL,EAAK0I,WAAWjU,MACzE1E,EAAqB6F,KAAK2C,SAAUpJ,QAEpCyG,KAAK8S,iBAIT5P,QAAA,WACE,CAAC7J,OAAQ2G,KAAK2C,SAAU3C,KAAK+R,SAC1B9W,SAAQ,SAAA8X,GAAW,OAAI7S,EAAaC,IAAI4S,EAzKhC,gBAgLX7S,EAAaC,IAAI3H,SAzJF,oBA2Jf+E,EAAgByC,KAAK2C,SAnLR,YAqLb3C,KAAK0I,QAAU,KACf1I,KAAK2C,SAAW,KAChB3C,KAAK+R,QAAU,KACf/R,KAAKgS,UAAY,KACjBhS,KAAKiS,SAAW,KAChBjS,KAAKkS,mBAAqB,KAC1BlS,KAAKmS,qBAAuB,KAC5BnS,KAAKqN,iBAAmB,KACxBrN,KAAKoS,gBAAkB,QAGzBY,aAAA,WACEhT,KAAKwS,mBAKP7J,WAAA,SAAW9N,GAMT,OALAA,EAAMoK,EAAA,GACDqC,GACAzM,GAELF,EA7MS,QA6MaE,EAAQgN,IACvBhN,KAGT+X,aAAA,SAAalH,GAAe,IAAAnB,EAAAvK,KACpB6S,EAAa7S,KAAK2C,SAASU,UAAUE,SA7KvB,QA8Kd0P,EAAYnN,EAAeQ,QAzKT,cAyKsCtG,KAAK+R,SAE9D/R,KAAK2C,SAAS3G,YACfgE,KAAK2C,SAAS3G,WAAW9B,WAAa2M,KAAKC,cAE7CtO,SAASkE,KAAKwW,YAAYlT,KAAK2C,UAGjC3C,KAAK2C,SAAS5G,MAAMI,QAAU,QAC9B6D,KAAK2C,SAASmC,gBAAgB,eAC9B9E,KAAK2C,SAASyB,aAAa,cAAc,GACzCpE,KAAK2C,SAASyB,aAAa,OAAQ,UACnCpE,KAAK2C,SAAS6C,UAAY,EAEtByN,IACFA,EAAUzN,UAAY,GAGpBqN,GACFvW,EAAO0D,KAAK2C,UAGd3C,KAAK2C,SAASU,UAAU2H,IAnMJ,QAqMhBhL,KAAK0I,QAAQ4H,OACftQ,KAAKmT,gBAGP,IAAMC,EAAqB,WACrB7I,EAAK7B,QAAQ4H,OACf/F,EAAK5H,SAAS2N,QAGhB/F,EAAK8C,kBAAmB,EACxBnN,EAAaqB,QAAQgJ,EAAK5H,SA5Nf,iBA4NsC,CAC/C+I,cAAAA,KAIJ,GAAImH,EAAY,CACd,IAAMtZ,EAAqBJ,EAAiC6G,KAAK+R,SAEjE7R,EAAaS,IAAIX,KAAK+R,QVjRL,gBUiR8BqB,GAC/CjZ,EAAqB6F,KAAK+R,QAASxY,QAEnC6Z,OAIJD,cAAA,WAAgB,IAAA5G,EAAAvM,KACdE,EAAaC,IAAI3H,SA3OF,oBA4Of0H,EAAaQ,GAAGlI,SA5OD,oBA4O0B,SAAAqG,GACnCrG,WAAaqG,EAAMkB,QACnBwM,EAAK5J,WAAa9D,EAAMkB,QACvBwM,EAAK5J,SAASY,SAAS1E,EAAMkB,SAChCwM,EAAK5J,SAAS2N,cAKpBmC,gBAAA,WAAkB,IAAAY,EAAArT,KACZA,KAAKiS,SACP/R,EAAaQ,GAAGV,KAAK2C,SApPA,4BAoPiC,SAAA9D,GAChDwU,EAAK3K,QAAQlB,UA7QN,WA6QkB3I,EAAM5B,KACjC4B,EAAM2D,iBACN6Q,EAAKrF,QACKqF,EAAK3K,QAAQlB,UAhRd,WAgR0B3I,EAAM5B,KACzCoW,EAAKC,gCAITpT,EAAaC,IAAIH,KAAK2C,SA7PD,+BAiQzB+P,gBAAA,WAAkB,IAAAa,EAAAvT,KACZA,KAAKiS,SACP/R,EAAaQ,GAAGrH,OArQJ,mBAqQ0B,WAAA,OAAMka,EAAKf,mBAEjDtS,EAAaC,IAAI9G,OAvQL,sBA2QhByZ,WAAA,WAAa,IAAAU,EAAAxT,KACXA,KAAK2C,SAAS5G,MAAMI,QAAU,OAC9B6D,KAAK2C,SAASyB,aAAa,eAAe,GAC1CpE,KAAK2C,SAASmC,gBAAgB,cAC9B9E,KAAK2C,SAASmC,gBAAgB,QAC9B9E,KAAKqN,kBAAmB,EACxBrN,KAAK2S,eAAc,WACjBna,SAASkE,KAAK2G,UAAUC,OAzQN,cA0QlBkQ,EAAKC,oBACLD,EAAKE,kBACLxT,EAAaqB,QAAQiS,EAAK7Q,SAzRd,yBA6RhBgR,gBAAA,WACE3T,KAAKgS,UAAUhW,WAAWyH,YAAYzD,KAAKgS,WAC3ChS,KAAKgS,UAAY,QAGnBW,cAAA,SAAc9V,GAAU,IAAA+W,EAAA5T,KAChB6T,EAAU7T,KAAK2C,SAASU,UAAUE,SArRpB,QAAA,OAuRlB,GAEF,GAAIvD,KAAKiS,UAAYjS,KAAK0I,QAAQmJ,SAAU,CA6B1C,GA5BA7R,KAAKgS,UAAYxZ,SAASsb,cAAc,OACxC9T,KAAKgS,UAAU+B,UA7RO,iBA+RlBF,GACF7T,KAAKgS,UAAU3O,UAAU2H,IAAI6I,GAG/Brb,SAASkE,KAAKwW,YAAYlT,KAAKgS,WAE/B9R,EAAaQ,GAAGV,KAAK2C,SA5SF,0BA4SiC,SAAA9D,GAC9C+U,EAAKzB,qBACPyB,EAAKzB,sBAAuB,EAI1BtT,EAAMkB,SAAWlB,EAAMmV,eAI3BJ,EAAKN,gCAGHO,GACFvX,EAAO0D,KAAKgS,WAGdhS,KAAKgS,UAAU3O,UAAU2H,IAnTP,SAqTb6I,EAEH,YADAhX,IAIF,IAAMoX,EAA6B9a,EAAiC6G,KAAKgS,WAEzE9R,EAAaS,IAAIX,KAAKgS,UVtXL,gBUsXgCnV,GACjD1C,EAAqB6F,KAAKgS,UAAWiC,QAChC,IAAKjU,KAAKiS,UAAYjS,KAAKgS,UAAW,CAC3ChS,KAAKgS,UAAU3O,UAAUC,OA/TP,QAiUlB,IAAM4Q,EAAiB,WACrBN,EAAKD,kBACL9W,KAGF,GAAImD,KAAK2C,SAASU,UAAUE,SAvUV,QAuUqC,CACrD,IAAM0Q,EAA6B9a,EAAiC6G,KAAKgS,WACzE9R,EAAaS,IAAIX,KAAKgS,UVlYP,gBUkYkCkC,GACjD/Z,EAAqB6F,KAAKgS,UAAWiC,QAErCC,SAGFrX,OAIJyW,2BAAA,WAA6B,IAAAa,EAAAnU,KAC3B,GAA8B,WAA1BA,KAAK0I,QAAQmJ,SAAuB,CAEtC,GADkB3R,EAAaqB,QAAQvB,KAAK2C,SApWxB,0BAqWNb,iBACZ,OAGF,IAAMsS,EAAqBpU,KAAK2C,SAAS0R,aAAe7b,SAAS0N,gBAAgBoO,aAE5EF,IACHpU,KAAK2C,SAAS5G,MAAMwY,UAAY,UAGlCvU,KAAK2C,SAASU,UAAU2H,IA9VJ,gBA+VpB,IAAMwJ,EAA0Brb,EAAiC6G,KAAK+R,SACtE7R,EAAaC,IAAIH,KAAK2C,SV3ZL,iBU4ZjBzC,EAAaS,IAAIX,KAAK2C,SV5ZL,iBU4Z+B,WAC9CwR,EAAKxR,SAASU,UAAUC,OAlWN,gBAmWb8Q,IACHlU,EAAaS,IAAIwT,EAAKxR,SV/ZT,iBU+ZmC,WAC9CwR,EAAKxR,SAAS5G,MAAMwY,UAAY,MAElCpa,EAAqBga,EAAKxR,SAAU6R,OAGxCra,EAAqB6F,KAAK2C,SAAU6R,GACpCxU,KAAK2C,SAAS2N,aAEdtQ,KAAKgO,UAQTwE,cAAA,WACE,IAAM4B,EACJpU,KAAK2C,SAAS0R,aAAe7b,SAAS0N,gBAAgBoO,cAEnDtU,KAAKkS,oBAAsBkC,IAC9BpU,KAAK2C,SAAS5G,MAAM0Y,YAAiBzU,KAAKoS,gBAA1C,MAGEpS,KAAKkS,qBAAuBkC,IAC9BpU,KAAK2C,SAAS5G,MAAM2Y,aAAkB1U,KAAKoS,gBAA3C,SAIJqB,kBAAA,WACEzT,KAAK2C,SAAS5G,MAAM0Y,YAAc,GAClCzU,KAAK2C,SAAS5G,MAAM2Y,aAAe,MAGrCpC,gBAAA,WACE,IAAMjN,EAAO7M,SAASkE,KAAK4I,wBAC3BtF,KAAKkS,mBAAqB7Z,KAAKsc,MAAMtP,EAAKI,KAAOJ,EAAKuP,OAASvb,OAAOwb,WACtE7U,KAAKoS,gBAAkBpS,KAAK8U,wBAG9BvC,cAAA,WAAgB,IAAAwC,EAAA/U,KACd,GAAIA,KAAKkS,mBAAoB,CAK3BpM,EAAeE,KA7YU,qDA8YtB/K,SAAQ,SAAAtC,GACP,IAAMqc,EAAgBrc,EAAQoD,MAAM2Y,aAC9BO,EAAoB5b,OAAOC,iBAAiBX,GAAS,iBAC3DgM,EAAYC,iBAAiBjM,EAAS,gBAAiBqc,GACvDrc,EAAQoD,MAAM2Y,aAAkBhb,WAAWub,GAAqBF,EAAK3C,gBAArE,QAIJtM,EAAeE,KArZW,eAsZvB/K,SAAQ,SAAAtC,GACP,IAAMuc,EAAevc,EAAQoD,MAAMoZ,YAC7BC,EAAmB/b,OAAOC,iBAAiBX,GAAS,gBAC1DgM,EAAYC,iBAAiBjM,EAAS,eAAgBuc,GACtDvc,EAAQoD,MAAMoZ,YAAiBzb,WAAW0b,GAAoBL,EAAK3C,gBAAnE,QAIJ,IAAM4C,EAAgBxc,SAASkE,KAAKX,MAAM2Y,aACpCO,EAAoB5b,OAAOC,iBAAiBd,SAASkE,MAAM,iBAEjEiI,EAAYC,iBAAiBpM,SAASkE,KAAM,gBAAiBsY,GAC7Dxc,SAASkE,KAAKX,MAAM2Y,aAAkBhb,WAAWub,GAAqBjV,KAAKoS,gBAA3E,KAGF5Z,SAASkE,KAAK2G,UAAU2H,IA/aJ,iBAkbtB0I,gBAAA,WAEE5N,EAAeE,KA3aY,qDA4axB/K,SAAQ,SAAAtC,GACP,IAAM0c,EAAU1Q,EAAYQ,iBAAiBxM,EAAS,sBAC/B,IAAZ0c,IACT1Q,EAAYE,oBAAoBlM,EAAS,iBACzCA,EAAQoD,MAAM2Y,aAAeW,MAKnCvP,EAAeE,KApba,eAqbzB/K,SAAQ,SAAAtC,GACP,IAAM2c,EAAS3Q,EAAYQ,iBAAiBxM,EAAS,qBAC/B,IAAX2c,IACT3Q,EAAYE,oBAAoBlM,EAAS,gBACzCA,EAAQoD,MAAMoZ,YAAcG,MAKlC,IAAMD,EAAU1Q,EAAYQ,iBAAiB3M,SAASkE,KAAM,sBACrC,IAAZ2Y,EACT7c,SAASkE,KAAKX,MAAM2Y,aAAe,IAEnC/P,EAAYE,oBAAoBrM,SAASkE,KAAM,iBAC/ClE,SAASkE,KAAKX,MAAM2Y,aAAeW,MAIvCP,mBAAA,WACE,IAAMS,EAAY/c,SAASsb,cAAc,OACzCyB,EAAUxB,UArdwB,0BAsdlCvb,SAASkE,KAAKwW,YAAYqC,GAC1B,IAAMC,EAAiBD,EAAUjQ,wBAAwBmQ,MAAQF,EAAUG,YAE3E,OADAld,SAASkE,KAAK+G,YAAY8R,GACnBC,KAKF9R,gBAAP,SAAuB7I,EAAQ6Q,GAC7B,OAAO1L,KAAK2D,MAAK,WACf,IAAIzG,EAAOK,EAAayC,KAhgBb,YAigBL0I,EAAOzD,EAAA,GACRqC,GACA3C,EAAYI,kBAAkB/E,MACX,iBAAXnF,GAAuBA,EAASA,EAAS,IAOtD,GAJKqC,IACHA,EAAO,IAAI4U,EAAM9R,KAAM0I,IAGH,iBAAX7N,EAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,GAAQ6Q,QACJhD,EAAQuF,MACjB/Q,EAAK+Q,KAAKvC,SAKT5H,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAxhBP,qDAqEb,MAtEY,+CA0EZ,OAAO2O,SArBLwK,GA8eN5R,EAAaQ,GAAGlI,SApgBU,0BAWG,yBAyfyC,SAAUqG,GAAO,IAAA8W,EAAA3V,KAC/ED,EAAS7G,EAAuB8G,MAEjB,MAAjBA,KAAKkL,SAAoC,SAAjBlL,KAAKkL,SAC/BrM,EAAM2D,iBAGRtC,EAAaS,IAAIZ,EAnhBH,iBAmhBuB,SAAAsS,GAC/BA,EAAUvQ,kBAKd5B,EAAaS,IAAIZ,EA1hBH,mBA0hByB,WACjCjE,EAAU6Z,IACZA,EAAKrF,cAKX,IAAIpT,EAAOK,EAAawC,EAtjBT,YAujBf,IAAK7C,EAAM,CACT,IAAMrC,EAAMoK,EAAA,GACPN,EAAYI,kBAAkBhF,GAC9B4E,EAAYI,kBAAkB/E,OAGnC9C,EAAO,IAAI4U,GAAM/R,EAAQlF,GAG3BqC,EAAK+Q,KAAKjO,SAUZpD,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAF,MAC3B8B,EAAE9B,GAAF,MAAakS,GAAMpO,gBACnBhC,EAAE9B,GAAF,MAAWoE,YAAc8N,GACzBpQ,EAAE9B,GAAF,MAAWqE,WAAa,WAEtB,OADAvC,EAAE9B,GAAF,MAAamE,EACN+N,GAAMpO,qBC3mBnB,IAAMkS,GAAW,CACf,aACA,OACA,OACA,WACA,WACA,SACA,MACA,cAUIC,GAAmB,8DAOnBC,GAAmB,qIAyBZC,GAAmB,CAE9BC,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAzCP,kBA0C7BC,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BC,KAAM,GACNC,EAAG,GACHC,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJrY,EAAG,GACHsY,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IAGC,SAASC,GAAaC,EAAYC,EAAWC,GAAY,IAAA/R,EAC9D,IAAK6R,EAAWlZ,OACd,OAAOkZ,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAQpB,IALA,IACMG,GADY,IAAI5e,OAAO6e,WACKC,gBAAgBL,EAAY,aACxDM,EAAgBrd,OAAOC,KAAK+c,GAC5BM,GAAWpS,EAAA,IAAGE,OAAH9F,MAAA4F,EAAagS,EAAgBvb,KAAKoD,iBAAiB,MAZNwY,EAAA,SAcrD5Z,EAAOC,GAd8C,IAAA6H,EAetD+R,EAAKF,EAAS3Z,GACd8Z,EAASD,EAAGE,SAAShd,cAE3B,IAAuC,IAAnC2c,EAAc9Y,QAAQkZ,GAGxB,OAFAD,EAAGvc,WAAWyH,YAAY8U,GAE1B,WAGF,IAAMG,GAAgBlS,EAAA,IAAGL,OAAH9F,MAAAmG,EAAa+R,EAAGvT,YAChC2T,EAAoB,GAAGxS,OAAO4R,EAAU,MAAQ,GAAIA,EAAUS,IAAW,IAE/EE,EAAczd,SAAQ,SAAA2d,IApFD,SAACA,EAAMC,GAC9B,IAAMC,EAAWF,EAAKH,SAAShd,cAE/B,IAAgD,IAA5Cod,EAAqBvZ,QAAQwZ,GAC/B,OAAoC,IAAhClD,GAAStW,QAAQwZ,IACZrY,QAAQmY,EAAKG,UAAUvd,MAAMqa,KAAqB+C,EAAKG,UAAUvd,MAAMsa,KASlF,IAHA,IAAMkD,EAASH,EAAqBpS,QAAO,SAAAwS,GAAS,OAAIA,aAAqBvd,UAGpEgD,EAAI,EAAGC,EAAMqa,EAAOpa,OAAQF,EAAIC,EAAKD,IAC5C,GAAIoa,EAAStd,MAAMwd,EAAOta,IACxB,OAAO,EAIX,OAAO,GAiEEwa,CAAiBN,EAAMD,IAC1BJ,EAAGzT,gBAAgB8T,EAAKH,cAfrB/Z,EAAI,EAAGC,EAAM0Z,EAASzZ,OAAQF,EAAIC,EAAKD,IAAK4Z,EAA5C5Z,GAoBT,OAAOuZ,EAAgBvb,KAAKyc,UC1F9B,IAAM1W,GAAO,UAKP2W,GAAqB,IAAI1d,OAAJ,wBAAyC,KAC9D2d,GAAwB,CAAC,WAAY,YAAa,cAElDxR,GAAc,CAClByR,UAAW,UACXC,SAAU,SACVC,MAAO,4BACPjY,QAAS,SACTkY,MAAO,kBACPC,KAAM,UACN9gB,SAAU,mBACVkY,UAAW,oBACX1L,OAAQ,2BACRgJ,UAAW,2BACXuL,kBAAmB,iBACnBrK,SAAU,mBACVsK,SAAU,UACV5B,WAAY,kBACZD,UAAW,SACXvI,aAAc,iBAGVqK,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAO,QACPC,OAAQ,SACRC,KAAM,QAGF5S,GAAU,CACdgS,WAAW,EACXC,SAAU,+GAGVhY,QAAS,cACTiY,MAAO,GACPC,MAAO,EACPC,MAAM,EACN9gB,UAAU,EACVkY,UAAW,MACX1L,OAAQ,EACRgJ,WAAW,EACXuL,kBAAmB,OACnBrK,SAAU,eACVsK,UAAU,EACV5B,WAAY,KACZD,UAAWhC,GACXvG,aAAc,MAGVzV,GAAQ,CACZogB,KAAI,kBACJC,OAAM,oBACNC,KAAI,kBACJC,MAAK,mBACLC,SAAQ,sBACRC,MAAK,mBACLC,QAAO,qBACPC,SAAQ,sBACRC,WAAU,wBACVC,WAAU,yBAuBNC,GAAAA,WACJ,SAAAA,EAAYliB,EAASkC,GACnB,QAAsB,IAAXsV,EAAAA,QACT,MAAM,IAAIrD,UAAU,kEAItB9M,KAAK8a,YAAa,EAClB9a,KAAK+a,SAAW,EAChB/a,KAAKgb,YAAc,GACnBhb,KAAKib,eAAiB,GACtBjb,KAAK0P,QAAU,KAGf1P,KAAKrH,QAAUA,EACfqH,KAAKnF,OAASmF,KAAK2I,WAAW9N,GAC9BmF,KAAKkb,IAAM,KAEXlb,KAAKmb,gBACL5d,EAAa5E,EAASqH,KAAK2Q,YAAYyK,SAAUpb,iCAmCnDqb,OAAA,WACErb,KAAK8a,YAAa,KAGpBQ,QAAA,WACEtb,KAAK8a,YAAa,KAGpBS,cAAA,WACEvb,KAAK8a,YAAc9a,KAAK8a,cAG1B3W,OAAA,SAAOtF,GACL,GAAKmB,KAAK8a,WAIV,GAAIjc,EAAO,CACT,IAAM2c,EAAUxb,KAAK2Q,YAAYyK,SAC7B5J,EAAUjU,EAAasB,EAAMoB,eAAgBub,GAE5ChK,IACHA,EAAU,IAAIxR,KAAK2Q,YACjB9R,EAAMoB,eACND,KAAKyb,sBAEPle,EAAasB,EAAMoB,eAAgBub,EAAShK,IAG9CA,EAAQyJ,eAAeS,OAASlK,EAAQyJ,eAAeS,MAEnDlK,EAAQmK,uBACVnK,EAAQoK,OAAO,KAAMpK,GAErBA,EAAQqK,OAAO,KAAMrK,OAElB,CACL,GAAIxR,KAAK8b,gBAAgBzY,UAAUE,SA7GjB,QA+GhB,YADAvD,KAAK6b,OAAO,KAAM7b,MAIpBA,KAAK4b,OAAO,KAAM5b,UAItBkD,QAAA,WACE2H,aAAa7K,KAAK+a,UAElBxd,EAAgByC,KAAKrH,QAASqH,KAAK2Q,YAAYyK,UAE/Clb,EAAaC,IAAIH,KAAKrH,QAASqH,KAAK2Q,YAAYtJ,WAChDnH,EAAaC,IAAIH,KAAKrH,QAAQwK,QAAb,UAA8C,gBAAiBnD,KAAK+b,mBAEjF/b,KAAKkb,KACPlb,KAAKkb,IAAIlf,WAAWyH,YAAYzD,KAAKkb,KAGvClb,KAAK8a,WAAa,KAClB9a,KAAK+a,SAAW,KAChB/a,KAAKgb,YAAc,KACnBhb,KAAKib,eAAiB,KAClBjb,KAAK0P,SACP1P,KAAK0P,QAAQa,UAGfvQ,KAAK0P,QAAU,KACf1P,KAAKrH,QAAU,KACfqH,KAAKnF,OAAS,KACdmF,KAAKkb,IAAM,QAGbjN,KAAA,WAAO,IAAA7K,EAAApD,KACL,GAAmC,SAA/BA,KAAKrH,QAAQoD,MAAMI,QACrB,MAAM,IAAIP,MAAM,uCAGlB,GAAIoE,KAAKgc,iBAAmBhc,KAAK8a,WAAY,CAC3C,IAAMzI,EAAYnS,EAAaqB,QAAQvB,KAAKrH,QAASqH,KAAK2Q,YAAY5W,MAAMsgB,MACtE4B,EZhHW,SAAjBC,EAAiBvjB,GACrB,IAAKH,SAAS0N,gBAAgBiW,aAC5B,OAAO,KAIT,GAAmC,mBAAxBxjB,EAAQyjB,YAA4B,CAC7C,IAAMC,EAAO1jB,EAAQyjB,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAI1jB,aAAmB2jB,WACd3jB,EAIJA,EAAQqD,WAINkgB,EAAevjB,EAAQqD,YAHrB,KY+FckgB,CAAelc,KAAKrH,SACjC4jB,EAA4B,OAAfN,EACjBjc,KAAKrH,QAAQ6jB,cAActW,gBAAgB3C,SAASvD,KAAKrH,SACzDsjB,EAAW1Y,SAASvD,KAAKrH,SAE3B,GAAI0Z,EAAUvQ,mBAAqBya,EACjC,OAGF,IAAMrB,EAAMlb,KAAK8b,gBACXW,EAAQtkB,EAAO6H,KAAK2Q,YAAYlO,MAEtCyY,EAAI9W,aAAa,KAAMqY,GACvBzc,KAAKrH,QAAQyL,aAAa,mBAAoBqY,GAE9Czc,KAAK0c,aAED1c,KAAKnF,OAAOye,WACd4B,EAAI7X,UAAU2H,IA3KE,QA8KlB,IAAM8F,EAA6C,mBAA1B9Q,KAAKnF,OAAOiW,UACnC9Q,KAAKnF,OAAOiW,UAAUvV,KAAKyE,KAAMkb,EAAKlb,KAAKrH,SAC3CqH,KAAKnF,OAAOiW,UAER6L,EAAa3c,KAAK4c,eAAe9L,GACvC9Q,KAAK6c,oBAAoBF,GAEzB,IAiBgD1W,EAjB1CmI,EAAYpO,KAAK8c,gBAiBvB,GAhBAvf,EAAa2d,EAAKlb,KAAK2Q,YAAYyK,SAAUpb,MAExCA,KAAKrH,QAAQ6jB,cAActW,gBAAgB3C,SAASvD,KAAKkb,MAC5D9M,EAAU8E,YAAYgI,GAGxBhb,EAAaqB,QAAQvB,KAAKrH,QAASqH,KAAK2Q,YAAY5W,MAAMwgB,UAE1Dva,KAAK0P,QAAU,IAAIS,EAAAA,QAAOnQ,KAAKrH,QAASuiB,EAAKlb,KAAKqQ,iBAAiBsM,IAEnEzB,EAAI7X,UAAU2H,IA9LI,QAoMd,iBAAkBxS,SAAS0N,iBAC7BD,EAAA,IAAGE,OAAH9F,MAAA4F,EAAazN,SAASkE,KAAK6J,UAAUtL,SAAQ,SAAAtC,GAC3CuH,EAAaQ,GAAG/H,EAAS,aZxIhB,kBY4Ib,IAAMokB,EAAW,WACX3Z,EAAKvI,OAAOye,WACdlW,EAAK4Z,iBAGP,IAAMC,EAAiB7Z,EAAK4X,YAC5B5X,EAAK4X,YAAc,KAEnB9a,EAAaqB,QAAQ6B,EAAKzK,QAASyK,EAAKuN,YAAY5W,MAAMugB,OA/M1C,QAiNZ2C,GACF7Z,EAAKyY,OAAO,KAAMzY,IAItB,GAAIpD,KAAKkb,IAAI7X,UAAUE,SA3NL,QA2NgC,CAChD,IAAMhK,EAAqBJ,EAAiC6G,KAAKkb,KACjEhb,EAAaS,IAAIX,KAAKkb,IZ5TP,gBY4T4B6B,GAC3C5iB,EAAqB6F,KAAKkb,IAAK3hB,QAE/BwjB,QAKN/O,KAAA,WAAO,IAAA5D,EAAApK,KACL,GAAKA,KAAK0P,QAAV,CAIA,IAAMwL,EAAMlb,KAAK8b,gBACXiB,EAAW,WAvOI,SAwOf3S,EAAK4Q,aAAoCE,EAAIlf,YAC/Ckf,EAAIlf,WAAWyH,YAAYyX,GAG7B9Q,EAAK8S,iBACL9S,EAAKzR,QAAQmM,gBAAgB,oBAC7B5E,EAAaqB,QAAQ6I,EAAKzR,QAASyR,EAAKuG,YAAY5W,MAAMqgB,QAC1DhQ,EAAKsF,QAAQa,WAIf,IADkBrQ,EAAaqB,QAAQvB,KAAKrH,QAASqH,KAAK2Q,YAAY5W,MAAMogB,MAC9DrY,iBAAd,CAQgD,IAAA0E,EAAhD,GAJA0U,EAAI7X,UAAUC,OAzPM,QA6PhB,iBAAkB9K,SAAS0N,iBAC7BM,EAAA,IAAGL,OAAH9F,MAAAmG,EAAahO,SAASkE,KAAK6J,UACxBtL,SAAQ,SAAAtC,GAAO,OAAIuH,EAAaC,IAAIxH,EAAS,YAAa0D,MAO/D,GAJA2D,KAAKib,eAAL,OAAqC,EACrCjb,KAAKib,eAAL,OAAqC,EACrCjb,KAAKib,eAAL,OAAqC,EAEjCjb,KAAKkb,IAAI7X,UAAUE,SAxQH,QAwQ8B,CAChD,IAAMhK,EAAqBJ,EAAiC+hB,GAE5Dhb,EAAaS,IAAIua,EZ1WA,gBY0WqB6B,GACtC5iB,EAAqB+gB,EAAK3hB,QAE1BwjB,IAGF/c,KAAKgb,YAAc,QAGrBxK,OAAA,WACuB,OAAjBxQ,KAAK0P,SACP1P,KAAK0P,QAAQe,oBAMjBuL,cAAA,WACE,OAAOvb,QAAQT,KAAKmd,eAGtBrB,cAAA,WACE,GAAI9b,KAAKkb,IACP,OAAOlb,KAAKkb,IAGd,IAAMviB,EAAUH,SAASsb,cAAc,OAIvC,OAHAnb,EAAQwgB,UAAYnZ,KAAKnF,OAAO0e,SAEhCvZ,KAAKkb,IAAMviB,EAAQ4N,SAAS,GACrBvG,KAAKkb,OAGdwB,WAAA,WACE,IAAMxB,EAAMlb,KAAK8b,gBACjB9b,KAAKod,kBAAkBtX,EAAeQ,QAvSX,iBAuS2C4U,GAAMlb,KAAKmd,YACjFjC,EAAI7X,UAAUC,OA/SM,OAEA,WAgTtB8Z,kBAAA,SAAkBzkB,EAAS0kB,GACzB,GAAgB,OAAZ1kB,EAIJ,MAAuB,iBAAZ0kB,GAAwBrjB,EAAUqjB,IACvCA,EAAQvO,SACVuO,EAAUA,EAAQ,SAIhBrd,KAAKnF,OAAO6e,KACV2D,EAAQrhB,aAAerD,IACzBA,EAAQwgB,UAAY,GACpBxgB,EAAQua,YAAYmK,IAGtB1kB,EAAQ2kB,YAAcD,EAAQC,mBAM9Btd,KAAKnF,OAAO6e,MACV1Z,KAAKnF,OAAO+e,WACdyD,EAAUxF,GAAawF,EAASrd,KAAKnF,OAAOkd,UAAW/X,KAAKnF,OAAOmd,aAGrErf,EAAQwgB,UAAYkE,GAEpB1kB,EAAQ2kB,YAAcD,MAI1BF,SAAA,WACE,IAAI3D,EAAQxZ,KAAKrH,QAAQE,aAAa,uBAQtC,OANK2gB,IACHA,EAAqC,mBAAtBxZ,KAAKnF,OAAO2e,MACzBxZ,KAAKnF,OAAO2e,MAAMje,KAAKyE,KAAKrH,SAC5BqH,KAAKnF,OAAO2e,OAGTA,KAKTnJ,iBAAA,SAAiBsM,GAAY,IAAApS,EAAAvK,KAuB3B,OAAAiF,EAAA,GAtBwB,CACtB6L,UAAW6L,EACX1L,UAAW,CACT7L,OAAQpF,KAAK+Q,aACb1B,KAAM,CACJkO,SAAUvd,KAAKnF,OAAO8e,mBAExB6D,MAAO,CACL7kB,QAAO,IAAMqH,KAAK2Q,YAAYlO,KAAvB,UAET0O,gBAAiB,CACfC,kBAAmBpR,KAAKnF,OAAOyU,WAGnCmO,SAAU,SAAAvgB,GACJA,EAAKwgB,oBAAsBxgB,EAAK4T,WAClCvG,EAAKoT,6BAA6BzgB,IAGtC0gB,SAAU,SAAA1gB,GAAI,OAAIqN,EAAKoT,6BAA6BzgB,KAKjD8C,KAAKnF,OAAO2U,iBAInBqN,oBAAA,SAAoBF,GAClB3c,KAAK8b,gBAAgBzY,UAAU2H,IAAO6S,cAAgBlB,MAGxD5L,WAAA,WAAa,IAAAxE,EAAAvM,KACLoF,EAAS,GAef,MAbkC,mBAAvBpF,KAAKnF,OAAOuK,OACrBA,EAAOxF,GAAK,SAAA1C,GAMV,OALAA,EAAK8T,QAAL/L,EAAA,GACK/H,EAAK8T,QACJzE,EAAK1R,OAAOuK,OAAOlI,EAAK8T,QAASzE,EAAK5T,UAAY,IAGjDuE,GAGTkI,EAAOA,OAASpF,KAAKnF,OAAOuK,OAGvBA,KAGT0X,cAAA,WACE,OAA8B,IAA1B9c,KAAKnF,OAAOuT,UACP5V,SAASkE,KAGd1C,EAAUgG,KAAKnF,OAAOuT,WACjBpO,KAAKnF,OAAOuT,UAGdtI,EAAeQ,QAAQtG,KAAKnF,OAAOuT,cAG5CwO,eAAA,SAAe9L,GACb,OAAO+I,GAAc/I,EAAUjV,kBAGjCsf,cAAA,WAAgB,IAAA9H,EAAArT,KACGA,KAAKnF,OAAO0G,QAAQ3H,MAAM,KAElCqB,SAAQ,SAAAsG,GACf,GAAgB,UAAZA,EACFrB,EAAaQ,GAAG2S,EAAK1a,QACnB0a,EAAK1C,YAAY5W,MAAMygB,MACvBnH,EAAKxY,OAAOjC,UACZ,SAAAiG,GAAK,OAAIwU,EAAKlP,OAAOtF,WAElB,GApaU,WAoaN0C,EAA4B,CACrC,IAAMuc,EAxaQ,UAwaEvc,EACd8R,EAAK1C,YAAY5W,MAAM4gB,WACvBtH,EAAK1C,YAAY5W,MAAM0gB,QACnBsD,EA3aQ,UA2aGxc,EACf8R,EAAK1C,YAAY5W,MAAM6gB,WACvBvH,EAAK1C,YAAY5W,MAAM2gB,SAEzBxa,EAAaQ,GAAG2S,EAAK1a,QACnBmlB,EACAzK,EAAKxY,OAAOjC,UACZ,SAAAiG,GAAK,OAAIwU,EAAKuI,OAAO/c,MAEvBqB,EAAaQ,GAAG2S,EAAK1a,QACnBolB,EACA1K,EAAKxY,OAAOjC,UACZ,SAAAiG,GAAK,OAAIwU,EAAKwI,OAAOhd,UAK3BmB,KAAK+b,kBAAoB,WACnB1I,EAAK1a,SACP0a,EAAKrF,QAIT9N,EAAaQ,GAAGV,KAAKrH,QAAQwK,QAAb,UACd,gBACAnD,KAAK+b,mBAGH/b,KAAKnF,OAAOjC,SACdoH,KAAKnF,OAALoK,EAAA,GACKjF,KAAKnF,OADV,CAEE0G,QAAS,SACT3I,SAAU,KAGZoH,KAAKge,eAITA,UAAA,WACE,IAAMC,SAAmBje,KAAKrH,QAAQE,aAAa,wBAE/CmH,KAAKrH,QAAQE,aAAa,UAA0B,WAAdolB,KACxCje,KAAKrH,QAAQyL,aACX,sBACApE,KAAKrH,QAAQE,aAAa,UAAY,IAGxCmH,KAAKrH,QAAQyL,aAAa,QAAS,QAIvCwX,OAAA,SAAO/c,EAAO2S,GACZ,IAAMgK,EAAUxb,KAAK2Q,YAAYyK,UACjC5J,EAAUA,GAAWjU,EAAasB,EAAMoB,eAAgBub,MAGtDhK,EAAU,IAAIxR,KAAK2Q,YACjB9R,EAAMoB,eACND,KAAKyb,sBAEPle,EAAasB,EAAMoB,eAAgBub,EAAShK,IAG1C3S,IACF2S,EAAQyJ,eACS,YAAfpc,EAAMuB,KA5eQ,QADA,UA8eZ,GAGFoR,EAAQsK,gBAAgBzY,UAAUE,SAxflB,SAEC,SAufjBiO,EAAQwJ,YACVxJ,EAAQwJ,YAxfW,QA4frBnQ,aAAa2G,EAAQuJ,UAErBvJ,EAAQwJ,YA9fa,OAggBhBxJ,EAAQ3W,OAAO4e,OAAUjI,EAAQ3W,OAAO4e,MAAMxL,KAKnDuD,EAAQuJ,SAAWrgB,YAAW,WArgBT,SAsgBf8W,EAAQwJ,aACVxJ,EAAQvD,SAETuD,EAAQ3W,OAAO4e,MAAMxL,MARtBuD,EAAQvD,WAWZ4N,OAAA,SAAOhd,EAAO2S,GACZ,IAAMgK,EAAUxb,KAAK2Q,YAAYyK,UACjC5J,EAAUA,GAAWjU,EAAasB,EAAMoB,eAAgBub,MAGtDhK,EAAU,IAAIxR,KAAK2Q,YACjB9R,EAAMoB,eACND,KAAKyb,sBAEPle,EAAasB,EAAMoB,eAAgBub,EAAShK,IAG1C3S,IACF2S,EAAQyJ,eACS,aAAfpc,EAAMuB,KAphBQ,QADA,UAshBZ,GAGFoR,EAAQmK,yBAIZ9Q,aAAa2G,EAAQuJ,UAErBvJ,EAAQwJ,YAniBY,MAqiBfxJ,EAAQ3W,OAAO4e,OAAUjI,EAAQ3W,OAAO4e,MAAMzL,KAKnDwD,EAAQuJ,SAAWrgB,YAAW,WA1iBV,QA2iBd8W,EAAQwJ,aACVxJ,EAAQxD,SAETwD,EAAQ3W,OAAO4e,MAAMzL,MARtBwD,EAAQxD,WAWZ2N,qBAAA,WACE,IAAK,IAAMpa,KAAWvB,KAAKib,eACzB,GAAIjb,KAAKib,eAAe1Z,GACtB,OAAO,EAIX,OAAO,KAGToH,WAAA,SAAW9N,GACT,IAAMqjB,EAAiBvZ,EAAYI,kBAAkB/E,KAAKrH,SAuC1D,OArCAoC,OAAOC,KAAKkjB,GAAgBjjB,SAAQ,SAAAkjB,IACe,IAA7C9E,GAAsB/Z,QAAQ6e,WACzBD,EAAeC,MAItBtjB,GAAsC,iBAArBA,EAAOuT,WAA0BvT,EAAOuT,UAAUU,SACrEjU,EAAOuT,UAAYvT,EAAOuT,UAAU,IASV,iBAN5BvT,EAAMoK,EAAA,GACDjF,KAAK2Q,YAAYrJ,QACjB4W,EACmB,iBAAXrjB,GAAuBA,EAASA,EAAS,KAGpC4e,QAChB5e,EAAO4e,MAAQ,CACbxL,KAAMpT,EAAO4e,MACbzL,KAAMnT,EAAO4e,QAIW,iBAAjB5e,EAAO2e,QAChB3e,EAAO2e,MAAQ3e,EAAO2e,MAAMle,YAGA,iBAAnBT,EAAOwiB,UAChBxiB,EAAOwiB,QAAUxiB,EAAOwiB,QAAQ/hB,YAGlCX,EAAgB8H,GAAM5H,EAAQmF,KAAK2Q,YAAY9I,aAE3ChN,EAAO+e,WACT/e,EAAO0e,SAAW1B,GAAahd,EAAO0e,SAAU1e,EAAOkd,UAAWld,EAAOmd,aAGpEnd,KAGT4gB,mBAAA,WACE,IAAM5gB,EAAS,GAEf,GAAImF,KAAKnF,OACP,IAAK,IAAMoC,KAAO+C,KAAKnF,OACjBmF,KAAK2Q,YAAYrJ,QAAQrK,KAAS+C,KAAKnF,OAAOoC,KAChDpC,EAAOoC,GAAO+C,KAAKnF,OAAOoC,IAKhC,OAAOpC,KAGTqiB,eAAA,WACE,IAAMhC,EAAMlb,KAAK8b,gBACXsC,EAAWlD,EAAIriB,aAAa,SAAS2C,MAAM4d,IAChC,OAAbgF,GAAqBA,EAASxf,OAAS,GACzCwf,EAASC,KAAI,SAAAC,GAAK,OAAIA,EAAMvlB,UACzBkC,SAAQ,SAAAsjB,GAAM,OAAIrD,EAAI7X,UAAUC,OAAOib,SAI9CZ,6BAAA,SAA6Ba,GAC3Bxe,KAAKkb,IAAMsD,EAAWhhB,SAASihB,OAC/Bze,KAAKkd,iBACLld,KAAK6c,oBAAoB7c,KAAK4c,eAAe4B,EAAW1N,eAG1DkM,eAAA,WACE,IAAM9B,EAAMlb,KAAK8b,gBACX4C,EAAsB1e,KAAKnF,OAAOye,UACA,OAApC4B,EAAIriB,aAAa,iBAIrBqiB,EAAI7X,UAAUC,OA/oBM,QAgpBpBtD,KAAKnF,OAAOye,WAAY,EACxBtZ,KAAKgO,OACLhO,KAAKiO,OACLjO,KAAKnF,OAAOye,UAAYoF,MAKnBhb,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACf,IAAIzG,EAAOK,EAAayC,KA7tBb,cA8tBL0I,EAA4B,iBAAX7N,GAAuBA,EAE9C,IAAKqC,IAAQ,eAAevB,KAAKd,MAI5BqC,IACHA,EAAO,IAAI2d,EAAQ7a,KAAM0I,IAGL,iBAAX7N,GAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,YAKJiJ,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAnvBP,uDAgHb,MAjHY,+CAqHZ,OAAO2O,gCAIP,OAAO7E,oCAIP,MA5Ha,2CAgIb,OAAO1I,qCAIP,MAnIW,kDAuIX,OAAO8N,SAjDLgT,GAuqBNje,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAG6C,IAChCf,EAAE9B,GAAG6C,IAAQoY,GAAQnX,gBACrBhC,EAAE9B,GAAG6C,IAAMuB,YAAc6W,GACzBnZ,EAAE9B,GAAG6C,IAAMwB,WAAa,WAEtB,OADAvC,EAAE9B,GAAG6C,IAAQsB,EACN8W,GAAQnX,qBC1xBrB,IAAMjB,GAAO,UAKP2W,GAAqB,IAAI1d,OAAJ,wBAAyC,KAE9D4L,GAAOrC,EAAA,GACR4V,GAAQvT,QADA,CAEXwJ,UAAW,QACXvP,QAAS,QACT8b,QAAS,GACT9D,SAAU,gJAMN1R,GAAW5C,EAAA,GACZ4V,GAAQhT,YADI,CAEfwV,QAAS,8BAGLtjB,GAAQ,CACZogB,KAAI,kBACJC,OAAM,oBACNC,KAAI,kBACJC,MAAK,mBACLC,SAAQ,sBACRC,MAAK,mBACLC,QAAO,qBACPC,SAAQ,sBACRC,WAAU,wBACVC,WAAU,yBAeN+D,GAAAA,SAAAA,+KAiCJ3C,cAAA,WACE,OAAOhc,KAAKmd,YAAcnd,KAAK4e,iBAGjClC,WAAA,WACE,IAAMxB,EAAMlb,KAAK8b,gBAGjB9b,KAAKod,kBAAkBtX,EAAeQ,QAlDnB,kBAkD2C4U,GAAMlb,KAAKmd,YACzE,IAAIE,EAAUrd,KAAK4e,cACI,mBAAZvB,IACTA,EAAUA,EAAQ9hB,KAAKyE,KAAKrH,UAG9BqH,KAAKod,kBAAkBtX,EAAeQ,QAvDjB,gBAuD2C4U,GAAMmC,GAEtEnC,EAAI7X,UAAUC,OA7DM,OACA,WAiEtBuZ,oBAAA,SAAoBF,GAClB3c,KAAK8b,gBAAgBzY,UAAU2H,IAAO6S,cAAgBlB,MAGxDiC,YAAA,WACE,OAAO5e,KAAKrH,QAAQE,aAAa,iBAC/BmH,KAAKnF,OAAOwiB,WAGhBH,eAAA,WACE,IAAMhC,EAAMlb,KAAK8b,gBACXsC,EAAWlD,EAAIriB,aAAa,SAAS2C,MAAM4d,IAChC,OAAbgF,GAAqBA,EAASxf,OAAS,GACzCwf,EAASC,KAAI,SAAAC,GAAK,OAAIA,EAAMvlB,UACzBkC,SAAQ,SAAAsjB,GAAM,OAAIrD,EAAI7X,UAAUC,OAAOib,SAMvC7a,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACf,IAAIzG,EAAOK,EAAayC,KA1Hb,cA2HL0I,EAA4B,iBAAX7N,EAAsBA,EAAS,KAEtD,IAAKqC,IAAQ,eAAevB,KAAKd,MAI5BqC,IACHA,EAAO,IAAIyhB,EAAQ3e,KAAM0I,GACzBnL,EAAayC,KAnIJ,aAmIoB9C,IAGT,iBAAXrC,GAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,YAKJiJ,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAjJP,uDAkDb,MAnDY,+CAuDZ,OAAO2O,gCAIP,OAAO7E,oCAIP,MA9Da,2CAkEb,OAAO1I,qCAIP,MArEW,kDAyEX,OAAO8N,SA5BL8W,CAAgB9D,IA8GtBje,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAG6C,IAChCf,EAAE9B,GAAG6C,IAAQkc,GAAQjb,gBACrBhC,EAAE9B,GAAG6C,IAAMuB,YAAc2a,GACzBjd,EAAE9B,GAAG6C,IAAMwB,WAAa,WAEtB,OADAvC,EAAE9B,GAAG6C,IAAQsB,EACN4a,GAAQjb,qBC/JrB,IAAMjB,GAAO,YAMP6E,GAAU,CACdlC,OAAQ,GACRyZ,OAAQ,OACR9e,OAAQ,IAGJ8H,GAAc,CAClBzC,OAAQ,SACRyZ,OAAQ,SACR9e,OAAQ,oBA2BJ+e,GAAAA,WACJ,SAAAA,EAAYnmB,EAASkC,GAAQ,IAAAuI,EAAApD,KAC3BA,KAAK2C,SAAWhK,EAChBqH,KAAK+e,eAAqC,SAApBpmB,EAAQuS,QAAqB7R,OAASV,EAC5DqH,KAAK0I,QAAU1I,KAAK2I,WAAW9N,GAC/BmF,KAAK4N,UAAe5N,KAAK0I,QAAQ3I,OAAbC,eAA8CA,KAAK0I,QAAQ3I,OAA3DC,sBAA6FA,KAAK0I,QAAQ3I,OAA1GC,kBACpBA,KAAKgf,SAAW,GAChBhf,KAAKif,SAAW,GAChBjf,KAAKkf,cAAgB,KACrBlf,KAAKmf,cAAgB,EAErBjf,EAAaQ,GAAGV,KAAK+e,eAlCP,uBAkCqC,SAAAlgB,GAAK,OAAIuE,EAAKgc,SAASvgB,MAE1EmB,KAAKqf,UACLrf,KAAKof,WAEL7hB,EAAa5E,EAxDA,eAwDmBqH,iCAelCqf,QAAA,WAAU,IAAAjV,EAAApK,KACFsf,EAAatf,KAAK+e,iBAAmB/e,KAAK+e,eAAe1lB,OAzC7C,SACE,WA4CdkmB,EAAuC,SAAxBvf,KAAK0I,QAAQmW,OAChCS,EACAtf,KAAK0I,QAAQmW,OAETW,EAhDc,aAgDDD,EACjBvf,KAAKyf,gBACL,EAEFzf,KAAKgf,SAAW,GAChBhf,KAAKif,SAAW,GAChBjf,KAAKmf,cAAgBnf,KAAK0f,mBAEV5Z,EAAeE,KAAKhG,KAAK4N,WAEjCyQ,KAAI,SAAA1lB,GACV,IAAMgnB,EAAiB3mB,EAAuBL,GACxCoH,EAAS4f,EAAiB7Z,EAAeQ,QAAQqZ,GAAkB,KAEzE,GAAI5f,EAAQ,CACV,IAAM6f,EAAY7f,EAAOuF,wBACzB,GAAIsa,EAAUnK,OAASmK,EAAUC,OAC/B,MAAO,CACLlb,EAAY4a,GAAcxf,GAAQwF,IAAMia,EACxCG,GAKN,OAAO,QAENlZ,QAAO,SAAAqZ,GAAI,OAAIA,KACfC,MAAK,SAAC9J,EAAGE,GAAJ,OAAUF,EAAE,GAAKE,EAAE,MACxBlb,SAAQ,SAAA6kB,GACP1V,EAAK4U,SAASjY,KAAK+Y,EAAK,IACxB1V,EAAK6U,SAASlY,KAAK+Y,EAAK,UAI9B5c,QAAA,WACE3F,EAAgByC,KAAK2C,SAnHR,gBAoHbzC,EAAaC,IAAIH,KAAK+e,eAnHX,iBAqHX/e,KAAK2C,SAAW,KAChB3C,KAAK+e,eAAiB,KACtB/e,KAAK0I,QAAU,KACf1I,KAAK4N,UAAY,KACjB5N,KAAKgf,SAAW,KAChBhf,KAAKif,SAAW,KAChBjf,KAAKkf,cAAgB,KACrBlf,KAAKmf,cAAgB,QAKvBxW,WAAA,SAAW9N,GAMT,GAA6B,iBAL7BA,EAAMoK,EAAA,GACDqC,GACmB,iBAAXzM,GAAuBA,EAASA,EAAS,KAGpCkF,QAAuB/F,EAAUa,EAAOkF,QAAS,CAAA,IAC3D7H,EAAO2C,EAAOkF,OAAd7H,GACDA,IACHA,EAAKC,EAAOsK,IACZ5H,EAAOkF,OAAO7H,GAAKA,GAGrB2C,EAAOkF,OAAP,IAAoB7H,EAKtB,OAFAyC,EAAgB8H,GAAM5H,EAAQgN,IAEvBhN,KAGT4kB,cAAA,WACE,OAAOzf,KAAK+e,iBAAmB1lB,OAC7B2G,KAAK+e,eAAeiB,YACpBhgB,KAAK+e,eAAevZ,aAGxBka,iBAAA,WACE,OAAO1f,KAAK+e,eAAe1K,cAAgBhc,KAAK4nB,IAC9CznB,SAASkE,KAAK2X,aACd7b,SAAS0N,gBAAgBmO,iBAI7B6L,iBAAA,WACE,OAAOlgB,KAAK+e,iBAAmB1lB,OAC7BA,OAAO8mB,YACPngB,KAAK+e,eAAezZ,wBAAwBua,UAGhDT,SAAA,WACE,IAAM5Z,EAAYxF,KAAKyf,gBAAkBzf,KAAK0I,QAAQtD,OAChDiP,EAAerU,KAAK0f,mBACpBU,EAAYpgB,KAAK0I,QAAQtD,OAC7BiP,EACArU,KAAKkgB,mBAMP,GAJIlgB,KAAKmf,gBAAkB9K,GACzBrU,KAAKqf,UAGH7Z,GAAa4a,EAAjB,CACE,IAAMrgB,EAASC,KAAKif,SAASjf,KAAKif,SAASrgB,OAAS,GAEhDoB,KAAKkf,gBAAkBnf,GACzBC,KAAKqgB,UAAUtgB,OAJnB,CAUA,GAAIC,KAAKkf,eAAiB1Z,EAAYxF,KAAKgf,SAAS,IAAMhf,KAAKgf,SAAS,GAAK,EAG3E,OAFAhf,KAAKkf,cAAgB,UACrBlf,KAAKsgB,SAIP,IAAK,IAAI5hB,EAAIsB,KAAKgf,SAASpgB,OAAQF,KAAM,CAChBsB,KAAKkf,gBAAkBlf,KAAKif,SAASvgB,IACxD8G,GAAaxF,KAAKgf,SAAStgB,UACM,IAAzBsB,KAAKgf,SAAStgB,EAAI,IACtB8G,EAAYxF,KAAKgf,SAAStgB,EAAI,KAGpCsB,KAAKqgB,UAAUrgB,KAAKif,SAASvgB,SAKnC2hB,UAAA,SAAUtgB,GACRC,KAAKkf,cAAgBnf,EAErBC,KAAKsgB,SAEL,IAAMC,EAAUvgB,KAAK4N,UAAUhU,MAAM,KAClCykB,KAAI,SAAAzlB,GAAQ,OAAOA,EAAP,iBAAgCmH,EAAhC,MAA4CnH,EAA5C,UAA8DmH,EAA9D,QAETygB,EAAO1a,EAAeQ,QAAQia,EAAQE,KAAK,MAE7CD,EAAKnd,UAAUE,SAvMU,kBAwM3BuC,EAAeQ,QA/LY,mBA+LsBka,EAAKrd,QAhMlC,cAiMjBE,UAAU2H,IAxMO,UA0MpBwV,EAAKnd,UAAU2H,IA1MK,YA6MpBwV,EAAKnd,UAAU2H,IA7MK,UA+MpBlF,EAAea,QAAQ6Z,EA5MG,qBA6MvBvlB,SAAQ,SAAAylB,GAGP5a,EAAekB,KAAK0Z,EAAcC,+BAC/B1lB,SAAQ,SAAA6kB,GAAI,OAAIA,EAAKzc,UAAU2H,IApNlB,aAuNhBlF,EAAekB,KAAK0Z,EAlNH,aAmNdzlB,SAAQ,SAAA2lB,GACP9a,EAAeS,SAASqa,EArNX,aAsNV3lB,SAAQ,SAAA6kB,GAAI,OAAIA,EAAKzc,UAAU2H,IA1NtB,oBA+NtB9K,EAAaqB,QAAQvB,KAAK+e,eApOV,wBAoO0C,CACxDrT,cAAe3L,OAInBugB,OAAA,WACExa,EAAeE,KAAKhG,KAAK4N,WACtBnH,QAAO,SAAAoa,GAAI,OAAIA,EAAKxd,UAAUE,SAtOX,aAuOnBtI,SAAQ,SAAA4lB,GAAI,OAAIA,EAAKxd,UAAUC,OAvOZ,gBA4OjBI,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACf,IAAIzG,EAAOK,EAAayC,KAnQb,gBA0QX,GAJK9C,IACHA,EAAO,IAAI4hB,EAAU9e,KAHW,iBAAXnF,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,YAKJiJ,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EArRP,yDA8Db,MA/DY,+CAmEZ,OAAO2O,SA1BLwX,GAuPN5e,EAAaQ,GAAGrH,OA7QS,8BA6QoB,WAC3CyM,EAAeE,KAzQS,uBA0QrB/K,SAAQ,SAAA6lB,GAAG,OAAI,IAAIhC,GAAUgC,EAAKnc,EAAYI,kBAAkB+b,UAUrElkB,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAG6C,IAChCf,EAAE9B,GAAG6C,IAAQqc,GAAUpb,gBACvBhC,EAAE9B,GAAG6C,IAAMuB,YAAc8a,GACzBpd,EAAE9B,GAAG6C,IAAMwB,WAAa,WAEtB,OADAvC,EAAE9B,GAAG6C,IAAQsB,EACN+a,GAAUpb,qBCtTvB,IAgCMqd,GAAAA,WACJ,SAAAA,EAAYpoB,GACVqH,KAAK2C,SAAWhK,EAEhB4E,EAAayC,KAAK2C,SAlCL,SAkCyB3C,iCAWxCiO,KAAA,WAAO,IAAA7K,EAAApD,KACL,KAAKA,KAAK2C,SAAS3G,YACjBgE,KAAK2C,SAAS3G,WAAW9B,WAAa2M,KAAKC,cAC3C9G,KAAK2C,SAASU,UAAUE,SArCJ,WAsCpBvD,KAAK2C,SAASU,UAAUE,SArCF,aAkCxB,CAOA,IAAI0D,EACElH,EAAS7G,EAAuB8G,KAAK2C,UACrCqe,EAAchhB,KAAK2C,SAASQ,QAtCN,qBAwC5B,GAAI6d,EAAa,CACf,IAAMC,EAAwC,OAAzBD,EAAYvI,UAA8C,OAAzBuI,EAAYvI,SAvC7C,wBADH,UA0ClBxR,GADAA,EAAWnB,EAAeE,KAAKib,EAAcD,IACzB/Z,EAASrI,OAAS,GAGxC,IAAIsiB,EAAY,KAYhB,GAVIja,IACFia,EAAYhhB,EAAaqB,QAAQ0F,EA9DvB,cA8D6C,CACrDyE,cAAe1L,KAAK2C,cAINzC,EAAaqB,QAAQvB,KAAK2C,SAjEhC,cAiEsD,CAChE+I,cAAezE,IAGHnF,kBACG,OAAdof,GAAsBA,EAAUpf,kBADnC,CAKA9B,KAAKqgB,UACHrgB,KAAK2C,SACLqe,GAGF,IAAMjE,EAAW,WACf7c,EAAaqB,QAAQ0F,EAjFT,gBAiFiC,CAC3CyE,cAAetI,EAAKT,WAEtBzC,EAAaqB,QAAQ6B,EAAKT,SAlFf,eAkFsC,CAC/C+I,cAAezE,KAIflH,EACFC,KAAKqgB,UAAUtgB,EAAQA,EAAO/D,WAAY+gB,GAE1CA,SAIJ7Z,QAAA,WACE3F,EAAgByC,KAAK2C,SAtGR,UAuGb3C,KAAK2C,SAAW,QAKlB0d,UAAA,SAAU1nB,EAASyV,EAAWvR,GAAU,IAAAuN,EAAApK,KAKhCmhB,IAJiB/S,GAAqC,OAAvBA,EAAUqK,UAA4C,OAAvBrK,EAAUqK,SAE5E3S,EAAeS,SAAS6H,EA7FN,WA4FlBtI,EAAeE,KA3FM,wBA2FmBoI,IAGZ,GACxBS,EAAkBhS,GACrBskB,GAAUA,EAAO9d,UAAUE,SAtGV,QAwGdwZ,EAAW,WAAA,OAAM3S,EAAKgX,oBAC1BzoB,EACAwoB,EACAtkB,IAGF,GAAIskB,GAAUtS,EAAiB,CAC7B,IAAMtV,EAAqBJ,EAAiCgoB,GAC5DA,EAAO9d,UAAUC,OA/GC,QAiHlBpD,EAAaS,IAAIwgB,EflJA,gBekJwBpE,GACzC5iB,EAAqBgnB,EAAQ5nB,QAE7BwjB,OAIJqE,oBAAA,SAAoBzoB,EAASwoB,EAAQtkB,GACnC,GAAIskB,EAAQ,CACVA,EAAO9d,UAAUC,OA7HG,UA+HpB,IAAM+d,EAAgBvb,EAAeQ,QApHJ,kCAoH4C6a,EAAOnlB,YAEhFqlB,GACFA,EAAche,UAAUC,OAlIN,UAqIgB,QAAhC6d,EAAOtoB,aAAa,SACtBsoB,EAAO/c,aAAa,iBAAiB,IAIzCzL,EAAQ0K,UAAU2H,IA1II,UA2Ie,QAAjCrS,EAAQE,aAAa,SACvBF,EAAQyL,aAAa,iBAAiB,GAGxC9H,EAAO3D,GAEHA,EAAQ0K,UAAUE,SA/IF,SAgJlB5K,EAAQ0K,UAAU2H,IA/IA,QAkJhBrS,EAAQqD,YAAcrD,EAAQqD,WAAWqH,UAAUE,SAtJ1B,oBAuJH5K,EAAQwK,QAjJZ,cAoJlB2C,EAAeE,KA/IU,oBAgJtB/K,SAAQ,SAAAqmB,GAAQ,OAAIA,EAASje,UAAU2H,IA1JxB,aA6JpBrS,EAAQyL,aAAa,iBAAiB,IAGpCvH,GACFA,OAMG6G,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACf,IAAMzG,EAAOK,EAAayC,KApLf,WAoLkC,IAAI+gB,EAAI/gB,MAErD,GAAsB,iBAAXnF,EAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,YAKJiJ,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAjMP,mDAwCb,MAzCY,qBA+BVooB,GA6KN7gB,EAAaQ,GAAGlI,SAnMU,wBAYG,mEAuLyC,SAAUqG,GAC9EA,EAAM2D,kBAEOjF,EAAayC,KA9MX,WA8M8B,IAAI+gB,GAAI/gB,OAChDiO,UAUPrR,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAF,IAC3B8B,EAAE9B,GAAF,IAAamhB,GAAIrd,gBACjBhC,EAAE9B,GAAF,IAAWoE,YAAc+c,GACzBrf,EAAE9B,GAAF,IAAWqE,WAAa,WAEtB,OADAvC,EAAE9B,GAAF,IAAamE,EACNgd,GAAIrd,qBCpOjB,IAgBMmE,GAAc,CAClByR,UAAW,UACXiI,SAAU,UACV9H,MAAO,UAGHnS,GAAU,CACdgS,WAAW,EACXiI,UAAU,EACV9H,MAAO,KAWH+H,GAAAA,WACJ,SAAAA,EAAY7oB,EAASkC,GACnBmF,KAAK2C,SAAWhK,EAChBqH,KAAK0I,QAAU1I,KAAK2I,WAAW9N,GAC/BmF,KAAK+a,SAAW,KAChB/a,KAAKmb,gBACL5d,EAAa5E,EAxCA,WAwCmBqH,iCAmBlCiO,KAAA,WAAO,IAAA7K,EAAApD,KAGL,IAFkBE,EAAaqB,QAAQvB,KAAK2C,SAtDhC,iBAwDEb,iBAAd,CAIA9B,KAAKyhB,gBAEDzhB,KAAK0I,QAAQ4Q,WACftZ,KAAK2C,SAASU,UAAU2H,IA5DN,QA+DpB,IAAM+R,EAAW,WACf3Z,EAAKT,SAASU,UAAUC,OA7DH,WA8DrBF,EAAKT,SAASU,UAAU2H,IA/DN,QAiElB9K,EAAaqB,QAAQ6B,EAAKT,SArEf,kBAuEPS,EAAKsF,QAAQ6Y,WACfne,EAAK2X,SAAWrgB,YAAW,WACzB0I,EAAK4K,SACJ5K,EAAKsF,QAAQ+Q,SAOpB,GAHAzZ,KAAK2C,SAASU,UAAUC,OA3EJ,QA4EpBhH,EAAO0D,KAAK2C,UACZ3C,KAAK2C,SAASU,UAAU2H,IA3ED,WA4EnBhL,KAAK0I,QAAQ4Q,UAAW,CAC1B,IAAM/f,EAAqBJ,EAAiC6G,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,ShB9GL,gBgB8G+Boa,GAChD5iB,EAAqB6F,KAAK2C,SAAUpJ,QAEpCwjB,QAIJ/O,KAAA,WAAO,IAAA5D,EAAApK,KACL,GAAKA,KAAK2C,SAASU,UAAUE,SAxFT,UA4FFrD,EAAaqB,QAAQvB,KAAK2C,SAnGhC,iBAqGEb,iBAAd,CAIA,IAAMib,EAAW,WACf3S,EAAKzH,SAASU,UAAU2H,IApGN,QAqGlB9K,EAAaqB,QAAQ6I,EAAKzH,SA1Gd,oBA8Gd,GADA3C,KAAK2C,SAASU,UAAUC,OAvGJ,QAwGhBtD,KAAK0I,QAAQ4Q,UAAW,CAC1B,IAAM/f,EAAqBJ,EAAiC6G,KAAK2C,UAEjEzC,EAAaS,IAAIX,KAAK2C,ShBzIL,gBgByI+Boa,GAChD5iB,EAAqB6F,KAAK2C,SAAUpJ,QAEpCwjB,QAIJ7Z,QAAA,WACElD,KAAKyhB,gBAEDzhB,KAAK2C,SAASU,UAAUE,SArHR,SAsHlBvD,KAAK2C,SAASU,UAAUC,OAtHN,QAyHpBpD,EAAaC,IAAIH,KAAK2C,SAjID,0BAkIrBpF,EAAgByC,KAAK2C,SArIR,YAuIb3C,KAAK2C,SAAW,KAChB3C,KAAK0I,QAAU,QAKjBC,WAAA,SAAW9N,GAST,OARAA,EAAMoK,EAAA,GACDqC,GACA3C,EAAYI,kBAAkB/E,KAAK2C,UAChB,iBAAX9H,GAAuBA,EAASA,EAAS,IAGtDF,EAtJS,QAsJaE,EAAQmF,KAAK2Q,YAAY9I,aAExChN,KAGTsgB,cAAA,WAAgB,IAAA5Q,EAAAvK,KACdE,EAAaQ,GAAGV,KAAK2C,SAvJA,yBAuBK,0BAgIiD,WAAA,OAAM4H,EAAKyD,aAGxFyT,cAAA,WACE5W,aAAa7K,KAAK+a,UAClB/a,KAAK+a,SAAW,QAKXrX,gBAAP,SAAuB7I,GACrB,OAAOmF,KAAK2D,MAAK,WACf,IAAIzG,EAAOK,EAAayC,KAtKb,YA6KX,GAJK9C,IACHA,EAAO,IAAIskB,EAAMxhB,KAHe,iBAAXnF,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBqC,EAAKrC,GACd,MAAM,IAAIiS,UAAJ,oBAAkCjS,EAAlC,KAGRqC,EAAKrC,GAAQmF,aAKZ8D,YAAP,SAAmBnL,GACjB,OAAO4E,EAAa5E,EAxLP,qDA8Cb,MA/CY,mDAmDZ,OAAOkP,mCAIP,OAAOP,SApBLka,UAiKN5kB,GAAmB,WACjB,IAAM8E,EAAIlF,IAEV,GAAIkF,EAAG,CACL,IAAMqC,EAAqBrC,EAAE9B,GAAF,MAC3B8B,EAAE9B,GAAF,MAAa4hB,GAAM9d,gBACnBhC,EAAE9B,GAAF,MAAWoE,YAAcwd,GACzB9f,EAAE9B,GAAF,MAAWqE,WAAa,WAEtB,OADAvC,EAAE9B,GAAF,MAAamE,EACNyd,GAAM9d,qBCrNJ,CACbhB,MAAAA,EACAwB,OAAAA,EACA+D,SAAAA,EACAmF,SAAAA,GACAqC,SAAAA,GACAqC,MAAAA,GACA6M,QAAAA,GACAG,UAAAA,GACAiC,IAAAA,GACAS,MAAAA,GACA3G,QAAAA", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-target')\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href')\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let {\n    transitionDuration,\n    transitionDelay\n  } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = parseFloat(transitionDuration)\n  const floatTransitionDelay = parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (parseFloat(transitionDuration) + parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ?\n      'element' :\n      toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new Error(\n        `${componentName.toUpperCase()}: ` +\n        `Option \"${property}\" provided type \"${valueType}\" ` +\n        `but expected type \"${expectedTypes}\".`)\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nexport {\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst mapData = (() => {\n  const storeData = {}\n  let id = 1\n  return {\n    set(element, key, data) {\n      if (typeof element.bsKey === 'undefined') {\n        element.bsKey = {\n          key,\n          id\n        }\n        id++\n      }\n\n      storeData[element.bsKey.id] = data\n    },\n    get(element, key) {\n      if (!element || typeof element.bsKey === 'undefined') {\n        return null\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        return storeData[keyProperties.id]\n      }\n\n      return null\n    },\n    delete(element, key) {\n      if (typeof element.bsKey === 'undefined') {\n        return\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        delete storeData[keyProperties.id]\n        delete element.bsKey\n      }\n    }\n  }\n})()\n\nconst Data = {\n  setData(instance, key, data) {\n    mapData.set(instance, key, data)\n  },\n  getData(instance, key) {\n    return mapData.get(instance, key)\n  },\n  removeData(instance, key) {\n    mapData.delete(instance, key)\n  }\n}\n\nexport default Data\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = [\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n]\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.indexOf(namespace) > -1) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.charAt(0) === '.'\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.indexOf(handlerKey) > -1) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASSNAME_ALERT = 'alert'\nconst CLASSNAME_FADE = 'fade'\nconst CLASSNAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n\n    if (this._element) {\n      Data.setData(element, DATA_KEY, this)\n    }\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASSNAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASSNAME_SHOW)\n\n    if (!element.classList.contains(CLASSNAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler.one(element, TRANSITION_END, () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Alert.jQueryInterface\n    $.fn[NAME].Constructor = Alert\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Alert.jQueryInterface\n    }\n  }\n})\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.getData(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Button.jQueryInterface\n    $.fn[NAME].Constructor = Button\n\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Button.jQueryInterface\n    }\n  }\n})\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {\n      ...element.dataset\n    }\n\n    Object.keys(attributes).forEach(key => {\n      attributes[key] = normalizeData(attributes[key])\n    })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  matches(element, selector) {\n    return element.matches(selector)\n  },\n\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    const children = [].concat(...element.children)\n\n    return children.filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (this.matches(ancestor, selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (this.matches(next, selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_RIGHT = 'carousel-item-right'\nconst CLASS_NAME_LEFT = 'carousel-item-left'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-slide], [data-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-ride=\"carousel\"]'\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel {\n  constructor(element, config) {\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._element = element\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._items = null\n    this._config = null\n    this._element = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      if (event.touches && event.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.key) {\n      case ARROW_LEFT_KEY:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEY:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrevDirection && activeIndex === 0) ||\n                            (isNextDirection && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = SelectorEngine.find(SELECTOR_ACTIVE, this._indicatorsElement)\n      for (let i = 0; i < indicators.length; i++) {\n        indicators[i].classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        nextIndicator.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = parseInt(element.getAttribute('data-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || (activeElement &&\n      this._getItemByDirection(direction, activeElement))\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_LEFT\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_RIGHT\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler.one(activeElement, TRANSITION_END, () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(() => {\n          EventHandler.trigger(this._element, EVENT_SLID, {\n            relatedTarget: nextElement,\n            direction: eventDirectionName,\n            from: activeElementIndex,\n            to: nextElementIndex\n          })\n        }, 0)\n      })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.getData(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.getData(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Carousel.jQueryInterface\n    $.fn[NAME].Constructor = Carousel\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Carousel.jQueryInterface\n    }\n  }\n})\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element = element\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-target=\"#${element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.filter(elem => container !== elem)\n      activesData = tempActiveData[0] ? Data.getData(tempActiveData[0], DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.setData(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._parent = null\n    this._element = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.getData(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Collapse.jQueryInterface\n    $.fn[NAME].Constructor = Collapse\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Collapse.jQueryInterface\n    }\n  }\n})\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPRIGHT = 'dropright'\nconst CLASS_NAME_DROPLEFT = 'dropleft'\nconst CLASS_NAME_MENURIGHT = 'dropdown-menu-right'\nconst CLASS_NAME_NAVBAR = 'navbar'\nconst CLASS_NAME_POSITION_STATIC = 'position-static'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = 'top-start'\nconst PLACEMENT_TOPEND = 'top-end'\nconst PLACEMENT_BOTTOM = 'bottom-start'\nconst PLACEMENT_BOTTOMEND = 'bottom-end'\nconst PLACEMENT_RIGHT = 'right-start'\nconst PLACEMENT_LEFT = 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'scrollParent',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element = element\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Disable totally Popper.js for Dropdown in Navbar\n    if (!this._inNavbar) {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper.js (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        parent.classList.add(CLASS_NAME_POSITION_STATIC)\n      }\n\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._element, EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n    let placement = PLACEMENT_BOTTOM\n\n    // Handle dropup\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      placement = this._menu.classList.contains(CLASS_NAME_MENURIGHT) ?\n        PLACEMENT_TOPEND :\n        PLACEMENT_TOP\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPRIGHT)) {\n      placement = PLACEMENT_RIGHT\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPLEFT)) {\n      placement = PLACEMENT_LEFT\n    } else if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n      placement = PLACEMENT_BOTTOMEND\n    }\n\n    return placement\n  }\n\n  _detectNavbar() {\n    return Boolean(this._element.closest(`.${CLASS_NAME_NAVBAR}`))\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...(this._config.offset(data.offsets, this._element) || {})\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper.js if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON ||\n      (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown.getParentFromElement(toggles[i])\n      const context = Data.getData(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && ((event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName)) ||\n          (event.type === 'keyup' && event.key === TAB_KEY)) &&\n          dropdownMenu.contains(event.target)) {\n        continue\n      }\n\n      const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.key === ARROW_UP_KEY && index > 0) { // Up\n      index--\n    }\n\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) { // Down\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  event.stopPropagation()\n  Dropdown.dropdownInterface(this, 'toggle')\n})\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => e.stopPropagation())\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Dropdown.jQueryInterface\n    $.fn[NAME].Constructor = Dropdown\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Dropdown.jQueryInterface\n    }\n  }\n})\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true,\n  show: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean',\n  show: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config = this._getConfig(config)\n    this._element = element\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      event => this.hide(event)\n    )\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._element = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (transition) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, TRANSITION_END, transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const animate = this._element.classList.contains(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE :\n      ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        this._triggerBackdropTransition()\n      })\n\n      if (animate) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, TRANSITION_END, callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (this._element.classList.contains(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, TRANSITION_END, callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _triggerBackdropTransition() {\n    if (this._config.backdrop === 'static') {\n      const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n      if (hideEvent.defaultPrevented) {\n        return\n      }\n\n      const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n      if (!isModalOverflowing) {\n        this._element.style.overflowY = 'hidden'\n      }\n\n      this._element.classList.add(CLASS_NAME_STATIC)\n      const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n      EventHandler.off(this._element, TRANSITION_END)\n      EventHandler.one(this._element, TRANSITION_END, () => {\n        this._element.classList.remove(CLASS_NAME_STATIC)\n        if (!isModalOverflowing) {\n          EventHandler.one(this._element, TRANSITION_END, () => {\n            this._element.style.overflowY = ''\n          })\n          emulateTransitionEnd(this._element, modalTransitionDuration)\n        }\n      })\n      emulateTransitionEnd(this._element, modalTransitionDuration)\n      this._element.focus()\n    } else {\n      this.hide()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n      // Adjust fixed content padding\n      SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n        .forEach(element => {\n          const actualPadding = element.style.paddingRight\n          const calculatedPadding = window.getComputedStyle(element)['padding-right']\n          Manipulator.setDataAttribute(element, 'padding-right', actualPadding)\n          element.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n        })\n\n      // Adjust sticky content margin\n      SelectorEngine.find(SELECTOR_STICKY_CONTENT)\n        .forEach(element => {\n          const actualMargin = element.style.marginRight\n          const calculatedMargin = window.getComputedStyle(element)['margin-right']\n          Manipulator.setDataAttribute(element, 'margin-right', actualMargin)\n          element.style.marginRight = `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`\n        })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = window.getComputedStyle(document.body)['padding-right']\n\n      Manipulator.setDataAttribute(document.body, 'padding-right', actualPadding)\n      document.body.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n      .forEach(element => {\n        const padding = Manipulator.getDataAttribute(element, 'padding-right')\n        if (typeof padding !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'padding-right')\n          element.style.paddingRight = padding\n        }\n      })\n\n    // Restore sticky content and navbar-toggler margin\n    SelectorEngine.find(`${SELECTOR_STICKY_CONTENT}`)\n      .forEach(element => {\n        const margin = Manipulator.getDataAttribute(element, 'margin-right')\n        if (typeof margin !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'margin-right')\n          element.style.marginRight = margin\n        }\n      })\n\n    // Restore body padding\n    const padding = Manipulator.getDataAttribute(document.body, 'padding-right')\n    if (typeof padding === 'undefined') {\n      document.body.style.paddingRight = ''\n    } else {\n      Manipulator.removeDataAttribute(document.body, 'padding-right')\n      document.body.style.paddingRight = padding\n    }\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.getData(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.show(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Modal.jQueryInterface\n    $.fn[NAME].Constructor = Modal\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Modal.jQueryInterface\n    }\n  }\n})\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (allowlistKeys.indexOf(elName) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'allowList', 'sanitizeFn']\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(number|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacement: '(string|array)',\n  boundary: '(string|element)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: 'right',\n  BOTTOM: 'bottom',\n  LEFT: 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"tooltip-arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: 0,\n  container: false,\n  fallbackPlacement: 'flip',\n  boundary: 'scrollParent',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper.js (https://popper.js.org)')\n    }\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.element = element\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n    Data.setData(element, this.constructor.DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = Data.getData(event.delegateTarget, dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.delegateTarget,\n          this._getDelegateConfig()\n        )\n        Data.setData(event.delegateTarget, dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    Data.removeData(this.element, this.constructor.DATA_KEY)\n\n    EventHandler.off(this.element, this.constructor.EVENT_KEY)\n    EventHandler.off(this.element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config = null\n    this.tip = null\n  }\n\n  show() {\n    if (this.element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (this.isWithContent() && this._isEnabled) {\n      const showEvent = EventHandler.trigger(this.element, this.constructor.Event.SHOW)\n      const shadowRoot = findShadowRoot(this.element)\n      const isInTheDom = shadowRoot === null ?\n        this.element.ownerDocument.documentElement.contains(this.element) :\n        shadowRoot.contains(this.element)\n\n      if (showEvent.defaultPrevented || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        tip.classList.add(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this.element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this._addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      Data.setData(tip, this.constructor.DATA_KEY, this)\n\n      if (!this.element.ownerDocument.documentElement.contains(this.tip)) {\n        container.appendChild(tip)\n      }\n\n      EventHandler.trigger(this.element, this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, this._getPopperConfig(attachment))\n\n      tip.classList.add(CLASS_NAME_SHOW)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children).forEach(element => {\n          EventHandler.on(element, 'mouseover', noop())\n        })\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n\n        const prevHoverState = this._hoverState\n        this._hoverState = null\n\n        EventHandler.trigger(this.element, this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n        const transitionDuration = getTransitionDurationFromElement(this.tip)\n        EventHandler.one(this.tip, TRANSITION_END, complete)\n        emulateTransitionEnd(this.tip, transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this.element, this.constructor.Event.HIDDEN)\n      this._popper.destroy()\n    }\n\n    const hideEvent = EventHandler.trigger(this.element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, TRANSITION_END, complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.allowList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this.element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          behavior: this.config.fallbackPlacement\n        },\n        arrow: {\n          element: `.${this.constructor.NAME}-arrow`\n        },\n        preventOverflow: {\n          boundariesElement: this.config.boundary\n        }\n      },\n      onCreate: data => {\n        if (data.originalPlacement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      },\n      onUpdate: data => this._handlePopperPlacementChange(data)\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...(this.config.offset(data.offsets, this.element) || {})\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this.element,\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this.element,\n          eventIn,\n          this.config.selector,\n          event => this._enter(event)\n        )\n        EventHandler.on(this.element,\n          eventOut,\n          this.config.selector,\n          event => this._leave(event)\n        )\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this.element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this.element.closest(`.${CLASS_NAME_MODAL}`),\n      'hide.bs.modal',\n      this._hideModalHandler\n    )\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) ||\n        context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this.element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    this.tip = popperData.instance.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tooltip.jQueryInterface\n    $.fn[NAME].Constructor = Tooltip\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tooltip.jQueryInterface\n    }\n  }\n})\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.setData(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Popover.jQueryInterface\n    $.fn[NAME].Constructor = Popover\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Popover.jQueryInterface\n    }\n  }\n})\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._element = null\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset +\n      scrollHeight -\n      this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = ScrollSpy.jQueryInterface\n    $.fn[NAME].Constructor = ScrollSpy\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return ScrollSpy.jQueryInterface\n    }\n  }\n})\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab {\n  constructor(element) {\n    this._element = element\n\n    Data.setData(this._element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    let hideEvent = null\n\n    if (previous) {\n      hideEvent = EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      })\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented ||\n      (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(\n      this._element,\n      listElement\n    )\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback &&\n      (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(\n      element,\n      active,\n      callback\n    )\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, TRANSITION_END, complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.getData(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.getData(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tab.jQueryInterface\n    $.fn[NAME].Constructor = Tab\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tab.jQueryInterface\n    }\n  }\n})\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._element = null\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Toast.jQueryInterface\n    $.fn[NAME].Constructor = Toast\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Toast.jQueryInterface\n    }\n  }\n})\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"]}