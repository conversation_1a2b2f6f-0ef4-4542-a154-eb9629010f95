// Handle missing images
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        img.onerror = function() {
            // Replace with a data URI for a simple placeholder
            this.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4QgFBCkjKPVnDgAAAB1pVFh0Q29tbWVudAAAAAAAQ3JlYXRlZCB3aXRoIEdJTVBkLmUHAAABSElEQVR42u3csU0DQRRF0TcbUgDFUAIlkFIKJVACJVBCCnEBJMYSdrHnvBJu4E76Z/Xnz8/Px/F4HLfb7XFd1+N8Ph/H43G8Xq/jdDqN5/M5rtdrfhzfxvv9Ht/7+XyO2+02Pp/PeL/fY1mWsW3bWNd1bNs21nUd27aNZVnGuq5jWZZxuVzGuq5jXdexLMtYlmVs2zaWZRnLsoxlWca2bWNd17Ft21jXdWzbNpZlGeu6jm3bxrquY9u2sa7r2LZtrOs6tm0b67qObdvGuq5j27axruvYtm2s6zq2bRvruo5t28a6rmPbtrGu69i2bazrOrZtG+u6jm3bxrquY9u2sa7r2LZtrOs6tm0b67qObdvGuq5j27axruvYtm2s6zq2bRvruo5t28a6rmPbtrGu69i2bazrOrZtG+u6jm3bxrquY9u2sa7r2LZtrOs6tm0bvwBe+UiSQz66/AAAAABJRU5ErkJggg==';
            this.alt = 'Placeholder Image';
        };
    });
});
