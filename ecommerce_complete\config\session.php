<?php

/**
 * Session Management
 *
 * This file handles session initialization and management for the Euro Spice ecommerce system.
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

/**
 * Check if user is logged in
 *
 * @return bool True if user is logged in, false otherwise
 */
function isLoggedIn()
{
    return isset($_SESSION['user_id']);
}

/**
 * Check if user is admin
 *
 * @return bool True if user is admin, false otherwise
 */
function isAdmin()
{
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * Check if user is staff
 *
 * @return bool True if user is staff, false otherwise
 */
function isStaff()
{
    return isset($_SESSION['user_role']) && ($_SESSION['user_role'] === 'admin' || $_SESSION['user_role'] === 'staff');
}

/**
 * Require user to be logged in
 * Redirects to login page if not logged in
 */
function requireLogin()
{
    if (!isLoggedIn()) {
        $_SESSION['error'] = "You must be logged in to access this page.";
        header("Location: /finance/ecommerce_complete/pages/account/login.php");
        exit();
    }
}

/**
 * Require user to be admin
 * Redirects to login page if not admin
 */
function requireAdmin()
{
    if (!isAdmin()) {
        $_SESSION['error'] = "You must be an administrator to access this page.";
        header("Location: /finance/ecommerce_complete/pages/account/login.php");
        exit();
    }
}

/**
 * Require user to be staff
 * Redirects to login page if not staff
 */
function requireStaff()
{
    if (!isStaff()) {
        $_SESSION['error'] = "You must be staff to access this page.";
        header("Location: /finance/ecommerce_complete/pages/account/login.php");
        exit();
    }
}
