<?php
// Include database configuration
require_once 'app/config/config.php';

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Debugging Inventory Issues</h2>";

try {
    // Check if inventory table exists
    $result = $conn->query("SHOW TABLES LIKE 'inventory'");
    $tableExists = $result->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p>✅ Inventory table exists.</p>";
        
        // Check table structure
        $stmt = $conn->query("DESCRIBE inventory");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Inventory Table Structure:</h3>";
        echo "<table border='1'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            foreach ($column as $key => $value) {
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Check existing records
        $stmt = $conn->query("SELECT COUNT(*) as count FROM inventory");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        echo "<p>There are currently $count records in the inventory table.</p>";
        
        if ($count > 0) {
            $stmt = $conn->query("SELECT id, prod_name, brand_name, price, stocks, status FROM inventory ORDER BY id DESC LIMIT 10");
            $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>Recent Inventory Records:</h3>";
            echo "<table border='1'>";
            echo "<tr><th>ID</th><th>Product Name</th><th>Brand</th><th>Price</th><th>Stocks</th><th>Status</th></tr>";
            
            foreach ($records as $record) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($record['id']) . "</td>";
                echo "<td>" . htmlspecialchars($record['prod_name'] ?? 'NULL') . "</td>";
                echo "<td>" . htmlspecialchars($record['brand_name'] ?? 'NULL') . "</td>";
                echo "<td>" . htmlspecialchars($record['price'] ?? 'NULL') . "</td>";
                echo "<td>" . htmlspecialchars($record['stocks'] ?? 'NULL') . "</td>";
                echo "<td>" . htmlspecialchars($record['status'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        }
        
        // Try to insert a test record
        echo "<h3>Attempting to insert a test record:</h3>";
        
        try {
            $conn->beginTransaction();
            
            $stmt = $conn->prepare("INSERT INTO inventory 
                (prod_name, brand_name, price, prod_measure, pack_type, expiry_date, delivered_date, country, batch_code, stocks, status) 
                VALUES 
                ('Spices', 'VISKASE', 1.00, 'Grams (g)', 'Jar', '2025-09-09 00:00:00', NOW(), 'Philippines', 'SFGE65', 1.00, 'approved')");
            $stmt->execute();
            $insertedId = $conn->lastInsertId();
            
            $conn->commit();
            
            echo "<p>✅ Successfully inserted test record with ID $insertedId.</p>";
        } catch (PDOException $e) {
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }
            echo "<p>❌ Error inserting test record: " . $e->getMessage() . "</p>";
        }
        
        // Check PHP error log
        echo "<h3>Recent PHP Errors:</h3>";
        $logPath = ini_get('error_log');
        if (file_exists($logPath)) {
            $logContent = file_get_contents($logPath);
            $lines = array_slice(explode("\n", $logContent), -20);
            echo "<pre>" . htmlspecialchars(implode("\n", $lines)) . "</pre>";
        } else {
            echo "<p>Could not find PHP error log at: $logPath</p>";
        }
    } else {
        echo "<p>❌ Inventory table does not exist. Creating it now...</p>";
        
        // Create inventory table
        $conn->exec("CREATE TABLE IF NOT EXISTS inventory (
            id INT AUTO_INCREMENT PRIMARY KEY,
            prod_image VARCHAR(255),
            prod_name VARCHAR(100) NOT NULL,
            brand_name VARCHAR(100),
            price DECIMAL(10,2) NOT NULL,
            prod_measure VARCHAR(50),
            pack_type VARCHAR(50),
            expiry_date DATETIME,
            delivered_date DATETIME,
            country VARCHAR(100),
            batch_code VARCHAR(50),
            stocks DECIMAL(10,2) NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'approved',
            order_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        echo "<p>✅ Inventory table created successfully.</p>";
    }
    
    // Check order_requests table for available orders
    echo "<h3>Available Orders:</h3>";
    try {
        $stmt = $conn->query("SELECT id, product_id, status FROM order_requests WHERE status = 'processing' LIMIT 10");
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($orders) > 0) {
            echo "<table border='1'>";
            echo "<tr><th>Order ID</th><th>Product ID</th><th>Status</th></tr>";
            
            foreach ($orders as $order) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($order['id']) . "</td>";
                echo "<td>" . htmlspecialchars($order['product_id'] ?? 'NULL') . "</td>";
                echo "<td>" . htmlspecialchars($order['status']) . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>No available orders found.</p>";
        }
    } catch (PDOException $e) {
        echo "<p>Error checking order_requests table: " . $e->getMessage() . "</p>";
    }
    
    echo "<p><a href='app/modules/inventory/inventory.php'>Go to Inventory Page</a></p>";
    echo "<p><a href='app/modules/logistics/logistics_management.php'>Go to Logistics Management</a></p>";
} catch (PDOException $e) {
    echo "<p>Database Error: " . $e->getMessage() . "</p>";
}
?>
