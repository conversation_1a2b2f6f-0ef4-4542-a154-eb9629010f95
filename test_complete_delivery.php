<?php
// Include database configuration
require_once 'app/config/config.php';

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Testing Complete Delivery Process</h2>";

// Get order ID from URL parameter or use a default value
$order_id = isset($_GET['order_id']) ? (int)$_GET['order_id'] : 1;
$status = 'delivered';
$notes = 'Test delivery completed via test script';

echo "<p>Using order ID: $order_id</p>";

try {
    // Start transaction
    $conn->beginTransaction();
    
    // Get order details
    $stmt = $conn->prepare("SELECT
                            orq.*,
                            p.prod_name,
                            p.name as product_name,
                            p.price,
                            p.prod_image,
                            a.id as approved_id,
                            a.brand_name,
                            a.prod_name as approved_prod_name,
                            a.price as approved_price,
                            a.prod_measure,
                            a.pack_type,
                            a.batch_code,
                            a.stocks,
                            a.country,
                            a.expiry_date
                        FROM order_requests orq
                        LEFT JOIN products p ON orq.product_id = p.id
                        LEFT JOIN approved a ON a.order_id = orq.id OR a.prod_name = p.prod_name OR a.id = orq.product_id
                        WHERE orq.id = :order_id");
    $stmt->bindValue(':order_id', $order_id, PDO::PARAM_INT);
    $stmt->execute();
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        echo "<p>Order not found. Creating a dummy order for testing.</p>";
        
        // Create a dummy order for testing
        $order = [
            'id' => $order_id,
            'user_id' => 1,
            'product_id' => 1,
            'quantity' => 1,
            'delivery_address' => 'Paliparan',
            'special_instructions' => 'Test delivery',
            'status' => 'processing',
            'driver_id' => null,
            'pickup_time' => null,
            'delivery_time' => null,
            'delivery_notes' => null,
            'prod_name' => 'Spices',
            'approved_prod_name' => 'Spices',
            'product_name' => 'Spices',
            'name' => 'Spices',
            'price' => 1.00,
            'prod_image' => '',
            'brand_name' => 'VISKASE',
            'approved_price' => 1.00,
            'prod_measure' => 'Grams (g)',
            'pack_type' => 'Jar',
            'batch_code' => 'SFGE65',
            'stocks' => 1,
            'country' => 'Philippines',
            'expiry_date' => date('Y-m-d', strtotime('+1 year'))
        ];
    } else {
        echo "<p>Found order with ID $order_id.</p>";
    }
    
    // Display order details
    echo "<h3>Order Details:</h3>";
    echo "<pre>" . print_r($order, true) . "</pre>";
    
    // Update order status
    $delivery_time = date('Y-m-d H:i:s');
    
    // Only update if the order exists in the database
    if (isset($order['id']) && $order['id'] == $order_id) {
        $update_sql = "UPDATE order_requests SET
                        status = :status,
                        delivery_time = :delivery_time,
                        delivery_notes = :notes
                        WHERE id = :order_id";
        $stmt = $conn->prepare($update_sql);
        $stmt->bindValue(':status', $status, PDO::PARAM_STR);
        $stmt->bindValue(':delivery_time', $delivery_time, PDO::PARAM_STR);
        $stmt->bindValue(':notes', $notes, PDO::PARAM_STR);
        $stmt->bindValue(':order_id', $order_id, PDO::PARAM_INT);
        $stmt->execute();
        
        echo "<p>Updated order status to 'delivered'.</p>";
    }
    
    // Extract product details
    $prod_name = 'Spices'; // Default to "Spices" as the product name
    
    // Try to get the product name from various sources in order of preference
    if (!empty($order['approved_prod_name']) && $order['approved_prod_name'] != 'Unknown Product Name') {
        $prod_name = $order['approved_prod_name'];
    } elseif (!empty($order['prod_name']) && $order['prod_name'] != 'Unknown Product Name') {
        $prod_name = $order['prod_name'];
    } elseif (!empty($order['product_name']) && $order['product_name'] != 'Unknown Product Name') {
        $prod_name = $order['product_name'];
    } elseif (!empty($order['name']) && $order['name'] != 'Unknown Product Name') {
        $prod_name = $order['name'];
    }
    
    // Make sure we have a product name
    if (empty($prod_name) || $prod_name === 'Unknown Product Name') {
        $prod_name = 'Spices';
    }
    
    echo "<p>Using product name: $prod_name</p>";
    
    $brand_name = $order['brand_name'] ?? 'VISKASE';
    $price = isset($order['approved_price']) && $order['approved_price'] > 0 ? $order['approved_price'] : ($order['price'] ?? 1.00);
    $prod_measure = $order['prod_measure'] ?? 'Grams (g)';
    $pack_type = $order['pack_type'] ?? 'Jar';
    $expiry_date = $order['expiry_date'] ?? date('Y-m-d', strtotime('+1 year'));
    $country = $order['country'] ?? 'Philippines';
    $batch_code = $order['batch_code'] ?? 'SFGE65';
    $stocks = $order['quantity'] ?? 1;
    $prod_image = $order['prod_image'] ?? '';
    
    // Check if inventory table exists
    $tableExists = $conn->query("SHOW TABLES LIKE 'inventory'")->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<p>Inventory table does not exist. Creating it now...</p>";
        
        // Create inventory table
        $conn->exec("CREATE TABLE IF NOT EXISTS inventory (
            id INT AUTO_INCREMENT PRIMARY KEY,
            prod_image VARCHAR(255),
            prod_name VARCHAR(100) NOT NULL,
            brand_name VARCHAR(100),
            price DECIMAL(10,2) NOT NULL,
            prod_measure VARCHAR(50),
            pack_type VARCHAR(50),
            expiry_date DATETIME,
            delivered_date DATETIME,
            country VARCHAR(100),
            batch_code VARCHAR(50),
            stocks DECIMAL(10,2) NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'approved',
            order_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        echo "<p>Inventory table created successfully.</p>";
    }
    
    // Insert new product into inventory
    echo "<p>Attempting to insert product into inventory...</p>";
    
    try {
        $insert_inventory_sql = "INSERT INTO inventory
                                (prod_image, prod_name, brand_name, price, prod_measure,
                                pack_type, expiry_date, delivered_date, country,
                                batch_code, stocks, status, order_id)
                                VALUES
                                (:prod_image, :prod_name, :brand_name, :price, :prod_measure,
                                :pack_type, :expiry_date, :delivered_date, :country,
                                :batch_code, :stocks, 'approved', :order_id)";
        $stmt = $conn->prepare($insert_inventory_sql);
        $stmt->bindValue(':prod_image', $prod_image, PDO::PARAM_STR);
        $stmt->bindValue(':prod_name', $prod_name, PDO::PARAM_STR);
        $stmt->bindValue(':brand_name', $brand_name, PDO::PARAM_STR);
        $stmt->bindValue(':price', $price, PDO::PARAM_STR);
        $stmt->bindValue(':prod_measure', $prod_measure, PDO::PARAM_STR);
        $stmt->bindValue(':pack_type', $pack_type, PDO::PARAM_STR);
        $stmt->bindValue(':expiry_date', $expiry_date, PDO::PARAM_STR);
        $stmt->bindValue(':delivered_date', $delivery_time, PDO::PARAM_STR);
        $stmt->bindValue(':country', $country, PDO::PARAM_STR);
        $stmt->bindValue(':batch_code', $batch_code, PDO::PARAM_STR);
        $stmt->bindValue(':stocks', $stocks, PDO::PARAM_INT);
        $stmt->bindValue(':order_id', $order_id, PDO::PARAM_INT);
        $stmt->execute();
        
        $insertedId = $conn->lastInsertId();
        echo "<p>✅ Successfully inserted product into inventory with ID $insertedId.</p>";
    } catch (PDOException $e) {
        echo "<p>❌ Error inserting product into inventory: " . $e->getMessage() . "</p>";
        
        // Try to find existing product with similar data
        $find_similar_sql = "SELECT id FROM inventory WHERE prod_name = :prod_name LIMIT 1";
        $stmt = $conn->prepare($find_similar_sql);
        $stmt->bindValue(':prod_name', $prod_name, PDO::PARAM_STR);
        $stmt->execute();
        $similar_product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($similar_product) {
            echo "<p>Found similar product with ID " . $similar_product['id'] . ". Updating stocks instead.</p>";
            
            // Update the existing product
            $update_sql = "UPDATE inventory
                          SET stocks = stocks + :stocks,
                              delivered_date = :delivered_date,
                              order_id = :order_id
                          WHERE id = :id";
            $stmt = $conn->prepare($update_sql);
            $stmt->bindValue(':stocks', $stocks, PDO::PARAM_INT);
            $stmt->bindValue(':delivered_date', $delivery_time, PDO::PARAM_STR);
            $stmt->bindValue(':order_id', $order_id, PDO::PARAM_INT);
            $stmt->bindValue(':id', $similar_product['id'], PDO::PARAM_INT);
            $stmt->execute();
            
            echo "<p>✅ Successfully updated existing product in inventory.</p>";
        }
    }
    
    // Commit transaction
    $conn->commit();
    
    // Get all inventory records
    $stmt = $conn->query("SELECT id, prod_name, brand_name, price, stocks, status FROM inventory ORDER BY id DESC");
    $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Current Inventory Records:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Product Name</th><th>Brand</th><th>Price</th><th>Stocks</th><th>Status</th></tr>";
    
    foreach ($records as $record) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($record['id']) . "</td>";
        echo "<td>" . htmlspecialchars($record['prod_name'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($record['brand_name'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($record['price'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($record['stocks'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($record['status'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<p><a href='app/modules/inventory/inventory.php'>Go to Inventory Page</a></p>";
    echo "<p><a href='app/modules/logistics/logistics_management.php'>Go to Logistics Management</a></p>";
} catch (PDOException $e) {
    // Rollback transaction on error
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    echo "<p>Database Error: " . $e->getMessage() . "</p>";
}
?>
