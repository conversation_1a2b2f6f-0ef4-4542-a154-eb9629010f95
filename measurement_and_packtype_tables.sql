-- SQL code to create measurement units and pack types tables
-- Import this file into your existing 'finance' database

-- Create measurement_units table
CREATE TABLE IF NOT EXISTS `measurement_units` (
  `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `unit_name` varchar(100) NOT NULL,
  `unit_symbol` varchar(20) NOT NULL,
  `description` text,
  `unit_logo` varchar(255),
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create pack_types table
CREATE TABLE IF NOT EXISTS `pack_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `pack_name` varchar(100) NOT NULL,
  `description` text,
  `material` varchar(100),
  `pack_logo` varchar(255),
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert some default measurement units
INSERT INTO `measurement_units` (`unit_name`, `unit_symbol`, `description`) VALUES
('Kilogram', 'kg', 'Standard unit of mass in the International System of Units'),
('Gram', 'g', 'Metric unit of mass equal to one thousandth of a kilogram'),
('Liter', 'L', 'Metric unit of volume'),
('Milliliter', 'mL', 'Metric unit of volume equal to one thousandth of a liter'),
('Pound', 'lb', 'Imperial unit of mass'),
('Ounce', 'oz', 'Imperial unit of mass equal to one sixteenth of a pound'),
('Teaspoon', 'tsp', 'Volume unit used in cooking'),
('Tablespoon', 'tbsp', 'Volume unit used in cooking equal to three teaspoons');

-- Insert some default package types
INSERT INTO `pack_types` (`pack_name`, `description`, `material`) VALUES
('Box', 'Standard cardboard box for packaging', 'Cardboard'),
('Bottle', 'Container with a narrow neck for liquids', 'Glass/Plastic'),
('Jar', 'Wide-mouthed container for food storage', 'Glass'),
('Bag', 'Flexible container for dry goods', 'Plastic/Paper'),
('Pouch', 'Small flexible container with sealed edges', 'Plastic/Foil'),
('Can', 'Cylindrical metal container for food preservation', 'Metal'),
('Sachet', 'Small sealed packet for single-use portions', 'Plastic/Foil'),
('Tube', 'Cylindrical flexible container with a cap', 'Plastic/Metal');
