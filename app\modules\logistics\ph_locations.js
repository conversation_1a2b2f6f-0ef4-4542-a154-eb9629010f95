/**
 * Philippine Locations Dropdown Handler
 * Uses a local API to populate province and city dropdowns
 */
document.addEventListener('DOMContentLoaded', function () {
    // Define popular cities for each province
    const popularCities = {
        'CAV': ['Dasmariñas', 'Bacoor', 'Imus', 'Tagaytay', '<PERSON>'], // <PERSON>avite
        'LAG': ['Santa Rosa', 'Calamba', 'San Pedro', 'Biñan', 'Los Baños'], // Laguna
        'RIZ': ['Antipolo', 'Cainta', 'Taytay'], // Rizal
        'NCR-1': ['Manila', 'Quezon City', 'Makati', 'Taguig', 'Pasig'], // Metro Manila
        'BAT': ['Batangas City', 'Lipa', 'Tanauan'], // Batangas
        'BUL': ['Malolos', 'San Jose del Monte', 'Meycauayan'] // Bulacan
    };

    // Default zip codes for provinces
    const defaultZipCodes = {
        'CAV': '4114', // Cavite
        'LAG': '4023', // Laguna
        'RIZ': '1870', // Rizal
        'NCR-1': '1000', // Metro Manila
        'BAT': '4200', // Batangas
        'BUL': '3000'  // Bulacan
    };

    // Initialize province dropdown
    function initializeProvinceDropdown() {
        const provinceSelect = document.getElementById('province');
        if (!provinceSelect) {
            console.error('Province dropdown element not found');
            return;
        }

        console.log('Initializing province dropdown');

        // Show loading state
        provinceSelect.innerHTML = '<option value="">Loading provinces...</option>';
        provinceSelect.disabled = true;

        // Fetch provinces from our local API
        fetch('ph_locations_api.php?type=provinces')
            .then(response => response.json())
            .then(provinces => {
                // Clear loading state
                provinceSelect.innerHTML = '<option value="">Select Province</option>';
                provinceSelect.disabled = false;

                // Add provinces to dropdown
                provinces.forEach(province => {
                    const option = document.createElement('option');
                    option.value = province.code;
                    option.textContent = province.name;
                    provinceSelect.appendChild(option);
                });

                console.log(`Loaded ${provinces.length} provinces`);

                // Set up change event
                provinceSelect.addEventListener('change', function () {
                    const provinceCode = this.value;
                    console.log('Province changed to:', provinceCode);
                    populateCityDropdown(provinceCode);
                });
            })
            .catch(error => {
                console.error('Error loading provinces:', error);
                provinceSelect.innerHTML = '<option value="">Error loading provinces</option>';
                provinceSelect.disabled = false;
            });
    }

    // Populate city dropdown based on selected province
    function populateCityDropdown(provinceCode) {
        const citySelect = document.getElementById('city');
        if (!citySelect) {
            console.error('City dropdown element not found');
            return;
        }

        // Show loading state
        citySelect.innerHTML = '<option value="">Loading cities...</option>';
        citySelect.disabled = true;

        if (!provinceCode) {
            citySelect.innerHTML = '<option value="">Select City</option>';
            citySelect.disabled = false;
            console.log('No province code provided, city dropdown cleared');
            return;
        }

        // Fetch cities from our local API
        fetch(`ph_locations_api.php?type=cities&code=${provinceCode}`)
            .then(response => response.json())
            .then(cities => {
                // Clear loading state
                citySelect.innerHTML = '<option value="">Select City</option>';
                citySelect.disabled = false;

                // Sort cities alphabetically
                cities.sort();

                // Get popular cities for this province
                const popular = popularCities[provinceCode] || [];

                // Add popular cities first if available
                if (popular.length > 0) {
                    // Create an optgroup for popular cities
                    const popularGroup = document.createElement('optgroup');
                    popularGroup.label = '★ Popular Cities';

                    popular.forEach(city => {
                        if (cities.includes(city)) {
                            const option = document.createElement('option');
                            option.value = city;
                            option.textContent = city;
                            popularGroup.appendChild(option);
                        }
                    });

                    if (popularGroup.children.length > 0) {
                        citySelect.appendChild(popularGroup);
                    }
                }

                // Add remaining cities
                const otherCities = cities.filter(city => !popular.includes(city));

                // Create an optgroup for other cities
                const otherGroup = document.createElement('optgroup');
                otherGroup.label = 'All Cities';

                otherCities.forEach(city => {
                    const option = document.createElement('option');
                    option.value = city;
                    option.textContent = city;
                    otherGroup.appendChild(option);
                });

                if (otherGroup.children.length > 0) {
                    citySelect.appendChild(otherGroup);
                }

                console.log(`Loaded ${cities.length} cities for province ${provinceCode}`);

                // Update zip code if empty
                updateZipCode(provinceCode);

                // Trigger a change event on the city select to update the address if a city is already selected
                if (citySelect.value) {
                    setTimeout(() => {
                        const event = new Event('change');
                        citySelect.dispatchEvent(event);
                    }, 50);
                }
            })
            .catch(error => {
                console.error('Error loading cities:', error);
                citySelect.innerHTML = '<option value="">Error loading cities</option>';
                citySelect.disabled = false;
            });
    }

    // Update zip code based on province
    function updateZipCode(provinceCode) {
        const zipCodeField = document.getElementById('zip_code');
        if (!zipCodeField || zipCodeField.value.trim() !== '') {
            return; // Don't update if field doesn't exist or already has a value
        }

        const zipCode = defaultZipCodes[provinceCode] || '1000';
        zipCodeField.value = zipCode;
    }

    // Function to update address based on selected city and province
    function updateAddressField() {
        const provinceSelect = document.getElementById('province');
        const citySelect = document.getElementById('city');
        const addressField = document.getElementById('delivery_address');

        if (!provinceSelect || !citySelect || !addressField) {
            return;
        }

        // Get selected values
        const provinceCode = provinceSelect.value;
        const provinceName = provinceSelect.options[provinceSelect.selectedIndex]?.text || '';
        const cityName = citySelect.value;

        // Only proceed if both city and province are selected
        if (provinceCode && cityName) {
            // Get current address value
            let currentAddress = addressField.value || '';

            // If address is empty or just the loading text, create a new one
            if (currentAddress === '' || currentAddress === 'Loading address...') {
                addressField.value = cityName + ', ' + provinceName + ', Philippines';
            }
        }
    }

    // Add change event listeners to update address
    function setupAddressUpdates() {
        const citySelect = document.getElementById('city');

        if (citySelect) {
            citySelect.addEventListener('change', updateAddressField);
        }
    }

    // Initialize the province dropdown
    initializeProvinceDropdown();

    // Setup address updates
    setupAddressUpdates();
});
