<?php
session_start();
require_once '../../config/config.php';

// Set the content type to JSON and prevent caching
header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

// Get user ID from request
$user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 0;

if ($user_id <= 0) {
    echo json_encode(['error' => 'Invalid user ID']);
    exit();
}

// Default values
$defaultData = [
    'address' => '',
    'province' => 'CAV', // Default to Cavite (using new code format)
    'city' => 'Dasmariñas',
    'zip_code' => '4114',
    'country' => 'Philippines'
];

try {
    // First, check if the user exists
    $userExistsQuery = "SELECT id FROM users WHERE id = :user_id";
    $stmt = $conn->prepare($userExistsQuery);
    $stmt->bindValue(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();

    if ($stmt->rowCount() === 0) {
        // User doesn't exist
        echo json_encode(array_merge(['error' => 'User not found'], $defaultData));
        exit();
    }

    // Try to get data from add_user table if it exists
    $userData = $defaultData;

    try {
        $addUserQuery = "SELECT province, city, zip_code, country FROM add_user WHERE user_id = :user_id";
        $stmt = $conn->prepare($addUserQuery);
        $stmt->bindValue(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $addUserData = $stmt->fetch(PDO::FETCH_ASSOC);
            $userData = array_merge($userData, $addUserData);
        }
    } catch (PDOException $e) {
        // Table might not exist or have different structure, just use defaults
        error_log("Error fetching from add_user: " . $e->getMessage());
    }

    // Try to get address from users table if the column exists
    try {
        $addressQuery = "SELECT address FROM users WHERE id = :user_id";
        $stmt = $conn->prepare($addressQuery);
        $stmt->bindValue(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $addressData = $stmt->fetch(PDO::FETCH_ASSOC);
            if (isset($addressData['address'])) {
                $userData['address'] = $addressData['address'];
            }
        }
    } catch (PDOException $e) {
        // Column might not exist, just use default address
        error_log("Error fetching address from users: " . $e->getMessage());
    }

    // Make sure all values are properly sanitized
    foreach ($userData as $key => $value) {
        if (is_string($value)) {
            // Remove any non-printable characters that might cause JSON issues
            $userData[$key] = preg_replace('/[\x00-\x1F\x7F]/u', '', $value);
        }
    }

    // Convert old province codes to new format if needed
    if (isset($userData['province'])) {
        $provinceCodeMap = [
            '0128' => 'CAV', // Cavite
            '0133' => 'LAG', // Laguna
            '0174' => 'RIZ', // Rizal
            '0137' => 'MM', // Metro Manila
            '0122' => 'BAT', // Batangas
            '0127' => 'BUL'  // Bulacan
        ];

        if (isset($provinceCodeMap[$userData['province']])) {
            $userData['province'] = $provinceCodeMap[$userData['province']];
        }
    }

    // Return the data with JSON_UNESCAPED_UNICODE to handle special characters
    echo json_encode($userData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
} catch (PDOException $e) {
    error_log("Error in get_user_address.php: " . $e->getMessage());

    // Create error response with default data
    $errorResponse = array_merge(
        ['error' => 'Database error: ' . $e->getMessage()],
        $defaultData
    );

    // Sanitize the error message
    if (isset($errorResponse['error']) && is_string($errorResponse['error'])) {
        $errorResponse['error'] = preg_replace('/[\x00-\x1F\x7F]/u', '', $errorResponse['error']);
    }

    // Return the error response with proper JSON encoding
    echo json_encode($errorResponse, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}
