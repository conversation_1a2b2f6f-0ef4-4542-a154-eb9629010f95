<?php
session_start();
require_once '../../config/config.php';

// Get order ID from URL parameter
$order_id = isset($_GET['order_id']) ? (int)$_GET['order_id'] : 0;

// Get order details
$order = null;
if ($order_id) {
    try {
        $stmt = $conn->prepare("SELECT * FROM order_requests WHERE id = :id");
        $stmt->bindValue(':id', $order_id, PDO::PARAM_INT);
        $stmt->execute();
        $order = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error fetching order: " . $e->getMessage());
    }
}

// If order doesn't exist, create a dummy order for demonstration
if (!$order) {
    $order = [
        'id' => $order_id ?: 1,
        'user_id' => 1,
        'product_id' => 1,
        'quantity' => 5,
        'delivery_address' => 'Sample Address, Manila, Philippines',
        'special_instructions' => 'Sample delivery instructions',
        'status' => 'processing',
        'driver_id' => null,
        'pickup_time' => null,
        'delivery_time' => null,
        'delivery_notes' => null,
        'prod_name' => 'Sample Product',
        'price' => 100.00,
        'prod_image' => '',
        'brand_name' => 'Sample Brand',
        'approved_price' => 100.00,
        'prod_measure' => 'kg',
        'pack_type' => 'box',
        'batch_code' => 'ABC123',
        'stocks' => 100,
        'country' => 'Philippines',
        'expiry_date' => date('Y-m-d', strtotime('+1 year'))
    ];
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Delivery Simulation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            padding-top: 20px;
        }

        .card {
            margin-bottom: 20px;
        }

        #map {
            height: 400px;
            width: 100%;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="mb-4">Simple Delivery Simulation</h1>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Delivery Map</h5>
                    </div>
                    <div class="card-body">
                        <div id="map"></div>
                        <div class="mt-3">
                            <button id="startDeliveryBtn" class="btn btn-primary">Start Delivery</button>
                            <button id="completeDeliveryBtn" class="btn btn-success">Complete Delivery</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Order Details</h5>
                    </div>
                    <div class="card-body">
                        <h6><?php echo htmlspecialchars($order['prod_name'] ?? ''); ?></h6>
                        <p>
                            <strong>Delivery Address:</strong><br>
                            <?php echo htmlspecialchars($order['delivery_address'] ?? ''); ?>
                        </p>
                        <p>
                            <strong>Quantity:</strong> <?php echo $order['quantity']; ?><br>
                            <strong>Price:</strong> $<?php echo number_format((float)($order['price'] ?? 0), 2); ?>
                        </p>
                        <div id="status-container" class="alert alert-info">
                            Status: <?php echo ucfirst($order['status']); ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Simple delivery simulation
        document.getElementById('startDeliveryBtn').addEventListener('click', function() {
            document.getElementById('status-container').innerHTML =
                '<i class="fas fa-truck"></i> Delivery in progress... Driver is on the way!';

            // Update status in database
            fetch('update_order_status.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `order_id=<?php echo $order_id; ?>&status=in_transit`
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Status updated:', data);
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        });

        document.getElementById('completeDeliveryBtn').addEventListener('click', function() {
            document.getElementById('status-container').innerHTML =
                '<i class="fas fa-check-circle"></i> Delivery completed! Product added to inventory.';

            // Update status in database
            fetch('update_delivery_status.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `order_id=<?php echo $order_id; ?>&status=delivered&notes=Delivered successfully`
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Delivery completed:', data);
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        });
    </script>
</body>

</html>