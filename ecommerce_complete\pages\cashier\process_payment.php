<?php
require_once '../../config/ecommerce_db.php';
require_once '../../config/session.php';

// Require login and staff privileges
requireStaff();

// Check if order_id is provided
if (!isset($_GET['order_id']) || empty($_GET['order_id'])) {
    header("Location: index.php");
    exit();
}

$order_id = $_GET['order_id'];

// Get order details
$order_sql = "SELECT o.*, CONCAT(u.first_name, ' ', u.last_name) as customer_name, u.email, u.phone, u.address
              FROM e_orders o
              JOIN e_users u ON o.user_id = u.id
              WHERE o.id = :order_id
              LIMIT 1";
$order = executeQuery($order_sql, [':order_id' => $order_id])->fetch();

// If order not found or already paid, redirect to index
if (!$order || $order['payment_status'] === 'paid') {
    header("Location: index.php");
    exit();
}

// Get order items
$items_sql = "SELECT oi.*, p.name, p.image_url
              FROM e_order_items oi
              JOIN e_products p ON oi.product_id = p.id
              WHERE oi.order_id = :order_id";
$items = executeQuery($items_sql, [':order_id' => $order_id])->fetchAll();

// Process payment
if (isset($_POST['process_payment'])) {
    $payment_method = $_POST['payment_method'] ?? 'cash';
    
    // Update order
    $update_sql = "UPDATE e_orders 
                  SET payment_status = 'paid', 
                      status = 'completed'
                  WHERE id = :order_id";
    executeQuery($update_sql, [':order_id' => $order_id]);
    
    // Update product stock
    foreach ($items as $item) {
        $update_stock_sql = "UPDATE e_products 
                            SET stock_quantity = stock_quantity - :quantity 
                            WHERE id = :product_id";
        executeQuery($update_stock_sql, [
            ':quantity' => $item['quantity'],
            ':product_id' => $item['product_id']
        ]);
    }
    
    // Redirect to confirmation page
    header("Location: order_confirmation.php?order_id=" . $order_id);
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Process Payment - Euro Spice</title>
    <link rel="stylesheet" href="../../plugins/bootstrap/css/bootstrap.min.css">
    <script defer src="../../plugins/fontawesome/js/all.js"></script>
    <style>
        .order-item {
            border-bottom: 1px solid #eee;
            padding: 10px 0;
        }
        .product-img {
            width: 60px;
            height: 60px;
            object-fit: cover;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-light border-bottom site-header sticky-top py-1"
        style="background-color: #f15b31;">
        <div class="container">
            <a class="navbar-brand fw-bold fs-4" href="../../index.php" style="color: white;"><b>Euro Spice</b></a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
                aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home"></i> Back to Cashier
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../account/settings.php">
                            <i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../account/logout.php">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container py-5">
        <h2 class="mb-4">Process Payment</h2>
        
        <div class="row">
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <div class="d-flex justify-content-between">
                            <h5 class="mb-0">Order Details</h5>
                            <span>Order #<?php echo $order_id; ?></span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <p><strong>Customer:</strong> <?php echo htmlspecialchars($order['customer_name']); ?></p>
                                <p><strong>Email:</strong> <?php echo htmlspecialchars($order['email']); ?></p>
                                <p><strong>Phone:</strong> <?php echo htmlspecialchars($order['phone']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Order Date:</strong> <?php echo date('F j, Y, g:i a', strtotime($order['created_at'])); ?></p>
                                <p><strong>Payment Status:</strong> <span class="text-danger">Unpaid</span></p>
                                <p><strong>Order Status:</strong> <?php echo ucfirst($order['status']); ?></p>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <p><strong>Shipping Address:</strong><br>
                            <?php echo nl2br(htmlspecialchars($order['shipping_address'] ?? $order['address'])); ?></p>
                        </div>
                        
                        <h6 class="mt-4 mb-3">Order Items</h6>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th class="text-center">Quantity</th>
                                    <th class="text-end">Price</th>
                                    <th class="text-end">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($items as $item): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($item['name']); ?></td>
                                        <td class="text-center"><?php echo $item['quantity']; ?></td>
                                        <td class="text-end">PHP <?php echo number_format($item['price'], 2); ?></td>
                                        <td class="text-end">PHP <?php echo number_format($item['price'] * $item['quantity'], 2); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="3" class="text-end">Subtotal:</th>
                                    <th class="text-end">PHP <?php echo number_format($order['total_amount'], 2); ?></th>
                                </tr>
                                <tr>
                                    <th colspan="3" class="text-end">Shipping:</th>
                                    <td class="text-end">Free</td>
                                </tr>
                                <tr>
                                    <th colspan="3" class="text-end">Total:</th>
                                    <th class="text-end">PHP <?php echo number_format($order['total_amount'], 2); ?></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Payment Information</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label for="payment_method" class="form-label">Payment Method</label>
                                <select class="form-select" id="payment_method" name="payment_method">
                                    <option value="cash">Cash</option>
                                    <option value="credit_card">Credit Card</option>
                                    <option value="gcash">GCash</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="amount_received" class="form-label">Amount Received</label>
                                <div class="input-group">
                                    <span class="input-group-text">PHP</span>
                                    <input type="number" class="form-control" id="amount_received" name="amount_received" value="<?php echo $order['total_amount']; ?>" min="<?php echo $order['total_amount']; ?>" step="0.01">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="change" class="form-label">Change</label>
                                <div class="input-group">
                                    <span class="input-group-text">PHP</span>
                                    <input type="text" class="form-control" id="change" readonly value="0.00">
                                </div>
                            </div>
                            
                            <button type="submit" name="process_payment" class="btn btn-success w-100">
                                Complete Payment
                            </button>
                            
                            <a href="index.php" class="btn btn-outline-secondary w-100 mt-2">
                                Cancel
                            </a>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script>
        // Calculate change
        document.getElementById('amount_received').addEventListener('input', function() {
            const amountReceived = parseFloat(this.value) || 0;
            const totalAmount = <?php echo $order['total_amount']; ?>;
            const change = amountReceived - totalAmount;
            
            document.getElementById('change').value = change.toFixed(2);
        });
    </script>
</body>

</html>
