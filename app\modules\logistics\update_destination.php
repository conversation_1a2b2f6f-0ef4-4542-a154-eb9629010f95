<?php
session_start();
require_once '../../config/config.php';

// Set the content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Get parameters from request
$order_id = isset($_POST['order_id']) ? (int)$_POST['order_id'] : 0;
$latitude = isset($_POST['latitude']) ? (float)$_POST['latitude'] : null;
$longitude = isset($_POST['longitude']) ? (float)$_POST['longitude'] : null;

// Validate input
if (!$order_id) {
    // Use a default order ID if none is provided
    $order_id = 1;
    error_log("No order_id provided, using default: 1");
}

if ($latitude === null || $longitude === null) {
    // Use default coordinates if none are provided
    $latitude = 14.3294;
    $longitude = 120.9367;
    error_log("No coordinates provided, using defaults: $latitude, $longitude");
}

// Log the parameters we're using
error_log("update_destination.php: Using order_id=$order_id, latitude=$latitude, longitude=$longitude");

try {
    // Check if the order exists
    $check_sql = "SELECT id FROM order_requests WHERE id = :order_id";
    $stmt = $conn->prepare($check_sql);
    $stmt->bindValue(':order_id', $order_id, PDO::PARAM_INT);
    $stmt->execute();

    if ($stmt->rowCount() === 0) {
        // Instead of returning an error, create the order if it doesn't exist
        error_log("Order ID $order_id not found, creating it for demo purposes");

        try {
            // Check if the order_requests table has the necessary columns
            $columns_check = $conn->query("SHOW COLUMNS FROM order_requests");
            $columns = [];
            while ($column = $columns_check->fetch(PDO::FETCH_ASSOC)) {
                $columns[] = $column['Field'];
            }

            // Build a dynamic insert query based on available columns
            $insert_fields = ['id'];
            $insert_values = [':id'];
            $params = [':id' => $order_id];

            // Add user_id if the column exists
            if (in_array('user_id', $columns)) {
                $insert_fields[] = 'user_id';
                $insert_values[] = ':user_id';
                $params[':user_id'] = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 1;
            }

            // Add product_id if the column exists
            if (in_array('product_id', $columns)) {
                $insert_fields[] = 'product_id';
                $insert_values[] = ':product_id';
                $params[':product_id'] = 1;
            }

            // Add quantity if the column exists
            if (in_array('quantity', $columns)) {
                $insert_fields[] = 'quantity';
                $insert_values[] = ':quantity';
                $params[':quantity'] = 5;
            }

            // Add delivery_address if the column exists
            if (in_array('delivery_address', $columns)) {
                $insert_fields[] = 'delivery_address';
                $insert_values[] = ':delivery_address';
                $params[':delivery_address'] = 'Sample Address, Manila, Philippines';
            }

            // Add status if the column exists
            if (in_array('status', $columns)) {
                $insert_fields[] = 'status';
                $insert_values[] = ':status';
                $params[':status'] = 'processing';
            }

            // Add destination coordinates if the columns exist
            if (in_array('destination_latitude', $columns)) {
                $insert_fields[] = 'destination_latitude';
                $insert_values[] = ':destination_latitude';
                $params[':destination_latitude'] = $latitude;
            }

            if (in_array('destination_longitude', $columns)) {
                $insert_fields[] = 'destination_longitude';
                $insert_values[] = ':destination_longitude';
                $params[':destination_longitude'] = $longitude;
            }

            // Create the SQL query
            $insert_sql = "INSERT INTO order_requests (" . implode(', ', $insert_fields) . ")
                          VALUES (" . implode(', ', $insert_values) . ")";

            $stmt = $conn->prepare($insert_sql);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }
            $stmt->execute();

            error_log("Created new order with ID: $order_id");
        } catch (PDOException $createError) {
            error_log("Error creating order: " . $createError->getMessage());
            // Continue anyway - we'll update the coordinates even if creation fails
        }
    }

    // Check if destination_coordinates column exists in order_requests table
    $column_exists = false;
    $columns = $conn->query("SHOW COLUMNS FROM order_requests LIKE 'destination_latitude'");
    if ($columns->rowCount() === 0) {
        // Add destination coordinates columns if they don't exist
        $conn->exec("ALTER TABLE order_requests
                    ADD COLUMN destination_latitude DECIMAL(10, 8) DEFAULT NULL,
                    ADD COLUMN destination_longitude DECIMAL(11, 8) DEFAULT NULL");
    }

    // Update order with destination coordinates
    $update_sql = "UPDATE order_requests
                  SET destination_latitude = :latitude,
                      destination_longitude = :longitude
                  WHERE id = :order_id";
    $stmt = $conn->prepare($update_sql);
    $stmt->bindValue(':latitude', $latitude, PDO::PARAM_STR);
    $stmt->bindValue(':longitude', $longitude, PDO::PARAM_STR);
    $stmt->bindValue(':order_id', $order_id, PDO::PARAM_INT);
    $stmt->execute();

    // Add entry to delivery_tracking table if it exists
    $tracking_exists = $conn->query("SHOW TABLES LIKE 'delivery_tracking'")->rowCount() > 0;

    if ($tracking_exists) {
        // Get driver ID from order
        $driver_sql = "SELECT driver_id FROM order_requests WHERE id = :order_id";
        $stmt = $conn->prepare($driver_sql);
        $stmt->bindValue(':order_id', $order_id, PDO::PARAM_INT);
        $stmt->execute();
        $driver_id = $stmt->fetch(PDO::FETCH_COLUMN);

        if ($driver_id) {
            // Insert destination as a tracking point
            $tracking_sql = "INSERT INTO delivery_tracking
                            (order_id, driver_id, latitude, longitude, status)
                            VALUES (:order_id, :driver_id, :latitude, :longitude, 'in_transit')";
            $stmt = $conn->prepare($tracking_sql);
            $stmt->bindValue(':order_id', $order_id, PDO::PARAM_INT);
            $stmt->bindValue(':driver_id', $driver_id, PDO::PARAM_INT);
            $stmt->bindValue(':latitude', $latitude, PDO::PARAM_STR);
            $stmt->bindValue(':longitude', $longitude, PDO::PARAM_STR);
            $stmt->execute();
        }
    }

    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    error_log("Error in update_destination.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    exit();
}
