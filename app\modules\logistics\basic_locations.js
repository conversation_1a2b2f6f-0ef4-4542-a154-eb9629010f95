/**
 * Basic Philippine Locations Dropdown Handler
 * Simple implementation with no dependencies
 */
document.addEventListener('DOMContentLoaded', function () {
    console.log('Basic locations script loaded');

    // Basic data for Philippines
    const provinces = [
        { code: 'CAV', name: '<PERSON>avi<PERSON>' },
        { code: 'LAG', name: 'Laguna' },
        { code: 'RI<PERSON>', name: 'R<PERSON><PERSON>' },
        { code: 'M<PERSON>', name: 'Metro Manila' },
        { code: 'BAT', name: 'Batangas' },
        { code: 'BUL', name: 'Bulacan' }
    ];

    const cities = {
        'CAV': ['Dasmariñas', 'Bacoor', 'Imus', 'Tagaytay', '<PERSON>', 'Silang', 'Trece Martires'],
        'LAG': ['Santa Rosa', 'Calamba', 'San Pedro', 'Biñan', 'Los Baños'],
        'RIZ': ['Antipolo', 'Cainta', 'Taytay', 'Angono', 'Binangonan'],
        'MM': ['Manila', 'Quezon City', 'Makati', 'Taguig', 'Pasig', 'Parañaque'],
        'BAT': ['Batangas City', 'Lipa', 'Tanauan', 'Santo Tomas', 'Nasugbu'],
        'BUL': ['Malolos', 'San Jose del Monte', 'Meycauayan', 'Baliuag', 'Santa Maria']
    };

    // Get DOM elements
    const provinceSelect = document.getElementById('province');
    const citySelect = document.getElementById('city');

    // Check if elements exist
    if (!provinceSelect || !citySelect) {
        console.error('Province or city select elements not found');
        return;
    }

    // Initialize province dropdown
    function initProvinceDropdown() {
        // Clear existing options
        provinceSelect.innerHTML = '<option value="">Select Province</option>';

        // Add provinces
        provinces.forEach(function (province) {
            const option = document.createElement('option');
            option.value = province.code;
            option.textContent = province.name;
            provinceSelect.appendChild(option);
        });

        console.log('Province dropdown initialized with', provinces.length, 'provinces');
    }

    // Initialize city dropdown based on selected province
    function updateCityDropdown() {
        // Get selected province
        const provinceCode = provinceSelect.value;

        // Clear existing options
        citySelect.innerHTML = '<option value="">Select City</option>';

        // If no province selected, just return
        if (!provinceCode) {
            return;
        }

        // Get cities for selected province
        const provinceCities = cities[provinceCode] || [];

        // Add cities to dropdown
        provinceCities.forEach(function (city) {
            const option = document.createElement('option');
            option.value = city;
            option.textContent = city;
            citySelect.appendChild(option);
        });

        console.log('City dropdown updated with', provinceCities.length, 'cities for', provinceCode);
    }

    // Update address when city is selected
    function updateAddress() {
        const addressField = document.getElementById('delivery_address');
        if (!addressField) {
            console.error('Address field not found');
            return;
        }

        // Get selected values
        const provinceIndex = provinceSelect.selectedIndex;
        const provinceName = provinceIndex > 0 ? provinceSelect.options[provinceIndex].text : '';
        const cityName = citySelect.value;

        console.log('Updating address with city:', cityName, 'province:', provinceName);

        // Only update if address is empty or contains default text
        if (addressField.value.trim() === '' ||
            addressField.value === 'Loading address...' ||
            addressField.value === 'Address will be updated when city is selected') {

            if (provinceName && cityName) {
                // Create a formatted address
                addressField.value = cityName + ', ' + provinceName + ', Philippines';
                console.log('Address updated to:', addressField.value);
            } else {
                // Set a placeholder message
                addressField.value = 'Address will be updated when city is selected';
                console.log('Address set to placeholder');
            }
        } else {
            console.log('Address field already has a value, not updating');
        }
    }

    // Function to handle pinpoint address
    function handlePinpointAddress() {
        const pinpointField = document.getElementById('pinpoint_address');
        const addressField = document.getElementById('delivery_address');

        if (!pinpointField || !addressField) {
            console.error('Pinpoint or address field not found');
            return;
        }

        // Add event listener to pinpoint field
        pinpointField.addEventListener('input', function () {
            // If pinpoint has a value, update the address field
            if (this.value.trim() !== '') {
                // Get city and province
                const provinceIndex = provinceSelect.selectedIndex;
                const provinceName = provinceIndex > 0 ? provinceSelect.options[provinceIndex].text : '';
                const cityName = citySelect.value;

                // Create a formatted address with the pinpoint
                if (provinceName && cityName) {
                    addressField.value = this.value + ', ' + cityName + ', ' + provinceName + ', Philippines';
                    console.log('Address updated with pinpoint:', addressField.value);
                } else {
                    addressField.value = this.value;
                    console.log('Address set to pinpoint only');
                }
            }
        });

        console.log('Pinpoint address handler initialized');
    }

    // Set up event listeners
    provinceSelect.addEventListener('change', function () {
        updateCityDropdown();
        updateAddress();
    });

    citySelect.addEventListener('change', updateAddress);

    // Initialize province dropdown
    initProvinceDropdown();

    // Initialize pinpoint address handler
    handlePinpointAddress();

    // Convert old province codes to new format
    window.convertProvinceCode = function (oldCode) {
        const codeMap = {
            '0128': 'CAV', // Cavite
            '0133': 'LAG', // Laguna
            '0174': 'RIZ', // Rizal
            '0137': 'MM',  // Metro Manila
            '0122': 'BAT', // Batangas
            '0127': 'BUL'  // Bulacan
        };

        return codeMap[oldCode] || oldCode;
    };
});
