<?php
// Include database configuration
require_once 'app/config/config.php';

// Check if inventory table exists
try {
    $result = $conn->query("SHOW TABLES LIKE 'inventory'");
    $inventoryExists = $result->rowCount() > 0;

    echo "Inventory table exists: " . ($inventoryExists ? "Yes" : "No") . "\n";

    if ($inventoryExists) {
        // Get table structure
        $result = $conn->query("DESCRIBE inventory");
        echo "\nInventory table structure:\n";
        echo "-------------------------\n";
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo $row['Field'] . " - " . $row['Type'] . " - " . ($row['Null'] === 'YES' ? 'NULL' : 'NOT NULL') . "\n";
        }

        // Get sample data
        $result = $conn->query("SELECT * FROM inventory LIMIT 5");
        $count = $result->rowCount();

        echo "\nSample data (" . $count . " rows):\n";
        echo "-------------------------\n";

        if ($count > 0) {
            while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                echo "ID: " . $row['id'] . "\n";
                echo "Product Name: " . $row['prod_name'] . "\n";
                echo "Brand: " . $row['brand_name'] . "\n";
                echo "Price: " . $row['price'] . "\n";
                echo "Stocks: " . $row['stocks'] . "\n";
                echo "-------------------------\n";
            }
        } else {
            echo "No data found in inventory table.\n";
        }
    }

    // Check if products table exists
    $result = $conn->query("SHOW TABLES LIKE 'products'");
    $productsExists = $result->rowCount() > 0;

    echo "\nProducts table exists: " . ($productsExists ? "Yes" : "No") . "\n";

    if ($productsExists) {
        // Get sample data
        $result = $conn->query("SELECT * FROM products LIMIT 5");
        $count = $result->rowCount();

        echo "\nSample data (" . $count . " rows):\n";
        echo "-------------------------\n";

        if ($count > 0) {
            while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                echo "ID: " . $row['id'] . "\n";
                echo "Name: " . $row['name'] . "\n";
                echo "Brand: " . $row['brand'] . "\n";
                echo "Price: " . $row['price'] . "\n";
                echo "Stock: " . $row['stock'] . "\n";
                echo "-------------------------\n";
            }
        } else {
            echo "No data found in products table.\n";
        }
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
