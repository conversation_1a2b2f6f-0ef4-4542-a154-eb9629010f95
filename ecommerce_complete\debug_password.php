<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database configuration
require_once 'config/database.php';

echo "<h1>Password Debug Tool</h1>";

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $action = $_POST['action'] ?? '';
    
    if ($action === 'check') {
        // Check password
        try {
            $sql = "SELECT * FROM users WHERE username = :username OR email = :email LIMIT 1";
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                ':username' => $username,
                ':email' => $username
            ]);
            
            $user = $stmt->fetch();
            
            if ($user) {
                echo "<h2>User Found</h2>";
                echo "<p>Username: " . htmlspecialchars($user['username']) . "</p>";
                echo "<p>Email: " . htmlspecialchars($user['email']) . "</p>";
                echo "<p>Role ID: " . htmlspecialchars($user['role_id']) . "</p>";
                echo "<p>Stored Password Hash: " . htmlspecialchars($user['password']) . "</p>";
                
                // Check if password matches
                $password_matches = password_verify($password, $user['password']);
                echo "<p>Password Match: " . ($password_matches ? "YES" : "NO") . "</p>";
                
                // Show password hash info
                $hash_info = password_get_info($user['password']);
                echo "<h3>Hash Info</h3>";
                echo "<pre>";
                print_r($hash_info);
                echo "</pre>";
            } else {
                echo "<p>User not found with username/email: " . htmlspecialchars($username) . "</p>";
            }
        } catch (PDOException $e) {
            echo "<p>Error: " . $e->getMessage() . "</p>";
        }
    } elseif ($action === 'hash') {
        // Generate hash for a password
        $hash = password_hash($password, PASSWORD_DEFAULT);
        echo "<h2>Password Hash</h2>";
        echo "<p>Password: " . htmlspecialchars($password) . "</p>";
        echo "<p>Hash: " . $hash . "</p>";
    }
}
?>

<h2>Check Password</h2>
<form method="POST">
    <div>
        <label for="username">Username or Email:</label>
        <input type="text" id="username" name="username" required>
    </div>
    <div>
        <label for="password">Password:</label>
        <input type="password" id="password" name="password" required>
    </div>
    <input type="hidden" name="action" value="check">
    <div>
        <button type="submit">Check Password</button>
    </div>
</form>

<h2>Generate Password Hash</h2>
<form method="POST">
    <div>
        <label for="password2">Password:</label>
        <input type="password" id="password2" name="password" required>
    </div>
    <input type="hidden" name="action" value="hash">
    <div>
        <button type="submit">Generate Hash</button>
    </div>
</form>
