{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js"], "names": ["MAX_UID", "MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "toString", "call", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "_window$getComputedSt", "window", "getComputedStyle", "transitionDuration", "transitionDelay", "floatTransitionDuration", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "nodeType", "emulateTransitionEnd", "duration", "called", "emulatedDuration", "addEventListener", "listener", "removeEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "Error", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "hasAttribute", "onDOMContentLoaded", "callback", "readyState", "mapData", "storeData", "id", "set", "key", "data", "bs<PERSON><PERSON>", "get", "keyProperties", "delete", "Data", "setData", "instance", "getData", "removeData", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "getUidEvent", "uid", "getEvent", "bootstrapHandler", "fn", "handler", "event", "<PERSON><PERSON><PERSON><PERSON>", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "this", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "len", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "replace", "custom", "indexOf", "add<PERSON><PERSON><PERSON>", "_normalizeParams", "handlers", "previousFn", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "on", "one", "_normalizeParams2", "inNamespace", "isNamespace", "char<PERSON>t", "elementEvent", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "$", "isNative", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "SELECTOR_DISMISS", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "CLASSNAME_ALERT", "CLASSNAME_FADE", "CLASSNAME_SHOW", "<PERSON><PERSON>", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "dispose", "closest", "_this", "classList", "remove", "contains", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "jQueryInterface", "each", "handle<PERSON><PERSON><PERSON>", "alertInstance", "getInstance", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "setAttribute", "normalizeData", "val", "Number", "normalizeDataKey", "chr", "button", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "_extends", "dataset", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "NODE_TEXT", "SelectorEngine", "matches", "find", "_ref", "concat", "Element", "prototype", "findOne", "children", "_ref2", "filter", "child", "parents", "ancestor", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "DIRECTION_NEXT", "DIRECTION_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_SLIDE", "CLASS_NAME_RIGHT", "CLASS_NAME_LEFT", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "PointerType", "TOUCH", "PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "direction", "_handleSwipe", "absDeltax", "abs", "_this2", "_keydown", "_addTouchEventListeners", "_this3", "start", "pointerType", "clientX", "touches", "end", "clearTimeout", "itemImg", "e", "add", "move", "tagName", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "from", "_setActiveIndicatorElement", "indicators", "nextIndicator", "elementInterval", "parseInt", "defaultInterval", "directionalClassName", "orderClassName", "_this4", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "carouselInterface", "action", "TypeError", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_CLICK", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DISABLED", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPRIGHT", "CLASS_NAME_DROPLEFT", "CLASS_NAME_MENURIGHT", "CLASS_NAME_NAVBAR", "CLASS_NAME_POSITION_STATIC", "SELECTOR_FORM_CHILD", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "flip", "boundary", "reference", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "isActive", "clearMenus", "getParentFromElement", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "focus", "destroy", "update", "scheduleUpdate", "stopPropagation", "constructor", "_getPlacement", "parentDropdown", "placement", "_getOffset", "offsets", "modifiers", "enabled", "preventOverflow", "boundariesElement", "applyStyle", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "dataApiKeydownHandler", "items", "backdrop", "EVENT_HIDE_PREVENTED", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "CLASS_NAME_SCROLLBAR_MEASURER", "CLASS_NAME_BACKDROP", "CLASS_NAME_OPEN", "CLASS_NAME_FADE", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_DISMISS", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "showEvent", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "transition", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_this5", "_triggerBackdropTransition", "_this6", "_this7", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "_this8", "animate", "createElement", "className", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "_this9", "isModalOverflowing", "scrollHeight", "clientHeight", "overflowY", "modalTransitionDuration", "paddingLeft", "paddingRight", "round", "right", "innerWidth", "_getScrollbarWidth", "_this10", "actualPadding", "calculatedPadding", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "_this11", "uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "nodeValue", "regExp", "attrRegex", "DefaultAllowlist", "*", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "_loop", "el", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacement", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_MODAL", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "complete", "_fixTransition", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "behavior", "arrow", "onCreate", "originalPlacement", "_handlePopperPlacementChange", "onUpdate", "eventIn", "eventOut", "_fixTitle", "titleType", "dataAttributes", "dataAttr", "tabClass", "map", "token", "tClass", "popperData", "popper", "initConfigAnimation", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "method", "EVENT_ACTIVATE", "EVENT_SCROLL", "CLASS_NAME_DROPDOWN_ITEM", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "queries", "link", "join", "listGroup", "navItem", "node", "spy", "CLASS_NAME_DROPDOWN_MENU", "SELECTOR_ACTIVE_UL", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "listElement", "itemSelector", "hideEvent", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdown", "CLASS_NAME_HIDE", "CLASS_NAME_SHOWING", "autohide", "Toast", "_clearTimeout"], "mappings": ";;;;;ioBAOA,IAAMA,QAAU,IACVC,wBAA0B,IAC1BC,eAAiB,gBAGjBC,OAAS,SAAAC,GACb,OAAIA,MAAAA,EACF,GAAUA,EAGL,GAAGC,SAASC,KAAKF,GAAKG,MAAM,eAAe,GAAGC,eASjDC,OAAS,SAAAC,GACb,GACEA,GAAUC,KAAKC,MAAMD,KAAKE,SAAWb,eAC9Bc,SAASC,eAAeL,IAEjC,OAAOA,GAGHM,YAAc,SAAAC,GAClB,IAAIC,EAAWD,EAAQE,aAAa,eAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAME,EAAWH,EAAQE,aAAa,QAEtCD,EAAWE,GAAyB,MAAbA,EAAmBA,EAASC,OAAS,KAG9D,OAAOH,GAGHI,uBAAyB,SAAAL,GAC7B,IAAMC,EAAWF,YAAYC,GAE7B,OAAIC,GACKJ,SAASS,cAAcL,GAAYA,EAGrC,MAGHM,uBAAyB,SAAAP,GAC7B,IAAMC,EAAWF,YAAYC,GAE7B,OAAOC,EAAWJ,SAASS,cAAcL,GAAY,MAGjDO,iCAAmC,SAAAR,GACvC,IAAKA,EACH,OAAO,EAFyC,IAAAS,EAS9CC,OAAOC,iBAAiBX,GAF1BY,EAPgDH,EAOhDG,mBACAC,EARgDJ,EAQhDI,gBAGIC,EAA0BC,WAAWH,GACrCI,EAAuBD,WAAWF,GAGxC,OAAKC,GAA4BE,GAKjCJ,EAAqBA,EAAmBK,MAAM,KAAK,GACnDJ,EAAkBA,EAAgBI,MAAM,KAAK,IAErCF,WAAWH,GAAsBG,WAAWF,IAAoB7B,yBAP/D,GAULkC,qBAAuB,SAAAlB,GAC3BA,EAAQmB,cAAc,IAAIC,MAAMnC,kBAG5BoC,UAAY,SAAAlC,GAAG,OAAKA,EAAI,IAAMA,GAAKmC,UAEnCC,qBAAuB,SAACvB,EAASwB,GACrC,IAAIC,GAAS,EAEPC,EAAmBF,EADD,EAOxBxB,EAAQ2B,iBAAiB1C,gBALzB,SAAS2C,IACPH,GAAS,EACTzB,EAAQ6B,oBAAoB5C,eAAgB2C,MAI9CE,YAAW,WACJL,GACHP,qBAAqBlB,KAEtB0B,IAGCK,gBAAkB,SAACC,EAAeC,EAAQC,GAC9CC,OAAOC,KAAKF,GAAaG,SAAQ,SAAAC,GAC/B,IAAMC,EAAgBL,EAAYI,GAC5BE,EAAQP,EAAOK,GACfG,EAAYD,GAASnB,UAAUmB,GACnC,UACAtD,OAAOsD,GAET,IAAK,IAAIE,OAAOH,GAAeI,KAAKF,GAClC,MAAM,IAAIG,MACLZ,EAAca,cAAdb,aACQM,EADX,oBACuCG,EADpCT,wBAEmBO,EAFtB,UAOFO,UAAY,SAAA9C,GAChB,IAAKA,EACH,OAAO,EAGT,GAAIA,EAAQ+C,OAAS/C,EAAQgD,YAAchD,EAAQgD,WAAWD,MAAO,CACnE,IAAME,EAAetC,iBAAiBX,GAChCkD,EAAkBvC,iBAAiBX,EAAQgD,YAEjD,MAAgC,SAAzBC,EAAaE,SACU,SAA5BD,EAAgBC,SACY,WAA5BF,EAAaG,WAGjB,OAAO,GAGHC,eAAiB,SAAjBA,EAAiBrD,GACrB,IAAKH,SAASyD,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxBvD,EAAQwD,YAA4B,CAC7C,IAAMC,EAAOzD,EAAQwD,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAIzD,aAAmB0D,WACd1D,EAIJA,EAAQgD,WAINK,EAAerD,EAAQgD,YAHrB,MAMLW,KAAO,WAAA,OAAM,cAEbC,OAAS,SAAA5D,GAAO,OAAIA,EAAQ6D,cAE5BC,UAAY,WAAM,IACdC,EAAWrD,OAAXqD,OAER,OAAIA,IAAWlE,SAASmE,KAAKC,aAAa,kBACjCF,EAGF,MAGHG,mBAAqB,SAAAC,GACG,YAAxBtE,SAASuE,WACXvE,SAAS8B,iBAAiB,mBAAoBwC,GAE9CA,KC7KEE,QAAW,WACf,IAAMC,EAAY,GACdC,EAAK,EACT,MAAO,CACLC,IADK,SACDxE,EAASyE,EAAKC,QACa,IAAlB1E,EAAQ2E,QACjB3E,EAAQ2E,MAAQ,CACdF,IAAAA,EACAF,GAAAA,GAEFA,KAGFD,EAAUtE,EAAQ2E,MAAMJ,IAAMG,GAEhCE,IAZK,SAYD5E,EAASyE,GACX,IAAKzE,QAAoC,IAAlBA,EAAQ2E,MAC7B,OAAO,KAGT,IAAME,EAAgB7E,EAAQ2E,MAC9B,OAAIE,EAAcJ,MAAQA,EACjBH,EAAUO,EAAcN,IAG1B,MAETO,OAxBK,SAwBE9E,EAASyE,GACd,QAA6B,IAAlBzE,EAAQ2E,MAAnB,CAIA,IAAME,EAAgB7E,EAAQ2E,MAC1BE,EAAcJ,MAAQA,WACjBH,EAAUO,EAAcN,WACxBvE,EAAQ2E,UAnCN,GAyCXI,KAAO,CACXC,QADW,SACHC,EAAUR,EAAKC,GACrBL,QAAQG,IAAIS,EAAUR,EAAKC,IAE7BQ,QAJW,SAIHD,EAAUR,GAChB,OAAOJ,QAAQO,IAAIK,EAAUR,IAE/BU,WAPW,SAOAF,EAAUR,GACnBJ,QAAQS,OAAOG,EAAUR,KC/CvBW,eAAiB,qBACjBC,eAAiB,OACjBC,cAAgB,SAChBC,cAAgB,GAClBC,SAAW,EACTC,aAAe,CACnBC,WAAY,YACZC,WAAY,YAERC,aAAe,CACnB,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,UASF,SAASC,YAAY7F,EAAS8F,GAC5B,OAAQA,GAAUA,EAAP,KAAeN,YAAiBxF,EAAQwF,UAAYA,WAGjE,SAASO,SAAS/F,GAChB,IAAM8F,EAAMD,YAAY7F,GAKxB,OAHAA,EAAQwF,SAAWM,EACnBP,cAAcO,GAAOP,cAAcO,IAAQ,GAEpCP,cAAcO,GAGvB,SAASE,iBAAiBhG,EAASiG,GACjC,OAAO,SAASC,EAAQC,GAOtB,OANAA,EAAMC,eAAiBpG,EAEnBkG,EAAQG,QACVC,aAAaC,IAAIvG,EAASmG,EAAMK,KAAMP,GAGjCA,EAAGQ,MAAMzG,EAAS,CAACmG,KAI9B,SAASO,2BAA2B1G,EAASC,EAAUgG,GACrD,OAAO,SAASC,EAAQC,GAGtB,IAFA,IAAMQ,EAAc3G,EAAQ4G,iBAAiB3G,GAElC4G,EAAWV,EAAXU,OAAkBA,GAAUA,IAAWC,KAAMD,EAASA,EAAO7D,WACtE,IAAK,IAAI+D,EAAIJ,EAAYK,OAAQD,KAC/B,GAAIJ,EAAYI,KAAOF,EAOrB,OANAV,EAAMC,eAAiBS,EAEnBX,EAAQG,QACVC,aAAaC,IAAIvG,EAASmG,EAAMK,KAAMP,GAGjCA,EAAGQ,MAAMI,EAAQ,CAACV,IAM/B,OAAO,MAIX,SAASc,YAAYC,EAAQhB,EAASiB,QAA2B,IAA3BA,IAAAA,EAAqB,MAGzD,IAFA,IAAMC,EAAejF,OAAOC,KAAK8E,GAExBH,EAAI,EAAGM,EAAMD,EAAaJ,OAAQD,EAAIM,EAAKN,IAAK,CACvD,IAAMZ,EAAQe,EAAOE,EAAaL,IAElC,GAAIZ,EAAMmB,kBAAoBpB,GAAWC,EAAMgB,qBAAuBA,EACpE,OAAOhB,EAIX,OAAO,KAGT,SAASoB,gBAAgBC,EAAmBtB,EAASuB,GACnD,IAAMC,EAAgC,iBAAZxB,EACpBoB,EAAkBI,EAAaD,EAAevB,EAGhDyB,EAAYH,EAAkBI,QAAQvC,eAAgB,IACpDwC,EAASpC,aAAakC,GAY5B,OAVIE,IACFF,EAAYE,GAGGjC,aAAakC,QAAQH,IAAc,IAGlDA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASI,WAAW/H,EAASwH,EAAmBtB,EAASuB,EAAcpB,GACrE,GAAiC,iBAAtBmB,GAAmCxH,EAA9C,CAIKkG,IACHA,EAAUuB,EACVA,EAAe,MAP4D,IAAAO,EAU5BT,gBAAgBC,EAAmBtB,EAASuB,GAAtFC,EAVsEM,EAAA,GAU1DV,EAV0DU,EAAA,GAUzCL,EAVyCK,EAAA,GAWvEd,EAASnB,SAAS/F,GAClBiI,EAAWf,EAAOS,KAAeT,EAAOS,GAAa,IACrDO,EAAajB,YAAYgB,EAAUX,EAAiBI,EAAaxB,EAAU,MAEjF,GAAIgC,EACFA,EAAW7B,OAAS6B,EAAW7B,QAAUA,MAD3C,CAMA,IAAMP,EAAMD,YAAYyB,EAAiBE,EAAkBI,QAAQxC,eAAgB,KAC7Ea,EAAKyB,EACThB,2BAA2B1G,EAASkG,EAASuB,GAC7CzB,iBAAiBhG,EAASkG,GAE5BD,EAAGkB,mBAAqBO,EAAaxB,EAAU,KAC/CD,EAAGqB,gBAAkBA,EACrBrB,EAAGI,OAASA,EACZJ,EAAGT,SAAWM,EACdmC,EAASnC,GAAOG,EAEhBjG,EAAQ2B,iBAAiBgG,EAAW1B,EAAIyB,KAG1C,SAASS,cAAcnI,EAASkH,EAAQS,EAAWzB,EAASiB,GAC1D,IAAMlB,EAAKgB,YAAYC,EAAOS,GAAYzB,EAASiB,GAE9ClB,IAILjG,EAAQ6B,oBAAoB8F,EAAW1B,EAAImC,QAAQjB,WAC5CD,EAAOS,GAAW1B,EAAGT,WAG9B,SAAS6C,yBAAyBrI,EAASkH,EAAQS,EAAWW,GAC5D,IAAMC,EAAoBrB,EAAOS,IAAc,GAE/CxF,OAAOC,KAAKmG,GAAmBlG,SAAQ,SAAAmG,GACrC,GAAIA,EAAWV,QAAQQ,IAAc,EAAG,CACtC,IAAMnC,EAAQoC,EAAkBC,GAEhCL,cAAcnI,EAASkH,EAAQS,EAAWxB,EAAMmB,gBAAiBnB,EAAMgB,wBAK7E,IAAMb,aAAe,CACnBmC,GADmB,SAChBzI,EAASmG,EAAOD,EAASuB,GAC1BM,WAAW/H,EAASmG,EAAOD,EAASuB,GAAc,IAGpDiB,IALmB,SAKf1I,EAASmG,EAAOD,EAASuB,GAC3BM,WAAW/H,EAASmG,EAAOD,EAASuB,GAAc,IAGpDlB,IATmB,SASfvG,EAASwH,EAAmBtB,EAASuB,GACvC,GAAiC,iBAAtBD,GAAmCxH,EAA9C,CADqD,IAAA2I,EAKJpB,gBAAgBC,EAAmBtB,EAASuB,GAAtFC,EAL8CiB,EAAA,GAKlCrB,EALkCqB,EAAA,GAKjBhB,EALiBgB,EAAA,GAM/CC,EAAcjB,IAAcH,EAC5BN,EAASnB,SAAS/F,GAClB6I,EAA8C,MAAhCrB,EAAkBsB,OAAO,GAE7C,QAA+B,IAApBxB,EAAX,CAUIuB,GACF1G,OAAOC,KAAK8E,GAAQ7E,SAAQ,SAAA0G,GAC1BV,yBAAyBrI,EAASkH,EAAQ6B,EAAcvB,EAAkBwB,MAAM,OAIpF,IAAMT,EAAoBrB,EAAOS,IAAc,GAC/CxF,OAAOC,KAAKmG,GAAmBlG,SAAQ,SAAA4G,GACrC,IAAMT,EAAaS,EAAYrB,QAAQtC,cAAe,IAEtD,IAAKsD,GAAepB,EAAkBM,QAAQU,IAAe,EAAG,CAC9D,IAAMrC,EAAQoC,EAAkBU,GAEhCd,cAAcnI,EAASkH,EAAQS,EAAWxB,EAAMmB,gBAAiBnB,EAAMgB,4BAvB3E,CAEE,IAAKD,IAAWA,EAAOS,GACrB,OAGFQ,cAAcnI,EAASkH,EAAQS,EAAWL,EAAiBI,EAAaxB,EAAU,SAsBtFgD,QA/CmB,SA+CXlJ,EAASmG,EAAOgD,GACtB,GAAqB,iBAAVhD,IAAuBnG,EAChC,OAAO,KAGT,IAKIoJ,EALEC,EAAIvF,YACJ6D,EAAYxB,EAAMyB,QAAQvC,eAAgB,IAC1CuD,EAAczC,IAAUwB,EACxB2B,EAAW1D,aAAakC,QAAQH,IAAc,EAGhD4B,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EACnBC,EAAM,KA4CV,OA1CId,GAAeS,IACjBD,EAAcC,EAAEjI,MAAM+E,EAAOgD,GAE7BE,EAAErJ,GAASkJ,QAAQE,GACnBG,GAAWH,EAAYO,uBACvBH,GAAkBJ,EAAYQ,gCAC9BH,EAAmBL,EAAYS,sBAG7BP,GACFI,EAAM7J,SAASiK,YAAY,eACvBC,UAAUpC,EAAW4B,GAAS,GAElCG,EAAM,IAAIM,YAAY7D,EAAO,CAC3BoD,QAAAA,EACAU,YAAY,SAKI,IAATd,GACThH,OAAOC,KAAK+G,GAAM9G,SAAQ,SAAAoC,GACxBtC,OAAO+H,eAAeR,EAAKjF,EAAK,CAC9BG,IAD8B,WAE5B,OAAOuE,EAAK1E,SAMhBgF,GACFC,EAAIS,iBAGFX,GACFxJ,EAAQmB,cAAcuI,GAGpBA,EAAID,uBAA2C,IAAhBL,GACjCA,EAAYe,iBAGPT,IC7SLU,KAAO,QACPC,QAAU,eACVC,SAAW,WACXC,UAAS,IAAOD,SAChBE,aAAe,YAEfC,iBAAmB,yBAEnBC,YAAW,QAAWH,UACtBI,aAAY,SAAYJ,UACxBK,qBAAoB,QAAWL,UAAYC,aAE3CK,gBAAkB,QAClBC,eAAiB,OACjBC,eAAiB,OAQjBC,MAAAA,WACJ,SAAAA,EAAYhL,GACV8G,KAAKmE,SAAWjL,EAEZ8G,KAAKmE,UACPlG,KAAKC,QAAQhF,EAASsK,SAAUxD,iCAYpCoE,MAAA,SAAMlL,GACJ,IAAMmL,EAAcnL,EAAU8G,KAAKsE,gBAAgBpL,GAAW8G,KAAKmE,SAC7DI,EAAcvE,KAAKwE,mBAAmBH,GAExB,OAAhBE,GAAwBA,EAAY5B,kBAIxC3C,KAAKyE,eAAeJ,MAGtBK,QAAA,WACEzG,KAAKI,WAAW2B,KAAKmE,SAAUX,UAC/BxD,KAAKmE,SAAW,QAKlBG,gBAAA,SAAgBpL,GACd,OAAOO,uBAAuBP,IAAYA,EAAQyL,QAAR,IAAoBZ,oBAGhES,mBAAA,SAAmBtL,GACjB,OAAOsG,aAAa4C,QAAQlJ,EAAS0K,gBAGvCa,eAAA,SAAevL,GAAS,IAAA0L,EAAA5E,KAGtB,GAFA9G,EAAQ2L,UAAUC,OAAOb,gBAEpB/K,EAAQ2L,UAAUE,SAASf,gBAAhC,CAKA,IAAMlK,EAAqBJ,iCAAiCR,GAE5DsG,aAAaoC,IAAI1I,EAASf,gBAAgB,WAAA,OAAMyM,EAAKI,gBAAgB9L,MACrEuB,qBAAqBvB,EAASY,QAP5BkG,KAAKgF,gBAAgB9L,MAUzB8L,gBAAA,SAAgB9L,GACVA,EAAQgD,YACVhD,EAAQgD,WAAW+I,YAAY/L,GAGjCsG,aAAa4C,QAAQlJ,EAAS2K,iBAKzBqB,gBAAP,SAAuB/J,GACrB,OAAO6E,KAAKmF,MAAK,WACf,IAAIvH,EAAOK,KAAKG,QAAQ4B,KAAMwD,UAEzB5F,IACHA,EAAO,IAAIsG,EAAMlE,OAGJ,UAAX7E,GACFyC,EAAKzC,GAAQ6E,YAKZoF,cAAP,SAAqBC,GACnB,OAAO,SAAUhG,GACXA,GACFA,EAAMgE,iBAGRgC,EAAcjB,MAAMpE,UAIjBsF,YAAP,SAAmBpM,GACjB,OAAO+E,KAAKG,QAAQlF,EAASsK,8DAhF7B,OAAOD,cAZLW,GAqGN1E,aAAamC,GAAG5I,SAAU+K,qBAAsBH,iBAAkBO,MAAMkB,cAAc,IAAIlB,QAS1F9G,oBAAmB,WACjB,IAAMmF,EAAIvF,YAEV,GAAIuF,EAAG,CACL,IAAMgD,EAAqBhD,EAAEpD,GAAGmE,MAChCf,EAAEpD,GAAGmE,MAAQY,MAAMgB,gBACnB3C,EAAEpD,GAAGmE,MAAMkC,YAActB,MACzB3B,EAAEpD,GAAGmE,MAAMmC,WAAa,WAEtB,OADAlD,EAAEpD,GAAGmE,MAAQiC,EACNrB,MAAMgB,qBCpJnB,IAAM5B,OAAO,SACPC,UAAU,eACVC,WAAW,YACXC,YAAS,IAAOD,WAChBE,eAAe,YAEfgC,kBAAoB,SAEpBC,qBAAuB,yBAEvB7B,uBAAoB,QAAWL,YAAYC,eAQ3CkC,OAAAA,WACJ,SAAAA,EAAY1M,GACV8G,KAAKmE,SAAWjL,EAChB+E,KAAKC,QAAQhF,EAASsK,WAAUxD,iCAWlC6F,OAAA,WAEE7F,KAAKmE,SAAS2B,aAAa,eAAgB9F,KAAKmE,SAASU,UAAUgB,OAAOH,uBAG5EhB,QAAA,WACEzG,KAAKI,WAAW2B,KAAKmE,SAAUX,YAC/BxD,KAAKmE,SAAW,QAKXe,gBAAP,SAAuB/J,GACrB,OAAO6E,KAAKmF,MAAK,WACf,IAAIvH,EAAOK,KAAKG,QAAQ4B,KAAMwD,YAEzB5F,IACHA,EAAO,IAAIgI,EAAO5F,OAGL,WAAX7E,GACFyC,EAAKzC,WAKJmK,YAAP,SAAmBpM,GACjB,OAAO+E,KAAKG,QAAQlF,EAASsK,gEAhC7B,OAAOD,gBATLqC,GC5BN,SAASG,cAAcC,GACrB,MAAY,SAARA,GAIQ,UAARA,IAIAA,IAAQC,OAAOD,GAAK1N,WACf2N,OAAOD,GAGJ,KAARA,GAAsB,SAARA,EACT,KAGFA,GAGT,SAASE,iBAAiBvI,GACxB,OAAOA,EAAImD,QAAQ,UAAU,SAAAqF,GAAG,MAAA,IAAQA,EAAI1N,iBD0D9C+G,aAAamC,GAAG5I,SAAU+K,uBAAsB6B,sBAAsB,SAAAtG,GACpEA,EAAMgE,iBAEN,IAAM+C,EAAS/G,EAAMU,OAAO4E,QAAQgB,sBAEhC/H,EAAOK,KAAKG,QAAQgI,EAAQ5C,YAC3B5F,IACHA,EAAO,IAAIgI,OAAOQ,IAGpBxI,EAAKiI,YAUPzI,oBAAmB,WACjB,IAAMmF,EAAIvF,YAEV,GAAIuF,EAAG,CACL,IAAMgD,EAAqBhD,EAAEpD,GAAGmE,QAChCf,EAAEpD,GAAGmE,QAAQsC,OAAOV,gBACpB3C,EAAEpD,GAAGmE,QAAMkC,YAAcI,OAEzBrD,EAAEpD,GAAGmE,QAAMmC,WAAa,WAEtB,OADAlD,EAAEpD,GAAGmE,QAAQiC,EACNK,OAAOV,qBCrFpB,IAAMmB,YAAc,CAClBC,iBADkB,SACDpN,EAASyE,EAAKjC,GAC7BxC,EAAQ4M,aAAR,QAA6BI,iBAAiBvI,GAAQjC,IAGxD6K,oBALkB,SAKErN,EAASyE,GAC3BzE,EAAQsN,gBAAR,QAAgCN,iBAAiBvI,KAGnD8I,kBATkB,SASAvN,GAChB,IAAKA,EACH,MAAO,GAGT,IAAMwN,EAAUC,SAAA,GACXzN,EAAQ0N,SAOb,OAJAvL,OAAOC,KAAKoL,GAAYnL,SAAQ,SAAAoC,GAC9B+I,EAAW/I,GAAOoI,cAAcW,EAAW/I,OAGtC+I,GAGTG,iBAzBkB,SAyBD3N,EAASyE,GACxB,OAAOoI,cAAc7M,EAAQE,aAAR,QAA6B8M,iBAAiBvI,MAGrEmJ,OA7BkB,SA6BX5N,GACL,IAAM6N,EAAO7N,EAAQ8N,wBAErB,MAAO,CACLC,IAAKF,EAAKE,IAAMlO,SAASmE,KAAKgK,UAC9BC,KAAMJ,EAAKI,KAAOpO,SAASmE,KAAKkK,aAIpCC,SAtCkB,SAsCTnO,GACP,MAAO,CACL+N,IAAK/N,EAAQoO,UACbH,KAAMjO,EAAQqO,cC3DdC,UAAY,EAEZC,eAAiB,CACrBC,QADqB,SACbxO,EAASC,GACf,OAAOD,EAAQwO,QAAQvO,IAGzBwO,KALqB,SAKhBxO,EAAUD,GAAoC,IAAA0O,EACjD,YADiD,IAApC1O,IAAAA,EAAUH,SAASyD,kBACzBoL,EAAA,IAAGC,OAAHlI,MAAAiI,EAAaE,QAAQC,UAAUjI,iBAAiBvH,KAAKW,EAASC,KAGvE6O,QATqB,SASb7O,EAAUD,GAChB,YADoD,IAApCA,IAAAA,EAAUH,SAASyD,iBAC5BsL,QAAQC,UAAUvO,cAAcjB,KAAKW,EAASC,IAGvD8O,SAbqB,SAaZ/O,EAASC,GAAU,IAAA+O,EACpBD,GAAWC,EAAA,IAAGL,OAAHlI,MAAAuI,EAAahP,EAAQ+O,UAEtC,OAAOA,EAASE,QAAO,SAAAC,GAAK,OAAIA,EAAMV,QAAQvO,OAGhDkP,QAnBqB,SAmBbnP,EAASC,GAKf,IAJA,IAAMkP,EAAU,GAEZC,EAAWpP,EAAQgD,WAEhBoM,GAAYA,EAAS9N,WAAa+N,KAAKC,cAAgBF,EAAS9N,WAAagN,WAC9ExH,KAAK0H,QAAQY,EAAUnP,IACzBkP,EAAQI,KAAKH,GAGfA,EAAWA,EAASpM,WAGtB,OAAOmM,GAGTK,KAnCqB,SAmChBxP,EAASC,GAGZ,IAFA,IAAIwP,EAAWzP,EAAQ0P,uBAEhBD,GAAU,CACf,GAAIA,EAASjB,QAAQvO,GACnB,MAAO,CAACwP,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAjDqB,SAiDhB3P,EAASC,GAGZ,IAFA,IAAI0P,EAAO3P,EAAQ4P,mBAEZD,GAAM,CACX,GAAI7I,KAAK0H,QAAQmB,EAAM1P,GACrB,MAAO,CAAC0P,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,KC7CLxF,OAAO,WACPC,UAAU,eACVC,WAAW,cACXC,YAAS,IAAOD,WAChBE,eAAe,YAEfqF,eAAiB,YACjBC,gBAAkB,aAClBC,uBAAyB,IACzBC,gBAAkB,GAElBC,QAAU,CACdC,SAAU,IACVC,UAAU,EACVC,OAAO,EACPC,MAAO,QACPC,MAAM,EACNC,OAAO,GAGHC,YAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,MAAO,mBACPC,KAAM,UACNC,MAAO,WAGHE,eAAiB,OACjBC,eAAiB,OACjBC,eAAiB,OACjBC,gBAAkB,QAElBC,YAAW,QAAWtG,YACtBuG,WAAU,OAAUvG,YACpBwG,cAAa,UAAaxG,YAC1ByG,iBAAgB,aAAgBzG,YAChC0G,iBAAgB,aAAgB1G,YAChC2G,iBAAgB,aAAgB3G,YAChC4G,gBAAe,YAAe5G,YAC9B6G,eAAc,WAAc7G,YAC5B8G,kBAAiB,cAAiB9G,YAClC+G,gBAAe,YAAe/G,YAC9BgH,iBAAgB,YAAehH,YAC/BiH,oBAAmB,OAAUjH,YAAYC,eACzCI,uBAAoB,QAAWL,YAAYC,eAE3CiH,oBAAsB,WACtBjF,oBAAoB,SACpBkF,iBAAmB,QACnBC,iBAAmB,sBACnBC,gBAAkB,qBAClBC,gBAAkB,qBAClBC,gBAAkB,qBAClBC,yBAA2B,gBAE3BC,gBAAkB,UAClBC,qBAAuB,wBACvBC,cAAgB,iBAChBC,kBAAoB,qBACpBC,mBAAqB,2CACrBC,oBAAsB,uBACtBC,oBAAsB,gCACtBC,mBAAqB,yBAErBC,YAAc,CAClBC,MAAO,QACPC,IAAK,OAQDC,SAAAA,WACJ,SAAAA,EAAY3S,EAASiC,GACnB6E,KAAK8L,OAAS,KACd9L,KAAK+L,UAAY,KACjB/L,KAAKgM,eAAiB,KACtBhM,KAAKiM,WAAY,EACjBjM,KAAKkM,YAAa,EAClBlM,KAAKmM,aAAe,KACpBnM,KAAKoM,YAAc,EACnBpM,KAAKqM,YAAc,EAEnBrM,KAAKsM,QAAUtM,KAAKuM,WAAWpR,GAC/B6E,KAAKmE,SAAWjL,EAChB8G,KAAKwM,mBAAqB/E,eAAeO,QAAQuD,oBAAqBvL,KAAKmE,UAC3EnE,KAAKyM,gBAAkB,iBAAkB1T,SAASyD,iBAAmBkQ,UAAUC,eAAiB,EAChG3M,KAAK4M,cAAgBtL,QAAQ1H,OAAOiT,cAEpC7M,KAAK8M,qBACL7O,KAAKC,QAAQhF,EAASsK,WAAUxD,iCAelC6I,KAAA,WACO7I,KAAKkM,YACRlM,KAAK+M,OAAOpD,mBAIhBqD,gBAAA,YAGOjU,SAASkU,QAAUjR,UAAUgE,KAAKmE,WACrCnE,KAAK6I,UAITH,KAAA,WACO1I,KAAKkM,YACRlM,KAAK+M,OAAOnD,mBAIhBL,MAAA,SAAMlK,GACCA,IACHW,KAAKiM,WAAY,GAGfxE,eAAeO,QAAQsD,mBAAoBtL,KAAKmE,YAClD/J,qBAAqB4F,KAAKmE,UAC1BnE,KAAKkN,OAAM,IAGbC,cAAcnN,KAAK+L,WACnB/L,KAAK+L,UAAY,QAGnBmB,MAAA,SAAM7N,GACCA,IACHW,KAAKiM,WAAY,GAGfjM,KAAK+L,YACPoB,cAAcnN,KAAK+L,WACnB/L,KAAK+L,UAAY,MAGf/L,KAAKsM,SAAWtM,KAAKsM,QAAQlD,WAAapJ,KAAKiM,YACjDjM,KAAKoN,kBAELpN,KAAK+L,UAAYsB,aACdtU,SAASuU,gBAAkBtN,KAAKgN,gBAAkBhN,KAAK6I,MAAM0E,KAAKvN,MACnEA,KAAKsM,QAAQlD,cAKnBoE,GAAA,SAAGC,GAAO,IAAA7I,EAAA5E,KACRA,KAAKgM,eAAiBvE,eAAeO,QAAQmD,qBAAsBnL,KAAKmE,UACxE,IAAMuJ,EAAc1N,KAAK2N,cAAc3N,KAAKgM,gBAE5C,KAAIyB,EAAQzN,KAAK8L,OAAO5L,OAAS,GAAKuN,EAAQ,GAI9C,GAAIzN,KAAKkM,WACP1M,aAAaoC,IAAI5B,KAAKmE,SAAU6F,YAAY,WAAA,OAAMpF,EAAK4I,GAAGC,UAD5D,CAKA,GAAIC,IAAgBD,EAGlB,OAFAzN,KAAKuJ,aACLvJ,KAAKkN,QAIP,IAAMU,EAAYH,EAAQC,EACxB/D,eACAC,eAEF5J,KAAK+M,OAAOa,EAAW5N,KAAK8L,OAAO2B,QAGrC/I,QAAA,WACElF,aAAaC,IAAIO,KAAKmE,SAAUV,aAChCxF,KAAKI,WAAW2B,KAAKmE,SAAUX,YAE/BxD,KAAK8L,OAAS,KACd9L,KAAKsM,QAAU,KACftM,KAAKmE,SAAW,KAChBnE,KAAK+L,UAAY,KACjB/L,KAAKiM,UAAY,KACjBjM,KAAKkM,WAAa,KAClBlM,KAAKgM,eAAiB,KACtBhM,KAAKwM,mBAAqB,QAK5BD,WAAA,SAAWpR,GAMT,OALAA,EAAMwL,SAAA,GACDwC,QACAhO,GAELF,gBAAgBqI,OAAMnI,EAAQuO,aACvBvO,KAGT0S,aAAA,WACE,IAAMC,EAAYlV,KAAKmV,IAAI/N,KAAKqM,aAEhC,KAAIyB,GAAa5E,iBAAjB,CAIA,IAAM0E,EAAYE,EAAY9N,KAAKqM,YAEnCrM,KAAKqM,YAAc,EAGfuB,EAAY,GACd5N,KAAK0I,OAIHkF,EAAY,GACd5N,KAAK6I,WAITiE,mBAAA,WAAqB,IAAAkB,EAAAhO,KACfA,KAAKsM,QAAQjD,UACf7J,aAAamC,GAAG3B,KAAKmE,SAAU8F,eAAe,SAAA5K,GAAK,OAAI2O,EAAKC,SAAS5O,MAG5C,UAAvBW,KAAKsM,QAAQ/C,QACf/J,aAAamC,GAAG3B,KAAKmE,SAAU+F,kBAAkB,SAAA7K,GAAK,OAAI2O,EAAKzE,MAAMlK,MACrEG,aAAamC,GAAG3B,KAAKmE,SAAUgG,kBAAkB,SAAA9K,GAAK,OAAI2O,EAAKd,MAAM7N,OAGnEW,KAAKsM,QAAQ7C,OAASzJ,KAAKyM,iBAC7BzM,KAAKkO,6BAITA,wBAAA,WAA0B,IAAAC,EAAAnO,KAClBoO,EAAQ,SAAA/O,GACR8O,EAAKvB,eAAiBlB,YAAYrM,EAAMgP,YAAYtS,eACtDoS,EAAK/B,YAAc/M,EAAMiP,QACfH,EAAKvB,gBACfuB,EAAK/B,YAAc/M,EAAMkP,QAAQ,GAAGD,UAalCE,EAAM,SAAAnP,GACN8O,EAAKvB,eAAiBlB,YAAYrM,EAAMgP,YAAYtS,iBACtDoS,EAAK9B,YAAchN,EAAMiP,QAAUH,EAAK/B,aAG1C+B,EAAKN,eACsB,UAAvBM,EAAK7B,QAAQ/C,QASf4E,EAAK5E,QACD4E,EAAKhC,cACPsC,aAAaN,EAAKhC,cAGpBgC,EAAKhC,aAAenR,YAAW,SAAAqE,GAAK,OAAI8O,EAAKjB,MAAM7N,KAAQ4J,uBAAyBkF,EAAK7B,QAAQlD,YAIrG3B,eAAeE,KAAK0D,kBAAmBrL,KAAKmE,UAAU5I,SAAQ,SAAAmT,GAC5DlP,aAAamC,GAAG+M,EAASjE,kBAAkB,SAAAkE,GAAC,OAAIA,EAAEtL,uBAGhDrD,KAAK4M,eACPpN,aAAamC,GAAG3B,KAAKmE,SAAUoG,mBAAmB,SAAAlL,GAAK,OAAI+O,EAAM/O,MACjEG,aAAamC,GAAG3B,KAAKmE,SAAUqG,iBAAiB,SAAAnL,GAAK,OAAImP,EAAInP,MAE7DW,KAAKmE,SAASU,UAAU+J,IAAI3D,4BAE5BzL,aAAamC,GAAG3B,KAAKmE,SAAUiG,kBAAkB,SAAA/K,GAAK,OAAI+O,EAAM/O,MAChEG,aAAamC,GAAG3B,KAAKmE,SAAUkG,iBAAiB,SAAAhL,GAAK,OA5C1C,SAAAA,GAEPA,EAAMkP,SAAWlP,EAAMkP,QAAQrO,OAAS,EAC1CiO,EAAK9B,YAAc,EAEnB8B,EAAK9B,YAAchN,EAAMkP,QAAQ,GAAGD,QAAUH,EAAK/B,YAuCIyC,CAAKxP,MAC9DG,aAAamC,GAAG3B,KAAKmE,SAAUmG,gBAAgB,SAAAjL,GAAK,OAAImP,EAAInP,UAIhE4O,SAAA,SAAS5O,GACP,IAAI,kBAAkBxD,KAAKwD,EAAMU,OAAO+O,SAIxC,OAAQzP,EAAM1B,KACZ,KAAKoL,eACH1J,EAAMgE,iBACNrD,KAAK0I,OACL,MACF,KAAKM,gBACH3J,EAAMgE,iBACNrD,KAAK6I,WAMX8E,cAAA,SAAczU,GAKZ,OAJA8G,KAAK8L,OAAS5S,GAAWA,EAAQgD,WAC/BuL,eAAeE,KAAKyD,cAAelS,EAAQgD,YAC3C,GAEK8D,KAAK8L,OAAO9K,QAAQ9H,MAG7B6V,oBAAA,SAAoBnB,EAAWoB,GAC7B,IAAMC,EAAkBrB,IAAcjE,eAChCuF,EAAkBtB,IAAchE,eAChC8D,EAAc1N,KAAK2N,cAAcqB,GACjCG,EAAgBnP,KAAK8L,OAAO5L,OAAS,EAI3C,IAHuBgP,GAAmC,IAAhBxB,GACjBuB,GAAmBvB,IAAgByB,KAEtCnP,KAAKsM,QAAQ9C,KACjC,OAAOwF,EAGT,IACMI,GAAa1B,GADLE,IAAchE,gBAAkB,EAAI,IACR5J,KAAK8L,OAAO5L,OAEtD,OAAsB,IAAfkP,EACLpP,KAAK8L,OAAO9L,KAAK8L,OAAO5L,OAAS,GACjCF,KAAK8L,OAAOsD,MAGhBC,mBAAA,SAAmBC,EAAeC,GAChC,IAAMC,EAAcxP,KAAK2N,cAAc2B,GACjCG,EAAYzP,KAAK2N,cAAclG,eAAeO,QAAQmD,qBAAsBnL,KAAKmE,WAEvF,OAAO3E,aAAa4C,QAAQpC,KAAKmE,SAAU4F,YAAa,CACtDuF,cAAAA,EACA1B,UAAW2B,EACXG,KAAMD,EACNjC,GAAIgC,OAIRG,2BAAA,SAA2BzW,GACzB,GAAI8G,KAAKwM,mBAAoB,CAE3B,IADA,IAAMoD,EAAanI,eAAeE,KAAKuD,gBAAiBlL,KAAKwM,oBACpDvM,EAAI,EAAGA,EAAI2P,EAAW1P,OAAQD,IACrC2P,EAAW3P,GAAG4E,UAAUC,OAAOY,qBAGjC,IAAMmK,EAAgB7P,KAAKwM,mBAAmBvE,SAC5CjI,KAAK2N,cAAczU,IAGjB2W,GACFA,EAAchL,UAAU+J,IAAIlJ,yBAKlC0H,gBAAA,WACE,IAAMlU,EAAU8G,KAAKgM,gBAAkBvE,eAAeO,QAAQmD,qBAAsBnL,KAAKmE,UAEzF,GAAKjL,EAAL,CAIA,IAAM4W,EAAkBC,SAAS7W,EAAQE,aAAa,iBAAkB,IAEpE0W,GACF9P,KAAKsM,QAAQ0D,gBAAkBhQ,KAAKsM,QAAQ0D,iBAAmBhQ,KAAKsM,QAAQlD,SAC5EpJ,KAAKsM,QAAQlD,SAAW0G,GAExB9P,KAAKsM,QAAQlD,SAAWpJ,KAAKsM,QAAQ0D,iBAAmBhQ,KAAKsM,QAAQlD,aAIzE2D,OAAA,SAAOa,EAAW1U,GAAS,IASrB+W,EACAC,EACAX,EAXqBY,EAAAnQ,KACnBgP,EAAgBvH,eAAeO,QAAQmD,qBAAsBnL,KAAKmE,UAClEiM,EAAqBpQ,KAAK2N,cAAcqB,GACxCqB,EAAcnX,GAAY8V,GAC9BhP,KAAK+O,oBAAoBnB,EAAWoB,GAEhCsB,EAAmBtQ,KAAK2N,cAAc0C,GACtCE,EAAYjP,QAAQtB,KAAK+L,WAgB/B,GAVI6B,IAAcjE,gBAChBsG,EAAuBnF,gBACvBoF,EAAiBnF,gBACjBwE,EAAqB1F,iBAErBoG,EAAuBpF,iBACvBqF,EAAiBlF,gBACjBuE,EAAqBzF,iBAGnBuG,GAAeA,EAAYxL,UAAUE,SAASW,qBAChD1F,KAAKkM,YAAa,OAKpB,IADmBlM,KAAKqP,mBAAmBgB,EAAad,GACzC5M,kBAIVqM,GAAkBqB,EAAvB,CAcA,GATArQ,KAAKkM,YAAa,EAEdqE,GACFvQ,KAAKuJ,QAGPvJ,KAAK2P,2BAA2BU,GAChCrQ,KAAKgM,eAAiBqE,EAElBrQ,KAAKmE,SAASU,UAAUE,SAAS6F,kBAAmB,CACtDyF,EAAYxL,UAAU+J,IAAIsB,GAE1BpT,OAAOuT,GAEPrB,EAAcnK,UAAU+J,IAAIqB,GAC5BI,EAAYxL,UAAU+J,IAAIqB,GAE1B,IAAMnW,EAAqBJ,iCAAiCsV,GAE5DxP,aAAaoC,IAAIoN,EAAe7W,gBAAgB,WAC9CkY,EAAYxL,UAAUC,OAAOmL,EAAsBC,GACnDG,EAAYxL,UAAU+J,IAAIlJ,qBAE1BsJ,EAAcnK,UAAUC,OAAOY,oBAAmBwK,EAAgBD,GAElEE,EAAKjE,YAAa,EAElBlR,YAAW,WACTwE,aAAa4C,QAAQ+N,EAAKhM,SAAU6F,WAAY,CAC9CsF,cAAee,EACfzC,UAAW2B,EACXG,KAAMU,EACN5C,GAAI8C,MAEL,MAGL7V,qBAAqBuU,EAAelV,QAEpCkV,EAAcnK,UAAUC,OAAOY,qBAC/B2K,EAAYxL,UAAU+J,IAAIlJ,qBAE1B1F,KAAKkM,YAAa,EAClB1M,aAAa4C,QAAQpC,KAAKmE,SAAU6F,WAAY,CAC9CsF,cAAee,EACfzC,UAAW2B,EACXG,KAAMU,EACN5C,GAAI8C,IAIJC,GACFvQ,KAAKkN,YAMFsD,kBAAP,SAAyBtX,EAASiC,GAChC,IAAIyC,EAAOK,KAAKG,QAAQlF,EAASsK,YAC7B8I,EAAO3F,SAAA,GACNwC,QACA9C,YAAYI,kBAAkBvN,IAGb,iBAAXiC,IACTmR,EAAO3F,SAAA,GACF2F,EACAnR,IAIP,IAAMsV,EAA2B,iBAAXtV,EAAsBA,EAASmR,EAAQhD,MAM7D,GAJK1L,IACHA,EAAO,IAAIiO,EAAS3S,EAASoT,IAGT,iBAAXnR,EACTyC,EAAK4P,GAAGrS,QACH,GAAsB,iBAAXsV,EAAqB,CACrC,QAA4B,IAAjB7S,EAAK6S,GACd,MAAM,IAAIC,UAAJ,oBAAkCD,EAAlC,KAGR7S,EAAK6S,UACInE,EAAQlD,UAAYkD,EAAQqE,OACrC/S,EAAK2L,QACL3L,EAAKsP,YAIFhI,gBAAP,SAAuB/J,GACrB,OAAO6E,KAAKmF,MAAK,WACf0G,EAAS2E,kBAAkBxQ,KAAM7E,SAI9ByV,oBAAP,SAA2BvR,GACzB,IAAMU,EAAStG,uBAAuBuG,MAEtC,GAAKD,GAAWA,EAAO8E,UAAUE,SAAS4F,qBAA1C,CAIA,IAAMxP,EAAMwL,SAAA,GACPN,YAAYI,kBAAkB1G,GAC9BsG,YAAYI,kBAAkBzG,OAE7B6Q,EAAa7Q,KAAK5G,aAAa,iBAEjCyX,IACF1V,EAAOiO,UAAW,GAGpByC,EAAS2E,kBAAkBzQ,EAAQ5E,GAE/B0V,GACF5S,KAAKG,QAAQ2B,EAAQyD,YAAUgK,GAAGqD,GAGpCxR,EAAMgE,qBAGDiC,YAAP,SAAmBpM,GACjB,OAAO+E,KAAKG,QAAQlF,EAASsK,gEAhd7B,OAAOD,0CAIP,OAAO4F,cA5BL0C,GAkfNrM,aAAamC,GAAG5I,SAAU+K,uBAAsB0H,oBAAqBK,SAAS+E,qBAE9EpR,aAAamC,GAAG/H,OAAQ8Q,qBAAqB,WAG3C,IAFA,IAAMoG,EAAYrJ,eAAeE,KAAK8D,oBAE7BxL,EAAI,EAAGM,EAAMuQ,EAAU5Q,OAAQD,EAAIM,EAAKN,IAC/C4L,SAAS2E,kBAAkBM,EAAU7Q,GAAIhC,KAAKG,QAAQ0S,EAAU7Q,GAAIuD,gBAWxEpG,oBAAmB,WACjB,IAAMmF,EAAIvF,YAEV,GAAIuF,EAAG,CACL,IAAMgD,EAAqBhD,EAAEpD,GAAGmE,QAChCf,EAAEpD,GAAGmE,QAAQuI,SAAS3G,gBACtB3C,EAAEpD,GAAGmE,QAAMkC,YAAcqG,SACzBtJ,EAAEpD,GAAGmE,QAAMmC,WAAa,WAEtB,OADAlD,EAAEpD,GAAGmE,QAAQiC,EACNsG,SAAS3G,qBCxlBtB,IAAM5B,OAAO,WACPC,UAAU,eACVC,WAAW,cACXC,YAAS,IAAOD,WAChBE,eAAe,YAEfyF,UAAU,CACdtD,QAAQ,EACRkL,OAAQ,IAGJrH,cAAc,CAClB7D,OAAQ,UACRkL,OAAQ,oBAGJC,WAAU,OAAUvN,YACpBwN,YAAW,QAAWxN,YACtByN,WAAU,OAAUzN,YACpB0N,aAAY,SAAY1N,YACxBK,uBAAoB,QAAWL,YAAYC,eAE3C0N,gBAAkB,OAClBC,oBAAsB,WACtBC,sBAAwB,aACxBC,qBAAuB,YAEvBC,MAAQ,QACRC,OAAS,SAETC,iBAAmB,qBACnB/L,uBAAuB,2BAQvBgM,SAAAA,WACJ,SAAAA,EAAYzY,EAASiC,GACnB6E,KAAK4R,kBAAmB,EACxB5R,KAAKmE,SAAWjL,EAChB8G,KAAKsM,QAAUtM,KAAKuM,WAAWpR,GAC/B6E,KAAK6R,cAAgBpK,eAAeE,KAC/BhC,uBAAH,WAAkCzM,EAAQuE,GAA1C,MACGkI,uBADH,kBACyCzM,EAAQuE,GADjD,MAMF,IAFA,IAAMqU,EAAarK,eAAeE,KAAKhC,wBAE9B1F,EAAI,EAAGM,EAAMuR,EAAW5R,OAAQD,EAAIM,EAAKN,IAAK,CACrD,IAAM8R,EAAOD,EAAW7R,GAClB9G,EAAWI,uBAAuBwY,GAClCC,EAAgBvK,eAAeE,KAAKxO,GACvCgP,QAAO,SAAA8J,GAAS,OAAIA,IAAc/Y,KAEpB,OAAbC,GAAqB6Y,EAAc9R,SACrCF,KAAKkS,UAAY/Y,EACjB6G,KAAK6R,cAAcpJ,KAAKsJ,IAI5B/R,KAAKmS,QAAUnS,KAAKsM,QAAQyE,OAAS/Q,KAAKoS,aAAe,KAEpDpS,KAAKsM,QAAQyE,QAChB/Q,KAAKqS,0BAA0BrS,KAAKmE,SAAUnE,KAAK6R,eAGjD7R,KAAKsM,QAAQzG,QACf7F,KAAK6F,SAGP5H,KAAKC,QAAQhF,EAASsK,WAAUxD,iCAelC6F,OAAA,WACM7F,KAAKmE,SAASU,UAAUE,SAASqM,iBACnCpR,KAAKsS,OAELtS,KAAKuS,UAITA,KAAA,WAAO,IAAA3N,EAAA5E,KACL,IAAIA,KAAK4R,mBACP5R,KAAKmE,SAASU,UAAUE,SAASqM,iBADnC,CAKA,IAAIoB,EACAC,EAEAzS,KAAKmS,SAUgB,KATvBK,EAAU/K,eAAeE,KAAK+J,iBAAkB1R,KAAKmS,SAClDhK,QAAO,SAAA4J,GACN,MAAmC,iBAAxBnN,EAAK0H,QAAQyE,OACfgB,EAAK3Y,aAAa,iBAAmBwL,EAAK0H,QAAQyE,OAGpDgB,EAAKlN,UAAUE,SAASsM,yBAGvBnR,SACVsS,EAAU,MAId,IAAME,EAAYjL,eAAeO,QAAQhI,KAAKkS,WAC9C,GAAIM,EAAS,CACX,IAAMG,EAAiBH,EAAQrK,QAAO,SAAA4J,GAAI,OAAIW,IAAcX,KAG5D,IAFAU,EAAcE,EAAe,GAAK1U,KAAKG,QAAQuU,EAAe,GAAInP,YAAY,OAE3DiP,EAAYb,iBAC7B,OAKJ,IADmBpS,aAAa4C,QAAQpC,KAAKmE,SAAU6M,YACxCrO,iBAAf,CAII6P,GACFA,EAAQjX,SAAQ,SAAAqX,GACVF,IAAcE,GAChBjB,EAASkB,kBAAkBD,EAAY,QAGpCH,GACHxU,KAAKC,QAAQ0U,EAAYpP,WAAU,SAKzC,IAAMsP,EAAY9S,KAAK+S,gBAEvB/S,KAAKmE,SAASU,UAAUC,OAAOuM,qBAC/BrR,KAAKmE,SAASU,UAAU+J,IAAI0C,uBAE5BtR,KAAKmE,SAASlI,MAAM6W,GAAa,EAE7B9S,KAAK6R,cAAc3R,QACrBF,KAAK6R,cAActW,SAAQ,SAAArC,GACzBA,EAAQ2L,UAAUC,OAAOyM,sBACzBrY,EAAQ4M,aAAa,iBAAiB,MAI1C9F,KAAKgT,kBAAiB,GAEtB,IAYMC,EAAU,UADaH,EAAU,GAAG/W,cAAgB+W,EAAU5Q,MAAM,IAEpEpI,EAAqBJ,iCAAiCsG,KAAKmE,UAEjE3E,aAAaoC,IAAI5B,KAAKmE,SAAUhM,gBAff,WACfyM,EAAKT,SAASU,UAAUC,OAAOwM,uBAC/B1M,EAAKT,SAASU,UAAU+J,IAAIyC,oBAAqBD,iBAEjDxM,EAAKT,SAASlI,MAAM6W,GAAa,GAEjClO,EAAKoO,kBAAiB,GAEtBxT,aAAa4C,QAAQwC,EAAKT,SAAU8M,gBAStCxW,qBAAqBuF,KAAKmE,SAAUrK,GACpCkG,KAAKmE,SAASlI,MAAM6W,GAAgB9S,KAAKmE,SAAS8O,GAAlD,UAGFX,KAAA,WAAO,IAAAtE,EAAAhO,KACL,IAAIA,KAAK4R,kBACN5R,KAAKmE,SAASU,UAAUE,SAASqM,mBAIjB5R,aAAa4C,QAAQpC,KAAKmE,SAAU+M,YACxCvO,iBAAf,CAIA,IAAMmQ,EAAY9S,KAAK+S,gBAEvB/S,KAAKmE,SAASlI,MAAM6W,GAAgB9S,KAAKmE,SAAS6C,wBAAwB8L,GAA1E,KAEAhW,OAAOkD,KAAKmE,UAEZnE,KAAKmE,SAASU,UAAU+J,IAAI0C,uBAC5BtR,KAAKmE,SAASU,UAAUC,OAAOuM,oBAAqBD,iBAEpD,IAAM8B,EAAqBlT,KAAK6R,cAAc3R,OAC9C,GAAIgT,EAAqB,EACvB,IAAK,IAAIjT,EAAI,EAAGA,EAAIiT,EAAoBjT,IAAK,CAC3C,IAAMmC,EAAUpC,KAAK6R,cAAc5R,GAC7B8R,EAAOtY,uBAAuB2I,GAEhC2P,IAASA,EAAKlN,UAAUE,SAASqM,mBACnChP,EAAQyC,UAAU+J,IAAI2C,sBACtBnP,EAAQ0D,aAAa,iBAAiB,IAK5C9F,KAAKgT,kBAAiB,GAStBhT,KAAKmE,SAASlI,MAAM6W,GAAa,GACjC,IAAMhZ,EAAqBJ,iCAAiCsG,KAAKmE,UAEjE3E,aAAaoC,IAAI5B,KAAKmE,SAAUhM,gBAVf,WACf6V,EAAKgF,kBAAiB,GACtBhF,EAAK7J,SAASU,UAAUC,OAAOwM,uBAC/BtD,EAAK7J,SAASU,UAAU+J,IAAIyC,qBAC5B7R,aAAa4C,QAAQ4L,EAAK7J,SAAUgN,iBAOtC1W,qBAAqBuF,KAAKmE,SAAUrK,OAGtCkZ,iBAAA,SAAiBG,GACfnT,KAAK4R,iBAAmBuB,KAG1BzO,QAAA,WACEzG,KAAKI,WAAW2B,KAAKmE,SAAUX,YAE/BxD,KAAKsM,QAAU,KACftM,KAAKmS,QAAU,KACfnS,KAAKmE,SAAW,KAChBnE,KAAK6R,cAAgB,KACrB7R,KAAK4R,iBAAmB,QAK1BrF,WAAA,SAAWpR,GAOT,OANAA,EAAMwL,SAAA,GACDwC,UACAhO,IAEE0K,OAASvE,QAAQnG,EAAO0K,QAC/B5K,gBAAgBqI,OAAMnI,EAAQuO,eACvBvO,KAGT4X,cAAA,WACE,OAAO/S,KAAKmE,SAASU,UAAUE,SAASyM,OAASA,MAAQC,UAG3DW,WAAA,WAAa,IAAAjE,EAAAnO,KACL+Q,EAAW/Q,KAAKsM,QAAhByE,OAEFxW,UAAUwW,QAEiB,IAAlBA,EAAOqC,aAA+C,IAAdrC,EAAO,KACxDA,EAASA,EAAO,IAGlBA,EAAStJ,eAAeO,QAAQ+I,GAGlC,IAAM5X,EAAcwM,uBAAN,iBAA2CoL,EAA3C,KAYd,OAVAtJ,eAAeE,KAAKxO,EAAU4X,GAC3BxV,SAAQ,SAAArC,GACP,IAAMma,EAAW5Z,uBAAuBP,GAExCiV,EAAKkE,0BACHgB,EACA,CAACna,OAIA6X,KAGTsB,0BAAA,SAA0BnZ,EAASoa,GACjC,GAAKpa,GAAYoa,EAAapT,OAA9B,CAIA,IAAMqT,EAASra,EAAQ2L,UAAUE,SAASqM,iBAE1CkC,EAAa/X,SAAQ,SAAAwW,GACfwB,EACFxB,EAAKlN,UAAUC,OAAOyM,sBAEtBQ,EAAKlN,UAAU+J,IAAI2C,sBAGrBQ,EAAKjM,aAAa,gBAAiByN,UAMhCV,kBAAP,SAAyB3Z,EAASiC,GAChC,IAAIyC,EAAOK,KAAKG,QAAQlF,EAASsK,YAC3B8I,EAAO3F,SAAA,GACRwC,UACA9C,YAAYI,kBAAkBvN,GACX,iBAAXiC,GAAuBA,EAASA,EAAS,IAWtD,IARKyC,GAAQ0O,EAAQzG,QAA4B,iBAAX1K,GAAuB,YAAYU,KAAKV,KAC5EmR,EAAQzG,QAAS,GAGdjI,IACHA,EAAO,IAAI+T,EAASzY,EAASoT,IAGT,iBAAXnR,EAAqB,CAC9B,QAA4B,IAAjByC,EAAKzC,GACd,MAAM,IAAIuV,UAAJ,oBAAkCvV,EAAlC,KAGRyC,EAAKzC,SAIF+J,gBAAP,SAAuB/J,GACrB,OAAO6E,KAAKmF,MAAK,WACfwM,EAASkB,kBAAkB7S,KAAM7E,SAI9BmK,YAAP,SAAmBpM,GACjB,OAAO+E,KAAKG,QAAQlF,EAASsK,gEAvQ7B,OAAOD,0CAIP,OAAO4F,gBA5CLwI,GAyTNnS,aAAamC,GAAG5I,SAAU+K,uBAAsB6B,wBAAsB,SAAUtG,GAEjD,MAAzBA,EAAMU,OAAO+O,SACfzP,EAAMgE,iBAGR,IAAMmQ,EAAcnN,YAAYI,kBAAkBzG,MAC5C7G,EAAWI,uBAAuByG,MACfyH,eAAeE,KAAKxO,GAE5BoC,SAAQ,SAAArC,GACvB,IACIiC,EADEyC,EAAOK,KAAKG,QAAQlF,EAASsK,YAE/B5F,GAEmB,OAAjBA,EAAKuU,SAAkD,iBAAvBqB,EAAYzC,SAC9CnT,EAAK0O,QAAQyE,OAASyC,EAAYzC,OAClCnT,EAAKuU,QAAUvU,EAAKwU,cAGtBjX,EAAS,UAETA,EAASqY,EAGX7B,SAASkB,kBAAkB3Z,EAASiC,SAWxCiC,oBAAmB,WACjB,IAAMmF,EAAIvF,YAEV,GAAIuF,EAAG,CACL,IAAMgD,EAAqBhD,EAAEpD,GAAGmE,QAChCf,EAAEpD,GAAGmE,QAAQqO,SAASzM,gBACtB3C,EAAEpD,GAAGmE,QAAMkC,YAAcmM,SACzBpP,EAAEpD,GAAGmE,QAAMmC,WAAa,WAEtB,OADAlD,EAAEpD,GAAGmE,QAAQiC,EACNoM,SAASzM,qBC/YtB,IAAM5B,OAAO,WACPC,UAAU,eACVC,WAAW,cACXC,YAAS,IAAOD,WAChBE,eAAe,YAEf+P,WAAa,SACbC,UAAY,QACZC,QAAU,MACVC,aAAe,UACfC,eAAiB,YACjBC,mBAAqB,EAErBC,eAAiB,IAAInY,OAAUgY,aAAd,IAA8BC,eAA9B,IAAgDJ,YAEjEvC,aAAU,OAAUzN,YACpB0N,eAAY,SAAY1N,YACxBuN,aAAU,OAAUvN,YACpBwN,cAAW,QAAWxN,YACtBuQ,YAAW,QAAWvQ,YACtBK,uBAAoB,QAAWL,YAAYC,eAC3CuQ,uBAAsB,UAAaxQ,YAAYC,eAC/CwQ,qBAAoB,QAAWzQ,YAAYC,eAE3CyQ,oBAAsB,WACtB/C,kBAAkB,OAClBgD,kBAAoB,SACpBC,qBAAuB,YACvBC,oBAAsB,WACtBC,qBAAuB,sBACvBC,kBAAoB,SACpBC,2BAA6B,kBAE7B9O,uBAAuB,2BACvB+O,oBAAsB,iBACtBC,cAAgB,iBAChBC,oBAAsB,cACtBC,uBAAyB,8DAEzBC,cAAgB,YAChBC,iBAAmB,UACnBC,iBAAmB,eACnBC,oBAAsB,aACtBC,gBAAkB,cAClBC,eAAiB,aAEjBhM,UAAU,CACdrC,OAAQ,EACRsO,MAAM,EACNC,SAAU,eACVC,UAAW,SACXjZ,QAAS,UACTkZ,aAAc,MAGV7L,cAAc,CAClB5C,OAAQ,2BACRsO,KAAM,UACNC,SAAU,mBACVC,UAAW,mBACXjZ,QAAS,SACTkZ,aAAc,iBASVC,SAAAA,WACJ,SAAAA,EAAYtc,EAASiC,GACnB6E,KAAKmE,SAAWjL,EAChB8G,KAAKyV,QAAU,KACfzV,KAAKsM,QAAUtM,KAAKuM,WAAWpR,GAC/B6E,KAAK0V,MAAQ1V,KAAK2V,kBAClB3V,KAAK4V,UAAY5V,KAAK6V,gBAEtB7V,KAAK8M,qBACL7O,KAAKC,QAAQhF,EAASsK,WAAUxD,iCAmBlC6F,OAAA,WACE,IAAI7F,KAAKmE,SAAS2R,WAAY9V,KAAKmE,SAASU,UAAUE,SAASoP,qBAA/D,CAIA,IAAM4B,EAAW/V,KAAKmE,SAASU,UAAUE,SAASqM,mBAElDoE,EAASQ,aAELD,GAIJ/V,KAAKuS,WAGPA,KAAA,WACE,KAAIvS,KAAKmE,SAAS2R,UAAY9V,KAAKmE,SAASU,UAAUE,SAASoP,sBAAwBnU,KAAK0V,MAAM7Q,UAAUE,SAASqM,oBAArH,CAIA,IAAML,EAASyE,EAASS,qBAAqBjW,KAAKmE,UAC5CmL,EAAgB,CACpBA,cAAetP,KAAKmE,UAKtB,IAFkB3E,aAAa4C,QAAQpC,KAAKmE,SAAU6M,aAAY1B,GAEpD3M,iBAAd,CAKA,IAAK3C,KAAK4V,UAAW,CACnB,QAAsB,IAAXM,OACT,MAAM,IAAIxF,UAAU,mEAGtB,IAAIyF,EAAmBnW,KAAKmE,SAEG,WAA3BnE,KAAKsM,QAAQgJ,UACfa,EAAmBpF,EACVxW,UAAUyF,KAAKsM,QAAQgJ,aAChCa,EAAmBnW,KAAKsM,QAAQgJ,eAGa,IAAlCtV,KAAKsM,QAAQgJ,UAAUlC,SAChC+C,EAAmBnW,KAAKsM,QAAQgJ,UAAU,KAOhB,iBAA1BtV,KAAKsM,QAAQ+I,UACftE,EAAOlM,UAAU+J,IAAI6F,4BAGvBzU,KAAKyV,QAAU,IAAIS,OAAOC,EAAkBnW,KAAK0V,MAAO1V,KAAKoW,oBAQvB,IAAAxO,EADxC,GAAI,iBAAkB7O,SAASyD,kBAC5BuU,EAAOpM,QAAQiQ,sBAChBhN,EAAA,IAAGC,OAAHlI,MAAAiI,EAAa7O,SAASmE,KAAK+K,UACxB1M,SAAQ,SAAAwW,GAAI,OAAIvS,aAAamC,GAAGoQ,EAAM,YAAa,KAAMlV,WAG9DmD,KAAKmE,SAASkS,QACdrW,KAAKmE,SAAS2B,aAAa,iBAAiB,GAE5C9F,KAAK0V,MAAM7Q,UAAUgB,OAAOuL,mBAC5BpR,KAAKmE,SAASU,UAAUgB,OAAOuL,mBAC/B5R,aAAa4C,QAAQ2O,EAAQE,cAAa3B,QAG5CgD,KAAA,WACE,IAAItS,KAAKmE,SAAS2R,WAAY9V,KAAKmE,SAASU,UAAUE,SAASoP,sBAAyBnU,KAAK0V,MAAM7Q,UAAUE,SAASqM,mBAAtH,CAIA,IAAML,EAASyE,EAASS,qBAAqBjW,KAAKmE,UAC5CmL,EAAgB,CACpBA,cAAetP,KAAKmE,UAGJ3E,aAAa4C,QAAQ2O,EAAQG,aAAY5B,GAE7C3M,mBAIV3C,KAAKyV,SACPzV,KAAKyV,QAAQa,UAGftW,KAAK0V,MAAM7Q,UAAUgB,OAAOuL,mBAC5BpR,KAAKmE,SAASU,UAAUgB,OAAOuL,mBAC/B5R,aAAa4C,QAAQ2O,EAAQI,eAAc7B,QAG7C5K,QAAA,WACEzG,KAAKI,WAAW2B,KAAKmE,SAAUX,YAC/BhE,aAAaC,IAAIO,KAAKmE,SAAUV,aAChCzD,KAAKmE,SAAW,KAChBnE,KAAK0V,MAAQ,KACT1V,KAAKyV,UACPzV,KAAKyV,QAAQa,UACbtW,KAAKyV,QAAU,SAInBc,OAAA,WACEvW,KAAK4V,UAAY5V,KAAK6V,gBAClB7V,KAAKyV,SACPzV,KAAKyV,QAAQe,oBAMjB1J,mBAAA,WAAqB,IAAAlI,EAAA5E,KACnBR,aAAamC,GAAG3B,KAAKmE,SAAU6P,aAAa,SAAA3U,GAC1CA,EAAMgE,iBACNhE,EAAMoX,kBACN7R,EAAKiB,eAIT0G,WAAA,SAAWpR,GAST,OARAA,EAAMwL,SAAA,GACD3G,KAAK0W,YAAYvN,QACjB9C,YAAYI,kBAAkBzG,KAAKmE,UACnChJ,GAGLF,gBAAgBqI,OAAMnI,EAAQ6E,KAAK0W,YAAYhN,aAExCvO,KAGTwa,gBAAA,WACE,OAAOlO,eAAeoB,KAAK7I,KAAKmE,SAAUwQ,eAAe,MAG3DgC,cAAA,WACE,IAAMC,EAAiB5W,KAAKmE,SAASjI,WACjC2a,EAAY7B,iBAehB,OAZI4B,EAAe/R,UAAUE,SAASqP,mBACpCyC,EAAY7W,KAAK0V,MAAM7Q,UAAUE,SAASwP,sBACxCQ,iBACAD,cACO8B,EAAe/R,UAAUE,SAASsP,sBAC3CwC,EAAY3B,gBACH0B,EAAe/R,UAAUE,SAASuP,qBAC3CuC,EAAY1B,eACHnV,KAAK0V,MAAM7Q,UAAUE,SAASwP,wBACvCsC,EAAY5B,qBAGP4B,KAGThB,cAAA,WACE,OAAOvU,QAAQtB,KAAKmE,SAASQ,QAAd,IAA0B6P,uBAG3CsC,WAAA,WAAa,IAAA9I,EAAAhO,KACL8G,EAAS,GAef,MAbmC,mBAAxB9G,KAAKsM,QAAQxF,OACtBA,EAAO3H,GAAK,SAAAvB,GAMV,OALAA,EAAKmZ,QAALpQ,SAAA,GACK/I,EAAKmZ,QACJ/I,EAAK1B,QAAQxF,OAAOlJ,EAAKmZ,QAAS/I,EAAK7J,WAAa,IAGnDvG,GAGTkJ,EAAOA,OAAS9G,KAAKsM,QAAQxF,OAGxBA,KAGTsP,iBAAA,WACE,IAAMb,EAAe,CACnBsB,UAAW7W,KAAK2W,gBAChBK,UAAW,CACTlQ,OAAQ9G,KAAK8W,aACb1B,KAAM,CACJ6B,QAASjX,KAAKsM,QAAQ8I,MAExB8B,gBAAiB,CACfC,kBAAmBnX,KAAKsM,QAAQ+I,YAYtC,MAN6B,WAAzBrV,KAAKsM,QAAQjQ,UACfkZ,EAAayB,UAAUI,WAAa,CAClCH,SAAS,IAIbtQ,SAAA,GACK4O,EACAvV,KAAKsM,QAAQiJ,iBAMb8B,kBAAP,SAAyBne,EAASiC,GAChC,IAAIyC,EAAOK,KAAKG,QAAQlF,EAASsK,YAOjC,GAJK5F,IACHA,EAAO,IAAI4X,EAAStc,EAHY,iBAAXiC,EAAsBA,EAAS,OAMhC,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjByC,EAAKzC,GACd,MAAM,IAAIuV,UAAJ,oBAAkCvV,EAAlC,KAGRyC,EAAKzC,SAIF+J,gBAAP,SAAuB/J,GACrB,OAAO6E,KAAKmF,MAAK,WACfqQ,EAAS6B,kBAAkBrX,KAAM7E,SAI9B6a,WAAP,SAAkB3W,GAChB,IAAIA,GAAUA,EAAM+G,SAAW0N,qBACb,UAAfzU,EAAMK,MAAoBL,EAAM1B,MAAQgW,SAM3C,IAFA,IAAM2D,EAAU7P,eAAeE,KAAKhC,wBAE3B1F,EAAI,EAAGM,EAAM+W,EAAQpX,OAAQD,EAAIM,EAAKN,IAAK,CAClD,IAAM8Q,EAASyE,EAASS,qBAAqBqB,EAAQrX,IAC/CsX,EAAUtZ,KAAKG,QAAQkZ,EAAQrX,GAAIuD,YACnC8L,EAAgB,CACpBA,cAAegI,EAAQrX,IAOzB,GAJIZ,GAAwB,UAAfA,EAAMK,OACjB4P,EAAckI,WAAanY,GAGxBkY,EAAL,CAIA,IAAME,EAAeF,EAAQ7B,MAC7B,GAAK4B,EAAQrX,GAAG4E,UAAUE,SAASqM,mBAInC,KAAI/R,IAA0B,UAAfA,EAAMK,MACjB,kBAAkB7D,KAAKwD,EAAMU,OAAO+O,UACpB,UAAfzP,EAAMK,MAAoBL,EAAM1B,MAAQgW,UACzC8D,EAAa1S,SAAS1F,EAAMU,SAKhC,IADkBP,aAAa4C,QAAQ2O,EAAQG,aAAY5B,GAC7C3M,iBAAd,CAMgD,IAAAuF,EAAhD,GAAI,iBAAkBnP,SAASyD,iBAC7B0L,EAAA,IAAGL,OAAHlI,MAAAuI,EAAanP,SAASmE,KAAK+K,UACxB1M,SAAQ,SAAAwW,GAAI,OAAIvS,aAAaC,IAAIsS,EAAM,YAAa,KAAMlV,WAG/Dya,EAAQrX,GAAG6F,aAAa,gBAAiB,SAErCyR,EAAQ9B,SACV8B,EAAQ9B,QAAQa,UAGlBmB,EAAa5S,UAAUC,OAAOsM,mBAC9BkG,EAAQrX,GAAG4E,UAAUC,OAAOsM,mBAC5B5R,aAAa4C,QAAQ2O,EAAQI,eAAc7B,SAIxC2G,qBAAP,SAA4B/c,GAC1B,OAAOO,uBAAuBP,IAAYA,EAAQgD,cAG7Cwb,sBAAP,SAA6BrY,GAQ3B,KAAI,kBAAkBxD,KAAKwD,EAAMU,OAAO+O,SACtCzP,EAAM1B,MAAQ+V,WAAcrU,EAAM1B,MAAQ8V,aACxCpU,EAAM1B,MAAQkW,gBAAkBxU,EAAM1B,MAAQiW,cAC9CvU,EAAMU,OAAO4E,QAAQgQ,iBACtBZ,eAAelY,KAAKwD,EAAM1B,QAI7B0B,EAAMgE,iBACNhE,EAAMoX,mBAEFzW,KAAK8V,WAAY9V,KAAK6E,UAAUE,SAASoP,sBAA7C,CAIA,IAAMpD,EAASyE,EAASS,qBAAqBjW,MACvC+V,EAAW/V,KAAK6E,UAAUE,SAASqM,mBAEzC,GAAI/R,EAAM1B,MAAQ8V,WAIhB,OAHezT,KAAK0H,QAAQ/B,wBAAwB3F,KAAOyH,eAAeiB,KAAK1I,KAAM2F,wBAAsB,IACpG0Q,aACPb,EAASQ,aAIX,GAAKD,GAAY1W,EAAM1B,MAAQ+V,UAA/B,CAKA,IAAMiE,EAAQlQ,eAAeE,KAAKkN,uBAAwB9D,GAAQ5I,OAAOnM,WAEzE,GAAK2b,EAAMzX,OAAX,CAIA,IAAIuN,EAAQkK,EAAM3W,QAAQ3B,EAAMU,QAE5BV,EAAM1B,MAAQiW,cAAgBnG,EAAQ,GACxCA,IAGEpO,EAAM1B,MAAQkW,gBAAkBpG,EAAQkK,EAAMzX,OAAS,GACzDuN,IAMFkK,EAFAlK,GAAmB,IAAXA,EAAe,EAAIA,GAEd4I,cAvBXb,EAASQ,iBA0BN1Q,YAAP,SAAmBpM,GACjB,OAAO+E,KAAKG,QAAQlF,EAASsK,gEA5X7B,OAAOD,0CAIP,OAAO4F,8CAIP,OAAOO,oBAvBL8L,GAqZNhW,aAAamC,GAAG5I,SAAUkb,uBAAwBtO,uBAAsB6P,SAASkC,uBACjFlY,aAAamC,GAAG5I,SAAUkb,uBAAwBU,cAAea,SAASkC,uBAC1ElY,aAAamC,GAAG5I,SAAU+K,uBAAsB0R,SAASQ,YACzDxW,aAAamC,GAAG5I,SAAUmb,qBAAsBsB,SAASQ,YACzDxW,aAAamC,GAAG5I,SAAU+K,uBAAsB6B,wBAAsB,SAAUtG,GAC9EA,EAAMgE,iBACNhE,EAAMoX,kBACNjB,SAAS6B,kBAAkBrX,KAAM,aAEnCR,aAAamC,GAAG5I,SAAU+K,uBAAsB4Q,qBAAqB,SAAA/F,GAAC,OAAIA,EAAE8H,qBAS5ErZ,oBAAmB,WACjB,IAAMmF,EAAIvF,YAEV,GAAIuF,EAAG,CACL,IAAMgD,EAAqBhD,EAAEpD,GAAGmE,QAChCf,EAAEpD,GAAGmE,QAAQkS,SAAStQ,gBACtB3C,EAAEpD,GAAGmE,QAAMkC,YAAcgQ,SACzBjT,EAAEpD,GAAGmE,QAAMmC,WAAa,WAEtB,OADAlD,EAAEpD,GAAGmE,QAAQiC,EACNiQ,SAAStQ,qBCrftB,IAAM5B,OAAO,QACPC,UAAU,eACVC,WAAW,WACXC,YAAS,IAAOD,WAChBE,eAAe,YACf+P,aAAa,SAEbtK,UAAU,CACdyO,UAAU,EACVvO,UAAU,EACVgN,OAAO,EACP9D,MAAM,GAGF7I,cAAc,CAClBkO,SAAU,mBACVvO,SAAU,UACVgN,MAAO,UACP9D,KAAM,WAGFrB,aAAU,OAAUzN,YACpBoU,qBAAoB,gBAAmBpU,YACvC0N,eAAY,SAAY1N,YACxBuN,aAAU,OAAUvN,YACpBwN,cAAW,QAAWxN,YACtBqU,cAAa,UAAarU,YAC1BsU,aAAY,SAAYtU,YACxBuU,oBAAmB,gBAAmBvU,YACtCwU,sBAAqB,kBAAqBxU,YAC1CyU,sBAAqB,kBAAqBzU,YAC1C0U,wBAAuB,oBAAuB1U,YAC9CK,uBAAoB,QAAWL,YAAYC,eAE3C0U,8BAAgC,0BAChCC,oBAAsB,iBACtBC,gBAAkB,aAClBC,gBAAkB,OAClBnH,kBAAkB,OAClBoH,kBAAoB,eAEpBC,gBAAkB,gBAClBC,oBAAsB,cACtB/S,uBAAuB,wBACvBgT,sBAAwB,yBACxBC,uBAAyB,oDACzBC,wBAA0B,cAQ1BC,MAAAA,WACJ,SAAAA,EAAY5f,EAASiC,GACnB6E,KAAKsM,QAAUtM,KAAKuM,WAAWpR,GAC/B6E,KAAKmE,SAAWjL,EAChB8G,KAAK+Y,QAAUtR,eAAeO,QAAQyQ,gBAAiBvf,GACvD8G,KAAKgZ,UAAY,KACjBhZ,KAAKiZ,UAAW,EAChBjZ,KAAKkZ,oBAAqB,EAC1BlZ,KAAKmZ,sBAAuB,EAC5BnZ,KAAK4R,kBAAmB,EACxB5R,KAAKoZ,gBAAkB,EACvBnb,KAAKC,QAAQhF,EAASsK,WAAUxD,iCAelC6F,OAAA,SAAOyJ,GACL,OAAOtP,KAAKiZ,SAAWjZ,KAAKsS,OAAStS,KAAKuS,KAAKjD,MAGjDiD,KAAA,SAAKjD,GAAe,IAAA1K,EAAA5E,KAClB,IAAIA,KAAKiZ,WAAYjZ,KAAK4R,iBAA1B,CAII5R,KAAKmE,SAASU,UAAUE,SAASwT,mBACnCvY,KAAK4R,kBAAmB,GAG1B,IAAMyH,EAAY7Z,aAAa4C,QAAQpC,KAAKmE,SAAU6M,aAAY,CAChE1B,cAAAA,IAGEtP,KAAKiZ,UAAYI,EAAU1W,mBAI/B3C,KAAKiZ,UAAW,EAEhBjZ,KAAKsZ,kBACLtZ,KAAKuZ,gBAELvZ,KAAKwZ,gBAELxZ,KAAKyZ,kBACLzZ,KAAK0Z,kBAELla,aAAamC,GAAG3B,KAAKmE,SACnB6T,oBACAW,uBACA,SAAAtZ,GAAK,OAAIuF,EAAK0N,KAAKjT,MAGrBG,aAAamC,GAAG3B,KAAK+Y,QAASZ,yBAAyB,WACrD3Y,aAAaoC,IAAIgD,EAAKT,SAAU+T,uBAAuB,SAAA7Y,GACjDA,EAAMU,SAAW6E,EAAKT,WACxBS,EAAKuU,sBAAuB,SAKlCnZ,KAAK2Z,eAAc,WAAA,OAAM/U,EAAKgV,aAAatK,WAG7CgD,KAAA,SAAKjT,GAAO,IAAA2O,EAAAhO,KAKV,IAJIX,GACFA,EAAMgE,iBAGHrD,KAAKiZ,WAAYjZ,KAAK4R,oBAITpS,aAAa4C,QAAQpC,KAAKmE,SAAU+M,cAExCvO,iBAAd,CAIA3C,KAAKiZ,UAAW,EAChB,IAAMY,EAAa7Z,KAAKmE,SAASU,UAAUE,SAASwT,iBAgBpD,GAdIsB,IACF7Z,KAAK4R,kBAAmB,GAG1B5R,KAAKyZ,kBACLzZ,KAAK0Z,kBAELla,aAAaC,IAAI1G,SAAU+e,eAE3B9X,KAAKmE,SAASU,UAAUC,OAAOsM,mBAE/B5R,aAAaC,IAAIO,KAAKmE,SAAU6T,qBAChCxY,aAAaC,IAAIO,KAAK+Y,QAASZ,yBAE3B0B,EAAY,CACd,IAAM/f,EAAqBJ,iCAAiCsG,KAAKmE,UAEjE3E,aAAaoC,IAAI5B,KAAKmE,SAAUhM,gBAAgB,SAAAkH,GAAK,OAAI2O,EAAK8L,WAAWza,MACzE5E,qBAAqBuF,KAAKmE,SAAUrK,QAEpCkG,KAAK8Z,iBAITpV,QAAA,WACE,CAAC9K,OAAQoG,KAAKmE,SAAUnE,KAAK+Y,SAC1Bxd,SAAQ,SAAAwe,GAAW,OAAIva,aAAaC,IAAIsa,EAAatW,gBAOxDjE,aAAaC,IAAI1G,SAAU+e,eAE3B7Z,KAAKI,WAAW2B,KAAKmE,SAAUX,YAE/BxD,KAAKsM,QAAU,KACftM,KAAKmE,SAAW,KAChBnE,KAAK+Y,QAAU,KACf/Y,KAAKgZ,UAAY,KACjBhZ,KAAKiZ,SAAW,KAChBjZ,KAAKkZ,mBAAqB,KAC1BlZ,KAAKmZ,qBAAuB,KAC5BnZ,KAAK4R,iBAAmB,KACxB5R,KAAKoZ,gBAAkB,QAGzBY,aAAA,WACEha,KAAKwZ,mBAKPjN,WAAA,SAAWpR,GAMT,OALAA,EAAMwL,SAAA,GACDwC,UACAhO,GAELF,gBAAgBqI,OAAMnI,EAAQuO,eACvBvO,KAGTye,aAAA,SAAatK,GAAe,IAAAnB,EAAAnO,KACpB6Z,EAAa7Z,KAAKmE,SAASU,UAAUE,SAASwT,iBAC9C0B,EAAYxS,eAAeO,QAAQ0Q,oBAAqB1Y,KAAK+Y,SAE9D/Y,KAAKmE,SAASjI,YACf8D,KAAKmE,SAASjI,WAAW1B,WAAa+N,KAAKC,cAE7CzP,SAASmE,KAAKgd,YAAYla,KAAKmE,UAGjCnE,KAAKmE,SAASlI,MAAMI,QAAU,QAC9B2D,KAAKmE,SAASqC,gBAAgB,eAC9BxG,KAAKmE,SAAS2B,aAAa,cAAc,GACzC9F,KAAKmE,SAAS2B,aAAa,OAAQ,UACnC9F,KAAKmE,SAAS+C,UAAY,EAEtB+S,IACFA,EAAU/S,UAAY,GAGpB2S,GACF/c,OAAOkD,KAAKmE,UAGdnE,KAAKmE,SAASU,UAAU+J,IAAIwC,mBAExBpR,KAAKsM,QAAQ+J,OACfrW,KAAKma,gBAGP,IAAMC,EAAqB,WACrBjM,EAAK7B,QAAQ+J,OACflI,EAAKhK,SAASkS,QAGhBlI,EAAKyD,kBAAmB,EACxBpS,aAAa4C,QAAQ+L,EAAKhK,SAAU8M,cAAa,CAC/C3B,cAAAA,KAIJ,GAAIuK,EAAY,CACd,IAAM/f,EAAqBJ,iCAAiCsG,KAAK+Y,SAEjEvZ,aAAaoC,IAAI5B,KAAK+Y,QAAS5gB,eAAgBiiB,GAC/C3f,qBAAqBuF,KAAK+Y,QAASjf,QAEnCsgB,OAIJD,cAAA,WAAgB,IAAAhK,EAAAnQ,KACdR,aAAaC,IAAI1G,SAAU+e,eAC3BtY,aAAamC,GAAG5I,SAAU+e,eAAe,SAAAzY,GACnCtG,WAAasG,EAAMU,QACnBoQ,EAAKhM,WAAa9E,EAAMU,QACvBoQ,EAAKhM,SAASY,SAAS1F,EAAMU,SAChCoQ,EAAKhM,SAASkS,cAKpBoD,gBAAA,WAAkB,IAAAY,EAAAra,KACZA,KAAKiZ,SACPzZ,aAAamC,GAAG3B,KAAKmE,SAAU8T,uBAAuB,SAAA5Y,GAChDgb,EAAK/N,QAAQjD,UAAYhK,EAAM1B,MAAQ8V,cACzCpU,EAAMgE,iBACNgX,EAAK/H,QACK+H,EAAK/N,QAAQjD,UAAYhK,EAAM1B,MAAQ8V,cACjD4G,EAAKC,gCAIT9a,aAAaC,IAAIO,KAAKmE,SAAU8T,0BAIpCyB,gBAAA,WAAkB,IAAAa,EAAAva,KACZA,KAAKiZ,SACPzZ,aAAamC,GAAG/H,OAAQme,cAAc,WAAA,OAAMwC,EAAKf,mBAEjDha,aAAaC,IAAI7F,OAAQme,iBAI7B+B,WAAA,WAAa,IAAAU,EAAAxa,KACXA,KAAKmE,SAASlI,MAAMI,QAAU,OAC9B2D,KAAKmE,SAAS2B,aAAa,eAAe,GAC1C9F,KAAKmE,SAASqC,gBAAgB,cAC9BxG,KAAKmE,SAASqC,gBAAgB,QAC9BxG,KAAK4R,kBAAmB,EACxB5R,KAAK2Z,eAAc,WACjB5gB,SAASmE,KAAK2H,UAAUC,OAAOwT,iBAC/BkC,EAAKC,oBACLD,EAAKE,kBACLlb,aAAa4C,QAAQoY,EAAKrW,SAAUgN,sBAIxCwJ,gBAAA,WACE3a,KAAKgZ,UAAU9c,WAAW+I,YAAYjF,KAAKgZ,WAC3ChZ,KAAKgZ,UAAY,QAGnBW,cAAA,SAActc,GAAU,IAAAud,EAAA5a,KAChB6a,EAAU7a,KAAKmE,SAASU,UAAUE,SAASwT,iBAC/CA,gBACA,GAEF,GAAIvY,KAAKiZ,UAAYjZ,KAAKsM,QAAQsL,SAAU,CA6B1C,GA5BA5X,KAAKgZ,UAAYjgB,SAAS+hB,cAAc,OACxC9a,KAAKgZ,UAAU+B,UAAY1C,oBAEvBwC,GACF7a,KAAKgZ,UAAUnU,UAAU+J,IAAIiM,GAG/B9hB,SAASmE,KAAKgd,YAAYla,KAAKgZ,WAE/BxZ,aAAamC,GAAG3B,KAAKmE,SAAU6T,qBAAqB,SAAA3Y,GAC9Cub,EAAKzB,qBACPyB,EAAKzB,sBAAuB,EAI1B9Z,EAAMU,SAAWV,EAAM2b,eAI3BJ,EAAKN,gCAGHO,GACF/d,OAAOkD,KAAKgZ,WAGdhZ,KAAKgZ,UAAUnU,UAAU+J,IAAIwC,oBAExByJ,EAEH,YADAxd,IAIF,IAAM4d,EAA6BvhB,iCAAiCsG,KAAKgZ,WAEzExZ,aAAaoC,IAAI5B,KAAKgZ,UAAW7gB,eAAgBkF,GACjD5C,qBAAqBuF,KAAKgZ,UAAWiC,QAChC,IAAKjb,KAAKiZ,UAAYjZ,KAAKgZ,UAAW,CAC3ChZ,KAAKgZ,UAAUnU,UAAUC,OAAOsM,mBAEhC,IAAM8J,EAAiB,WACrBN,EAAKD,kBACLtd,KAGF,GAAI2C,KAAKmE,SAASU,UAAUE,SAASwT,iBAAkB,CACrD,IAAM0C,EAA6BvhB,iCAAiCsG,KAAKgZ,WACzExZ,aAAaoC,IAAI5B,KAAKgZ,UAAW7gB,eAAgB+iB,GACjDzgB,qBAAqBuF,KAAKgZ,UAAWiC,QAErCC,SAGF7d,OAIJid,2BAAA,WAA6B,IAAAa,EAAAnb,KAC3B,GAA8B,WAA1BA,KAAKsM,QAAQsL,SAAuB,CAEtC,GADkBpY,aAAa4C,QAAQpC,KAAKmE,SAAU0T,sBACxClV,iBACZ,OAGF,IAAMyY,EAAqBpb,KAAKmE,SAASkX,aAAetiB,SAASyD,gBAAgB8e,aAE5EF,IACHpb,KAAKmE,SAASlI,MAAMsf,UAAY,UAGlCvb,KAAKmE,SAASU,UAAU+J,IAAI4J,mBAC5B,IAAMgD,EAA0B9hB,iCAAiCsG,KAAK+Y,SACtEvZ,aAAaC,IAAIO,KAAKmE,SAAUhM,gBAChCqH,aAAaoC,IAAI5B,KAAKmE,SAAUhM,gBAAgB,WAC9CgjB,EAAKhX,SAASU,UAAUC,OAAO0T,mBAC1B4C,IACH5b,aAAaoC,IAAIuZ,EAAKhX,SAAUhM,gBAAgB,WAC9CgjB,EAAKhX,SAASlI,MAAMsf,UAAY,MAElC9gB,qBAAqB0gB,EAAKhX,SAAUqX,OAGxC/gB,qBAAqBuF,KAAKmE,SAAUqX,GACpCxb,KAAKmE,SAASkS,aAEdrW,KAAKsS,UAQTkH,cAAA,WACE,IAAM4B,EACJpb,KAAKmE,SAASkX,aAAetiB,SAASyD,gBAAgB8e,cAEnDtb,KAAKkZ,oBAAsBkC,IAC9Bpb,KAAKmE,SAASlI,MAAMwf,YAAiBzb,KAAKoZ,gBAA1C,MAGEpZ,KAAKkZ,qBAAuBkC,IAC9Bpb,KAAKmE,SAASlI,MAAMyf,aAAkB1b,KAAKoZ,gBAA3C,SAIJqB,kBAAA,WACEza,KAAKmE,SAASlI,MAAMwf,YAAc,GAClCzb,KAAKmE,SAASlI,MAAMyf,aAAe,MAGrCpC,gBAAA,WACE,IAAMvS,EAAOhO,SAASmE,KAAK8J,wBAC3BhH,KAAKkZ,mBAAqBtgB,KAAK+iB,MAAM5U,EAAKI,KAAOJ,EAAK6U,OAAShiB,OAAOiiB,WACtE7b,KAAKoZ,gBAAkBpZ,KAAK8b,wBAG9BvC,cAAA,WAAgB,IAAAwC,EAAA/b,KACd,GAAIA,KAAKkZ,mBAAoB,CAK3BzR,eAAeE,KAAKiR,wBACjBrd,SAAQ,SAAArC,GACP,IAAM8iB,EAAgB9iB,EAAQ+C,MAAMyf,aAC9BO,EAAoBriB,OAAOC,iBAAiBX,GAAS,iBAC3DmN,YAAYC,iBAAiBpN,EAAS,gBAAiB8iB,GACvD9iB,EAAQ+C,MAAMyf,aAAkBzhB,WAAWgiB,GAAqBF,EAAK3C,gBAArE,QAIJ3R,eAAeE,KAAKkR,yBACjBtd,SAAQ,SAAArC,GACP,IAAMgjB,EAAehjB,EAAQ+C,MAAMkgB,YAC7BC,EAAmBxiB,OAAOC,iBAAiBX,GAAS,gBAC1DmN,YAAYC,iBAAiBpN,EAAS,eAAgBgjB,GACtDhjB,EAAQ+C,MAAMkgB,YAAiBliB,WAAWmiB,GAAoBL,EAAK3C,gBAAnE,QAIJ,IAAM4C,EAAgBjjB,SAASmE,KAAKjB,MAAMyf,aACpCO,EAAoBriB,OAAOC,iBAAiBd,SAASmE,MAAM,iBAEjEmJ,YAAYC,iBAAiBvN,SAASmE,KAAM,gBAAiB8e,GAC7DjjB,SAASmE,KAAKjB,MAAMyf,aAAkBzhB,WAAWgiB,GAAqBjc,KAAKoZ,gBAA3E,KAGFrgB,SAASmE,KAAK2H,UAAU+J,IAAI0J,oBAG9BoC,gBAAA,WAEEjT,eAAeE,KAAKiR,wBACjBrd,SAAQ,SAAArC,GACP,IAAMmjB,EAAUhW,YAAYQ,iBAAiB3N,EAAS,sBAC/B,IAAZmjB,IACThW,YAAYE,oBAAoBrN,EAAS,iBACzCA,EAAQ+C,MAAMyf,aAAeW,MAKnC5U,eAAeE,KAAf,GAAuBkR,yBACpBtd,SAAQ,SAAArC,GACP,IAAMojB,EAASjW,YAAYQ,iBAAiB3N,EAAS,qBAC/B,IAAXojB,IACTjW,YAAYE,oBAAoBrN,EAAS,gBACzCA,EAAQ+C,MAAMkgB,YAAcG,MAKlC,IAAMD,EAAUhW,YAAYQ,iBAAiB9N,SAASmE,KAAM,sBACrC,IAAZmf,EACTtjB,SAASmE,KAAKjB,MAAMyf,aAAe,IAEnCrV,YAAYE,oBAAoBxN,SAASmE,KAAM,iBAC/CnE,SAASmE,KAAKjB,MAAMyf,aAAeW,MAIvCP,mBAAA,WACE,IAAMS,EAAYxjB,SAAS+hB,cAAc,OACzCyB,EAAUxB,UAAY3C,8BACtBrf,SAASmE,KAAKgd,YAAYqC,GAC1B,IAAMC,EAAiBD,EAAUvV,wBAAwByV,MAAQF,EAAUG,YAE3E,OADA3jB,SAASmE,KAAK+H,YAAYsX,GACnBC,KAKFtX,gBAAP,SAAuB/J,EAAQmU,GAC7B,OAAOtP,KAAKmF,MAAK,WACf,IAAIvH,EAAOK,KAAKG,QAAQ4B,KAAMwD,YACxB8I,EAAO3F,SAAA,GACRwC,UACA9C,YAAYI,kBAAkBzG,MACX,iBAAX7E,GAAuBA,EAASA,EAAS,IAOtD,GAJKyC,IACHA,EAAO,IAAIkb,EAAM9Y,KAAMsM,IAGH,iBAAXnR,EAAqB,CAC9B,QAA4B,IAAjByC,EAAKzC,GACd,MAAM,IAAIuV,UAAJ,oBAAkCvV,EAAlC,KAGRyC,EAAKzC,GAAQmU,QACJhD,EAAQiG,MACjB3U,EAAK2U,KAAKjD,SAKThK,YAAP,SAAmBpM,GACjB,OAAO+E,KAAKG,QAAQlF,EAASsK,gEAnd7B,OAAOD,0CAIP,OAAO4F,gBArBL2P,GA8eNtZ,aAAamC,GAAG5I,SAAU+K,uBAAsB6B,wBAAsB,SAAUtG,GAAO,IAAAsd,EAAA3c,KAC/ED,EAAStG,uBAAuBuG,MAEjB,MAAjBA,KAAK8O,SAAoC,SAAjB9O,KAAK8O,SAC/BzP,EAAMgE,iBAGR7D,aAAaoC,IAAI7B,EAAQiR,cAAY,SAAAqI,GAC/BA,EAAU1W,kBAKdnD,aAAaoC,IAAI7B,EAAQoR,gBAAc,WACjCnV,UAAU2gB,IACZA,EAAKtG,cAKX,IAAIzY,EAAOK,KAAKG,QAAQ2B,EAAQyD,YAChC,IAAK5F,EAAM,CACT,IAAMzC,EAAMwL,SAAA,GACPN,YAAYI,kBAAkB1G,GAC9BsG,YAAYI,kBAAkBzG,OAGnCpC,EAAO,IAAIkb,MAAM/Y,EAAQ5E,GAG3ByC,EAAK2U,KAAKvS,SAUZ5C,oBAAmB,WACjB,IAAMmF,EAAIvF,YAEV,GAAIuF,EAAG,CACL,IAAMgD,EAAqBhD,EAAEpD,GAAGmE,QAChCf,EAAEpD,GAAGmE,QAAQwV,MAAM5T,gBACnB3C,EAAEpD,GAAGmE,QAAMkC,YAAcsT,MACzBvW,EAAEpD,GAAGmE,QAAMmC,WAAa,WAEtB,OADAlD,EAAEpD,GAAGmE,QAAQiC,EACNuT,MAAM5T,qBC3mBnB,IAAM0X,SAAW,CACf,aACA,OACA,OACA,WACA,WACA,SACA,MACA,cAGIC,uBAAyB,iBAOzBC,iBAAmB,8DAOnBC,iBAAmB,qIAEnBC,iBAAmB,SAACC,EAAMC,GAC9B,IAAMC,EAAWF,EAAKG,SAAS3kB,cAE/B,IAAgD,IAA5CykB,EAAqBlc,QAAQmc,GAC/B,OAAoC,IAAhCP,SAAS5b,QAAQmc,IACZ7b,QAAQ2b,EAAKI,UAAU7kB,MAAMskB,mBAAqBG,EAAKI,UAAU7kB,MAAMukB,mBASlF,IAHA,IAAMO,EAASJ,EAAqB/U,QAAO,SAAAoV,GAAS,OAAIA,aAAqB3hB,UAGpEqE,EAAI,EAAGM,EAAM+c,EAAOpd,OAAQD,EAAIM,EAAKN,IAC5C,GAAIkd,EAAS3kB,MAAM8kB,EAAOrd,IACxB,OAAO,EAIX,OAAO,GAGIud,iBAAmB,CAE9BC,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAAQZ,wBAC5Ca,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BC,KAAM,GACNC,EAAG,GACHC,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJve,EAAG,GACHwe,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IAGC,SAASC,aAAaC,EAAYC,EAAWC,GAAY,IAAA7X,EAC9D,IAAK2X,EAAWrf,OACd,OAAOqf,EAGT,GAAIE,GAAoC,mBAAfA,EACvB,OAAOA,EAAWF,GAQpB,IALA,IACMG,GADY,IAAI9lB,OAAO+lB,WACKC,gBAAgBL,EAAY,aACxDM,EAAgBxkB,OAAOC,KAAKkkB,GAC5BM,GAAWlY,EAAA,IAAGC,OAAHlI,MAAAiI,EAAa8X,EAAgBxiB,KAAK4C,iBAAiB,MAZNigB,EAAA,SAcrD9f,EAAOM,GAd8C,IAAA2H,EAetD8X,EAAKF,EAAS7f,GACdggB,EAASD,EAAG5C,SAAS3kB,cAE3B,IAAuC,IAAnConB,EAAc7e,QAAQif,GAGxB,OAFAD,EAAG9jB,WAAW+I,YAAY+a,GAE1B,WAGF,IAAME,GAAgBhY,EAAA,IAAGL,OAAHlI,MAAAuI,EAAa8X,EAAGtZ,YAChCyZ,EAAoB,GAAGtY,OAAO2X,EAAU,MAAQ,GAAIA,EAAUS,IAAW,IAE/EC,EAAc3kB,SAAQ,SAAA0hB,GACfD,iBAAiBC,EAAMkD,IAC1BH,EAAGxZ,gBAAgByW,EAAKG,cAfrBnd,EAAI,EAAGM,EAAMuf,EAAS5f,OAAQD,EAAIM,EAAKN,IAAK8f,EAA5C9f,GAoBT,OAAOyf,EAAgBxiB,KAAKkjB,UC1F9B,IAAM9c,OAAO,UACPC,UAAU,eACVC,WAAW,aACXC,YAAS,IAAOD,WAChB6c,aAAe,aACfC,mBAAqB,IAAI1kB,OAAJ,UAAqBykB,aAArB,OAAyC,KAC9DE,sBAAwB,CAAC,WAAY,YAAa,cAElD7W,cAAc,CAClB8W,UAAW,UACXC,SAAU,SACVC,MAAO,4BACPte,QAAS,SACTue,MAAO,kBACPC,KAAM,UACNznB,SAAU,mBACV0d,UAAW,oBACX/P,OAAQ,2BACR4L,UAAW,2BACXmO,kBAAmB,iBACnBxL,SAAU,mBACVyL,SAAU,UACVrB,WAAY,kBACZD,UAAW,SACXjK,aAAc,iBAGVwL,cAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAO,QACPC,OAAQ,SACRC,KAAM,QAGFjY,UAAU,CACdqX,WAAW,EACXC,SAAU,+GAGVre,QAAS,cACTse,MAAO,GACPC,MAAO,EACPC,MAAM,EACNznB,UAAU,EACV0d,UAAW,MACX/P,OAAQ,EACR4L,WAAW,EACXmO,kBAAmB,OACnBxL,SAAU,eACVyL,UAAU,EACVrB,WAAY,KACZD,UAAWhC,iBACXjI,aAAc,MAGVjb,QAAQ,CACZ+mB,KAAI,OAAS5d,YACb6d,OAAM,SAAW7d,YACjB8d,KAAI,OAAS9d,YACb+d,MAAK,QAAU/d,YACfge,SAAQ,WAAahe,YACrBie,MAAK,QAAUje,YACfke,QAAO,UAAYle,YACnBme,SAAQ,WAAane,YACrBoe,WAAU,aAAepe,YACzBqe,WAAU,aAAere,aAGrB8U,kBAAkB,OAClBwJ,iBAAmB,QACnB3Q,kBAAkB,OAElB4Q,iBAAmB,OACnBC,gBAAkB,MAElBC,uBAAyB,iBAEzBC,cAAgB,QAChBC,cAAgB,QAChBC,cAAgB,QAChBC,eAAiB,SAQjBC,QAAAA,WACJ,SAAAA,EAAYrpB,EAASiC,GACnB,QAAsB,IAAX+a,OACT,MAAM,IAAIxF,UAAU,kEAItB1Q,KAAKwiB,YAAa,EAClBxiB,KAAKyiB,SAAW,EAChBziB,KAAK0iB,YAAc,GACnB1iB,KAAK2iB,eAAiB,GACtB3iB,KAAKyV,QAAU,KAGfzV,KAAK9G,QAAUA,EACf8G,KAAK7E,OAAS6E,KAAKuM,WAAWpR,GAC9B6E,KAAK4iB,IAAM,KAEX5iB,KAAK6iB,gBACL5kB,KAAKC,QAAQhF,EAAS8G,KAAK0W,YAAYlT,SAAUxD,iCAmCnD8iB,OAAA,WACE9iB,KAAKwiB,YAAa,KAGpBO,QAAA,WACE/iB,KAAKwiB,YAAa,KAGpBQ,cAAA,WACEhjB,KAAKwiB,YAAcxiB,KAAKwiB,cAG1B3c,OAAA,SAAOxG,GACL,GAAKW,KAAKwiB,WAIV,GAAInjB,EAAO,CACT,IAAM4jB,EAAUjjB,KAAK0W,YAAYlT,SAC7B+T,EAAUtZ,KAAKG,QAAQiB,EAAMC,eAAgB2jB,GAE5C1L,IACHA,EAAU,IAAIvX,KAAK0W,YACjBrX,EAAMC,eACNU,KAAKkjB,sBAEPjlB,KAAKC,QAAQmB,EAAMC,eAAgB2jB,EAAS1L,IAG9CA,EAAQoL,eAAeQ,OAAS5L,EAAQoL,eAAeQ,MAEnD5L,EAAQ6L,uBACV7L,EAAQ8L,OAAO,KAAM9L,GAErBA,EAAQ+L,OAAO,KAAM/L,OAElB,CACL,GAAIvX,KAAKujB,gBAAgB1e,UAAUE,SAASqM,mBAE1C,YADApR,KAAKsjB,OAAO,KAAMtjB,MAIpBA,KAAKqjB,OAAO,KAAMrjB,UAItB0E,QAAA,WACE+J,aAAazO,KAAKyiB,UAElBxkB,KAAKI,WAAW2B,KAAK9G,QAAS8G,KAAK0W,YAAYlT,UAE/ChE,aAAaC,IAAIO,KAAK9G,QAAS8G,KAAK0W,YAAYjT,WAChDjE,aAAaC,IAAIO,KAAK9G,QAAQyL,QAAb,IAAyBod,kBAAqB,gBAAiB/hB,KAAKwjB,mBAEjFxjB,KAAK4iB,KACP5iB,KAAK4iB,IAAI1mB,WAAW+I,YAAYjF,KAAK4iB,KAGvC5iB,KAAKwiB,WAAa,KAClBxiB,KAAKyiB,SAAW,KAChBziB,KAAK0iB,YAAc,KACnB1iB,KAAK2iB,eAAiB,KAClB3iB,KAAKyV,SACPzV,KAAKyV,QAAQa,UAGftW,KAAKyV,QAAU,KACfzV,KAAK9G,QAAU,KACf8G,KAAK7E,OAAS,KACd6E,KAAK4iB,IAAM,QAGbrQ,KAAA,WAAO,IAAA3N,EAAA5E,KACL,GAAmC,SAA/BA,KAAK9G,QAAQ+C,MAAMI,QACrB,MAAM,IAAIP,MAAM,uCAGlB,GAAIkE,KAAKyjB,iBAAmBzjB,KAAKwiB,WAAY,CAC3C,IAAMnJ,EAAY7Z,aAAa4C,QAAQpC,KAAK9G,QAAS8G,KAAK0W,YAAYpc,MAAMinB,MACtEmC,EAAannB,eAAeyD,KAAK9G,SACjCyqB,EAA4B,OAAfD,EACjB1jB,KAAK9G,QAAQ0qB,cAAcpnB,gBAAgBuI,SAAS/E,KAAK9G,SACzDwqB,EAAW3e,SAAS/E,KAAK9G,SAE3B,GAAImgB,EAAU1W,mBAAqBghB,EACjC,OAGF,IAAMf,EAAM5iB,KAAKujB,gBACXM,EAAQnrB,OAAOsH,KAAK0W,YAAYpT,MAEtCsf,EAAI9c,aAAa,KAAM+d,GACvB7jB,KAAK9G,QAAQ4M,aAAa,mBAAoB+d,GAE9C7jB,KAAK8jB,aAED9jB,KAAK7E,OAAOqlB,WACdoC,EAAI/d,UAAU+J,IAAI2J,mBAGpB,IAAM1B,EAA6C,mBAA1B7W,KAAK7E,OAAO0b,UACnC7W,KAAK7E,OAAO0b,UAAUte,KAAKyH,KAAM4iB,EAAK5iB,KAAK9G,SAC3C8G,KAAK7E,OAAO0b,UAERkN,EAAa/jB,KAAKgkB,eAAenN,GACvC7W,KAAKikB,oBAAoBF,GAEzB,IAiBgDnc,EAjB1C8K,EAAY1S,KAAKkkB,gBAiBvB,GAhBAjmB,KAAKC,QAAQ0kB,EAAK5iB,KAAK0W,YAAYlT,SAAUxD,MAExCA,KAAK9G,QAAQ0qB,cAAcpnB,gBAAgBuI,SAAS/E,KAAK4iB,MAC5DlQ,EAAUwH,YAAY0I,GAGxBpjB,aAAa4C,QAAQpC,KAAK9G,QAAS8G,KAAK0W,YAAYpc,MAAMmnB,UAE1DzhB,KAAKyV,QAAU,IAAIS,OAAOlW,KAAK9G,QAAS0pB,EAAK5iB,KAAKoW,iBAAiB2N,IAEnEnB,EAAI/d,UAAU+J,IAAIwC,mBAMd,iBAAkBrY,SAASyD,iBAC7BoL,EAAA,IAAGC,OAAHlI,MAAAiI,EAAa7O,SAASmE,KAAK+K,UAAU1M,SAAQ,SAAArC,GAC3CsG,aAAamC,GAAGzI,EAAS,YAAa2D,WAI1C,IAAMsnB,EAAW,WACXvf,EAAKzJ,OAAOqlB,WACd5b,EAAKwf,iBAGP,IAAMC,EAAiBzf,EAAK8d,YAC5B9d,EAAK8d,YAAc,KAEnBljB,aAAa4C,QAAQwC,EAAK1L,QAAS0L,EAAK8R,YAAYpc,MAAMknB,OAEtD6C,IAAmBpC,iBACrBrd,EAAK0e,OAAO,KAAM1e,IAItB,GAAI5E,KAAK4iB,IAAI/d,UAAUE,SAASwT,mBAAkB,CAChD,IAAMze,EAAqBJ,iCAAiCsG,KAAK4iB,KACjEpjB,aAAaoC,IAAI5B,KAAK4iB,IAAKzqB,eAAgBgsB,GAC3C1pB,qBAAqBuF,KAAK4iB,IAAK9oB,QAE/BqqB,QAKN7R,KAAA,WAAO,IAAAtE,EAAAhO,KACL,GAAKA,KAAKyV,QAAV,CAIA,IAAMmN,EAAM5iB,KAAKujB,gBACXY,EAAW,WACXnW,EAAK0U,cAAgBV,kBAAoBY,EAAI1mB,YAC/C0mB,EAAI1mB,WAAW+I,YAAY2d,GAG7B5U,EAAKsW,iBACLtW,EAAK9U,QAAQsN,gBAAgB,oBAC7BhH,aAAa4C,QAAQ4L,EAAK9U,QAAS8U,EAAK0I,YAAYpc,MAAMgnB,QAC1DtT,EAAKyH,QAAQa,WAIf,IADkB9W,aAAa4C,QAAQpC,KAAK9G,QAAS8G,KAAK0W,YAAYpc,MAAM+mB,MAC9D1e,iBAAd,CAQgD,IAAAuF,EAAhD,GAJA0a,EAAI/d,UAAUC,OAAOsM,mBAIjB,iBAAkBrY,SAASyD,iBAC7B0L,EAAA,IAAGL,OAAHlI,MAAAuI,EAAanP,SAASmE,KAAK+K,UACxB1M,SAAQ,SAAArC,GAAO,OAAIsG,aAAaC,IAAIvG,EAAS,YAAa2D,SAO/D,GAJAmD,KAAK2iB,eAAeN,gBAAiB,EACrCriB,KAAK2iB,eAAeP,gBAAiB,EACrCpiB,KAAK2iB,eAAeR,gBAAiB,EAEjCniB,KAAK4iB,IAAI/d,UAAUE,SAASwT,mBAAkB,CAChD,IAAMze,EAAqBJ,iCAAiCkpB,GAE5DpjB,aAAaoC,IAAIghB,EAAKzqB,eAAgBgsB,GACtC1pB,qBAAqBmoB,EAAK9oB,QAE1BqqB,IAGFnkB,KAAK0iB,YAAc,QAGrBnM,OAAA,WACuB,OAAjBvW,KAAKyV,SACPzV,KAAKyV,QAAQe,oBAMjBiN,cAAA,WACE,OAAOniB,QAAQtB,KAAKukB,eAGtBhB,cAAA,WACE,GAAIvjB,KAAK4iB,IACP,OAAO5iB,KAAK4iB,IAGd,IAAM1pB,EAAUH,SAAS+hB,cAAc,OAIvC,OAHA5hB,EAAQknB,UAAYpgB,KAAK7E,OAAOslB,SAEhCzgB,KAAK4iB,IAAM1pB,EAAQ+O,SAAS,GACrBjI,KAAK4iB,OAGdkB,WAAA,WACE,IAAMlB,EAAM5iB,KAAKujB,gBACjBvjB,KAAKwkB,kBAAkB/c,eAAeO,QAAQka,uBAAwBU,GAAM5iB,KAAKukB,YACjF3B,EAAI/d,UAAUC,OAAOyT,kBAAiBnH,sBAGxCoT,kBAAA,SAAkBtrB,EAASurB,GACzB,GAAgB,OAAZvrB,EAIJ,MAAuB,iBAAZurB,GAAwBlqB,UAAUkqB,IACvCA,EAAQrR,SACVqR,EAAUA,EAAQ,SAIhBzkB,KAAK7E,OAAOylB,KACV6D,EAAQvoB,aAAehD,IACzBA,EAAQknB,UAAY,GACpBlnB,EAAQghB,YAAYuK,IAGtBvrB,EAAQwrB,YAAcD,EAAQC,mBAM9B1kB,KAAK7E,OAAOylB,MACV5gB,KAAK7E,OAAO2lB,WACd2D,EAAUnF,aAAamF,EAASzkB,KAAK7E,OAAOqkB,UAAWxf,KAAK7E,OAAOskB,aAGrEvmB,EAAQknB,UAAYqE,GAEpBvrB,EAAQwrB,YAAcD,MAI1BF,SAAA,WACE,IAAI7D,EAAQ1gB,KAAK9G,QAAQE,aAAa,uBAQtC,OANKsnB,IACHA,EAAqC,mBAAtB1gB,KAAK7E,OAAOulB,MACzB1gB,KAAK7E,OAAOulB,MAAMnoB,KAAKyH,KAAK9G,SAC5B8G,KAAK7E,OAAOulB,OAGTA,KAKTtK,iBAAA,SAAiB2N,GAAY,IAAA5V,EAAAnO,KAuB3B,OAAA2G,SAAA,GAtBwB,CACtBkQ,UAAWkN,EACX/M,UAAW,CACTlQ,OAAQ9G,KAAK8W,aACb1B,KAAM,CACJuP,SAAU3kB,KAAK7E,OAAO0lB,mBAExB+D,MAAO,CACL1rB,QAAO,IAAM8G,KAAK0W,YAAYpT,KAAvB,UAET4T,gBAAiB,CACfC,kBAAmBnX,KAAK7E,OAAOka,WAGnCwP,SAAU,SAAAjnB,GACJA,EAAKknB,oBAAsBlnB,EAAKiZ,WAClC1I,EAAK4W,6BAA6BnnB,IAGtConB,SAAU,SAAApnB,GAAI,OAAIuQ,EAAK4W,6BAA6BnnB,KAKjDoC,KAAK7E,OAAOoa,iBAInB0O,oBAAA,SAAoBF,GAClB/jB,KAAKujB,gBAAgB1e,UAAU+J,IAAOyR,aAAtC,IAAsD0D,MAGxDjN,WAAA,WAAa,IAAA3G,EAAAnQ,KACL8G,EAAS,GAef,MAbkC,mBAAvB9G,KAAK7E,OAAO2L,OACrBA,EAAO3H,GAAK,SAAAvB,GAMV,OALAA,EAAKmZ,QAALpQ,SAAA,GACK/I,EAAKmZ,QACJ5G,EAAKhV,OAAO2L,OAAOlJ,EAAKmZ,QAAS5G,EAAKjX,UAAY,IAGjD0E,GAGTkJ,EAAOA,OAAS9G,KAAK7E,OAAO2L,OAGvBA,KAGTod,cAAA,WACE,OAA8B,IAA1BlkB,KAAK7E,OAAOuX,UACP3Z,SAASmE,KAGd3C,UAAUyF,KAAK7E,OAAOuX,WACjB1S,KAAK7E,OAAOuX,UAGdjL,eAAeO,QAAQhI,KAAK7E,OAAOuX,cAG5CsR,eAAA,SAAenN,GACb,OAAOkK,cAAclK,EAAU9a,kBAGjC8mB,cAAA,WAAgB,IAAAxI,EAAAra,KACGA,KAAK7E,OAAOiH,QAAQjI,MAAM,KAElCoB,SAAQ,SAAA6G,GACf,GAAgB,UAAZA,EACF5C,aAAamC,GAAG0Y,EAAKnhB,QACnBmhB,EAAK3D,YAAYpc,MAAMonB,MACvBrH,EAAKlf,OAAOhC,UACZ,SAAAkG,GAAK,OAAIgb,EAAKxU,OAAOxG,WAElB,GAAI+C,IAAYkgB,eAAgB,CACrC,IAAM2C,EAAU7iB,IAAY+f,cAC1B9H,EAAK3D,YAAYpc,MAAMunB,WACvBxH,EAAK3D,YAAYpc,MAAMqnB,QACnBuD,EAAW9iB,IAAY+f,cAC3B9H,EAAK3D,YAAYpc,MAAMwnB,WACvBzH,EAAK3D,YAAYpc,MAAMsnB,SAEzBpiB,aAAamC,GAAG0Y,EAAKnhB,QACnB+rB,EACA5K,EAAKlf,OAAOhC,UACZ,SAAAkG,GAAK,OAAIgb,EAAKgJ,OAAOhkB,MAEvBG,aAAamC,GAAG0Y,EAAKnhB,QACnBgsB,EACA7K,EAAKlf,OAAOhC,UACZ,SAAAkG,GAAK,OAAIgb,EAAKiJ,OAAOjkB,UAK3BW,KAAKwjB,kBAAoB,WACnBnJ,EAAKnhB,SACPmhB,EAAK/H,QAIT9S,aAAamC,GAAG3B,KAAK9G,QAAQyL,QAAb,IAAyBod,kBACvC,gBACA/hB,KAAKwjB,mBAGHxjB,KAAK7E,OAAOhC,SACd6G,KAAK7E,OAALwL,SAAA,GACK3G,KAAK7E,OADV,CAEEiH,QAAS,SACTjJ,SAAU,KAGZ6G,KAAKmlB,eAITA,UAAA,WACE,IAAMC,SAAmBplB,KAAK9G,QAAQE,aAAa,wBAE/C4G,KAAK9G,QAAQE,aAAa,UAA0B,WAAdgsB,KACxCplB,KAAK9G,QAAQ4M,aACX,sBACA9F,KAAK9G,QAAQE,aAAa,UAAY,IAGxC4G,KAAK9G,QAAQ4M,aAAa,QAAS,QAIvCud,OAAA,SAAOhkB,EAAOkY,GACZ,IAAM0L,EAAUjjB,KAAK0W,YAAYlT,UACjC+T,EAAUA,GAAWtZ,KAAKG,QAAQiB,EAAMC,eAAgB2jB,MAGtD1L,EAAU,IAAIvX,KAAK0W,YACjBrX,EAAMC,eACNU,KAAKkjB,sBAEPjlB,KAAKC,QAAQmB,EAAMC,eAAgB2jB,EAAS1L,IAG1ClY,IACFkY,EAAQoL,eACS,YAAftjB,EAAMK,KAAqB0iB,cAAgBD,gBACzC,GAGF5K,EAAQgM,gBAAgB1e,UAAUE,SAASqM,oBAC3CmG,EAAQmL,cAAgBV,iBAC1BzK,EAAQmL,YAAcV,kBAIxBvT,aAAa8I,EAAQkL,UAErBlL,EAAQmL,YAAcV,iBAEjBzK,EAAQpc,OAAOwlB,OAAUpJ,EAAQpc,OAAOwlB,MAAMpO,KAKnDgF,EAAQkL,SAAWznB,YAAW,WACxBuc,EAAQmL,cAAgBV,kBAC1BzK,EAAQhF,SAETgF,EAAQpc,OAAOwlB,MAAMpO,MARtBgF,EAAQhF,WAWZ+Q,OAAA,SAAOjkB,EAAOkY,GACZ,IAAM0L,EAAUjjB,KAAK0W,YAAYlT,UACjC+T,EAAUA,GAAWtZ,KAAKG,QAAQiB,EAAMC,eAAgB2jB,MAGtD1L,EAAU,IAAIvX,KAAK0W,YACjBrX,EAAMC,eACNU,KAAKkjB,sBAEPjlB,KAAKC,QAAQmB,EAAMC,eAAgB2jB,EAAS1L,IAG1ClY,IACFkY,EAAQoL,eACS,aAAftjB,EAAMK,KAAsB0iB,cAAgBD,gBAC1C,GAGF5K,EAAQ6L,yBAIZ3U,aAAa8I,EAAQkL,UAErBlL,EAAQmL,YAAcT,gBAEjB1K,EAAQpc,OAAOwlB,OAAUpJ,EAAQpc,OAAOwlB,MAAMrO,KAKnDiF,EAAQkL,SAAWznB,YAAW,WACxBuc,EAAQmL,cAAgBT,iBAC1B1K,EAAQjF,SAETiF,EAAQpc,OAAOwlB,MAAMrO,MARtBiF,EAAQjF,WAWZ8Q,qBAAA,WACE,IAAK,IAAMhhB,KAAWpC,KAAK2iB,eACzB,GAAI3iB,KAAK2iB,eAAevgB,GACtB,OAAO,EAIX,OAAO,KAGTmK,WAAA,SAAWpR,GACT,IAAMkqB,EAAiBhf,YAAYI,kBAAkBzG,KAAK9G,SAuC1D,OArCAmC,OAAOC,KAAK+pB,GAAgB9pB,SAAQ,SAAA+pB,IACe,IAA7C/E,sBAAsBvf,QAAQskB,WACzBD,EAAeC,MAItBnqB,GAAsC,iBAArBA,EAAOuX,WAA0BvX,EAAOuX,UAAUU,SACrEjY,EAAOuX,UAAYvX,EAAOuX,UAAU,IASV,iBAN5BvX,EAAMwL,SAAA,GACD3G,KAAK0W,YAAYvN,QACjBkc,EACmB,iBAAXlqB,GAAuBA,EAASA,EAAS,KAGpCwlB,QAChBxlB,EAAOwlB,MAAQ,CACbpO,KAAMpX,EAAOwlB,MACbrO,KAAMnX,EAAOwlB,QAIW,iBAAjBxlB,EAAOulB,QAChBvlB,EAAOulB,MAAQvlB,EAAOulB,MAAMpoB,YAGA,iBAAnB6C,EAAOspB,UAChBtpB,EAAOspB,QAAUtpB,EAAOspB,QAAQnsB,YAGlC2C,gBAAgBqI,OAAMnI,EAAQ6E,KAAK0W,YAAYhN,aAE3CvO,EAAO2lB,WACT3lB,EAAOslB,SAAWnB,aAAankB,EAAOslB,SAAUtlB,EAAOqkB,UAAWrkB,EAAOskB,aAGpEtkB,KAGT+nB,mBAAA,WACE,IAAM/nB,EAAS,GAEf,GAAI6E,KAAK7E,OACP,IAAK,IAAMwC,KAAOqC,KAAK7E,OACjB6E,KAAK0W,YAAYvN,QAAQxL,KAASqC,KAAK7E,OAAOwC,KAChDxC,EAAOwC,GAAOqC,KAAK7E,OAAOwC,IAKhC,OAAOxC,KAGTmpB,eAAA,WACE,IAAM1B,EAAM5iB,KAAKujB,gBACXgC,EAAW3C,EAAIxpB,aAAa,SAASZ,MAAM8nB,oBAChC,OAAbiF,GAAqBA,EAASrlB,OAAS,GACzCqlB,EAASC,KAAI,SAAAC,GAAK,OAAIA,EAAMnsB,UACzBiC,SAAQ,SAAAmqB,GAAM,OAAI9C,EAAI/d,UAAUC,OAAO4gB,SAI9CX,6BAAA,SAA6BY,GAC3B3lB,KAAK4iB,IAAM+C,EAAWxnB,SAASynB,OAC/B5lB,KAAKskB,iBACLtkB,KAAKikB,oBAAoBjkB,KAAKgkB,eAAe2B,EAAW9O,eAG1DuN,eAAA,WACE,IAAMxB,EAAM5iB,KAAKujB,gBACXsC,EAAsB7lB,KAAK7E,OAAOqlB,UACA,OAApCoC,EAAIxpB,aAAa,iBAIrBwpB,EAAI/d,UAAUC,OAAOyT,mBACrBvY,KAAK7E,OAAOqlB,WAAY,EACxBxgB,KAAKsS,OACLtS,KAAKuS,OACLvS,KAAK7E,OAAOqlB,UAAYqF,MAKnB3gB,gBAAP,SAAuB/J,GACrB,OAAO6E,KAAKmF,MAAK,WACf,IAAIvH,EAAOK,KAAKG,QAAQ4B,KAAMwD,YACxB8I,EAA4B,iBAAXnR,GAAuBA,EAE9C,IAAKyC,IAAQ,eAAe/B,KAAKV,MAI5ByC,IACHA,EAAO,IAAI2kB,EAAQviB,KAAMsM,IAGL,iBAAXnR,GAAqB,CAC9B,QAA4B,IAAjByC,EAAKzC,GACd,MAAM,IAAIuV,UAAJ,oBAAkCvV,EAAlC,KAGRyC,EAAKzC,YAKJmK,YAAP,SAAmBpM,GACjB,OAAO+E,KAAKG,QAAQlF,EAASsK,gEAnoB7B,OAAOD,0CAIP,OAAO4F,uCAIP,OAAO7F,wCAIP,OAAOE,yCAIP,OAAOlJ,0CAIP,OAAOmJ,gDAIP,OAAOiG,oBAjDL6Y,GAuqBNnlB,oBAAmB,WACjB,IAAMmF,EAAIvF,YAEV,GAAIuF,EAAG,CACL,IAAMgD,EAAqBhD,EAAEpD,GAAGmE,QAChCf,EAAEpD,GAAGmE,QAAQif,QAAQrd,gBACrB3C,EAAEpD,GAAGmE,QAAMkC,YAAc+c,QACzBhgB,EAAEpD,GAAGmE,QAAMmC,WAAa,WAEtB,OADAlD,EAAEpD,GAAGmE,QAAQiC,EACNgd,QAAQrd,qBC1xBrB,IAAM5B,OAAO,UACPC,UAAU,eACVC,WAAW,aACXC,YAAS,IAAOD,WAChB6c,eAAe,aACfC,qBAAqB,IAAI1kB,OAAJ,UAAqBykB,eAArB,OAAyC,KAE9DlX,UAAOxC,SAAA,GACR4b,QAAQpZ,QADA,CAEX0N,UAAW,QACXzU,QAAS,QACTqiB,QAAS,GACThE,SAAU,gJAMN/W,cAAW/C,SAAA,GACZ4b,QAAQ7Y,YADI,CAEf+a,QAAS,8BAGLnqB,QAAQ,CACZ+mB,KAAI,OAAS5d,YACb6d,OAAM,SAAW7d,YACjB8d,KAAI,OAAS9d,YACb+d,MAAK,QAAU/d,YACfge,SAAQ,WAAahe,YACrBie,MAAK,QAAUje,YACfke,QAAO,UAAYle,YACnBme,SAAQ,WAAane,YACrBoe,WAAU,aAAepe,YACzBqe,WAAU,aAAere,aAGrB8U,kBAAkB,OAClBnH,kBAAkB,OAElB0U,eAAiB,kBACjBC,iBAAmB,gBAQnBC,QAAAA,SAAAA,oGAiCJvC,cAAA,WACE,OAAOzjB,KAAKukB,YAAcvkB,KAAKimB,iBAGjCnC,WAAA,WACE,IAAMlB,EAAM5iB,KAAKujB,gBAGjBvjB,KAAKwkB,kBAAkB/c,eAAeO,QAAQ8d,eAAgBlD,GAAM5iB,KAAKukB,YACzE,IAAIE,EAAUzkB,KAAKimB,cACI,mBAAZxB,IACTA,EAAUA,EAAQlsB,KAAKyH,KAAK9G,UAG9B8G,KAAKwkB,kBAAkB/c,eAAeO,QAAQ+d,iBAAkBnD,GAAM6B,GAEtE7B,EAAI/d,UAAUC,OAAOyT,kBAAiBnH,sBAKxC6S,oBAAA,SAAoBF,GAClB/jB,KAAKujB,gBAAgB1e,UAAU+J,IAAOyR,eAAtC,IAAsD0D,MAGxDkC,YAAA,WACE,OAAOjmB,KAAK9G,QAAQE,aAAa,iBAC/B4G,KAAK7E,OAAOspB,WAGhBH,eAAA,WACE,IAAM1B,EAAM5iB,KAAKujB,gBACXgC,EAAW3C,EAAIxpB,aAAa,SAASZ,MAAM8nB,sBAChC,OAAbiF,GAAqBA,EAASrlB,OAAS,GACzCqlB,EAASC,KAAI,SAAAC,GAAK,OAAIA,EAAMnsB,UACzBiC,SAAQ,SAAAmqB,GAAM,OAAI9C,EAAI/d,UAAUC,OAAO4gB,SAMvCxgB,gBAAP,SAAuB/J,GACrB,OAAO6E,KAAKmF,MAAK,WACf,IAAIvH,EAAOK,KAAKG,QAAQ4B,KAAMwD,YACxB8I,EAA4B,iBAAXnR,EAAsBA,EAAS,KAEtD,IAAKyC,IAAQ,eAAe/B,KAAKV,MAI5ByC,IACHA,EAAO,IAAIooB,EAAQhmB,KAAMsM,GACzBrO,KAAKC,QAAQ8B,KAAMwD,WAAU5F,IAGT,iBAAXzC,GAAqB,CAC9B,QAA4B,IAAjByC,EAAKzC,GACd,MAAM,IAAIuV,UAAJ,oBAAkCvV,EAAlC,KAGRyC,EAAKzC,YAKJmK,YAAP,SAAmBpM,GACjB,OAAO+E,KAAKG,QAAQlF,EAASsK,gEA/F7B,OAAOD,0CAIP,OAAO4F,uCAIP,OAAO7F,wCAIP,OAAOE,yCAIP,OAAOlJ,0CAIP,OAAOmJ,gDAIP,OAAOiG,oBA5BLsc,CAAgBzD,SA8GtBnlB,oBAAmB,WACjB,IAAMmF,EAAIvF,YAEV,GAAIuF,EAAG,CACL,IAAMgD,EAAqBhD,EAAEpD,GAAGmE,QAChCf,EAAEpD,GAAGmE,QAAQ0iB,QAAQ9gB,gBACrB3C,EAAEpD,GAAGmE,QAAMkC,YAAcwgB,QACzBzjB,EAAEpD,GAAGmE,QAAMmC,WAAa,WAEtB,OADAlD,EAAEpD,GAAGmE,QAAQiC,EACNygB,QAAQ9gB,qBC/JrB,IAAM5B,OAAO,YACPC,UAAU,eACVC,WAAW,eACXC,YAAS,IAAOD,WAChBE,eAAe,YAEfyF,UAAU,CACdrC,OAAQ,GACRof,OAAQ,OACRnmB,OAAQ,IAGJ2J,cAAc,CAClB5C,OAAQ,SACRof,OAAQ,SACRnmB,OAAQ,oBAGJomB,eAAc,WAAc1iB,YAC5B2iB,aAAY,SAAY3iB,YACxBiH,sBAAmB,OAAUjH,YAAYC,eAEzC2iB,yBAA2B,gBAC3B3gB,oBAAoB,SAEpB4gB,kBAAoB,sBACpBC,wBAA0B,oBAC1BC,mBAAqB,YACrBC,mBAAqB,YACrBC,oBAAsB,mBACtBC,kBAAoB,YACpBC,yBAA2B,mBAE3BC,cAAgB,SAChBC,gBAAkB,WAQlBC,UAAAA,WACJ,SAAAA,EAAY7tB,EAASiC,GAAQ,IAAAyJ,EAAA5E,KAC3BA,KAAKmE,SAAWjL,EAChB8G,KAAKgnB,eAAqC,SAApB9tB,EAAQ4V,QAAqBlV,OAASV,EAC5D8G,KAAKsM,QAAUtM,KAAKuM,WAAWpR,GAC/B6E,KAAKkS,UAAelS,KAAKsM,QAAQvM,OAAjC,IAA2CymB,mBAA3C,KAAkExmB,KAAKsM,QAAQvM,OAA/E,IAAyF2mB,oBAAzF,KAAiH1mB,KAAKsM,QAAQvM,OAA9H,KAAyIsmB,yBACzIrmB,KAAKinB,SAAW,GAChBjnB,KAAKknB,SAAW,GAChBlnB,KAAKmnB,cAAgB,KACrBnnB,KAAKonB,cAAgB,EAErB5nB,aAAamC,GAAG3B,KAAKgnB,eAAgBZ,cAAc,SAAA/mB,GAAK,OAAIuF,EAAKyiB,SAAShoB,MAE1EW,KAAKsnB,UACLtnB,KAAKqnB,WAELppB,KAAKC,QAAQhF,EAASsK,WAAUxD,iCAelCsnB,QAAA,WAAU,IAAAtZ,EAAAhO,KACFunB,EAAavnB,KAAKgnB,iBAAmBhnB,KAAKgnB,eAAeptB,OAC7DitB,cACAC,gBAEIU,EAAuC,SAAxBxnB,KAAKsM,QAAQ4Z,OAChCqB,EACAvnB,KAAKsM,QAAQ4Z,OAETuB,EAAaD,IAAiBV,gBAClC9mB,KAAK0nB,gBACL,EAEF1nB,KAAKinB,SAAW,GAChBjnB,KAAKknB,SAAW,GAChBlnB,KAAKonB,cAAgBpnB,KAAK2nB,mBAEVlgB,eAAeE,KAAK3H,KAAKkS,WAEjCsT,KAAI,SAAAtsB,GACV,IAAM0uB,EAAiBruB,uBAAuBL,GACxC6G,EAAS6nB,EAAiBngB,eAAeO,QAAQ4f,GAAkB,KAEzE,GAAI7nB,EAAQ,CACV,IAAM8nB,EAAY9nB,EAAOiH,wBACzB,GAAI6gB,EAAUpL,OAASoL,EAAUC,OAC/B,MAAO,CACLzhB,YAAYmhB,GAAcznB,GAAQkH,IAAMwgB,EACxCG,GAKN,OAAO,QAENzf,QAAO,SAAA4f,GAAI,OAAIA,KACfC,MAAK,SAACtK,EAAGE,GAAJ,OAAUF,EAAE,GAAKE,EAAE,MACxBriB,SAAQ,SAAAwsB,GACP/Z,EAAKiZ,SAASxe,KAAKsf,EAAK,IACxB/Z,EAAKkZ,SAASze,KAAKsf,EAAK,UAI9BrjB,QAAA,WACEzG,KAAKI,WAAW2B,KAAKmE,SAAUX,YAC/BhE,aAAaC,IAAIO,KAAKgnB,eAAgBvjB,aAEtCzD,KAAKmE,SAAW,KAChBnE,KAAKgnB,eAAiB,KACtBhnB,KAAKsM,QAAU,KACftM,KAAKkS,UAAY,KACjBlS,KAAKinB,SAAW,KAChBjnB,KAAKknB,SAAW,KAChBlnB,KAAKmnB,cAAgB,KACrBnnB,KAAKonB,cAAgB,QAKvB7a,WAAA,SAAWpR,GAMT,GAA6B,iBAL7BA,EAAMwL,SAAA,GACDwC,UACmB,iBAAXhO,GAAuBA,EAASA,EAAS,KAGpC4E,QAAuBxF,UAAUY,EAAO4E,QAAS,CAAA,IAC3DtC,EAAOtC,EAAO4E,OAAdtC,GACDA,IACHA,EAAK/E,OAAO4K,QACZnI,EAAO4E,OAAOtC,GAAKA,GAGrBtC,EAAO4E,OAAP,IAAoBtC,EAKtB,OAFAxC,gBAAgBqI,OAAMnI,EAAQuO,eAEvBvO,KAGTusB,cAAA,WACE,OAAO1nB,KAAKgnB,iBAAmBptB,OAC7BoG,KAAKgnB,eAAeiB,YACpBjoB,KAAKgnB,eAAe9f,aAGxBygB,iBAAA,WACE,OAAO3nB,KAAKgnB,eAAe3L,cAAgBziB,KAAKsvB,IAC9CnvB,SAASmE,KAAKme,aACdtiB,SAASyD,gBAAgB6e,iBAI7B8M,iBAAA,WACE,OAAOnoB,KAAKgnB,iBAAmBptB,OAC7BA,OAAOwuB,YACPpoB,KAAKgnB,eAAehgB,wBAAwB8gB,UAGhDT,SAAA,WACE,IAAMngB,EAAYlH,KAAK0nB,gBAAkB1nB,KAAKsM,QAAQxF,OAChDuU,EAAerb,KAAK2nB,mBACpBU,EAAYroB,KAAKsM,QAAQxF,OAC7BuU,EACArb,KAAKmoB,mBAMP,GAJInoB,KAAKonB,gBAAkB/L,GACzBrb,KAAKsnB,UAGHpgB,GAAamhB,EAAjB,CACE,IAAMtoB,EAASC,KAAKknB,SAASlnB,KAAKknB,SAAShnB,OAAS,GAEhDF,KAAKmnB,gBAAkBpnB,GACzBC,KAAKsoB,UAAUvoB,OAJnB,CAUA,GAAIC,KAAKmnB,eAAiBjgB,EAAYlH,KAAKinB,SAAS,IAAMjnB,KAAKinB,SAAS,GAAK,EAG3E,OAFAjnB,KAAKmnB,cAAgB,UACrBnnB,KAAKuoB,SAIP,IAAK,IAAItoB,EAAID,KAAKinB,SAAS/mB,OAAQD,KAAM,CAChBD,KAAKmnB,gBAAkBnnB,KAAKknB,SAASjnB,IACxDiH,GAAalH,KAAKinB,SAAShnB,UACM,IAAzBD,KAAKinB,SAAShnB,EAAI,IACtBiH,EAAYlH,KAAKinB,SAAShnB,EAAI,KAGpCD,KAAKsoB,UAAUtoB,KAAKknB,SAASjnB,SAKnCqoB,UAAA,SAAUvoB,GACRC,KAAKmnB,cAAgBpnB,EAErBC,KAAKuoB,SAEL,IAAMC,EAAUxoB,KAAKkS,UAAU/X,MAAM,KAClCqrB,KAAI,SAAArsB,GAAQ,OAAOA,EAAP,iBAAgC4G,EAAhC,MAA4C5G,EAA5C,UAA8D4G,EAA9D,QAET0oB,EAAOhhB,eAAeO,QAAQwgB,EAAQE,KAAK,MAE7CD,EAAK5jB,UAAUE,SAASshB,2BAC1B5e,eAAeO,QAAQ4e,yBAA0B6B,EAAK9jB,QAAQgiB,oBAC3D9hB,UAAU+J,IAAIlJ,qBAEjB+iB,EAAK5jB,UAAU+J,IAAIlJ,uBAGnB+iB,EAAK5jB,UAAU+J,IAAIlJ,qBAEnB+B,eAAeY,QAAQogB,EAAMlC,yBAC1BhrB,SAAQ,SAAAotB,GAGPlhB,eAAeiB,KAAKigB,EAAcnC,mBAAlC,KAAyDE,qBACtDnrB,SAAQ,SAAAwsB,GAAI,OAAIA,EAAKljB,UAAU+J,IAAIlJ,wBAGtC+B,eAAeiB,KAAKigB,EAAWlC,oBAC5BlrB,SAAQ,SAAAqtB,GACPnhB,eAAeQ,SAAS2gB,EAASpC,oBAC9BjrB,SAAQ,SAAAwsB,GAAI,OAAIA,EAAKljB,UAAU+J,IAAIlJ,+BAKhDlG,aAAa4C,QAAQpC,KAAKgnB,eAAgBb,eAAgB,CACxD7W,cAAevP,OAInBwoB,OAAA,WACE9gB,eAAeE,KAAK3H,KAAKkS,WACtB/J,QAAO,SAAA0gB,GAAI,OAAIA,EAAKhkB,UAAUE,SAASW,wBACvCnK,SAAQ,SAAAstB,GAAI,OAAIA,EAAKhkB,UAAUC,OAAOY,2BAKpCR,gBAAP,SAAuB/J,GACrB,OAAO6E,KAAKmF,MAAK,WACf,IAAIvH,EAAOK,KAAKG,QAAQ4B,KAAMwD,YAO9B,GAJK5F,IACHA,EAAO,IAAImpB,EAAU/mB,KAHW,iBAAX7E,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjByC,EAAKzC,GACd,MAAM,IAAIuV,UAAJ,oBAAkCvV,EAAlC,KAGRyC,EAAKzC,YAKJmK,YAAP,SAAmBpM,GACjB,OAAO+E,KAAKG,QAAQlF,EAASsK,gEAvN7B,OAAOD,0CAIP,OAAO4F,gBA1BL4d,GAuPNvnB,aAAamC,GAAG/H,OAAQ8Q,uBAAqB,WAC3CjD,eAAeE,KAAK2e,mBACjB/qB,SAAQ,SAAAutB,GAAG,OAAI,IAAI/B,UAAU+B,EAAKziB,YAAYI,kBAAkBqiB,UAUrE1rB,oBAAmB,WACjB,IAAMmF,EAAIvF,YAEV,GAAIuF,EAAG,CACL,IAAMgD,EAAqBhD,EAAEpD,GAAGmE,QAChCf,EAAEpD,GAAGmE,QAAQyjB,UAAU7hB,gBACvB3C,EAAEpD,GAAGmE,QAAMkC,YAAcuhB,UACzBxkB,EAAEpD,GAAGmE,QAAMmC,WAAa,WAEtB,OADAlD,EAAEpD,GAAGmE,QAAQiC,EACNwhB,UAAU7hB,qBCtTvB,IAAM5B,OAAO,MACPC,UAAU,eACVC,WAAW,SACXC,YAAS,IAAOD,WAChBE,eAAe,YAEfwN,aAAU,OAAUzN,YACpB0N,eAAY,SAAY1N,YACxBuN,aAAU,OAAUvN,YACpBwN,cAAW,QAAWxN,YACtBK,uBAAoB,QAAWL,YAAYC,eAE3CqlB,yBAA2B,gBAC3BrjB,oBAAoB,SACpByO,sBAAsB,WACtBoE,kBAAkB,OAClBnH,kBAAkB,OAElBuV,oBAAoB,YACpBJ,0BAA0B,oBAC1Brb,kBAAkB,UAClB8d,mBAAqB,wBACrBrjB,uBAAuB,kEACvBihB,2BAA2B,mBAC3BqC,+BAAiC,kCAQjCC,IAAAA,WACJ,SAAAA,EAAYhwB,GACV8G,KAAKmE,SAAWjL,EAEhB+E,KAAKC,QAAQ8B,KAAKmE,SAAUX,WAAUxD,iCAWxCuS,KAAA,WAAO,IAAA3N,EAAA5E,KACL,KAAKA,KAAKmE,SAASjI,YACjB8D,KAAKmE,SAASjI,WAAW1B,WAAa+N,KAAKC,cAC3CxI,KAAKmE,SAASU,UAAUE,SAASW,sBACjC1F,KAAKmE,SAASU,UAAUE,SAASoP,wBAHnC,CAOA,IAAIxL,EACE5I,EAAStG,uBAAuBuG,KAAKmE,UACrCglB,EAAcnpB,KAAKmE,SAASQ,QAAQ4hB,2BAE1C,GAAI4C,EAAa,CACf,IAAMC,EAAwC,OAAzBD,EAAY/L,UAA8C,OAAzB+L,EAAY/L,SAAoB4L,mBAAqB9d,kBAE3GvC,GADAA,EAAWlB,eAAeE,KAAKyhB,EAAcD,IACzBxgB,EAASzI,OAAS,GAGxC,IAAImpB,EAAY,KAYhB,GAVI1gB,IACF0gB,EAAY7pB,aAAa4C,QAAQuG,EAAUuI,aAAY,CACrD5B,cAAetP,KAAKmE,cAIN3E,aAAa4C,QAAQpC,KAAKmE,SAAU6M,aAAY,CAChE1B,cAAe3G,IAGHhG,kBACG,OAAd0mB,GAAsBA,EAAU1mB,kBADnC,CAKA3C,KAAKsoB,UACHtoB,KAAKmE,SACLglB,GAGF,IAAMhF,EAAW,WACf3kB,aAAa4C,QAAQuG,EAAUwI,eAAc,CAC3C7B,cAAe1K,EAAKT,WAEtB3E,aAAa4C,QAAQwC,EAAKT,SAAU8M,cAAa,CAC/C3B,cAAe3G,KAIf5I,EACFC,KAAKsoB,UAAUvoB,EAAQA,EAAO7D,WAAYioB,GAE1CA,SAIJzf,QAAA,WACEzG,KAAKI,WAAW2B,KAAKmE,SAAUX,YAC/BxD,KAAKmE,SAAW,QAKlBmkB,UAAA,SAAUpvB,EAASwZ,EAAWrV,GAAU,IAAA2Q,EAAAhO,KAKhCspB,IAJiB5W,GAAqC,OAAvBA,EAAU0K,UAA4C,OAAvB1K,EAAU0K,SAE5E3V,eAAeQ,SAASyK,EAAWxH,mBADnCzD,eAAeE,KAAKqhB,mBAAoBtW,IAGZ,GACxBS,EAAkB9V,GACrBisB,GAAUA,EAAOzkB,UAAUE,SAASwT,mBAEjC4L,EAAW,WAAA,OAAMnW,EAAKub,oBAC1BrwB,EACAowB,EACAjsB,IAGF,GAAIisB,GAAUnW,EAAiB,CAC7B,IAAMrZ,EAAqBJ,iCAAiC4vB,GAC5DA,EAAOzkB,UAAUC,OAAOsM,mBAExB5R,aAAaoC,IAAI0nB,EAAQnxB,eAAgBgsB,GACzC1pB,qBAAqB6uB,EAAQxvB,QAE7BqqB,OAIJoF,oBAAA,SAAoBrwB,EAASowB,EAAQjsB,GACnC,GAAIisB,EAAQ,CACVA,EAAOzkB,UAAUC,OAAOY,qBAExB,IAAM8jB,EAAgB/hB,eAAeO,QAAQihB,+BAAgCK,EAAOptB,YAEhFstB,GACFA,EAAc3kB,UAAUC,OAAOY,qBAGG,QAAhC4jB,EAAOlwB,aAAa,SACtBkwB,EAAOxjB,aAAa,iBAAiB,IAIzC5M,EAAQ2L,UAAU+J,IAAIlJ,qBACe,QAAjCxM,EAAQE,aAAa,SACvBF,EAAQ4M,aAAa,iBAAiB,GAGxChJ,OAAO5D,GAEHA,EAAQ2L,UAAUE,SAASwT,oBAC7Brf,EAAQ2L,UAAU+J,IAAIwC,mBAGpBlY,EAAQgD,YAAchD,EAAQgD,WAAW2I,UAAUE,SAASgkB,6BACtC7vB,EAAQyL,QAAQgiB,sBAGtClf,eAAeE,KAAKif,4BACjBrrB,SAAQ,SAAAkuB,GAAQ,OAAIA,EAAS5kB,UAAU+J,IAAIlJ,wBAGhDxM,EAAQ4M,aAAa,iBAAiB,IAGpCzI,GACFA,OAMG6H,gBAAP,SAAuB/J,GACrB,OAAO6E,KAAKmF,MAAK,WACf,IAAMvH,EAAOK,KAAKG,QAAQ4B,KAAMwD,aAAa,IAAI0lB,EAAIlpB,MAErD,GAAsB,iBAAX7E,EAAqB,CAC9B,QAA4B,IAAjByC,EAAKzC,GACd,MAAM,IAAIuV,UAAJ,oBAAkCvV,EAAlC,KAGRyC,EAAKzC,YAKJmK,YAAP,SAAmBpM,GACjB,OAAO+E,KAAKG,QAAQlF,EAASsK,gEAzJ7B,OAAOD,gBAVL2lB,GA6KN1pB,aAAamC,GAAG5I,SAAU+K,uBAAsB6B,wBAAsB,SAAUtG,GAC9EA,EAAMgE,kBAEOpF,KAAKG,QAAQ4B,KAAMwD,aAAa,IAAI0lB,IAAIlpB,OAChDuS,UAUPnV,oBAAmB,WACjB,IAAMmF,EAAIvF,YAEV,GAAIuF,EAAG,CACL,IAAMgD,EAAqBhD,EAAEpD,GAAGmE,QAChCf,EAAEpD,GAAGmE,QAAQ4lB,IAAIhkB,gBACjB3C,EAAEpD,GAAGmE,QAAMkC,YAAc0jB,IACzB3mB,EAAEpD,GAAGmE,QAAMmC,WAAa,WAEtB,OADAlD,EAAEpD,GAAGmE,QAAQiC,EACN2jB,IAAIhkB,qBCpOjB,IAAM5B,OAAO,QACPC,UAAU,eACVC,WAAW,WACXC,YAAS,IAAOD,WAEhBwU,sBAAmB,gBAAmBvU,YACtCyN,aAAU,OAAUzN,YACpB0N,eAAY,SAAY1N,YACxBuN,aAAU,OAAUvN,YACpBwN,cAAW,QAAWxN,YAEtB8U,kBAAkB,OAClBmR,gBAAkB,OAClBtY,kBAAkB,OAClBuY,mBAAqB,UAErBjgB,cAAc,CAClB8W,UAAW,UACXoJ,SAAU,UACVjJ,MAAO,UAGHxX,UAAU,CACdqX,WAAW,EACXoJ,UAAU,EACVjJ,MAAO,KAGHhI,wBAAwB,yBAQxBkR,MAAAA,WACJ,SAAAA,EAAY3wB,EAASiC,GACnB6E,KAAKmE,SAAWjL,EAChB8G,KAAKsM,QAAUtM,KAAKuM,WAAWpR,GAC/B6E,KAAKyiB,SAAW,KAChBziB,KAAK6iB,gBACL5kB,KAAKC,QAAQhF,EAASsK,WAAUxD,iCAmBlCuS,KAAA,WAAO,IAAA3N,EAAA5E,KAGL,IAFkBR,aAAa4C,QAAQpC,KAAKmE,SAAU6M,cAExCrO,iBAAd,CAIA3C,KAAK8pB,gBAED9pB,KAAKsM,QAAQkU,WACfxgB,KAAKmE,SAASU,UAAU+J,IAAI2J,mBAG9B,IAAM4L,EAAW,WACfvf,EAAKT,SAASU,UAAUC,OAAO6kB,oBAC/B/kB,EAAKT,SAASU,UAAU+J,IAAIwC,mBAE5B5R,aAAa4C,QAAQwC,EAAKT,SAAU8M,eAEhCrM,EAAK0H,QAAQsd,WACfhlB,EAAK6d,SAAWznB,YAAW,WACzB4J,EAAK0N,SACJ1N,EAAK0H,QAAQqU,SAOpB,GAHA3gB,KAAKmE,SAASU,UAAUC,OAAO4kB,iBAC/B5sB,OAAOkD,KAAKmE,UACZnE,KAAKmE,SAASU,UAAU+J,IAAI+a,oBACxB3pB,KAAKsM,QAAQkU,UAAW,CAC1B,IAAM1mB,EAAqBJ,iCAAiCsG,KAAKmE,UAEjE3E,aAAaoC,IAAI5B,KAAKmE,SAAUhM,eAAgBgsB,GAChD1pB,qBAAqBuF,KAAKmE,SAAUrK,QAEpCqqB,QAIJ7R,KAAA,WAAO,IAAAtE,EAAAhO,KACL,GAAKA,KAAKmE,SAASU,UAAUE,SAASqM,qBAIpB5R,aAAa4C,QAAQpC,KAAKmE,SAAU+M,cAExCvO,iBAAd,CAIA,IAAMwhB,EAAW,WACfnW,EAAK7J,SAASU,UAAU+J,IAAI8a,iBAC5BlqB,aAAa4C,QAAQ4L,EAAK7J,SAAUgN,iBAItC,GADAnR,KAAKmE,SAASU,UAAUC,OAAOsM,mBAC3BpR,KAAKsM,QAAQkU,UAAW,CAC1B,IAAM1mB,EAAqBJ,iCAAiCsG,KAAKmE,UAEjE3E,aAAaoC,IAAI5B,KAAKmE,SAAUhM,eAAgBgsB,GAChD1pB,qBAAqBuF,KAAKmE,SAAUrK,QAEpCqqB,QAIJzf,QAAA,WACE1E,KAAK8pB,gBAED9pB,KAAKmE,SAASU,UAAUE,SAASqM,oBACnCpR,KAAKmE,SAASU,UAAUC,OAAOsM,mBAGjC5R,aAAaC,IAAIO,KAAKmE,SAAU6T,uBAChC/Z,KAAKI,WAAW2B,KAAKmE,SAAUX,YAE/BxD,KAAKmE,SAAW,KAChBnE,KAAKsM,QAAU,QAKjBC,WAAA,SAAWpR,GAST,OARAA,EAAMwL,SAAA,GACDwC,UACA9C,YAAYI,kBAAkBzG,KAAKmE,UAChB,iBAAXhJ,GAAuBA,EAASA,EAAS,IAGtDF,gBAAgBqI,OAAMnI,EAAQ6E,KAAK0W,YAAYhN,aAExCvO,KAGT0nB,cAAA,WAAgB,IAAA1U,EAAAnO,KACdR,aAAamC,GAAG3B,KAAKmE,SAAU6T,sBAAqBW,yBAAuB,WAAA,OAAMxK,EAAKmE,aAGxFwX,cAAA,WACErb,aAAazO,KAAKyiB,UAClBziB,KAAKyiB,SAAW,QAKXvd,gBAAP,SAAuB/J,GACrB,OAAO6E,KAAKmF,MAAK,WACf,IAAIvH,EAAOK,KAAKG,QAAQ4B,KAAMwD,YAO9B,GAJK5F,IACHA,EAAO,IAAIisB,EAAM7pB,KAHe,iBAAX7E,GAAuBA,IAMxB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjByC,EAAKzC,GACd,MAAM,IAAIuV,UAAJ,oBAAkCvV,EAAlC,KAGRyC,EAAKzC,GAAQ6E,aAKZsF,YAAP,SAAmBpM,GACjB,OAAO+E,KAAKG,QAAQlF,EAASsK,gEA1I7B,OAAOD,8CAIP,OAAOmG,8CAIP,OAAOP,gBApBL0gB,GAiKNzsB,oBAAmB,WACjB,IAAMmF,EAAIvF,YAEV,GAAIuF,EAAG,CACL,IAAMgD,EAAqBhD,EAAEpD,GAAGmE,QAChCf,EAAEpD,GAAGmE,QAAQumB,MAAM3kB,gBACnB3C,EAAEpD,GAAGmE,QAAMkC,YAAcqkB,MACzBtnB,EAAEpD,GAAGmE,QAAMmC,WAAa,WAEtB,OADAlD,EAAEpD,GAAGmE,QAAQiC,EACNskB,MAAM3kB", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-target')\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href')\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let {\n    transitionDuration,\n    transitionDelay\n  } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = parseFloat(transitionDuration)\n  const floatTransitionDelay = parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (parseFloat(transitionDuration) + parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ?\n      'element' :\n      toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new Error(\n        `${componentName.toUpperCase()}: ` +\n        `Option \"${property}\" provided type \"${valueType}\" ` +\n        `but expected type \"${expectedTypes}\".`)\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nexport {\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst mapData = (() => {\n  const storeData = {}\n  let id = 1\n  return {\n    set(element, key, data) {\n      if (typeof element.bsKey === 'undefined') {\n        element.bsKey = {\n          key,\n          id\n        }\n        id++\n      }\n\n      storeData[element.bsKey.id] = data\n    },\n    get(element, key) {\n      if (!element || typeof element.bsKey === 'undefined') {\n        return null\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        return storeData[keyProperties.id]\n      }\n\n      return null\n    },\n    delete(element, key) {\n      if (typeof element.bsKey === 'undefined') {\n        return\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        delete storeData[keyProperties.id]\n        delete element.bsKey\n      }\n    }\n  }\n})()\n\nconst Data = {\n  setData(instance, key, data) {\n    mapData.set(instance, key, data)\n  },\n  getData(instance, key) {\n    return mapData.get(instance, key)\n  },\n  removeData(instance, key) {\n    mapData.delete(instance, key)\n  }\n}\n\nexport default Data\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = [\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n]\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.indexOf(namespace) > -1) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.charAt(0) === '.'\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.indexOf(handlerKey) > -1) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASSNAME_ALERT = 'alert'\nconst CLASSNAME_FADE = 'fade'\nconst CLASSNAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n\n    if (this._element) {\n      Data.setData(element, DATA_KEY, this)\n    }\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASSNAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASSNAME_SHOW)\n\n    if (!element.classList.contains(CLASSNAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler.one(element, TRANSITION_END, () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Alert.jQueryInterface\n    $.fn[NAME].Constructor = Alert\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Alert.jQueryInterface\n    }\n  }\n})\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.getData(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Button.jQueryInterface\n    $.fn[NAME].Constructor = Button\n\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Button.jQueryInterface\n    }\n  }\n})\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {\n      ...element.dataset\n    }\n\n    Object.keys(attributes).forEach(key => {\n      attributes[key] = normalizeData(attributes[key])\n    })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  matches(element, selector) {\n    return element.matches(selector)\n  },\n\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    const children = [].concat(...element.children)\n\n    return children.filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (this.matches(ancestor, selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (this.matches(next, selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_RIGHT = 'carousel-item-right'\nconst CLASS_NAME_LEFT = 'carousel-item-left'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-slide], [data-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-ride=\"carousel\"]'\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel {\n  constructor(element, config) {\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._element = element\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._items = null\n    this._config = null\n    this._element = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      if (event.touches && event.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.key) {\n      case ARROW_LEFT_KEY:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEY:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrevDirection && activeIndex === 0) ||\n                            (isNextDirection && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = SelectorEngine.find(SELECTOR_ACTIVE, this._indicatorsElement)\n      for (let i = 0; i < indicators.length; i++) {\n        indicators[i].classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        nextIndicator.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = parseInt(element.getAttribute('data-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || (activeElement &&\n      this._getItemByDirection(direction, activeElement))\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_LEFT\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_RIGHT\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler.one(activeElement, TRANSITION_END, () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(() => {\n          EventHandler.trigger(this._element, EVENT_SLID, {\n            relatedTarget: nextElement,\n            direction: eventDirectionName,\n            from: activeElementIndex,\n            to: nextElementIndex\n          })\n        }, 0)\n      })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.getData(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.getData(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Carousel.jQueryInterface\n    $.fn[NAME].Constructor = Carousel\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Carousel.jQueryInterface\n    }\n  }\n})\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element = element\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-target=\"#${element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.filter(elem => container !== elem)\n      activesData = tempActiveData[0] ? Data.getData(tempActiveData[0], DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.setData(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._parent = null\n    this._element = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.getData(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Collapse.jQueryInterface\n    $.fn[NAME].Constructor = Collapse\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Collapse.jQueryInterface\n    }\n  }\n})\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPRIGHT = 'dropright'\nconst CLASS_NAME_DROPLEFT = 'dropleft'\nconst CLASS_NAME_MENURIGHT = 'dropdown-menu-right'\nconst CLASS_NAME_NAVBAR = 'navbar'\nconst CLASS_NAME_POSITION_STATIC = 'position-static'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = 'top-start'\nconst PLACEMENT_TOPEND = 'top-end'\nconst PLACEMENT_BOTTOM = 'bottom-start'\nconst PLACEMENT_BOTTOMEND = 'bottom-end'\nconst PLACEMENT_RIGHT = 'right-start'\nconst PLACEMENT_LEFT = 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'scrollParent',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element = element\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Disable totally Popper.js for Dropdown in Navbar\n    if (!this._inNavbar) {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper.js (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        parent.classList.add(CLASS_NAME_POSITION_STATIC)\n      }\n\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._element, EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n    let placement = PLACEMENT_BOTTOM\n\n    // Handle dropup\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      placement = this._menu.classList.contains(CLASS_NAME_MENURIGHT) ?\n        PLACEMENT_TOPEND :\n        PLACEMENT_TOP\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPRIGHT)) {\n      placement = PLACEMENT_RIGHT\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPLEFT)) {\n      placement = PLACEMENT_LEFT\n    } else if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n      placement = PLACEMENT_BOTTOMEND\n    }\n\n    return placement\n  }\n\n  _detectNavbar() {\n    return Boolean(this._element.closest(`.${CLASS_NAME_NAVBAR}`))\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...(this._config.offset(data.offsets, this._element) || {})\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper.js if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON ||\n      (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown.getParentFromElement(toggles[i])\n      const context = Data.getData(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && ((event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName)) ||\n          (event.type === 'keyup' && event.key === TAB_KEY)) &&\n          dropdownMenu.contains(event.target)) {\n        continue\n      }\n\n      const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.key === ARROW_UP_KEY && index > 0) { // Up\n      index--\n    }\n\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) { // Down\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  event.stopPropagation()\n  Dropdown.dropdownInterface(this, 'toggle')\n})\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => e.stopPropagation())\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Dropdown.jQueryInterface\n    $.fn[NAME].Constructor = Dropdown\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Dropdown.jQueryInterface\n    }\n  }\n})\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true,\n  show: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean',\n  show: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config = this._getConfig(config)\n    this._element = element\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      event => this.hide(event)\n    )\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._element = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (transition) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, TRANSITION_END, transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const animate = this._element.classList.contains(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE :\n      ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        this._triggerBackdropTransition()\n      })\n\n      if (animate) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, TRANSITION_END, callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (this._element.classList.contains(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, TRANSITION_END, callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _triggerBackdropTransition() {\n    if (this._config.backdrop === 'static') {\n      const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n      if (hideEvent.defaultPrevented) {\n        return\n      }\n\n      const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n      if (!isModalOverflowing) {\n        this._element.style.overflowY = 'hidden'\n      }\n\n      this._element.classList.add(CLASS_NAME_STATIC)\n      const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n      EventHandler.off(this._element, TRANSITION_END)\n      EventHandler.one(this._element, TRANSITION_END, () => {\n        this._element.classList.remove(CLASS_NAME_STATIC)\n        if (!isModalOverflowing) {\n          EventHandler.one(this._element, TRANSITION_END, () => {\n            this._element.style.overflowY = ''\n          })\n          emulateTransitionEnd(this._element, modalTransitionDuration)\n        }\n      })\n      emulateTransitionEnd(this._element, modalTransitionDuration)\n      this._element.focus()\n    } else {\n      this.hide()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n      // Adjust fixed content padding\n      SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n        .forEach(element => {\n          const actualPadding = element.style.paddingRight\n          const calculatedPadding = window.getComputedStyle(element)['padding-right']\n          Manipulator.setDataAttribute(element, 'padding-right', actualPadding)\n          element.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n        })\n\n      // Adjust sticky content margin\n      SelectorEngine.find(SELECTOR_STICKY_CONTENT)\n        .forEach(element => {\n          const actualMargin = element.style.marginRight\n          const calculatedMargin = window.getComputedStyle(element)['margin-right']\n          Manipulator.setDataAttribute(element, 'margin-right', actualMargin)\n          element.style.marginRight = `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`\n        })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = window.getComputedStyle(document.body)['padding-right']\n\n      Manipulator.setDataAttribute(document.body, 'padding-right', actualPadding)\n      document.body.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n      .forEach(element => {\n        const padding = Manipulator.getDataAttribute(element, 'padding-right')\n        if (typeof padding !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'padding-right')\n          element.style.paddingRight = padding\n        }\n      })\n\n    // Restore sticky content and navbar-toggler margin\n    SelectorEngine.find(`${SELECTOR_STICKY_CONTENT}`)\n      .forEach(element => {\n        const margin = Manipulator.getDataAttribute(element, 'margin-right')\n        if (typeof margin !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'margin-right')\n          element.style.marginRight = margin\n        }\n      })\n\n    // Restore body padding\n    const padding = Manipulator.getDataAttribute(document.body, 'padding-right')\n    if (typeof padding === 'undefined') {\n      document.body.style.paddingRight = ''\n    } else {\n      Manipulator.removeDataAttribute(document.body, 'padding-right')\n      document.body.style.paddingRight = padding\n    }\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.getData(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.show(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Modal.jQueryInterface\n    $.fn[NAME].Constructor = Modal\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Modal.jQueryInterface\n    }\n  }\n})\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (allowlistKeys.indexOf(elName) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'allowList', 'sanitizeFn']\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(number|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacement: '(string|array)',\n  boundary: '(string|element)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: 'right',\n  BOTTOM: 'bottom',\n  LEFT: 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"tooltip-arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: 0,\n  container: false,\n  fallbackPlacement: 'flip',\n  boundary: 'scrollParent',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper.js (https://popper.js.org)')\n    }\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.element = element\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n    Data.setData(element, this.constructor.DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = Data.getData(event.delegateTarget, dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.delegateTarget,\n          this._getDelegateConfig()\n        )\n        Data.setData(event.delegateTarget, dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    Data.removeData(this.element, this.constructor.DATA_KEY)\n\n    EventHandler.off(this.element, this.constructor.EVENT_KEY)\n    EventHandler.off(this.element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config = null\n    this.tip = null\n  }\n\n  show() {\n    if (this.element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (this.isWithContent() && this._isEnabled) {\n      const showEvent = EventHandler.trigger(this.element, this.constructor.Event.SHOW)\n      const shadowRoot = findShadowRoot(this.element)\n      const isInTheDom = shadowRoot === null ?\n        this.element.ownerDocument.documentElement.contains(this.element) :\n        shadowRoot.contains(this.element)\n\n      if (showEvent.defaultPrevented || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        tip.classList.add(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this.element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this._addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      Data.setData(tip, this.constructor.DATA_KEY, this)\n\n      if (!this.element.ownerDocument.documentElement.contains(this.tip)) {\n        container.appendChild(tip)\n      }\n\n      EventHandler.trigger(this.element, this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, this._getPopperConfig(attachment))\n\n      tip.classList.add(CLASS_NAME_SHOW)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children).forEach(element => {\n          EventHandler.on(element, 'mouseover', noop())\n        })\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n\n        const prevHoverState = this._hoverState\n        this._hoverState = null\n\n        EventHandler.trigger(this.element, this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n        const transitionDuration = getTransitionDurationFromElement(this.tip)\n        EventHandler.one(this.tip, TRANSITION_END, complete)\n        emulateTransitionEnd(this.tip, transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this.element, this.constructor.Event.HIDDEN)\n      this._popper.destroy()\n    }\n\n    const hideEvent = EventHandler.trigger(this.element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, TRANSITION_END, complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.allowList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this.element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          behavior: this.config.fallbackPlacement\n        },\n        arrow: {\n          element: `.${this.constructor.NAME}-arrow`\n        },\n        preventOverflow: {\n          boundariesElement: this.config.boundary\n        }\n      },\n      onCreate: data => {\n        if (data.originalPlacement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      },\n      onUpdate: data => this._handlePopperPlacementChange(data)\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...(this.config.offset(data.offsets, this.element) || {})\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this.element,\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this.element,\n          eventIn,\n          this.config.selector,\n          event => this._enter(event)\n        )\n        EventHandler.on(this.element,\n          eventOut,\n          this.config.selector,\n          event => this._leave(event)\n        )\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this.element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this.element.closest(`.${CLASS_NAME_MODAL}`),\n      'hide.bs.modal',\n      this._hideModalHandler\n    )\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) ||\n        context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this.element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    this.tip = popperData.instance.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tooltip.jQueryInterface\n    $.fn[NAME].Constructor = Tooltip\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tooltip.jQueryInterface\n    }\n  }\n})\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.setData(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Popover.jQueryInterface\n    $.fn[NAME].Constructor = Popover\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Popover.jQueryInterface\n    }\n  }\n})\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._element = null\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset +\n      scrollHeight -\n      this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = ScrollSpy.jQueryInterface\n    $.fn[NAME].Constructor = ScrollSpy\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return ScrollSpy.jQueryInterface\n    }\n  }\n})\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab {\n  constructor(element) {\n    this._element = element\n\n    Data.setData(this._element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    let hideEvent = null\n\n    if (previous) {\n      hideEvent = EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      })\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented ||\n      (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(\n      this._element,\n      listElement\n    )\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback &&\n      (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(\n      element,\n      active,\n      callback\n    )\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, TRANSITION_END, complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.getData(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.getData(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tab.jQueryInterface\n    $.fn[NAME].Constructor = Tab\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tab.jQueryInterface\n    }\n  }\n})\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._element = null\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Toast.jQueryInterface\n    $.fn[NAME].Constructor = Toast\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Toast.jQueryInterface\n    }\n  }\n})\n\nexport default Toast\n"]}