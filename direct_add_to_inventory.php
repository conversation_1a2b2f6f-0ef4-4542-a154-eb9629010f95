<?php
// Include database configuration
require_once 'app/config/config.php';

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h2>Direct Add to Inventory</h2>";

try {
    // Start transaction
    $conn->beginTransaction();
    
    // Check if inventory table exists
    $tableExists = $conn->query("SHOW TABLES LIKE 'inventory'")->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<p>Inventory table does not exist. Creating it now...</p>";
        
        // Create inventory table
        $conn->exec("CREATE TABLE IF NOT EXISTS inventory (
            id INT AUTO_INCREMENT PRIMARY KEY,
            prod_image VARCHAR(255),
            prod_name VARCHAR(100) NOT NULL,
            brand_name VARCHAR(100),
            price DECIMAL(10,2) NOT NULL,
            prod_measure VARCHAR(50),
            pack_type VARCHAR(50),
            expiry_date DATETIME,
            delivered_date DATETIME,
            country VARCHAR(100),
            batch_code VARCHAR(50),
            stocks DECIMAL(10,2) NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'approved',
            order_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        echo "<p>Inventory table created successfully.</p>";
    }
    
    // Insert a new record with product name "Spices"
    $stmt = $conn->prepare("INSERT INTO inventory 
        (prod_name, brand_name, price, prod_measure, pack_type, expiry_date, delivered_date, country, batch_code, stocks, status) 
        VALUES 
        ('Spices', 'VISKASE', 1.00, 'Grams (g)', 'Jar', '2025-09-09 00:00:00', NOW(), 'Philippines', 'SFGE65', 1.00, 'approved')");
    $stmt->execute();
    $insertedId = $conn->lastInsertId();
    
    // Commit transaction
    $conn->commit();
    
    echo "<p>✅ Successfully inserted product into inventory with ID $insertedId.</p>";
    
    // Get all inventory records
    $stmt = $conn->query("SELECT id, prod_name, brand_name, price, stocks, status FROM inventory ORDER BY id DESC");
    $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Current Inventory Records:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Product Name</th><th>Brand</th><th>Price</th><th>Stocks</th><th>Status</th></tr>";
    
    foreach ($records as $record) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($record['id']) . "</td>";
        echo "<td>" . htmlspecialchars($record['prod_name'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($record['brand_name'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($record['price'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($record['stocks'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($record['status'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<p><a href='app/modules/inventory/inventory.php'>Go to Inventory Page</a></p>";
} catch (PDOException $e) {
    // Rollback transaction on error
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
