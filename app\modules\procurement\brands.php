<?php
require_once '../../config/config.php';

// Handle form submission
$success_message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $brand_name = $_POST['brand_name'];
    $country = $_POST['country'];
    $brand_logo = '';

    if (isset($_FILES['brand_logo']) && $_FILES['brand_logo']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = 'uploads/brands/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        $file_ext = pathinfo($_FILES['brand_logo']['name'], PATHINFO_EXTENSION);
        $file_name = uniqid('brand_', true) . '.' . $file_ext;
        $target_path = $upload_dir . $file_name;
        if (move_uploaded_file($_FILES['brand_logo']['tmp_name'], $target_path)) {
            $brand_logo = $target_path;
        }
    }

    if ($brand_logo) {
        $stmt = $conn->prepare("INSERT INTO brand (brand_logo, brand_name, country) VALUES (?, ?, ?)");
        $stmt->execute([$brand_logo, $brand_name, $country]);
        $success_message = 'Brand added successfully!';
    } else {
        $success_message = 'Failed to upload brand logo.';
    }
}

// Fetch all brands
$brands = [];
$sql = "SELECT * FROM brand ORDER BY id DESC";
$brands_result = $conn->query($sql);
if ($brands_result) {
    $brands = $brands_result->fetchAll(PDO::FETCH_ASSOC);
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Brands</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
    <style>
        /* Title styling with orange background */
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            background-color: #f15b31;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: inline-block;
            min-width: 200px;
            text-align: center;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Procurement Management</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="spices.php">Manage Spices</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="brands.php">Manage Brands</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="suppliers.php">Manage Suppliers</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="measurement_units.php">Manage Units</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pack_types.php">Manage Pack Types</a>
                    </li>
                </ul>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="procurement.php">Orders</a>
                    <a class="nav-link" href="logout.php">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h2>Manage Brands</h2>
        <?php if ($success_message): ?>
            <div class="alert alert-success"><?php echo $success_message; ?></div>
        <?php endif; ?>
        <div class="card mb-4">
            <div class="card-header">Add New Brand</div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="brand_logo" class="form-label">Brand Logo (Image)</label>
                        <input type="file" class="form-control" id="brand_logo" name="brand_logo" accept="image/*" required>
                    </div>
                    <div class="mb-3">
                        <label for="brand_name" class="form-label">Brand Name</label>
                        <input type="text" class="form-control" id="brand_name" name="brand_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="country" class="form-label">Country</label>
                        <input type="text" class="form-control" id="country" name="country" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Add Brand</button>
                </form>
            </div>
        </div>
        <div class="card">
            <div class="card-header">Brands List</div>
            <div class="card-body">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Brand Logo</th>
                            <th>Brand Name</th>
                            <th>Country</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($brands as $brand): ?>
                            <tr>
                                <td><?php echo $brand['id']; ?></td>
                                <td>
                                    <?php if ($brand['brand_logo']): ?>
                                        <img src="<?php echo htmlspecialchars($brand['brand_logo']); ?>" alt="Logo" style="max-width: 60px; max-height: 60px;">
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($brand['brand_name']); ?></td>
                                <td><?php echo htmlspecialchars($brand['country']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Back to Dashboard Button -->
        <div class="text-center mt-4 mb-4">
            <a href="../admin/admin_dashboard.php" class="btn" style="background-color: #f15b31; color: white;">Back to Dashboard</a>
        </div>
    </div>
</body>

</html>