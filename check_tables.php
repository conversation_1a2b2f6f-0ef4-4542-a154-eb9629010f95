<?php
// Include database configuration
require_once 'app/config/config.php';

try {
    // Check order_requests table
    $result = $conn->query("SHOW TABLES LIKE 'order_requests'");
    $tableExists = $result->rowCount() > 0;
    
    echo "Order Requests table exists: " . ($tableExists ? "Yes" : "No") . "\n";
    
    if ($tableExists) {
        $result = $conn->query("DESCRIBE order_requests");
        echo "\nOrder Requests table structure:\n";
        echo "-------------------------\n";
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo $row['Field'] . " - " . $row['Type'] . " - " . ($row['Null'] === 'YES' ? 'NULL' : 'NOT NULL') . "\n";
        }
        
        // Get sample data
        $result = $conn->query("SELECT * FROM order_requests LIMIT 1");
        $count = $result->rowCount();
        
        echo "\nSample data (" . $count . " rows):\n";
        echo "-------------------------\n";
        
        if ($count > 0) {
            $row = $result->fetch(PDO::FETCH_ASSOC);
            foreach ($row as $key => $value) {
                echo $key . ": " . $value . "\n";
            }
        } else {
            echo "No data found in order_requests table.\n";
        }
    }
    
    // Check approved table
    $result = $conn->query("SHOW TABLES LIKE 'approved'");
    $tableExists = $result->rowCount() > 0;
    
    echo "\n\nApproved table exists: " . ($tableExists ? "Yes" : "No") . "\n";
    
    if ($tableExists) {
        $result = $conn->query("DESCRIBE approved");
        echo "\nApproved table structure:\n";
        echo "-------------------------\n";
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo $row['Field'] . " - " . $row['Type'] . " - " . ($row['Null'] === 'YES' ? 'NULL' : 'NOT NULL') . "\n";
        }
        
        // Get sample data
        $result = $conn->query("SELECT * FROM approved LIMIT 1");
        $count = $result->rowCount();
        
        echo "\nSample data (" . $count . " rows):\n";
        echo "-------------------------\n";
        
        if ($count > 0) {
            $row = $result->fetch(PDO::FETCH_ASSOC);
            foreach ($row as $key => $value) {
                echo $key . ": " . $value . "\n";
            }
        } else {
            echo "No data found in approved table.\n";
        }
    }
    
    // Check inventory table
    $result = $conn->query("SHOW TABLES LIKE 'inventory'");
    $tableExists = $result->rowCount() > 0;
    
    echo "\n\nInventory table exists: " . ($tableExists ? "Yes" : "No") . "\n";
    
    if ($tableExists) {
        $result = $conn->query("DESCRIBE inventory");
        echo "\nInventory table structure:\n";
        echo "-------------------------\n";
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo $row['Field'] . " - " . $row['Type'] . " - " . ($row['Null'] === 'YES' ? 'NULL' : 'NOT NULL') . "\n";
        }
        
        // Get sample data
        $result = $conn->query("SELECT * FROM inventory LIMIT 1");
        $count = $result->rowCount();
        
        echo "\nSample data (" . $count . " rows):\n";
        echo "-------------------------\n";
        
        if ($count > 0) {
            $row = $result->fetch(PDO::FETCH_ASSOC);
            foreach ($row as $key => $value) {
                echo $key . ": " . $value . "\n";
            }
        } else {
            echo "No data found in inventory table.\n";
        }
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
