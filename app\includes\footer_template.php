        </div><!-- End of container-me -->
        </section>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js"></script>

        <script>
            // Logout function with notification
            function logout() {
                // Show confirmation dialog
                if (confirm("Are you sure you want to log out?")) {
                    // Create notification element
                    const notification = document.createElement('div');
                    notification.style.position = 'fixed';
                    notification.style.top = '20px';
                    notification.style.left = '50%';
                    notification.style.transform = 'translateX(-50%)';
                    notification.style.backgroundColor = '#D14118';
                    notification.style.color = 'white';
                    notification.style.padding = '15px 25px';
                    notification.style.borderRadius = '5px';
                    notification.style.zIndex = '9999';
                    notification.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
                    notification.textContent = 'Logging you out...';

                    // Add notification to body
                    document.body.appendChild(notification);

                    // Set timeout to redirect after showing notification
                    setTimeout(function() {
                        window.location.href = '<?php echo BASE_URL; ?>/app/modules/auth/login.php';
                    }, 1500); // Redirect after 1.5 seconds
                }
            }
        </script>

        <!-- Add any additional module-specific scripts here -->
        <?php if (isset($additional_scripts)): ?>
            <?php echo $additional_scripts; ?>
        <?php endif; ?>
        </body>

        </html>