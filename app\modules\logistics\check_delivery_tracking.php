<?php
require_once '../../config/config.php';

echo "<h1>Checking Delivery Tracking Table</h1>";

try {
    // Check if the delivery_tracking table exists
    $tableExists = $conn->query("SHOW TABLES LIKE 'delivery_tracking'")->rowCount() > 0;

    if ($tableExists) {
        echo "<p>✅ delivery_tracking table exists</p>";

        // Check the structure of the table
        $columns = $conn->query("SHOW COLUMNS FROM delivery_tracking")->fetchAll(PDO::FETCH_ASSOC);
        echo "<h2>Table Structure:</h2>";
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li><strong>{$column['Field']}</strong> - {$column['Type']}</li>";
        }
        echo "</ul>";

        // Check if there's any data in the table
        $count = $conn->query("SELECT COUNT(*) FROM delivery_tracking")->fetchColumn();
        echo "<p>The table contains {$count} records.</p>";
    } else {
        echo "<p>❌ delivery_tracking table does not exist</p>";

        // Create the table
        echo "<h2>Creating delivery_tracking table...</h2>";

        $createTableSQL = "CREATE TABLE delivery_tracking (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_id INT NOT NULL,
            latitude DECIMAL(10,8) NOT NULL,
            longitude DECIMAL(11,8) NOT NULL,
            status VARCHAR(50) NOT NULL,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            FOREIGN KEY (order_id) REFERENCES order_requests(id) ON DELETE CASCADE
        )";

        $conn->exec($createTableSQL);
        echo "<p>✅ delivery_tracking table created successfully!</p>";
    }

    echo "<p><a href='track_deliveries.php' class='btn btn-primary'>Go to Track Deliveries</a></p>";
} catch (PDOException $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
