{"version": 3, "file": "bootstrap.esm.js", "sources": ["../../js/src/util/index.js", "../../js/src/dom/data.js", "../../js/src/dom/event-handler.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/manipulator.js", "../../js/src/dom/selector-engine.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/util/sanitizer.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = obj => {\n  if (obj === null || obj === undefined) {\n    return `${obj}`\n  }\n\n  return {}.toString.call(obj).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * --------------------------------------------------------------------------\n * Public Util Api\n * --------------------------------------------------------------------------\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-target')\n\n  if (!selector || selector === '#') {\n    const hrefAttr = element.getAttribute('href')\n\n    selector = hrefAttr && hrefAttr !== '#' ? hrefAttr.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let {\n    transitionDuration,\n    transitionDelay\n  } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = parseFloat(transitionDuration)\n  const floatTransitionDelay = parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (parseFloat(transitionDuration) + parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = obj => (obj[0] || obj).nodeType\n\nconst emulateTransitionEnd = (element, duration) => {\n  let called = false\n  const durationPadding = 5\n  const emulatedDuration = duration + durationPadding\n  function listener() {\n    called = true\n    element.removeEventListener(TRANSITION_END, listener)\n  }\n\n  element.addEventListener(TRANSITION_END, listener)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(element)\n    }\n  }, emulatedDuration)\n}\n\nconst typeCheckConfig = (componentName, config, configTypes) => {\n  Object.keys(configTypes).forEach(property => {\n    const expectedTypes = configTypes[property]\n    const value = config[property]\n    const valueType = value && isElement(value) ?\n      'element' :\n      toType(value)\n\n    if (!new RegExp(expectedTypes).test(valueType)) {\n      throw new Error(\n        `${componentName.toUpperCase()}: ` +\n        `Option \"${property}\" provided type \"${valueType}\" ` +\n        `but expected type \"${expectedTypes}\".`)\n    }\n  })\n}\n\nconst isVisible = element => {\n  if (!element) {\n    return false\n  }\n\n  if (element.style && element.parentNode && element.parentNode.style) {\n    const elementStyle = getComputedStyle(element)\n    const parentNodeStyle = getComputedStyle(element.parentNode)\n\n    return elementStyle.display !== 'none' &&\n      parentNodeStyle.display !== 'none' &&\n      elementStyle.visibility !== 'hidden'\n  }\n\n  return false\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => function () {}\n\nconst reflow = element => element.offsetHeight\n\nconst getjQuery = () => {\n  const { jQuery } = window\n\n  if (jQuery && !document.body.hasAttribute('data-no-jquery')) {\n    return jQuery\n  }\n\n  return null\n}\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', callback)\n  } else {\n    callback()\n  }\n}\n\nexport {\n  TRANSITION_END,\n  getUID,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  triggerTransitionEnd,\n  isElement,\n  emulateTransitionEnd,\n  typeCheckConfig,\n  isVisible,\n  findShadowRoot,\n  noop,\n  reflow,\n  getjQuery,\n  onDOMContentLoaded\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst mapData = (() => {\n  const storeData = {}\n  let id = 1\n  return {\n    set(element, key, data) {\n      if (typeof element.bsKey === 'undefined') {\n        element.bsKey = {\n          key,\n          id\n        }\n        id++\n      }\n\n      storeData[element.bsKey.id] = data\n    },\n    get(element, key) {\n      if (!element || typeof element.bsKey === 'undefined') {\n        return null\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        return storeData[keyProperties.id]\n      }\n\n      return null\n    },\n    delete(element, key) {\n      if (typeof element.bsKey === 'undefined') {\n        return\n      }\n\n      const keyProperties = element.bsKey\n      if (keyProperties.key === key) {\n        delete storeData[keyProperties.id]\n        delete element.bsKey\n      }\n    }\n  }\n})()\n\nconst Data = {\n  setData(instance, key, data) {\n    mapData.set(instance, key, data)\n  },\n  getData(instance, key) {\n    return mapData.get(instance, key)\n  },\n  removeData(instance, key) {\n    mapData.delete(instance, key)\n  }\n}\n\nexport default Data\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\nconst nativeEvents = [\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n]\n\n/**\n * ------------------------------------------------------------------------\n * Private methods\n * ------------------------------------------------------------------------\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (let i = domElements.length; i--;) {\n        if (domElements[i] === target) {\n          event.delegateTarget = target\n\n          if (handler.oneOff) {\n            EventHandler.off(element, event.type, fn)\n          }\n\n          return fn.apply(target, [event])\n        }\n      }\n    }\n\n    // To please ESLint\n    return null\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  const uidEventList = Object.keys(events)\n\n  for (let i = 0, len = uidEventList.length; i < len; i++) {\n    const event = events[uidEventList[i]]\n\n    if (event.originalHandler === handler && event.delegationSelector === delegationSelector) {\n      return event\n    }\n  }\n\n  return null\n}\n\nfunction normalizeParams(originalTypeEvent, handler, delegationFn) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFn : handler\n\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  let typeEvent = originalTypeEvent.replace(stripNameRegex, '')\n  const custom = customEvents[typeEvent]\n\n  if (custom) {\n    typeEvent = custom\n  }\n\n  const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n  if (!isNative) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFn, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFn\n    delegationFn = null\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFn = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFn) {\n    previousFn.oneOff = previousFn.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFn) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  Object.keys(storeElementEvent).forEach(handlerKey => {\n    if (handlerKey.indexOf(namespace) > -1) {\n      const event = storeElementEvent[handlerKey]\n\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  })\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, false)\n  },\n\n  one(element, event, handler, delegationFn) {\n    addHandler(element, event, handler, delegationFn, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFn) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParams(originalTypeEvent, handler, delegationFn)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.charAt(0) === '.'\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      Object.keys(events).forEach(elementEvent => {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      })\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    Object.keys(storeElementEvent).forEach(keyHandlers => {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.indexOf(handlerKey) > -1) {\n        const event = storeElementEvent[keyHandlers]\n\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    })\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = event.replace(stripNameRegex, '')\n    const inNamespace = event !== typeEvent\n    const isNative = nativeEvents.indexOf(typeEvent) > -1\n\n    let jQueryEvent\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n    let evt = null\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    if (isNative) {\n      evt = document.createEvent('HTMLEvents')\n      evt.initEvent(typeEvent, bubbles, true)\n    } else {\n      evt = new CustomEvent(event, {\n        bubbles,\n        cancelable: true\n      })\n    }\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      Object.keys(args).forEach(key => {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      })\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && typeof jQueryEvent !== 'undefined') {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'alert'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst SELECTOR_DISMISS = '[data-dismiss=\"alert\"]'\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASSNAME_ALERT = 'alert'\nconst CLASSNAME_FADE = 'fade'\nconst CLASSNAME_SHOW = 'show'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n\n    if (this._element) {\n      Data.setData(element, DATA_KEY, this)\n    }\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  close(element) {\n    const rootElement = element ? this._getRootElement(element) : this._element\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent === null || customEvent.defaultPrevented) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    return getElementFromSelector(element) || element.closest(`.${CLASSNAME_ALERT}`)\n  }\n\n  _triggerCloseEvent(element) {\n    return EventHandler.trigger(element, EVENT_CLOSE)\n  }\n\n  _removeElement(element) {\n    element.classList.remove(CLASSNAME_SHOW)\n\n    if (!element.classList.contains(CLASSNAME_FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = getTransitionDurationFromElement(element)\n\n    EventHandler.one(element, TRANSITION_END, () => this._destroyElement(element))\n    emulateTransitionEnd(element, transitionDuration)\n  }\n\n  _destroyElement(element) {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element)\n    }\n\n    EventHandler.trigger(element, EVENT_CLOSED)\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DISMISS, Alert.handleDismiss(new Alert()))\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Alert to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Alert.jQueryInterface\n    $.fn[NAME].Constructor = Alert\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Alert.jQueryInterface\n    }\n  }\n})\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'button'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"button\"]'\n\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n\n  let data = Data.getData(button, DATA_KEY)\n  if (!data) {\n    data = new Button(button)\n  }\n\n  data.toggle()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Button to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Button.jQueryInterface\n    $.fn[NAME].Constructor = Button\n\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Button.jQueryInterface\n    }\n  }\n})\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(val) {\n  if (val === 'true') {\n    return true\n  }\n\n  if (val === 'false') {\n    return false\n  }\n\n  if (val === Number(val).toString()) {\n    return Number(val)\n  }\n\n  if (val === '' || val === 'null') {\n    return null\n  }\n\n  return val\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.to<PERSON><PERSON><PERSON><PERSON>ase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {\n      ...element.dataset\n    }\n\n    Object.keys(attributes).forEach(key => {\n      attributes[key] = normalizeData(attributes[key])\n    })\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-${normalizeDataKey(key)}`))\n  },\n\n  offset(element) {\n    const rect = element.getBoundingClientRect()\n\n    return {\n      top: rect.top + document.body.scrollTop,\n      left: rect.left + document.body.scrollLeft\n    }\n  },\n\n  position(element) {\n    return {\n      top: element.offsetTop,\n      left: element.offsetLeft\n    }\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NODE_TEXT = 3\n\nconst SelectorEngine = {\n  matches(element, selector) {\n    return element.matches(selector)\n  },\n\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    const children = [].concat(...element.children)\n\n    return children.filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n\n    let ancestor = element.parentNode\n\n    while (ancestor && ancestor.nodeType === Node.ELEMENT_NODE && ancestor.nodeType !== NODE_TEXT) {\n      if (this.matches(ancestor, selector)) {\n        parents.push(ancestor)\n      }\n\n      ancestor = ancestor.parentNode\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (this.matches(next, selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  triggerTransitionEnd,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'carousel'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  slide: false,\n  pause: 'hover',\n  wrap: true,\n  touch: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  slide: '(boolean|string)',\n  pause: '(string|boolean)',\n  wrap: 'boolean',\n  touch: 'boolean'\n}\n\nconst DIRECTION_NEXT = 'next'\nconst DIRECTION_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_RIGHT = 'carousel-item-right'\nconst CLASS_NAME_LEFT = 'carousel-item-left'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_ITEM = '.active.carousel-item'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_NEXT_PREV = '.carousel-item-next, .carousel-item-prev'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-slide], [data-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-ride=\"carousel\"]'\n\nconst PointerType = {\n  TOUCH: 'touch',\n  PEN: 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel {\n  constructor(element, config) {\n    this._items = null\n    this._interval = null\n    this._activeElement = null\n    this._isPaused = false\n    this._isSliding = false\n    this.touchTimeout = null\n    this.touchStartX = 0\n    this.touchDeltaX = 0\n\n    this._config = this._getConfig(config)\n    this._element = element\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._touchSupported = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent = Boolean(window.PointerEvent)\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(DIRECTION_PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (SelectorEngine.findOne(SELECTOR_NEXT_PREV, this._element)) {\n      triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config && this._config.interval && !this._isPaused) {\n      this._updateInterval()\n\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex ?\n      DIRECTION_NEXT :\n      DIRECTION_PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._items = null\n    this._config = null\n    this._element = null\n    this._interval = null\n    this._isPaused = null\n    this._isSliding = null\n    this._activeElement = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, event => this.pause(event))\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, event => this.cycle(event))\n    }\n\n    if (this._config.touch && this._touchSupported) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    const start = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchStartX = event.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.touches[0].clientX\n      }\n    }\n\n    const move = event => {\n      // ensure swiping with one touch and not pinching\n      if (event.touches && event.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = event => {\n      if (this._pointerEvent && PointerType[event.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n\n        this.touchTimeout = setTimeout(event => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    SelectorEngine.find(SELECTOR_ITEM_IMG, this._element).forEach(itemImg => {\n      EventHandler.on(itemImg, EVENT_DRAG_START, e => e.preventDefault())\n    })\n\n    if (this._pointerEvent) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.key) {\n      case ARROW_LEFT_KEY:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEY:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode ?\n      SelectorEngine.find(SELECTOR_ITEM, element.parentNode) :\n      []\n\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === DIRECTION_NEXT\n    const isPrevDirection = direction === DIRECTION_PREV\n    const activeIndex = this._getItemIndex(activeElement)\n    const lastItemIndex = this._items.length - 1\n    const isGoingToWrap = (isPrevDirection && activeIndex === 0) ||\n                            (isNextDirection && activeIndex === lastItemIndex)\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta = direction === DIRECTION_PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1 ?\n      this._items[this._items.length - 1] :\n      this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element))\n\n    return EventHandler.trigger(this._element, EVENT_SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = SelectorEngine.find(SELECTOR_ACTIVE, this._indicatorsElement)\n      for (let i = 0; i < indicators.length; i++) {\n        indicators[i].classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        nextIndicator.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = parseInt(element.getAttribute('data-interval'), 10)\n\n    if (elementInterval) {\n      this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n      this._config.interval = elementInterval\n    } else {\n      this._config.interval = this._config.defaultInterval || this._config.interval\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement = element || (activeElement &&\n      this._getItemByDirection(direction, activeElement))\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === DIRECTION_NEXT) {\n      directionalClassName = CLASS_NAME_LEFT\n      orderClassName = CLASS_NAME_NEXT\n      eventDirectionName = DIRECTION_LEFT\n    } else {\n      directionalClassName = CLASS_NAME_RIGHT\n      orderClassName = CLASS_NAME_PREV\n      eventDirectionName = DIRECTION_RIGHT\n    }\n\n    if (nextElement && nextElement.classList.contains(CLASS_NAME_ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n    this._activeElement = nextElement\n\n    if (this._element.classList.contains(CLASS_NAME_SLIDE)) {\n      nextElement.classList.add(orderClassName)\n\n      reflow(nextElement)\n\n      activeElement.classList.add(directionalClassName)\n      nextElement.classList.add(directionalClassName)\n\n      const transitionDuration = getTransitionDurationFromElement(activeElement)\n\n      EventHandler.one(activeElement, TRANSITION_END, () => {\n        nextElement.classList.remove(directionalClassName, orderClassName)\n        nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n        activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n        this._isSliding = false\n\n        setTimeout(() => {\n          EventHandler.trigger(this._element, EVENT_SLID, {\n            relatedTarget: nextElement,\n            direction: eventDirectionName,\n            from: activeElementIndex,\n            to: nextElementIndex\n          })\n        }, 0)\n      })\n\n      emulateTransitionEnd(activeElement, transitionDuration)\n    } else {\n      activeElement.classList.remove(CLASS_NAME_ACTIVE)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      this._isSliding = false\n      EventHandler.trigger(this._element, EVENT_SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static carouselInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    let _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element)\n    }\n\n    if (typeof config === 'object') {\n      _config = {\n        ..._config,\n        ...config\n      }\n    }\n\n    const action = typeof config === 'string' ? config : _config.slide\n\n    if (!data) {\n      data = new Carousel(element, _config)\n    }\n\n    if (typeof config === 'number') {\n      data.to(config)\n    } else if (typeof action === 'string') {\n      if (typeof data[action] === 'undefined') {\n        throw new TypeError(`No method named \"${action}\"`)\n      }\n\n      data[action]()\n    } else if (_config.interval && _config.ride) {\n      data.pause()\n      data.cycle()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Carousel.carouselInterface(this, config)\n    })\n  }\n\n  static dataApiClickHandler(event) {\n    const target = getElementFromSelector(this)\n\n    if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel.carouselInterface(target, config)\n\n    if (slideIndex) {\n      Data.getData(target, DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, Carousel.dataApiClickHandler)\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    Carousel.carouselInterface(carousels[i], Data.getData(carousels[i], DATA_KEY))\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Carousel to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Carousel.jQueryInterface\n    $.fn[NAME].Constructor = Carousel\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Carousel.jQueryInterface\n    }\n  }\n})\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getSelectorFromElement,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'collapse'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  toggle: true,\n  parent: ''\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(string|element)'\n}\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.show, .collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"collapse\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Collapse {\n  constructor(element, config) {\n    this._isTransitioning = false\n    this._element = element\n    this._config = this._getConfig(config)\n    this._triggerArray = SelectorEngine.find(\n      `${SELECTOR_DATA_TOGGLE}[href=\"#${element.id}\"],` +\n      `${SELECTOR_DATA_TOGGLE}[data-target=\"#${element.id}\"]`\n    )\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggleList.length; i < len; i++) {\n      const elem = toggleList[i]\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElem => foundElem === element)\n\n      if (selector !== null && filterElement.length) {\n        this._selector = selector\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._parent = this._config.parent ? this._getParent() : null\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning ||\n      this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    let actives\n    let activesData\n\n    if (this._parent) {\n      actives = SelectorEngine.find(SELECTOR_ACTIVES, this._parent)\n        .filter(elem => {\n          if (typeof this._config.parent === 'string') {\n            return elem.getAttribute('data-parent') === this._config.parent\n          }\n\n          return elem.classList.contains(CLASS_NAME_COLLAPSE)\n        })\n\n      if (actives.length === 0) {\n        actives = null\n      }\n    }\n\n    const container = SelectorEngine.findOne(this._selector)\n    if (actives) {\n      const tempActiveData = actives.filter(elem => container !== elem)\n      activesData = tempActiveData[0] ? Data.getData(tempActiveData[0], DATA_KEY) : null\n\n      if (activesData && activesData._isTransitioning) {\n        return\n      }\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    if (actives) {\n      actives.forEach(elemActive => {\n        if (container !== elemActive) {\n          Collapse.collapseInterface(elemActive, 'hide')\n        }\n\n        if (!activesData) {\n          Data.setData(elemActive, DATA_KEY, null)\n        }\n      })\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    if (this._triggerArray.length) {\n      this._triggerArray.forEach(element => {\n        element.classList.remove(CLASS_NAME_COLLAPSED)\n        element.setAttribute('aria-expanded', true)\n      })\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      this.setTransitioning(false)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n\n    emulateTransitionEnd(this._element, transitionDuration)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning ||\n      !this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    const triggerArrayLength = this._triggerArray.length\n    if (triggerArrayLength > 0) {\n      for (let i = 0; i < triggerArrayLength; i++) {\n        const trigger = this._triggerArray[i]\n        const elem = getElementFromSelector(trigger)\n\n        if (elem && !elem.classList.contains(CLASS_NAME_SHOW)) {\n          trigger.classList.add(CLASS_NAME_COLLAPSED)\n          trigger.setAttribute('aria-expanded', false)\n        }\n      }\n    }\n\n    this.setTransitioning(true)\n\n    const complete = () => {\n      this.setTransitioning(false)\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n    const transitionDuration = getTransitionDurationFromElement(this._element)\n\n    EventHandler.one(this._element, TRANSITION_END, complete)\n    emulateTransitionEnd(this._element, transitionDuration)\n  }\n\n  setTransitioning(isTransitioning) {\n    this._isTransitioning = isTransitioning\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._parent = null\n    this._element = null\n    this._triggerArray = null\n    this._isTransitioning = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(WIDTH) ? WIDTH : HEIGHT\n  }\n\n  _getParent() {\n    let { parent } = this._config\n\n    if (isElement(parent)) {\n      // it's a jQuery object\n      if (typeof parent.jquery !== 'undefined' || typeof parent[0] !== 'undefined') {\n        parent = parent[0]\n      }\n    } else {\n      parent = SelectorEngine.findOne(parent)\n    }\n\n    const selector = `${SELECTOR_DATA_TOGGLE}[data-parent=\"${parent}\"]`\n\n    SelectorEngine.find(selector, parent)\n      .forEach(element => {\n        const selected = getElementFromSelector(element)\n\n        this._addAriaAndCollapsedClass(\n          selected,\n          [element]\n        )\n      })\n\n    return parent\n  }\n\n  _addAriaAndCollapsedClass(element, triggerArray) {\n    if (!element || !triggerArray.length) {\n      return\n    }\n\n    const isOpen = element.classList.contains(CLASS_NAME_SHOW)\n\n    triggerArray.forEach(elem => {\n      if (isOpen) {\n        elem.classList.remove(CLASS_NAME_COLLAPSED)\n      } else {\n        elem.classList.add(CLASS_NAME_COLLAPSED)\n      }\n\n      elem.setAttribute('aria-expanded', isOpen)\n    })\n  }\n\n  // Static\n\n  static collapseInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (!data && _config.toggle && typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    if (!data) {\n      data = new Collapse(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Collapse.collapseInterface(this, config)\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A') {\n    event.preventDefault()\n  }\n\n  const triggerData = Manipulator.getDataAttributes(this)\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  selectorElements.forEach(element => {\n    const data = Data.getData(element, DATA_KEY)\n    let config\n    if (data) {\n      // update parent attribute\n      if (data._parent === null && typeof triggerData.parent === 'string') {\n        data._config.parent = triggerData.parent\n        data._parent = data._getParent()\n      }\n\n      config = 'toggle'\n    } else {\n      config = triggerData\n    }\n\n    Collapse.collapseInterface(element, config)\n  })\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Collapse to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Collapse.jQueryInterface\n    $.fn[NAME].Constructor = Collapse\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Collapse.jQueryInterface\n    }\n  }\n})\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getElementFromSelector,\n  isElement,\n  isVisible,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'dropdown'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst SPACE_KEY = 'Space'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst REGEXP_KEYDOWN = new RegExp(`${ARROW_UP_KEY}|${ARROW_DOWN_KEY}|${ESCAPE_KEY}`)\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPRIGHT = 'dropright'\nconst CLASS_NAME_DROPLEFT = 'dropleft'\nconst CLASS_NAME_MENURIGHT = 'dropdown-menu-right'\nconst CLASS_NAME_NAVBAR = 'navbar'\nconst CLASS_NAME_POSITION_STATIC = 'position-static'\n\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"dropdown\"]'\nconst SELECTOR_FORM_CHILD = '.dropdown form'\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = 'top-start'\nconst PLACEMENT_TOPEND = 'top-end'\nconst PLACEMENT_BOTTOM = 'bottom-start'\nconst PLACEMENT_BOTTOMEND = 'bottom-end'\nconst PLACEMENT_RIGHT = 'right-start'\nconst PLACEMENT_LEFT = 'left-start'\n\nconst Default = {\n  offset: 0,\n  flip: true,\n  boundary: 'scrollParent',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null\n}\n\nconst DefaultType = {\n  offset: '(number|string|function)',\n  flip: 'boolean',\n  boundary: '(string|element)',\n  reference: '(string|element)',\n  display: 'string',\n  popperConfig: '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element = element\n    this._popper = null\n    this._config = this._getConfig(config)\n    this._menu = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const isActive = this._element.classList.contains(CLASS_NAME_SHOW)\n\n    Dropdown.clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show()\n  }\n\n  show() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    // Disable totally Popper.js for Dropdown in Navbar\n    if (!this._inNavbar) {\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper.js (https://popper.js.org)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        parent.classList.add(CLASS_NAME_POSITION_STATIC)\n      }\n\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n      !parent.closest(SELECTOR_NAVBAR_NAV)) {\n      [].concat(...document.body.children)\n        .forEach(elem => EventHandler.on(elem, 'mouseover', null, noop()))\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (this._element.disabled || this._element.classList.contains(CLASS_NAME_DISABLED) || !this._menu.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this._element)\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.toggle(CLASS_NAME_SHOW)\n    this._element.classList.toggle(CLASS_NAME_SHOW)\n    EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._element, EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_CLICK, event => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...config\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _getMenuElement() {\n    return SelectorEngine.next(this._element, SELECTOR_MENU)[0]\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._element.parentNode\n    let placement = PLACEMENT_BOTTOM\n\n    // Handle dropup\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      placement = this._menu.classList.contains(CLASS_NAME_MENURIGHT) ?\n        PLACEMENT_TOPEND :\n        PLACEMENT_TOP\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPRIGHT)) {\n      placement = PLACEMENT_RIGHT\n    } else if (parentDropdown.classList.contains(CLASS_NAME_DROPLEFT)) {\n      placement = PLACEMENT_LEFT\n    } else if (this._menu.classList.contains(CLASS_NAME_MENURIGHT)) {\n      placement = PLACEMENT_BOTTOMEND\n    }\n\n    return placement\n  }\n\n  _detectNavbar() {\n    return Boolean(this._element.closest(`.${CLASS_NAME_NAVBAR}`))\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...(this._config.offset(data.offsets, this._element) || {})\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper.js if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static dropdownInterface(element, config) {\n    let data = Data.getData(element, DATA_KEY)\n    const _config = typeof config === 'object' ? config : null\n\n    if (!data) {\n      data = new Dropdown(element, _config)\n    }\n\n    if (typeof config === 'string') {\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    }\n  }\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      Dropdown.dropdownInterface(this, config)\n    })\n  }\n\n  static clearMenus(event) {\n    if (event && (event.button === RIGHT_MOUSE_BUTTON ||\n      (event.type === 'keyup' && event.key !== TAB_KEY))) {\n      return\n    }\n\n    const toggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown.getParentFromElement(toggles[i])\n      const context = Data.getData(toggles[i], DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!toggles[i].classList.contains(CLASS_NAME_SHOW)) {\n        continue\n      }\n\n      if (event && ((event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName)) ||\n          (event.type === 'keyup' && event.key === TAB_KEY)) &&\n          dropdownMenu.contains(event.target)) {\n        continue\n      }\n\n      const hideEvent = EventHandler.trigger(parent, EVENT_HIDE, relatedTarget)\n      if (hideEvent.defaultPrevented) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children)\n          .forEach(elem => EventHandler.off(elem, 'mouseover', null, noop()))\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      dropdownMenu.classList.remove(CLASS_NAME_SHOW)\n      toggles[i].classList.remove(CLASS_NAME_SHOW)\n      EventHandler.trigger(parent, EVENT_HIDDEN, relatedTarget)\n    }\n  }\n\n  static getParentFromElement(element) {\n    return getElementFromSelector(element) || element.parentNode\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName) ?\n      event.key === SPACE_KEY || (event.key !== ESCAPE_KEY &&\n      ((event.key !== ARROW_DOWN_KEY && event.key !== ARROW_UP_KEY) ||\n        event.target.closest(SELECTOR_MENU))) :\n      !REGEXP_KEYDOWN.test(event.key)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || this.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    const parent = Dropdown.getParentFromElement(this)\n    const isActive = this.classList.contains(CLASS_NAME_SHOW)\n\n    if (event.key === ESCAPE_KEY) {\n      const button = this.matches(SELECTOR_DATA_TOGGLE) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0]\n      button.focus()\n      Dropdown.clearMenus()\n      return\n    }\n\n    if (!isActive || event.key === SPACE_KEY) {\n      Dropdown.clearMenus()\n      return\n    }\n\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, parent).filter(isVisible)\n\n    if (!items.length) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.key === ARROW_UP_KEY && index > 0) { // Up\n      index--\n    }\n\n    if (event.key === ARROW_DOWN_KEY && index < items.length - 1) { // Down\n      index++\n    }\n\n    // index is -1 if the first keydown is an ArrowUp\n    index = index === -1 ? 0 : index\n\n    items[index].focus()\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  event.stopPropagation()\n  Dropdown.dropdownInterface(this, 'toggle')\n})\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_FORM_CHILD, e => e.stopPropagation())\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Dropdown to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Dropdown.jQueryInterface\n    $.fn[NAME].Constructor = Dropdown\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Dropdown.jQueryInterface\n    }\n  }\n})\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  isVisible,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'modal'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true,\n  show: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean',\n  show: 'boolean'\n}\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEUP_DISMISS = `mouseup.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SCROLLBAR_MEASURER = 'modal-scrollbar-measure'\nconst CLASS_NAME_BACKDROP = 'modal-backdrop'\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"modal\"]'\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"modal\"]'\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config = this._getConfig(config)\n    this._element = element\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, element)\n    this._backdrop = null\n    this._isShown = false\n    this._isBodyOverflowing = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning = false\n    this._scrollbarWidth = 0\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if (this._element.classList.contains(CLASS_NAME_FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (this._isShown || showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.on(this._element,\n      EVENT_CLICK_DISMISS,\n      SELECTOR_DATA_DISMISS,\n      event => this.hide(event)\n    )\n\n    EventHandler.on(this._dialog, EVENT_MOUSEDOWN_DISMISS, () => {\n      EventHandler.one(this._element, EVENT_MOUSEUP_DISMISS, event => {\n        if (event.target === this._element) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    EventHandler.off(this._dialog, EVENT_MOUSEDOWN_DISMISS)\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, event => this._hideModal(event))\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach(htmlElement => EventHandler.off(htmlElement, EVENT_KEY))\n\n    /**\n     * `document` has 2 events `EVENT_FOCUSIN` and `EVENT_CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `EVENT_CLICK_DATA_API` event that should remain\n     */\n    EventHandler.off(document, EVENT_FOCUSIN)\n\n    Data.removeData(this._element, DATA_KEY)\n\n    this._config = null\n    this._element = null\n    this._dialog = null\n    this._backdrop = null\n    this._isShown = null\n    this._isBodyOverflowing = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning = null\n    this._scrollbarWidth = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _showElement(relatedTarget) {\n    const transition = this._element.classList.contains(CLASS_NAME_FADE)\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    if (transition) {\n      reflow(this._element)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    if (transition) {\n      const transitionDuration = getTransitionDurationFromElement(this._dialog)\n\n      EventHandler.one(this._dialog, TRANSITION_END, transitionComplete)\n      emulateTransitionEnd(this._dialog, transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    EventHandler.off(document, EVENT_FOCUSIN) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => {\n      if (document !== event.target &&\n          this._element !== event.target &&\n          !this._element.contains(event.target)) {\n        this._element.focus()\n      }\n    })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown) {\n      EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n        if (this._config.keyboard && event.key === ESCAPE_KEY) {\n          event.preventDefault()\n          this.hide()\n        } else if (!this._config.keyboard && event.key === ESCAPE_KEY) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else {\n      EventHandler.off(this._element, EVENT_KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      EventHandler.on(window, EVENT_RESIZE, () => this._adjustDialog())\n    } else {\n      EventHandler.off(window, EVENT_RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    this._backdrop.parentNode.removeChild(this._backdrop)\n    this._backdrop = null\n  }\n\n  _showBackdrop(callback) {\n    const animate = this._element.classList.contains(CLASS_NAME_FADE) ?\n      CLASS_NAME_FADE :\n      ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = CLASS_NAME_BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      document.body.appendChild(this._backdrop)\n\n      EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        this._triggerBackdropTransition()\n      })\n\n      if (animate) {\n        reflow(this._backdrop)\n      }\n\n      this._backdrop.classList.add(CLASS_NAME_SHOW)\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n\n      EventHandler.one(this._backdrop, TRANSITION_END, callback)\n      emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      this._backdrop.classList.remove(CLASS_NAME_SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        callback()\n      }\n\n      if (this._element.classList.contains(CLASS_NAME_FADE)) {\n        const backdropTransitionDuration = getTransitionDurationFromElement(this._backdrop)\n        EventHandler.one(this._backdrop, TRANSITION_END, callbackRemove)\n        emulateTransitionEnd(this._backdrop, backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else {\n      callback()\n    }\n  }\n\n  _triggerBackdropTransition() {\n    if (this._config.backdrop === 'static') {\n      const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n      if (hideEvent.defaultPrevented) {\n        return\n      }\n\n      const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n\n      if (!isModalOverflowing) {\n        this._element.style.overflowY = 'hidden'\n      }\n\n      this._element.classList.add(CLASS_NAME_STATIC)\n      const modalTransitionDuration = getTransitionDurationFromElement(this._dialog)\n      EventHandler.off(this._element, TRANSITION_END)\n      EventHandler.one(this._element, TRANSITION_END, () => {\n        this._element.classList.remove(CLASS_NAME_STATIC)\n        if (!isModalOverflowing) {\n          EventHandler.one(this._element, TRANSITION_END, () => {\n            this._element.style.overflowY = ''\n          })\n          emulateTransitionEnd(this._element, modalTransitionDuration)\n        }\n      })\n      emulateTransitionEnd(this._element, modalTransitionDuration)\n      this._element.focus()\n    } else {\n      this.hide()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = Math.round(rect.left + rect.right) < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n      // Adjust fixed content padding\n      SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n        .forEach(element => {\n          const actualPadding = element.style.paddingRight\n          const calculatedPadding = window.getComputedStyle(element)['padding-right']\n          Manipulator.setDataAttribute(element, 'padding-right', actualPadding)\n          element.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n        })\n\n      // Adjust sticky content margin\n      SelectorEngine.find(SELECTOR_STICKY_CONTENT)\n        .forEach(element => {\n          const actualMargin = element.style.marginRight\n          const calculatedMargin = window.getComputedStyle(element)['margin-right']\n          Manipulator.setDataAttribute(element, 'margin-right', actualMargin)\n          element.style.marginRight = `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`\n        })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = window.getComputedStyle(document.body)['padding-right']\n\n      Manipulator.setDataAttribute(document.body, 'padding-right', actualPadding)\n      document.body.style.paddingRight = `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`\n    }\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    SelectorEngine.find(SELECTOR_FIXED_CONTENT)\n      .forEach(element => {\n        const padding = Manipulator.getDataAttribute(element, 'padding-right')\n        if (typeof padding !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'padding-right')\n          element.style.paddingRight = padding\n        }\n      })\n\n    // Restore sticky content and navbar-toggler margin\n    SelectorEngine.find(`${SELECTOR_STICKY_CONTENT}`)\n      .forEach(element => {\n        const margin = Manipulator.getDataAttribute(element, 'margin-right')\n        if (typeof margin !== 'undefined') {\n          Manipulator.removeDataAttribute(element, 'margin-right')\n          element.style.marginRight = margin\n        }\n      })\n\n    // Restore body padding\n    const padding = Manipulator.getDataAttribute(document.body, 'padding-right')\n    if (typeof padding === 'undefined') {\n      document.body.style.paddingRight = ''\n    } else {\n      Manipulator.removeDataAttribute(document.body, 'padding-right')\n      document.body.style.paddingRight = padding\n    }\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = CLASS_NAME_SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = {\n        ...Default,\n        ...Manipulator.getDataAttributes(this),\n        ...(typeof config === 'object' && config ? config : {})\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  let data = Data.getData(target, DATA_KEY)\n  if (!data) {\n    const config = {\n      ...Manipulator.getDataAttributes(target),\n      ...Manipulator.getDataAttributes(this)\n    }\n\n    data = new Modal(target, config)\n  }\n\n  data.show(this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Modal to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Modal.jQueryInterface\n    $.fn[NAME].Constructor = Modal\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Modal.jQueryInterface\n    }\n  }\n})\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttrs = [\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n]\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular 7 https://github.com/angular/angular/blob/7.2.4/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attr, allowedAttributeList) => {\n  const attrName = attr.nodeName.toLowerCase()\n\n  if (allowedAttributeList.indexOf(attrName) !== -1) {\n    if (uriAttrs.indexOf(attrName) !== -1) {\n      return Boolean(attr.nodeValue.match(SAFE_URL_PATTERN) || attr.nodeValue.match(DATA_URL_PATTERN))\n    }\n\n    return true\n  }\n\n  const regExp = allowedAttributeList.filter(attrRegex => attrRegex instanceof RegExp)\n\n  // Check if a regular expression validates the attribute.\n  for (let i = 0, len = regExp.length; i < len; i++) {\n    if (attrName.match(regExp[i])) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFn) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFn && typeof sanitizeFn === 'function') {\n    return sanitizeFn(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const allowlistKeys = Object.keys(allowList)\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (let i = 0, len = elements.length; i < len; i++) {\n    const el = elements[i]\n    const elName = el.nodeName.toLowerCase()\n\n    if (allowlistKeys.indexOf(elName) === -1) {\n      el.parentNode.removeChild(el)\n\n      continue\n    }\n\n    const attributeList = [].concat(...el.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elName] || [])\n\n    attributeList.forEach(attr => {\n      if (!allowedAttribute(attr, allowedAttributes)) {\n        el.removeAttribute(attr.nodeName)\n      }\n    })\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  findShadowRoot,\n  getTransitionDurationFromElement,\n  getUID,\n  isElement,\n  noop,\n  typeCheckConfig\n} from './util/index'\nimport {\n  DefaultAllowlist,\n  sanitizeHtml\n} from './util/sanitizer'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport Popper from 'popper.js'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tooltip'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.tooltip'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-tooltip'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\nconst DISALLOWED_ATTRIBUTES = ['sanitize', 'allowList', 'sanitizeFn']\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(number|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacement: '(string|array)',\n  boundary: '(string|element)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object)'\n}\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: 'right',\n  BOTTOM: 'bottom',\n  LEFT: 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n                    '<div class=\"tooltip-arrow\"></div>' +\n                    '<div class=\"tooltip-inner\"></div></div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: 0,\n  container: false,\n  fallbackPlacement: 'flip',\n  boundary: 'scrollParent',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst HOVER_STATE_SHOW = 'show'\nconst HOVER_STATE_OUT = 'out'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tooltip {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper.js (https://popper.js.org)')\n    }\n\n    // private\n    this._isEnabled = true\n    this._timeout = 0\n    this._hoverState = ''\n    this._activeTrigger = {}\n    this._popper = null\n\n    // Protected\n    this.element = element\n    this.config = this._getConfig(config)\n    this.tip = null\n\n    this._setListeners()\n    Data.setData(element, this.constructor.DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const dataKey = this.constructor.DATA_KEY\n      let context = Data.getData(event.delegateTarget, dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.delegateTarget,\n          this._getDelegateConfig()\n        )\n        Data.setData(event.delegateTarget, dataKey, context)\n      }\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter(null, context)\n      } else {\n        context._leave(null, context)\n      }\n    } else {\n      if (this.getTipElement().classList.contains(CLASS_NAME_SHOW)) {\n        this._leave(null, this)\n        return\n      }\n\n      this._enter(null, this)\n    }\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    Data.removeData(this.element, this.constructor.DATA_KEY)\n\n    EventHandler.off(this.element, this.constructor.EVENT_KEY)\n    EventHandler.off(this.element.closest(`.${CLASS_NAME_MODAL}`), 'hide.bs.modal', this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.parentNode.removeChild(this.tip)\n    }\n\n    this._isEnabled = null\n    this._timeout = null\n    this._hoverState = null\n    this._activeTrigger = null\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._popper = null\n    this.element = null\n    this.config = null\n    this.tip = null\n  }\n\n  show() {\n    if (this.element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (this.isWithContent() && this._isEnabled) {\n      const showEvent = EventHandler.trigger(this.element, this.constructor.Event.SHOW)\n      const shadowRoot = findShadowRoot(this.element)\n      const isInTheDom = shadowRoot === null ?\n        this.element.ownerDocument.documentElement.contains(this.element) :\n        shadowRoot.contains(this.element)\n\n      if (showEvent.defaultPrevented || !isInTheDom) {\n        return\n      }\n\n      const tip = this.getTipElement()\n      const tipId = getUID(this.constructor.NAME)\n\n      tip.setAttribute('id', tipId)\n      this.element.setAttribute('aria-describedby', tipId)\n\n      this.setContent()\n\n      if (this.config.animation) {\n        tip.classList.add(CLASS_NAME_FADE)\n      }\n\n      const placement = typeof this.config.placement === 'function' ?\n        this.config.placement.call(this, tip, this.element) :\n        this.config.placement\n\n      const attachment = this._getAttachment(placement)\n      this._addAttachmentClass(attachment)\n\n      const container = this._getContainer()\n      Data.setData(tip, this.constructor.DATA_KEY, this)\n\n      if (!this.element.ownerDocument.documentElement.contains(this.tip)) {\n        container.appendChild(tip)\n      }\n\n      EventHandler.trigger(this.element, this.constructor.Event.INSERTED)\n\n      this._popper = new Popper(this.element, tip, this._getPopperConfig(attachment))\n\n      tip.classList.add(CLASS_NAME_SHOW)\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement) {\n        [].concat(...document.body.children).forEach(element => {\n          EventHandler.on(element, 'mouseover', noop())\n        })\n      }\n\n      const complete = () => {\n        if (this.config.animation) {\n          this._fixTransition()\n        }\n\n        const prevHoverState = this._hoverState\n        this._hoverState = null\n\n        EventHandler.trigger(this.element, this.constructor.Event.SHOWN)\n\n        if (prevHoverState === HOVER_STATE_OUT) {\n          this._leave(null, this)\n        }\n      }\n\n      if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n        const transitionDuration = getTransitionDurationFromElement(this.tip)\n        EventHandler.one(this.tip, TRANSITION_END, complete)\n        emulateTransitionEnd(this.tip, transitionDuration)\n      } else {\n        complete()\n      }\n    }\n  }\n\n  hide() {\n    if (!this._popper) {\n      return\n    }\n\n    const tip = this.getTipElement()\n    const complete = () => {\n      if (this._hoverState !== HOVER_STATE_SHOW && tip.parentNode) {\n        tip.parentNode.removeChild(tip)\n      }\n\n      this._cleanTipClass()\n      this.element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this.element, this.constructor.Event.HIDDEN)\n      this._popper.destroy()\n    }\n\n    const hideEvent = EventHandler.trigger(this.element, this.constructor.Event.HIDE)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      [].concat(...document.body.children)\n        .forEach(element => EventHandler.off(element, 'mouseover', noop))\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n\n    if (this.tip.classList.contains(CLASS_NAME_FADE)) {\n      const transitionDuration = getTransitionDurationFromElement(tip)\n\n      EventHandler.one(tip, TRANSITION_END, complete)\n      emulateTransitionEnd(tip, transitionDuration)\n    } else {\n      complete()\n    }\n\n    this._hoverState = ''\n  }\n\n  update() {\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Protected\n\n  isWithContent() {\n    return Boolean(this.getTitle())\n  }\n\n  getTipElement() {\n    if (this.tip) {\n      return this.tip\n    }\n\n    const element = document.createElement('div')\n    element.innerHTML = this.config.template\n\n    this.tip = element.children[0]\n    return this.tip\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TOOLTIP_INNER, tip), this.getTitle())\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  setElementContent(element, content) {\n    if (element === null) {\n      return\n    }\n\n    if (typeof content === 'object' && isElement(content)) {\n      if (content.jquery) {\n        content = content[0]\n      }\n\n      // content is a DOM node or a jQuery\n      if (this.config.html) {\n        if (content.parentNode !== element) {\n          element.innerHTML = ''\n          element.appendChild(content)\n        }\n      } else {\n        element.textContent = content.textContent\n      }\n\n      return\n    }\n\n    if (this.config.html) {\n      if (this.config.sanitize) {\n        content = sanitizeHtml(content, this.config.allowList, this.config.sanitizeFn)\n      }\n\n      element.innerHTML = content\n    } else {\n      element.textContent = content\n    }\n  }\n\n  getTitle() {\n    let title = this.element.getAttribute('data-original-title')\n\n    if (!title) {\n      title = typeof this.config.title === 'function' ?\n        this.config.title.call(this.element) :\n        this.config.title\n    }\n\n    return title\n  }\n\n  // Private\n\n  _getPopperConfig(attachment) {\n    const defaultBsConfig = {\n      placement: attachment,\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          behavior: this.config.fallbackPlacement\n        },\n        arrow: {\n          element: `.${this.constructor.NAME}-arrow`\n        },\n        preventOverflow: {\n          boundariesElement: this.config.boundary\n        }\n      },\n      onCreate: data => {\n        if (data.originalPlacement !== data.placement) {\n          this._handlePopperPlacementChange(data)\n        }\n      },\n      onUpdate: data => this._handlePopperPlacementChange(data)\n    }\n\n    return {\n      ...defaultBsConfig,\n      ...this.config.popperConfig\n    }\n  }\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this.config.offset === 'function') {\n      offset.fn = data => {\n        data.offsets = {\n          ...data.offsets,\n          ...(this.config.offset(data.offsets, this.element) || {})\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this.config.offset\n    }\n\n    return offset\n  }\n\n  _getContainer() {\n    if (this.config.container === false) {\n      return document.body\n    }\n\n    if (isElement(this.config.container)) {\n      return this.config.container\n    }\n\n    return SelectorEngine.findOne(this.config.container)\n  }\n\n  _getAttachment(placement) {\n    return AttachmentMap[placement.toUpperCase()]\n  }\n\n  _setListeners() {\n    const triggers = this.config.trigger.split(' ')\n\n    triggers.forEach(trigger => {\n      if (trigger === 'click') {\n        EventHandler.on(this.element,\n          this.constructor.Event.CLICK,\n          this.config.selector,\n          event => this.toggle(event)\n        )\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSEENTER :\n          this.constructor.Event.FOCUSIN\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.Event.MOUSELEAVE :\n          this.constructor.Event.FOCUSOUT\n\n        EventHandler.on(this.element,\n          eventIn,\n          this.config.selector,\n          event => this._enter(event)\n        )\n        EventHandler.on(this.element,\n          eventOut,\n          this.config.selector,\n          event => this._leave(event)\n        )\n      }\n    })\n\n    this._hideModalHandler = () => {\n      if (this.element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this.element.closest(`.${CLASS_NAME_MODAL}`),\n      'hide.bs.modal',\n      this._hideModalHandler\n    )\n\n    if (this.config.selector) {\n      this.config = {\n        ...this.config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const titleType = typeof this.element.getAttribute('data-original-title')\n\n    if (this.element.getAttribute('title') || titleType !== 'string') {\n      this.element.setAttribute(\n        'data-original-title',\n        this.element.getAttribute('title') || ''\n      )\n\n      this.element.setAttribute('title', '')\n    }\n  }\n\n  _enter(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = true\n    }\n\n    if (context.getTipElement().classList.contains(CLASS_NAME_SHOW) ||\n        context._hoverState === HOVER_STATE_SHOW) {\n      context._hoverState = HOVER_STATE_SHOW\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_SHOW\n\n    if (!context.config.delay || !context.config.delay.show) {\n      context.show()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_SHOW) {\n        context.show()\n      }\n    }, context.config.delay.show)\n  }\n\n  _leave(event, context) {\n    const dataKey = this.constructor.DATA_KEY\n    context = context || Data.getData(event.delegateTarget, dataKey)\n\n    if (!context) {\n      context = new this.constructor(\n        event.delegateTarget,\n        this._getDelegateConfig()\n      )\n      Data.setData(event.delegateTarget, dataKey, context)\n    }\n\n    if (event) {\n      context._activeTrigger[\n        event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER\n      ] = false\n    }\n\n    if (context._isWithActiveTrigger()) {\n      return\n    }\n\n    clearTimeout(context._timeout)\n\n    context._hoverState = HOVER_STATE_OUT\n\n    if (!context.config.delay || !context.config.delay.hide) {\n      context.hide()\n      return\n    }\n\n    context._timeout = setTimeout(() => {\n      if (context._hoverState === HOVER_STATE_OUT) {\n        context.hide()\n      }\n    }, context.config.delay.hide)\n  }\n\n  _isWithActiveTrigger() {\n    for (const trigger in this._activeTrigger) {\n      if (this._activeTrigger[trigger]) {\n        return true\n      }\n    }\n\n    return false\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this.element)\n\n    Object.keys(dataAttributes).forEach(dataAttr => {\n      if (DISALLOWED_ATTRIBUTES.indexOf(dataAttr) !== -1) {\n        delete dataAttributes[dataAttr]\n      }\n    })\n\n    if (config && typeof config.container === 'object' && config.container.jquery) {\n      config.container = config.container[0]\n    }\n\n    config = {\n      ...this.constructor.Default,\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    if (config.sanitize) {\n      config.template = sanitizeHtml(config.template, config.allowList, config.sanitizeFn)\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    if (this.config) {\n      for (const key in this.config) {\n        if (this.constructor.Default[key] !== this.config[key]) {\n          config[key] = this.config[key]\n        }\n      }\n    }\n\n    return config\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  _handlePopperPlacementChange(popperData) {\n    this.tip = popperData.instance.popper\n    this._cleanTipClass()\n    this._addAttachmentClass(this._getAttachment(popperData.placement))\n  }\n\n  _fixTransition() {\n    const tip = this.getTipElement()\n    const initConfigAnimation = this.config.animation\n    if (tip.getAttribute('x-placement') !== null) {\n      return\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE)\n    this.config.animation = false\n    this.hide()\n    this.show()\n    this.config.animation = initConfigAnimation\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Tooltip(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tooltip to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tooltip.jQueryInterface\n    $.fn[NAME].Constructor = Tooltip\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tooltip.jQueryInterface\n    }\n  }\n})\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery, onDOMContentLoaded } from './util/index'\nimport Data from './dom/data'\nimport SelectorEngine from './dom/selector-engine'\nimport Tooltip from './tooltip'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'popover'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.popover'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst CLASS_PREFIX = 'bs-popover'\nconst BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div></div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(string|element|function)'\n}\n\nconst Event = {\n  HIDE: `hide${EVENT_KEY}`,\n  HIDDEN: `hidden${EVENT_KEY}`,\n  SHOW: `show${EVENT_KEY}`,\n  SHOWN: `shown${EVENT_KEY}`,\n  INSERTED: `inserted${EVENT_KEY}`,\n  CLICK: `click${EVENT_KEY}`,\n  FOCUSIN: `focusin${EVENT_KEY}`,\n  FOCUSOUT: `focusout${EVENT_KEY}`,\n  MOUSEENTER: `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE: `mouseleave${EVENT_KEY}`\n}\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Popover extends Tooltip {\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  static get DATA_KEY() {\n    return DATA_KEY\n  }\n\n  static get Event() {\n    return Event\n  }\n\n  static get EVENT_KEY() {\n    return EVENT_KEY\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Overrides\n\n  isWithContent() {\n    return this.getTitle() || this._getContent()\n  }\n\n  setContent() {\n    const tip = this.getTipElement()\n\n    // we use append for html objects to maintain js events\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_TITLE, tip), this.getTitle())\n    let content = this._getContent()\n    if (typeof content === 'function') {\n      content = content.call(this.element)\n    }\n\n    this.setElementContent(SelectorEngine.findOne(SELECTOR_CONTENT, tip), content)\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _addAttachmentClass(attachment) {\n    this.getTipElement().classList.add(`${CLASS_PREFIX}-${attachment}`)\n  }\n\n  _getContent() {\n    return this.element.getAttribute('data-content') ||\n      this.config.content\n  }\n\n  _cleanTipClass() {\n    const tip = this.getTipElement()\n    const tabClass = tip.getAttribute('class').match(BSCLS_PREFIX_REGEX)\n    if (tabClass !== null && tabClass.length > 0) {\n      tabClass.map(token => token.trim())\n        .forEach(tClass => tip.classList.remove(tClass))\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data && /dispose|hide/.test(config)) {\n        return\n      }\n\n      if (!data) {\n        data = new Popover(this, _config)\n        Data.setData(this, DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Popover to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Popover.jQueryInterface\n    $.fn[NAME].Constructor = Popover\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Popover.jQueryInterface\n    }\n  }\n})\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  getSelectorFromElement,\n  getUID,\n  isElement,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'scrollspy'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst Default = {\n  offset: 10,\n  method: 'auto',\n  target: ''\n}\n\nconst DefaultType = {\n  offset: 'number',\n  method: 'string',\n  target: '(string|element)'\n}\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_SCROLL = `scroll${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-spy=\"scroll\"]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst METHOD_OFFSET = 'offset'\nconst METHOD_POSITION = 'position'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config = this._getConfig(config)\n    this._selector = `${this._config.target} ${SELECTOR_NAV_LINKS}, ${this._config.target} ${SELECTOR_LIST_ITEMS}, ${this._config.target} .${CLASS_NAME_DROPDOWN_ITEM}`\n    this._offsets = []\n    this._targets = []\n    this._activeTarget = null\n    this._scrollHeight = 0\n\n    EventHandler.on(this._scrollElement, EVENT_SCROLL, event => this._process(event))\n\n    this.refresh()\n    this._process()\n\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window ?\n      METHOD_OFFSET :\n      METHOD_POSITION\n\n    const offsetMethod = this._config.method === 'auto' ?\n      autoMethod :\n      this._config.method\n\n    const offsetBase = offsetMethod === METHOD_POSITION ?\n      this._getScrollTop() :\n      0\n\n    this._offsets = []\n    this._targets = []\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = SelectorEngine.find(this._selector)\n\n    targets.map(element => {\n      const targetSelector = getSelectorFromElement(element)\n      const target = targetSelector ? SelectorEngine.findOne(targetSelector) : null\n\n      if (target) {\n        const targetBCR = target.getBoundingClientRect()\n        if (targetBCR.width || targetBCR.height) {\n          return [\n            Manipulator[offsetMethod](target).top + offsetBase,\n            targetSelector\n          ]\n        }\n      }\n\n      return null\n    })\n      .filter(item => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach(item => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    EventHandler.off(this._scrollElement, EVENT_KEY)\n\n    this._element = null\n    this._scrollElement = null\n    this._config = null\n    this._selector = null\n    this._offsets = null\n    this._targets = null\n    this._activeTarget = null\n    this._scrollHeight = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    if (typeof config.target !== 'string' && isElement(config.target)) {\n      let { id } = config.target\n      if (!id) {\n        id = getUID(NAME)\n        config.target.id = id\n      }\n\n      config.target = `#${id}`\n    }\n\n    typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window ?\n      this._scrollElement.pageYOffset :\n      this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window ?\n      window.innerHeight :\n      this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll = this._config.offset +\n      scrollHeight -\n      this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    for (let i = this._offsets.length; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector.split(',')\n      .map(selector => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const link = SelectorEngine.findOne(queries.join(','))\n\n    if (link.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, link.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n\n      link.classList.add(CLASS_NAME_ACTIVE)\n    } else {\n      // Set triggered link as active\n      link.classList.add(CLASS_NAME_ACTIVE)\n\n      SelectorEngine.parents(link, SELECTOR_NAV_LIST_GROUP)\n        .forEach(listGroup => {\n          // Set triggered links parents as active\n          // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n          SelectorEngine.prev(listGroup, `${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`)\n            .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n\n          // Handle special case when .nav-link is inside .nav-item\n          SelectorEngine.prev(listGroup, SELECTOR_NAV_ITEMS)\n            .forEach(navItem => {\n              SelectorEngine.children(navItem, SELECTOR_NAV_LINKS)\n                .forEach(item => item.classList.add(CLASS_NAME_ACTIVE))\n            })\n        })\n    }\n\n    EventHandler.trigger(this._scrollElement, EVENT_ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    SelectorEngine.find(this._selector)\n      .filter(node => node.classList.contains(CLASS_NAME_ACTIVE))\n      .forEach(node => node.classList.remove(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  SelectorEngine.find(SELECTOR_DATA_SPY)\n    .forEach(spy => new ScrollSpy(spy, Manipulator.getDataAttributes(spy)))\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .ScrollSpy to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = ScrollSpy.jQueryInterface\n    $.fn[NAME].Constructor = ScrollSpy\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return ScrollSpy.jQueryInterface\n    }\n  }\n})\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getElementFromSelector,\n  getTransitionDurationFromElement,\n  reflow\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'tab'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_MENU = 'dropdown-menu'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_DISABLED = 'disabled'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ACTIVE_UL = ':scope > li > .active'\nconst SELECTOR_DATA_TOGGLE = '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_ACTIVE_CHILD = ':scope > .dropdown-menu .active'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab {\n  constructor(element) {\n    this._element = element\n\n    Data.setData(this._element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  show() {\n    if ((this._element.parentNode &&\n      this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n      this._element.classList.contains(CLASS_NAME_ACTIVE)) ||\n      this._element.classList.contains(CLASS_NAME_DISABLED)) {\n      return\n    }\n\n    let previous\n    const target = getElementFromSelector(this._element)\n    const listElement = this._element.closest(SELECTOR_NAV_LIST_GROUP)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? SELECTOR_ACTIVE_UL : SELECTOR_ACTIVE\n      previous = SelectorEngine.find(itemSelector, listElement)\n      previous = previous[previous.length - 1]\n    }\n\n    let hideEvent = null\n\n    if (previous) {\n      hideEvent = EventHandler.trigger(previous, EVENT_HIDE, {\n        relatedTarget: this._element\n      })\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget: previous\n    })\n\n    if (showEvent.defaultPrevented ||\n      (hideEvent !== null && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._activate(\n      this._element,\n      listElement\n    )\n\n    const complete = () => {\n      EventHandler.trigger(previous, EVENT_HIDDEN, {\n        relatedTarget: this._element\n      })\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget: previous\n      })\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    Data.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL') ?\n      SelectorEngine.find(SELECTOR_ACTIVE_UL, container) :\n      SelectorEngine.children(container, SELECTOR_ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback &&\n      (active && active.classList.contains(CLASS_NAME_FADE))\n\n    const complete = () => this._transitionComplete(\n      element,\n      active,\n      callback\n    )\n\n    if (active && isTransitioning) {\n      const transitionDuration = getTransitionDurationFromElement(active)\n      active.classList.remove(CLASS_NAME_SHOW)\n\n      EventHandler.one(active, TRANSITION_END, complete)\n      emulateTransitionEnd(active, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      active.classList.remove(CLASS_NAME_ACTIVE)\n\n      const dropdownChild = SelectorEngine.findOne(SELECTOR_DROPDOWN_ACTIVE_CHILD, active.parentNode)\n\n      if (dropdownChild) {\n        dropdownChild.classList.remove(CLASS_NAME_ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    reflow(element)\n\n    if (element.classList.contains(CLASS_NAME_FADE)) {\n      element.classList.add(CLASS_NAME_SHOW)\n    }\n\n    if (element.parentNode && element.parentNode.classList.contains(CLASS_NAME_DROPDOWN_MENU)) {\n      const dropdownElement = element.closest(SELECTOR_DROPDOWN)\n\n      if (dropdownElement) {\n        SelectorEngine.find(SELECTOR_DROPDOWN_TOGGLE)\n          .forEach(dropdown => dropdown.classList.add(CLASS_NAME_ACTIVE))\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Data.getData(this, DATA_KEY) || new Tab(this)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n\n  const data = Data.getData(this, DATA_KEY) || new Tab(this)\n  data.show()\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Tab to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Tab.jQueryInterface\n    $.fn[NAME].Constructor = Tab\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Tab.jQueryInterface\n    }\n  }\n})\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.0.0-alpha3): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  getjQuery,\n  onDOMContentLoaded,\n  TRANSITION_END,\n  emulateTransitionEnd,\n  getTransitionDurationFromElement,\n  reflow,\n  typeCheckConfig\n} from './util/index'\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME = 'toast'\nconst VERSION = '5.0.0-alpha3'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\nconst SELECTOR_DATA_DISMISS = '[data-dismiss=\"toast\"]'\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n    Data.setData(element, DATA_KEY, this)\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      this._element.classList.add(CLASS_NAME_SHOW)\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE)\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(CLASS_NAME_SHOW)) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    if (this._config.animation) {\n      const transitionDuration = getTransitionDurationFromElement(this._element)\n\n      EventHandler.one(this._element, TRANSITION_END, complete)\n      emulateTransitionEnd(this._element, transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this._element.classList.contains(CLASS_NAME_SHOW)) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    EventHandler.off(this._element, EVENT_CLICK_DISMISS)\n    Data.removeData(this._element, DATA_KEY)\n\n    this._element = null\n    this._config = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...Manipulator.getDataAttributes(this._element),\n      ...(typeof config === 'object' && config ? config : {})\n    }\n\n    typeCheckConfig(NAME, config, this.constructor.DefaultType)\n\n    return config\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, SELECTOR_DATA_DISMISS, () => this.hide())\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n\n  static jQueryInterface(config) {\n    return this.each(function () {\n      let data = Data.getData(this, DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n\n  static getInstance(element) {\n    return Data.getData(element, DATA_KEY)\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n * add .Toast to jQuery only if jQuery is present\n */\n\nonDOMContentLoaded(() => {\n  const $ = getjQuery()\n  /* istanbul ignore if */\n  if ($) {\n    const JQUERY_NO_CONFLICT = $.fn[NAME]\n    $.fn[NAME] = Toast.jQueryInterface\n    $.fn[NAME].Constructor = Toast\n    $.fn[NAME].noConflict = () => {\n      $.fn[NAME] = JQUERY_NO_CONFLICT\n      return Toast.jQueryInterface\n    }\n  }\n})\n\nexport default Toast\n"], "names": ["MAX_UID", "MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "obj", "undefined", "toString", "call", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttr", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "window", "getComputedStyle", "transitionDuration", "transitionDelay", "floatTransitionDuration", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "nodeType", "emulateTransitionEnd", "duration", "called", "durationPadding", "emulatedDuration", "listener", "removeEventListener", "addEventListener", "setTimeout", "typeCheckConfig", "componentName", "config", "configTypes", "Object", "keys", "for<PERSON>ach", "property", "expectedTypes", "value", "valueType", "RegExp", "test", "Error", "toUpperCase", "isVisible", "style", "parentNode", "elementStyle", "parentNodeStyle", "display", "visibility", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "hasAttribute", "onDOMContentLoaded", "callback", "readyState", "mapData", "storeData", "id", "set", "key", "data", "bs<PERSON><PERSON>", "get", "keyProperties", "delete", "Data", "setData", "instance", "getData", "removeData", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "getUidEvent", "uid", "getEvent", "bootstrapHandler", "fn", "handler", "event", "<PERSON><PERSON><PERSON><PERSON>", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "i", "length", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "uidEventList", "len", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParams", "originalTypeEvent", "delegationFn", "delegation", "typeEvent", "replace", "custom", "isNative", "indexOf", "add<PERSON><PERSON><PERSON>", "handlers", "previousFn", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "on", "one", "inNamespace", "isNamespace", "char<PERSON>t", "elementEvent", "slice", "keyHandlers", "trigger", "args", "$", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "evt", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "createEvent", "initEvent", "CustomEvent", "cancelable", "defineProperty", "preventDefault", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "SELECTOR_DISMISS", "EVENT_CLOSE", "EVENT_CLOSED", "EVENT_CLICK_DATA_API", "CLASSNAME_ALERT", "CLASSNAME_FADE", "CLASSNAME_SHOW", "<PERSON><PERSON>", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "_removeElement", "dispose", "closest", "classList", "remove", "contains", "_destroyElement", "<PERSON><PERSON><PERSON><PERSON>", "jQueryInterface", "each", "handle<PERSON><PERSON><PERSON>", "alertInstance", "getInstance", "JQUERY_NO_CONFLICT", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "setAttribute", "button", "normalizeData", "val", "Number", "normalizeDataKey", "chr", "Manipulator", "setDataAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "dataset", "getDataAttribute", "offset", "rect", "getBoundingClientRect", "top", "scrollTop", "left", "scrollLeft", "position", "offsetTop", "offsetLeft", "NODE_TEXT", "SelectorEngine", "matches", "find", "concat", "Element", "prototype", "findOne", "children", "filter", "child", "parents", "ancestor", "Node", "ELEMENT_NODE", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "DIRECTION_NEXT", "DIRECTION_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_SLIDE", "CLASS_NAME_RIGHT", "CLASS_NAME_LEFT", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "CLASS_NAME_POINTER_EVENT", "SELECTOR_ACTIVE", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_NEXT_PREV", "SELECTOR_INDICATORS", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "PointerType", "TOUCH", "PEN", "Carousel", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_indicatorsElement", "_touchSupported", "navigator", "maxTouchPoints", "_pointerEvent", "PointerEvent", "_addEventListeners", "_slide", "nextWhenVisible", "hidden", "cycle", "clearInterval", "_updateInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "direction", "_handleSwipe", "absDeltax", "abs", "_keydown", "_addTouchEventListeners", "start", "pointerType", "clientX", "touches", "move", "end", "clearTimeout", "itemImg", "e", "add", "tagName", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "isGoingToWrap", "delta", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "from", "_setActiveIndicatorElement", "indicators", "nextIndicator", "elementInterval", "parseInt", "defaultInterval", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "slideEvent", "carouselInterface", "action", "TypeError", "ride", "dataApiClickHandler", "slideIndex", "carousels", "parent", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElem", "_selector", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "container", "tempActiveData", "startEvent", "elemActive", "collapseInterface", "dimension", "_getDimension", "setTransitioning", "complete", "capitalizedDimension", "scrollSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isTransitioning", "j<PERSON>y", "selected", "trigger<PERSON><PERSON>y", "isOpen", "triggerData", "selectorElements", "ESCAPE_KEY", "SPACE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "REGEXP_KEYDOWN", "EVENT_CLICK", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DISABLED", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPRIGHT", "CLASS_NAME_DROPLEFT", "CLASS_NAME_MENURIGHT", "CLASS_NAME_NAVBAR", "CLASS_NAME_POSITION_STATIC", "SELECTOR_FORM_CHILD", "SELECTOR_MENU", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "flip", "boundary", "reference", "popperConfig", "Dropdown", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "isActive", "clearMenus", "getParentFromElement", "showEvent", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "focus", "hideEvent", "destroy", "update", "scheduleUpdate", "stopPropagation", "constructor", "_getPlacement", "parentDropdown", "placement", "_getOffset", "offsets", "modifiers", "enabled", "preventOverflow", "boundariesElement", "applyStyle", "dropdownInterface", "toggles", "context", "clickEvent", "dropdownMenu", "dataApiKeydownHandler", "items", "backdrop", "EVENT_HIDE_PREVENTED", "EVENT_FOCUSIN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_MOUSEUP_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "CLASS_NAME_SCROLLBAR_MEASURER", "CLASS_NAME_BACKDROP", "CLASS_NAME_OPEN", "CLASS_NAME_FADE", "CLASS_NAME_STATIC", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "SELECTOR_DATA_DISMISS", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "Modal", "_dialog", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_scrollbarWidth", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "_showBackdrop", "_showElement", "transition", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "append<PERSON><PERSON><PERSON>", "_enforceFocus", "transitionComplete", "_triggerBackdropTransition", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "animate", "createElement", "className", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "scrollHeight", "clientHeight", "overflowY", "modalTransitionDuration", "paddingLeft", "paddingRight", "round", "right", "innerWidth", "_getScrollbarWidth", "actualPadding", "calculatedPadding", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "uriAttrs", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attr", "allowedAttributeList", "attrName", "nodeName", "nodeValue", "regExp", "attrRegex", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "allow<PERSON><PERSON><PERSON><PERSON>", "elements", "el", "el<PERSON>ame", "attributeList", "allowedAttributes", "innerHTML", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "DISALLOWED_ATTRIBUTES", "animation", "template", "title", "delay", "html", "fallbackPlacement", "sanitize", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "HIDE", "HIDDEN", "SHOW", "SHOWN", "INSERTED", "CLICK", "FOCUSIN", "FOCUSOUT", "MOUSEENTER", "MOUSELEAVE", "CLASS_NAME_MODAL", "HOVER_STATE_SHOW", "HOVER_STATE_OUT", "SELECTOR_TOOLTIP_INNER", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "_hideModalHandler", "isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "attachment", "_getAttachment", "_addAttachmentClass", "_get<PERSON><PERSON><PERSON>", "_fixTransition", "prevHoverState", "_cleanTipClass", "getTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "textContent", "defaultBsConfig", "behavior", "arrow", "onCreate", "originalPlacement", "_handlePopperPlacementChange", "onUpdate", "triggers", "eventIn", "eventOut", "_fixTitle", "titleType", "dataAttributes", "dataAttr", "tabClass", "map", "token", "tClass", "popperData", "popper", "initConfigAnimation", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "method", "EVENT_ACTIVATE", "EVENT_SCROLL", "CLASS_NAME_DROPDOWN_ITEM", "SELECTOR_DATA_SPY", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "METHOD_OFFSET", "METHOD_POSITION", "ScrollSpy", "_scrollElement", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "targetSelector", "targetBCR", "height", "item", "sort", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "isActiveTarget", "queries", "link", "join", "listGroup", "navItem", "node", "spy", "CLASS_NAME_DROPDOWN_MENU", "SELECTOR_ACTIVE_UL", "SELECTOR_DROPDOWN_ACTIVE_CHILD", "Tab", "listElement", "itemSelector", "activeElements", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "dropdownElement", "dropdown", "CLASS_NAME_HIDE", "CLASS_NAME_SHOWING", "autohide", "Toast", "_clearTimeout"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAMA,OAAO,GAAG,OAAhB;AACA,IAAMC,uBAAuB,GAAG,IAAhC;AACA,IAAMC,cAAc,GAAG,eAAvB;;AAGA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAAC,GAAG,EAAI;AACpB,MAAIA,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAKC,SAA5B,EAAuC;AACrC,gBAAUD,GAAV;AACD;;AAED,SAAO,GAAGE,QAAH,CAAYC,IAAZ,CAAiBH,GAAjB,EAAsBI,KAAtB,CAA4B,aAA5B,EAA2C,CAA3C,EAA8CC,WAA9C,EAAP;AACD,CAND;AAQA;AACA;AACA;AACA;AACA;;;AAEA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAAC,MAAM,EAAI;AACvB,KAAG;AACDA,IAAAA,MAAM,IAAIC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgBd,OAA3B,CAAV;AACD,GAFD,QAESe,QAAQ,CAACC,cAAT,CAAwBL,MAAxB,CAFT;;AAIA,SAAOA,MAAP;AACD,CAND;;AAQA,IAAMM,WAAW,GAAG,SAAdA,WAAc,CAAAC,OAAO,EAAI;AAC7B,MAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,aAArB,CAAf;;AAEA,MAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;AACjC,QAAME,QAAQ,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAjB;AAEAD,IAAAA,QAAQ,GAAGE,QAAQ,IAAIA,QAAQ,KAAK,GAAzB,GAA+BA,QAAQ,CAACC,IAAT,EAA/B,GAAiD,IAA5D;AACD;;AAED,SAAOH,QAAP;AACD,CAVD;;AAYA,IAAMI,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAAL,OAAO,EAAI;AACxC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;;AAEA,MAAIC,QAAJ,EAAc;AACZ,WAAOJ,QAAQ,CAACS,aAAT,CAAuBL,QAAvB,IAAmCA,QAAnC,GAA8C,IAArD;AACD;;AAED,SAAO,IAAP;AACD,CARD;;AAUA,IAAMM,sBAAsB,GAAG,SAAzBA,sBAAyB,CAAAP,OAAO,EAAI;AACxC,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B;AAEA,SAAOC,QAAQ,GAAGJ,QAAQ,CAACS,aAAT,CAAuBL,QAAvB,CAAH,GAAsC,IAArD;AACD,CAJD;;AAMA,IAAMO,gCAAgC,GAAG,SAAnCA,gCAAmC,CAAAR,OAAO,EAAI;AAClD,MAAI,CAACA,OAAL,EAAc;AACZ,WAAO,CAAP;AACD,GAHiD;;;AAAA,8BAS9CS,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,CAT8C;AAAA,MAOhDW,kBAPgD,yBAOhDA,kBAPgD;AAAA,MAQhDC,eARgD,yBAQhDA,eARgD;;AAWlD,MAAMC,uBAAuB,GAAGC,UAAU,CAACH,kBAAD,CAA1C;AACA,MAAMI,oBAAoB,GAAGD,UAAU,CAACF,eAAD,CAAvC,CAZkD;;AAelD,MAAI,CAACC,uBAAD,IAA4B,CAACE,oBAAjC,EAAuD;AACrD,WAAO,CAAP;AACD,GAjBiD;;;AAoBlDJ,EAAAA,kBAAkB,GAAGA,kBAAkB,CAACK,KAAnB,CAAyB,GAAzB,EAA8B,CAA9B,CAArB;AACAJ,EAAAA,eAAe,GAAGA,eAAe,CAACI,KAAhB,CAAsB,GAAtB,EAA2B,CAA3B,CAAlB;AAEA,SAAO,CAACF,UAAU,CAACH,kBAAD,CAAV,GAAiCG,UAAU,CAACF,eAAD,CAA5C,IAAiE7B,uBAAxE;AACD,CAxBD;;AA0BA,IAAMkC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAAAjB,OAAO,EAAI;AACtCA,EAAAA,OAAO,CAACkB,aAAR,CAAsB,IAAIC,KAAJ,CAAUnC,cAAV,CAAtB;AACD,CAFD;;AAIA,IAAMoC,SAAS,GAAG,SAAZA,SAAY,CAAAlC,GAAG;AAAA,SAAI,CAACA,GAAG,CAAC,CAAD,CAAH,IAAUA,GAAX,EAAgBmC,QAApB;AAAA,CAArB;;AAEA,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAuB,CAACtB,OAAD,EAAUuB,QAAV,EAAuB;AAClD,MAAIC,MAAM,GAAG,KAAb;AACA,MAAMC,eAAe,GAAG,CAAxB;AACA,MAAMC,gBAAgB,GAAGH,QAAQ,GAAGE,eAApC;;AACA,WAASE,QAAT,GAAoB;AAClBH,IAAAA,MAAM,GAAG,IAAT;AACAxB,IAAAA,OAAO,CAAC4B,mBAAR,CAA4B5C,cAA5B,EAA4C2C,QAA5C;AACD;;AAED3B,EAAAA,OAAO,CAAC6B,gBAAR,CAAyB7C,cAAzB,EAAyC2C,QAAzC;AACAG,EAAAA,UAAU,CAAC,YAAM;AACf,QAAI,CAACN,MAAL,EAAa;AACXP,MAAAA,oBAAoB,CAACjB,OAAD,CAApB;AACD;AACF,GAJS,EAIP0B,gBAJO,CAAV;AAKD,CAfD;;AAiBA,IAAMK,eAAe,GAAG,SAAlBA,eAAkB,CAACC,aAAD,EAAgBC,MAAhB,EAAwBC,WAAxB,EAAwC;AAC9DC,EAAAA,MAAM,CAACC,IAAP,CAAYF,WAAZ,EAAyBG,OAAzB,CAAiC,UAAAC,QAAQ,EAAI;AAC3C,QAAMC,aAAa,GAAGL,WAAW,CAACI,QAAD,CAAjC;AACA,QAAME,KAAK,GAAGP,MAAM,CAACK,QAAD,CAApB;AACA,QAAMG,SAAS,GAAGD,KAAK,IAAIpB,SAAS,CAACoB,KAAD,CAAlB,GAChB,SADgB,GAEhBvD,MAAM,CAACuD,KAAD,CAFR;;AAIA,QAAI,CAAC,IAAIE,MAAJ,CAAWH,aAAX,EAA0BI,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;AAC9C,YAAM,IAAIG,KAAJ,CACDZ,aAAa,CAACa,WAAd,EAAH,yBACWP,QADX,2BACuCG,SADvC,sCAEsBF,aAFtB,SADI,CAAN;AAID;AACF,GAbD;AAcD,CAfD;;AAiBA,IAAMO,SAAS,GAAG,SAAZA,SAAY,CAAA9C,OAAO,EAAI;AAC3B,MAAI,CAACA,OAAL,EAAc;AACZ,WAAO,KAAP;AACD;;AAED,MAAIA,OAAO,CAAC+C,KAAR,IAAiB/C,OAAO,CAACgD,UAAzB,IAAuChD,OAAO,CAACgD,UAAR,CAAmBD,KAA9D,EAAqE;AACnE,QAAME,YAAY,GAAGvC,gBAAgB,CAACV,OAAD,CAArC;AACA,QAAMkD,eAAe,GAAGxC,gBAAgB,CAACV,OAAO,CAACgD,UAAT,CAAxC;AAEA,WAAOC,YAAY,CAACE,OAAb,KAAyB,MAAzB,IACLD,eAAe,CAACC,OAAhB,KAA4B,MADvB,IAELF,YAAY,CAACG,UAAb,KAA4B,QAF9B;AAGD;;AAED,SAAO,KAAP;AACD,CAfD;;AAiBA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB,CAAArD,OAAO,EAAI;AAChC,MAAI,CAACH,QAAQ,CAACyD,eAAT,CAAyBC,YAA9B,EAA4C;AAC1C,WAAO,IAAP;AACD,GAH+B;;;AAMhC,MAAI,OAAOvD,OAAO,CAACwD,WAAf,KAA+B,UAAnC,EAA+C;AAC7C,QAAMC,IAAI,GAAGzD,OAAO,CAACwD,WAAR,EAAb;AACA,WAAOC,IAAI,YAAYC,UAAhB,GAA6BD,IAA7B,GAAoC,IAA3C;AACD;;AAED,MAAIzD,OAAO,YAAY0D,UAAvB,EAAmC;AACjC,WAAO1D,OAAP;AACD,GAb+B;;;AAgBhC,MAAI,CAACA,OAAO,CAACgD,UAAb,EAAyB;AACvB,WAAO,IAAP;AACD;;AAED,SAAOK,cAAc,CAACrD,OAAO,CAACgD,UAAT,CAArB;AACD,CArBD;;AAuBA,IAAMW,IAAI,GAAG,SAAPA,IAAO;AAAA,SAAM,YAAY,EAAlB;AAAA,CAAb;;AAEA,IAAMC,MAAM,GAAG,SAATA,MAAS,CAAA5D,OAAO;AAAA,SAAIA,OAAO,CAAC6D,YAAZ;AAAA,CAAtB;;AAEA,IAAMC,SAAS,GAAG,SAAZA,SAAY,GAAM;AAAA,gBACHrD,MADG;AAAA,MACdsD,MADc,WACdA,MADc;;AAGtB,MAAIA,MAAM,IAAI,CAAClE,QAAQ,CAACmE,IAAT,CAAcC,YAAd,CAA2B,gBAA3B,CAAf,EAA6D;AAC3D,WAAOF,MAAP;AACD;;AAED,SAAO,IAAP;AACD,CARD;;AAUA,IAAMG,kBAAkB,GAAG,SAArBA,kBAAqB,CAAAC,QAAQ,EAAI;AACrC,MAAItE,QAAQ,CAACuE,UAAT,KAAwB,SAA5B,EAAuC;AACrCvE,IAAAA,QAAQ,CAACgC,gBAAT,CAA0B,kBAA1B,EAA8CsC,QAA9C;AACD,GAFD,MAEO;AACLA,IAAAA,QAAQ;AACT;AACF,CAND;;ACtLA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAEA,IAAME,OAAO,GAAI,YAAM;AACrB,MAAMC,SAAS,GAAG,EAAlB;AACA,MAAIC,EAAE,GAAG,CAAT;AACA,SAAO;AACLC,IAAAA,GADK,eACDxE,OADC,EACQyE,GADR,EACaC,IADb,EACmB;AACtB,UAAI,OAAO1E,OAAO,CAAC2E,KAAf,KAAyB,WAA7B,EAA0C;AACxC3E,QAAAA,OAAO,CAAC2E,KAAR,GAAgB;AACdF,UAAAA,GAAG,EAAHA,GADc;AAEdF,UAAAA,EAAE,EAAFA;AAFc,SAAhB;AAIAA,QAAAA,EAAE;AACH;;AAEDD,MAAAA,SAAS,CAACtE,OAAO,CAAC2E,KAAR,CAAcJ,EAAf,CAAT,GAA8BG,IAA9B;AACD,KAXI;AAYLE,IAAAA,GAZK,eAYD5E,OAZC,EAYQyE,GAZR,EAYa;AAChB,UAAI,CAACzE,OAAD,IAAY,OAAOA,OAAO,CAAC2E,KAAf,KAAyB,WAAzC,EAAsD;AACpD,eAAO,IAAP;AACD;;AAED,UAAME,aAAa,GAAG7E,OAAO,CAAC2E,KAA9B;;AACA,UAAIE,aAAa,CAACJ,GAAd,KAAsBA,GAA1B,EAA+B;AAC7B,eAAOH,SAAS,CAACO,aAAa,CAACN,EAAf,CAAhB;AACD;;AAED,aAAO,IAAP;AACD,KAvBI;AAwBLO,IAAAA,MAxBK,mBAwBE9E,OAxBF,EAwBWyE,GAxBX,EAwBgB;AACnB,UAAI,OAAOzE,OAAO,CAAC2E,KAAf,KAAyB,WAA7B,EAA0C;AACxC;AACD;;AAED,UAAME,aAAa,GAAG7E,OAAO,CAAC2E,KAA9B;;AACA,UAAIE,aAAa,CAACJ,GAAd,KAAsBA,GAA1B,EAA+B;AAC7B,eAAOH,SAAS,CAACO,aAAa,CAACN,EAAf,CAAhB;AACA,eAAOvE,OAAO,CAAC2E,KAAf;AACD;AACF;AAlCI,GAAP;AAoCD,CAvCe,EAAhB;;AAyCA,IAAMI,IAAI,GAAG;AACXC,EAAAA,OADW,mBACHC,QADG,EACOR,GADP,EACYC,IADZ,EACkB;AAC3BL,IAAAA,OAAO,CAACG,GAAR,CAAYS,QAAZ,EAAsBR,GAAtB,EAA2BC,IAA3B;AACD,GAHU;AAIXQ,EAAAA,OAJW,mBAIHD,QAJG,EAIOR,GAJP,EAIY;AACrB,WAAOJ,OAAO,CAACO,GAAR,CAAYK,QAAZ,EAAsBR,GAAtB,CAAP;AACD,GANU;AAOXU,EAAAA,UAPW,sBAOAF,QAPA,EAOUR,GAPV,EAOe;AACxBJ,IAAAA,OAAO,CAACS,MAAR,CAAeG,QAAf,EAAyBR,GAAzB;AACD;AATU,CAAb;;ACtDA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;;AAEA,IAAMW,cAAc,GAAG,oBAAvB;AACA,IAAMC,cAAc,GAAG,MAAvB;AACA,IAAMC,aAAa,GAAG,QAAtB;AACA,IAAMC,aAAa,GAAG,EAAtB;;AACA,IAAIC,QAAQ,GAAG,CAAf;AACA,IAAMC,YAAY,GAAG;AACnBC,EAAAA,UAAU,EAAE,WADO;AAEnBC,EAAAA,UAAU,EAAE;AAFO,CAArB;AAIA,IAAMC,YAAY,GAAG,CACnB,OADmB,EAEnB,UAFmB,EAGnB,SAHmB,EAInB,WAJmB,EAKnB,aALmB,EAMnB,YANmB,EAOnB,gBAPmB,EAQnB,WARmB,EASnB,UATmB,EAUnB,WAVmB,EAWnB,aAXmB,EAYnB,WAZmB,EAanB,SAbmB,EAcnB,UAdmB,EAenB,OAfmB,EAgBnB,mBAhBmB,EAiBnB,YAjBmB,EAkBnB,WAlBmB,EAmBnB,UAnBmB,EAoBnB,aApBmB,EAqBnB,aArBmB,EAsBnB,aAtBmB,EAuBnB,WAvBmB,EAwBnB,cAxBmB,EAyBnB,eAzBmB,EA0BnB,cA1BmB,EA2BnB,eA3BmB,EA4BnB,YA5BmB,EA6BnB,OA7BmB,EA8BnB,MA9BmB,EA+BnB,QA/BmB,EAgCnB,OAhCmB,EAiCnB,QAjCmB,EAkCnB,QAlCmB,EAmCnB,SAnCmB,EAoCnB,UApCmB,EAqCnB,MArCmB,EAsCnB,QAtCmB,EAuCnB,cAvCmB,EAwCnB,QAxCmB,EAyCnB,MAzCmB,EA0CnB,kBA1CmB,EA2CnB,kBA3CmB,EA4CnB,OA5CmB,EA6CnB,OA7CmB,EA8CnB,QA9CmB,CAArB;AAiDA;AACA;AACA;AACA;AACA;;AAEA,SAASC,WAAT,CAAqB7F,OAArB,EAA8B8F,GAA9B,EAAmC;AACjC,SAAQA,GAAG,IAAOA,GAAP,UAAeN,QAAQ,EAA3B,IAAoCxF,OAAO,CAACwF,QAA5C,IAAwDA,QAAQ,EAAvE;AACD;;AAED,SAASO,QAAT,CAAkB/F,OAAlB,EAA2B;AACzB,MAAM8F,GAAG,GAAGD,WAAW,CAAC7F,OAAD,CAAvB;AAEAA,EAAAA,OAAO,CAACwF,QAAR,GAAmBM,GAAnB;AACAP,EAAAA,aAAa,CAACO,GAAD,CAAb,GAAqBP,aAAa,CAACO,GAAD,CAAb,IAAsB,EAA3C;AAEA,SAAOP,aAAa,CAACO,GAAD,CAApB;AACD;;AAED,SAASE,gBAAT,CAA0BhG,OAA1B,EAAmCiG,EAAnC,EAAuC;AACrC,SAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;AAC7BA,IAAAA,KAAK,CAACC,cAAN,GAAuBpG,OAAvB;;AAEA,QAAIkG,OAAO,CAACG,MAAZ,EAAoB;AAClBC,MAAAA,YAAY,CAACC,GAAb,CAAiBvG,OAAjB,EAA0BmG,KAAK,CAACK,IAAhC,EAAsCP,EAAtC;AACD;;AAED,WAAOA,EAAE,CAACQ,KAAH,CAASzG,OAAT,EAAkB,CAACmG,KAAD,CAAlB,CAAP;AACD,GARD;AASD;;AAED,SAASO,0BAAT,CAAoC1G,OAApC,EAA6CC,QAA7C,EAAuDgG,EAAvD,EAA2D;AACzD,SAAO,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;AAC7B,QAAMQ,WAAW,GAAG3G,OAAO,CAAC4G,gBAAR,CAAyB3G,QAAzB,CAApB;;AAEA,aAAW4G,MAAX,GAAsBV,KAAtB,CAAWU,MAAX,EAA6BA,MAAM,IAAIA,MAAM,KAAK,IAAlD,EAAwDA,MAAM,GAAGA,MAAM,CAAC7D,UAAxE,EAAoF;AAClF,WAAK,IAAI8D,CAAC,GAAGH,WAAW,CAACI,MAAzB,EAAiCD,CAAC,EAAlC,GAAuC;AACrC,YAAIH,WAAW,CAACG,CAAD,CAAX,KAAmBD,MAAvB,EAA+B;AAC7BV,UAAAA,KAAK,CAACC,cAAN,GAAuBS,MAAvB;;AAEA,cAAIX,OAAO,CAACG,MAAZ,EAAoB;AAClBC,YAAAA,YAAY,CAACC,GAAb,CAAiBvG,OAAjB,EAA0BmG,KAAK,CAACK,IAAhC,EAAsCP,EAAtC;AACD;;AAED,iBAAOA,EAAE,CAACQ,KAAH,CAASI,MAAT,EAAiB,CAACV,KAAD,CAAjB,CAAP;AACD;AACF;AACF,KAf4B;;;AAkB7B,WAAO,IAAP;AACD,GAnBD;AAoBD;;AAED,SAASa,WAAT,CAAqBC,MAArB,EAA6Bf,OAA7B,EAAsCgB,kBAAtC,EAAiE;AAAA,MAA3BA,kBAA2B;AAA3BA,IAAAA,kBAA2B,GAAN,IAAM;AAAA;;AAC/D,MAAMC,YAAY,GAAGhF,MAAM,CAACC,IAAP,CAAY6E,MAAZ,CAArB;;AAEA,OAAK,IAAIH,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGD,YAAY,CAACJ,MAAnC,EAA2CD,CAAC,GAAGM,GAA/C,EAAoDN,CAAC,EAArD,EAAyD;AACvD,QAAMX,KAAK,GAAGc,MAAM,CAACE,YAAY,CAACL,CAAD,CAAb,CAApB;;AAEA,QAAIX,KAAK,CAACkB,eAAN,KAA0BnB,OAA1B,IAAqCC,KAAK,CAACe,kBAAN,KAA6BA,kBAAtE,EAA0F;AACxF,aAAOf,KAAP;AACD;AACF;;AAED,SAAO,IAAP;AACD;;AAED,SAASmB,eAAT,CAAyBC,iBAAzB,EAA4CrB,OAA5C,EAAqDsB,YAArD,EAAmE;AACjE,MAAMC,UAAU,GAAG,OAAOvB,OAAP,KAAmB,QAAtC;AACA,MAAMmB,eAAe,GAAGI,UAAU,GAAGD,YAAH,GAAkBtB,OAApD,CAFiE;;AAKjE,MAAIwB,SAAS,GAAGH,iBAAiB,CAACI,OAAlB,CAA0BtC,cAA1B,EAA0C,EAA1C,CAAhB;AACA,MAAMuC,MAAM,GAAGnC,YAAY,CAACiC,SAAD,CAA3B;;AAEA,MAAIE,MAAJ,EAAY;AACVF,IAAAA,SAAS,GAAGE,MAAZ;AACD;;AAED,MAAMC,QAAQ,GAAGjC,YAAY,CAACkC,OAAb,CAAqBJ,SAArB,IAAkC,CAAC,CAApD;;AAEA,MAAI,CAACG,QAAL,EAAe;AACbH,IAAAA,SAAS,GAAGH,iBAAZ;AACD;;AAED,SAAO,CAACE,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,CAAP;AACD;;AAED,SAASK,UAAT,CAAoB/H,OAApB,EAA6BuH,iBAA7B,EAAgDrB,OAAhD,EAAyDsB,YAAzD,EAAuEnB,MAAvE,EAA+E;AAC7E,MAAI,OAAOkB,iBAAP,KAA6B,QAA7B,IAAyC,CAACvH,OAA9C,EAAuD;AACrD;AACD;;AAED,MAAI,CAACkG,OAAL,EAAc;AACZA,IAAAA,OAAO,GAAGsB,YAAV;AACAA,IAAAA,YAAY,GAAG,IAAf;AACD;;AAR4E,yBAU5BF,eAAe,CAACC,iBAAD,EAAoBrB,OAApB,EAA6BsB,YAA7B,CAVa;AAAA,MAUtEC,UAVsE;AAAA,MAU1DJ,eAV0D;AAAA,MAUzCK,SAVyC;;AAW7E,MAAMT,MAAM,GAAGlB,QAAQ,CAAC/F,OAAD,CAAvB;AACA,MAAMgI,QAAQ,GAAGf,MAAM,CAACS,SAAD,CAAN,KAAsBT,MAAM,CAACS,SAAD,CAAN,GAAoB,EAA1C,CAAjB;AACA,MAAMO,UAAU,GAAGjB,WAAW,CAACgB,QAAD,EAAWX,eAAX,EAA4BI,UAAU,GAAGvB,OAAH,GAAa,IAAnD,CAA9B;;AAEA,MAAI+B,UAAJ,EAAgB;AACdA,IAAAA,UAAU,CAAC5B,MAAX,GAAoB4B,UAAU,CAAC5B,MAAX,IAAqBA,MAAzC;AAEA;AACD;;AAED,MAAMP,GAAG,GAAGD,WAAW,CAACwB,eAAD,EAAkBE,iBAAiB,CAACI,OAAlB,CAA0BvC,cAA1B,EAA0C,EAA1C,CAAlB,CAAvB;AACA,MAAMa,EAAE,GAAGwB,UAAU,GACnBf,0BAA0B,CAAC1G,OAAD,EAAUkG,OAAV,EAAmBsB,YAAnB,CADP,GAEnBxB,gBAAgB,CAAChG,OAAD,EAAUkG,OAAV,CAFlB;AAIAD,EAAAA,EAAE,CAACiB,kBAAH,GAAwBO,UAAU,GAAGvB,OAAH,GAAa,IAA/C;AACAD,EAAAA,EAAE,CAACoB,eAAH,GAAqBA,eAArB;AACApB,EAAAA,EAAE,CAACI,MAAH,GAAYA,MAAZ;AACAJ,EAAAA,EAAE,CAACT,QAAH,GAAcM,GAAd;AACAkC,EAAAA,QAAQ,CAAClC,GAAD,CAAR,GAAgBG,EAAhB;AAEAjG,EAAAA,OAAO,CAAC6B,gBAAR,CAAyB6F,SAAzB,EAAoCzB,EAApC,EAAwCwB,UAAxC;AACD;;AAED,SAASS,aAAT,CAAuBlI,OAAvB,EAAgCiH,MAAhC,EAAwCS,SAAxC,EAAmDxB,OAAnD,EAA4DgB,kBAA5D,EAAgF;AAC9E,MAAMjB,EAAE,GAAGe,WAAW,CAACC,MAAM,CAACS,SAAD,CAAP,EAAoBxB,OAApB,EAA6BgB,kBAA7B,CAAtB;;AAEA,MAAI,CAACjB,EAAL,EAAS;AACP;AACD;;AAEDjG,EAAAA,OAAO,CAAC4B,mBAAR,CAA4B8F,SAA5B,EAAuCzB,EAAvC,EAA2CkC,OAAO,CAACjB,kBAAD,CAAlD;AACA,SAAOD,MAAM,CAACS,SAAD,CAAN,CAAkBzB,EAAE,CAACT,QAArB,CAAP;AACD;;AAED,SAAS4C,wBAAT,CAAkCpI,OAAlC,EAA2CiH,MAA3C,EAAmDS,SAAnD,EAA8DW,SAA9D,EAAyE;AACvE,MAAMC,iBAAiB,GAAGrB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;AAEAvF,EAAAA,MAAM,CAACC,IAAP,CAAYkG,iBAAZ,EAA+BjG,OAA/B,CAAuC,UAAAkG,UAAU,EAAI;AACnD,QAAIA,UAAU,CAACT,OAAX,CAAmBO,SAAnB,IAAgC,CAAC,CAArC,EAAwC;AACtC,UAAMlC,KAAK,GAAGmC,iBAAiB,CAACC,UAAD,CAA/B;AAEAL,MAAAA,aAAa,CAAClI,OAAD,EAAUiH,MAAV,EAAkBS,SAAlB,EAA6BvB,KAAK,CAACkB,eAAnC,EAAoDlB,KAAK,CAACe,kBAA1D,CAAb;AACD;AACF,GAND;AAOD;;AAED,IAAMZ,YAAY,GAAG;AACnBkC,EAAAA,EADmB,cAChBxI,OADgB,EACPmG,KADO,EACAD,OADA,EACSsB,YADT,EACuB;AACxCO,IAAAA,UAAU,CAAC/H,OAAD,EAAUmG,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC,KAAxC,CAAV;AACD,GAHkB;AAKnBiB,EAAAA,GALmB,eAKfzI,OALe,EAKNmG,KALM,EAKCD,OALD,EAKUsB,YALV,EAKwB;AACzCO,IAAAA,UAAU,CAAC/H,OAAD,EAAUmG,KAAV,EAAiBD,OAAjB,EAA0BsB,YAA1B,EAAwC,IAAxC,CAAV;AACD,GAPkB;AASnBjB,EAAAA,GATmB,eASfvG,OATe,EASNuH,iBATM,EASarB,OATb,EASsBsB,YATtB,EASoC;AACrD,QAAI,OAAOD,iBAAP,KAA6B,QAA7B,IAAyC,CAACvH,OAA9C,EAAuD;AACrD;AACD;;AAHoD,4BAKJsH,eAAe,CAACC,iBAAD,EAAoBrB,OAApB,EAA6BsB,YAA7B,CALX;AAAA,QAK9CC,UAL8C;AAAA,QAKlCJ,eALkC;AAAA,QAKjBK,SALiB;;AAMrD,QAAMgB,WAAW,GAAGhB,SAAS,KAAKH,iBAAlC;AACA,QAAMN,MAAM,GAAGlB,QAAQ,CAAC/F,OAAD,CAAvB;AACA,QAAM2I,WAAW,GAAGpB,iBAAiB,CAACqB,MAAlB,CAAyB,CAAzB,MAAgC,GAApD;;AAEA,QAAI,OAAOvB,eAAP,KAA2B,WAA/B,EAA4C;AAC1C;AACA,UAAI,CAACJ,MAAD,IAAW,CAACA,MAAM,CAACS,SAAD,CAAtB,EAAmC;AACjC;AACD;;AAEDQ,MAAAA,aAAa,CAAClI,OAAD,EAAUiH,MAAV,EAAkBS,SAAlB,EAA6BL,eAA7B,EAA8CI,UAAU,GAAGvB,OAAH,GAAa,IAArE,CAAb;AACA;AACD;;AAED,QAAIyC,WAAJ,EAAiB;AACfxG,MAAAA,MAAM,CAACC,IAAP,CAAY6E,MAAZ,EAAoB5E,OAApB,CAA4B,UAAAwG,YAAY,EAAI;AAC1CT,QAAAA,wBAAwB,CAACpI,OAAD,EAAUiH,MAAV,EAAkB4B,YAAlB,EAAgCtB,iBAAiB,CAACuB,KAAlB,CAAwB,CAAxB,CAAhC,CAAxB;AACD,OAFD;AAGD;;AAED,QAAMR,iBAAiB,GAAGrB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C;AACAvF,IAAAA,MAAM,CAACC,IAAP,CAAYkG,iBAAZ,EAA+BjG,OAA/B,CAAuC,UAAA0G,WAAW,EAAI;AACpD,UAAMR,UAAU,GAAGQ,WAAW,CAACpB,OAAZ,CAAoBrC,aAApB,EAAmC,EAAnC,CAAnB;;AAEA,UAAI,CAACoD,WAAD,IAAgBnB,iBAAiB,CAACO,OAAlB,CAA0BS,UAA1B,IAAwC,CAAC,CAA7D,EAAgE;AAC9D,YAAMpC,KAAK,GAAGmC,iBAAiB,CAACS,WAAD,CAA/B;AAEAb,QAAAA,aAAa,CAAClI,OAAD,EAAUiH,MAAV,EAAkBS,SAAlB,EAA6BvB,KAAK,CAACkB,eAAnC,EAAoDlB,KAAK,CAACe,kBAA1D,CAAb;AACD;AACF,KARD;AASD,GA7CkB;AA+CnB8B,EAAAA,OA/CmB,mBA+CXhJ,OA/CW,EA+CFmG,KA/CE,EA+CK8C,IA/CL,EA+CW;AAC5B,QAAI,OAAO9C,KAAP,KAAiB,QAAjB,IAA6B,CAACnG,OAAlC,EAA2C;AACzC,aAAO,IAAP;AACD;;AAED,QAAMkJ,CAAC,GAAGpF,SAAS,EAAnB;AACA,QAAM4D,SAAS,GAAGvB,KAAK,CAACwB,OAAN,CAActC,cAAd,EAA8B,EAA9B,CAAlB;AACA,QAAMqD,WAAW,GAAGvC,KAAK,KAAKuB,SAA9B;AACA,QAAMG,QAAQ,GAAGjC,YAAY,CAACkC,OAAb,CAAqBJ,SAArB,IAAkC,CAAC,CAApD;AAEA,QAAIyB,WAAJ;AACA,QAAIC,OAAO,GAAG,IAAd;AACA,QAAIC,cAAc,GAAG,IAArB;AACA,QAAIC,gBAAgB,GAAG,KAAvB;AACA,QAAIC,GAAG,GAAG,IAAV;;AAEA,QAAIb,WAAW,IAAIQ,CAAnB,EAAsB;AACpBC,MAAAA,WAAW,GAAGD,CAAC,CAAC/H,KAAF,CAAQgF,KAAR,EAAe8C,IAAf,CAAd;AAEAC,MAAAA,CAAC,CAAClJ,OAAD,CAAD,CAAWgJ,OAAX,CAAmBG,WAAnB;AACAC,MAAAA,OAAO,GAAG,CAACD,WAAW,CAACK,oBAAZ,EAAX;AACAH,MAAAA,cAAc,GAAG,CAACF,WAAW,CAACM,6BAAZ,EAAlB;AACAH,MAAAA,gBAAgB,GAAGH,WAAW,CAACO,kBAAZ,EAAnB;AACD;;AAED,QAAI7B,QAAJ,EAAc;AACZ0B,MAAAA,GAAG,GAAG1J,QAAQ,CAAC8J,WAAT,CAAqB,YAArB,CAAN;AACAJ,MAAAA,GAAG,CAACK,SAAJ,CAAclC,SAAd,EAAyB0B,OAAzB,EAAkC,IAAlC;AACD,KAHD,MAGO;AACLG,MAAAA,GAAG,GAAG,IAAIM,WAAJ,CAAgB1D,KAAhB,EAAuB;AAC3BiD,QAAAA,OAAO,EAAPA,OAD2B;AAE3BU,QAAAA,UAAU,EAAE;AAFe,OAAvB,CAAN;AAID,KAjC2B;;;AAoC5B,QAAI,OAAOb,IAAP,KAAgB,WAApB,EAAiC;AAC/B9G,MAAAA,MAAM,CAACC,IAAP,CAAY6G,IAAZ,EAAkB5G,OAAlB,CAA0B,UAAAoC,GAAG,EAAI;AAC/BtC,QAAAA,MAAM,CAAC4H,cAAP,CAAsBR,GAAtB,EAA2B9E,GAA3B,EAAgC;AAC9BG,UAAAA,GAD8B,iBACxB;AACJ,mBAAOqE,IAAI,CAACxE,GAAD,CAAX;AACD;AAH6B,SAAhC;AAKD,OAND;AAOD;;AAED,QAAI6E,gBAAJ,EAAsB;AACpBC,MAAAA,GAAG,CAACS,cAAJ;AACD;;AAED,QAAIX,cAAJ,EAAoB;AAClBrJ,MAAAA,OAAO,CAACkB,aAAR,CAAsBqI,GAAtB;AACD;;AAED,QAAIA,GAAG,CAACD,gBAAJ,IAAwB,OAAOH,WAAP,KAAuB,WAAnD,EAAgE;AAC9DA,MAAAA,WAAW,CAACa,cAAZ;AACD;;AAED,WAAOT,GAAP;AACD;AA1GkB,CAArB;;AC1MA;AACA;AACA;AACA;AACA;;AAEA,IAAMU,IAAI,GAAG,OAAb;AACA,IAAMC,OAAO,GAAG,cAAhB;AACA,IAAMC,QAAQ,GAAG,UAAjB;AACA,IAAMC,SAAS,SAAOD,QAAtB;AACA,IAAME,YAAY,GAAG,WAArB;AAEA,IAAMC,gBAAgB,GAAG,wBAAzB;AAEA,IAAMC,WAAW,aAAWH,SAA5B;AACA,IAAMI,YAAY,cAAYJ,SAA9B;AACA,IAAMK,oBAAoB,aAAWL,SAAX,GAAuBC,YAAjD;AAEA,IAAMK,eAAe,GAAG,OAAxB;AACA,IAAMC,cAAc,GAAG,MAAvB;AACA,IAAMC,cAAc,GAAG,MAAvB;AAEA;AACA;AACA;AACA;AACA;;IAEMC;AACJ,iBAAY7K,OAAZ,EAAqB;AACnB,SAAK8K,QAAL,GAAgB9K,OAAhB;;AAEA,QAAI,KAAK8K,QAAT,EAAmB;AACjB/F,MAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,QAAtB,EAAgC,IAAhC;AACD;AACF;;;;;AAQD;SAEAY,QAAA,eAAM/K,OAAN,EAAe;AACb,QAAMgL,WAAW,GAAGhL,OAAO,GAAG,KAAKiL,eAAL,CAAqBjL,OAArB,CAAH,GAAmC,KAAK8K,QAAnE;;AACA,QAAMI,WAAW,GAAG,KAAKC,kBAAL,CAAwBH,WAAxB,CAApB;;AAEA,QAAIE,WAAW,KAAK,IAAhB,IAAwBA,WAAW,CAAC5B,gBAAxC,EAA0D;AACxD;AACD;;AAED,SAAK8B,cAAL,CAAoBJ,WAApB;AACD;;SAEDK,UAAA,mBAAU;AACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,QAA/B;AACA,SAAKW,QAAL,GAAgB,IAAhB;AACD;;;SAIDG,kBAAA,yBAAgBjL,OAAhB,EAAyB;AACvB,WAAOO,sBAAsB,CAACP,OAAD,CAAtB,IAAmCA,OAAO,CAACsL,OAAR,OAAoBZ,eAApB,CAA1C;AACD;;SAEDS,qBAAA,4BAAmBnL,OAAnB,EAA4B;AAC1B,WAAOsG,YAAY,CAAC0C,OAAb,CAAqBhJ,OAArB,EAA8BuK,WAA9B,CAAP;AACD;;SAEDa,iBAAA,wBAAepL,OAAf,EAAwB;AAAA;;AACtBA,IAAAA,OAAO,CAACuL,SAAR,CAAkBC,MAAlB,CAAyBZ,cAAzB;;AAEA,QAAI,CAAC5K,OAAO,CAACuL,SAAR,CAAkBE,QAAlB,CAA2Bd,cAA3B,CAAL,EAAiD;AAC/C,WAAKe,eAAL,CAAqB1L,OAArB;;AACA;AACD;;AAED,QAAMW,kBAAkB,GAAGH,gCAAgC,CAACR,OAAD,CAA3D;AAEAsG,IAAAA,YAAY,CAACmC,GAAb,CAAiBzI,OAAjB,EAA0BhB,cAA1B,EAA0C;AAAA,aAAM,KAAI,CAAC0M,eAAL,CAAqB1L,OAArB,CAAN;AAAA,KAA1C;AACAsB,IAAAA,oBAAoB,CAACtB,OAAD,EAAUW,kBAAV,CAApB;AACD;;SAED+K,kBAAA,yBAAgB1L,OAAhB,EAAyB;AACvB,QAAIA,OAAO,CAACgD,UAAZ,EAAwB;AACtBhD,MAAAA,OAAO,CAACgD,UAAR,CAAmB2I,WAAnB,CAA+B3L,OAA/B;AACD;;AAEDsG,IAAAA,YAAY,CAAC0C,OAAb,CAAqBhJ,OAArB,EAA8BwK,YAA9B;AACD;;;QAIMoB,kBAAP,yBAAuB3J,MAAvB,EAA+B;AAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;AAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,QAAnB,CAAX;;AAEA,UAAI,CAACzF,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAImG,KAAJ,CAAU,IAAV,CAAP;AACD;;AAED,UAAI5I,MAAM,KAAK,OAAf,EAAwB;AACtByC,QAAAA,IAAI,CAACzC,MAAD,CAAJ,CAAa,IAAb;AACD;AACF,KAVM,CAAP;AAWD;;QAEM6J,gBAAP,uBAAqBC,aAArB,EAAoC;AAClC,WAAO,UAAU5F,KAAV,EAAiB;AACtB,UAAIA,KAAJ,EAAW;AACTA,QAAAA,KAAK,CAAC6D,cAAN;AACD;;AAED+B,MAAAA,aAAa,CAAChB,KAAd,CAAoB,IAApB;AACD,KAND;AAOD;;QAEMiB,cAAP,qBAAmBhM,OAAnB,EAA4B;AAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,QAAtB,CAAP;AACD;;;;wBAlFoB;AACnB,aAAOD,OAAP;AACD;;;;;AAmFH;AACA;AACA;AACA;AACA;;;AACA5D,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,oBAA1B,EAAgDH,gBAAhD,EAAkEO,KAAK,CAACiB,aAAN,CAAoB,IAAIjB,KAAJ,EAApB,CAAlE;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA3G,kBAAkB,CAAC,YAAM;AACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;AACA;;AACA,MAAIoF,CAAJ,EAAO;AACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,IAAL,CAA3B;AACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,IAAL,IAAaY,KAAK,CAACe,eAAnB;AACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,IAAL,EAAWiC,WAAX,GAAyBrB,KAAzB;;AACA3B,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,IAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,IAAL,IAAagC,kBAAb;AACA,aAAOpB,KAAK,CAACe,eAAb;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;ACjJA;AACA;AACA;AACA;AACA;;AAEA,IAAM3B,MAAI,GAAG,QAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,WAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAME,cAAY,GAAG,WAArB;AAEA,IAAM+B,iBAAiB,GAAG,QAA1B;AAEA,IAAMC,oBAAoB,GAAG,wBAA7B;AAEA,IAAM5B,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AAEA;AACA;AACA;AACA;AACA;;IAEMiC;AACJ,kBAAYtM,OAAZ,EAAqB;AACnB,SAAK8K,QAAL,GAAgB9K,OAAhB;AACA+E,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;AACD;;;;;AAQD;SAEAoC,SAAA,kBAAS;AACP;AACA,SAAKzB,QAAL,CAAc0B,YAAd,CAA2B,cAA3B,EAA2C,KAAK1B,QAAL,CAAcS,SAAd,CAAwBgB,MAAxB,CAA+BH,iBAA/B,CAA3C;AACD;;SAEDf,UAAA,mBAAU;AACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;AACA,SAAKW,QAAL,GAAgB,IAAhB;AACD;;;SAIMc,kBAAP,yBAAuB3J,MAAvB,EAA+B;AAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;AAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;AAEA,UAAI,CAACzF,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAI4H,MAAJ,CAAW,IAAX,CAAP;AACD;;AAED,UAAIrK,MAAM,KAAK,QAAf,EAAyB;AACvByC,QAAAA,IAAI,CAACzC,MAAD,CAAJ;AACD;AACF,KAVM,CAAP;AAWD;;SAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;AAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;AACD;;;;wBAlCoB;AACnB,aAAOD,SAAP;AACD;;;;;AAmCH;AACA;AACA;AACA;AACA;;;AAEA5D,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4B,oBAAhD,EAAsE,UAAAlG,KAAK,EAAI;AAC7EA,EAAAA,KAAK,CAAC6D,cAAN;AAEA,MAAMyC,MAAM,GAAGtG,KAAK,CAACU,MAAN,CAAayE,OAAb,CAAqBe,oBAArB,CAAf;AAEA,MAAI3H,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAauH,MAAb,EAAqBtC,UAArB,CAAX;;AACA,MAAI,CAACzF,IAAL,EAAW;AACTA,IAAAA,IAAI,GAAG,IAAI4H,MAAJ,CAAWG,MAAX,CAAP;AACD;;AAED/H,EAAAA,IAAI,CAAC6H,MAAL;AACD,CAXD;AAaA;AACA;AACA;AACA;AACA;AACA;;AAEArI,kBAAkB,CAAC,YAAM;AACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;AACA;;AACA,MAAIoF,CAAJ,EAAO;AACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;AACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAaqC,MAAM,CAACV,eAApB;AACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBI,MAAzB;;AAEApD,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;AACA,aAAOK,MAAM,CAACV,eAAd;AACD,KAHD;AAID;AACF,CAbiB,CAAlB;;AC1GA;AACA;AACA;AACA;AACA;AACA;AAEA,SAASc,aAAT,CAAuBC,GAAvB,EAA4B;AAC1B,MAAIA,GAAG,KAAK,MAAZ,EAAoB;AAClB,WAAO,IAAP;AACD;;AAED,MAAIA,GAAG,KAAK,OAAZ,EAAqB;AACnB,WAAO,KAAP;AACD;;AAED,MAAIA,GAAG,KAAKC,MAAM,CAACD,GAAD,CAAN,CAAYvN,QAAZ,EAAZ,EAAoC;AAClC,WAAOwN,MAAM,CAACD,GAAD,CAAb;AACD;;AAED,MAAIA,GAAG,KAAK,EAAR,IAAcA,GAAG,KAAK,MAA1B,EAAkC;AAChC,WAAO,IAAP;AACD;;AAED,SAAOA,GAAP;AACD;;AAED,SAASE,gBAAT,CAA0BpI,GAA1B,EAA+B;AAC7B,SAAOA,GAAG,CAACkD,OAAJ,CAAY,QAAZ,EAAsB,UAAAmF,GAAG;AAAA,iBAAQA,GAAG,CAACvN,WAAJ,EAAR;AAAA,GAAzB,CAAP;AACD;;AAED,IAAMwN,WAAW,GAAG;AAClBC,EAAAA,gBADkB,4BACDhN,OADC,EACQyE,GADR,EACajC,KADb,EACoB;AACpCxC,IAAAA,OAAO,CAACwM,YAAR,WAA6BK,gBAAgB,CAACpI,GAAD,CAA7C,EAAsDjC,KAAtD;AACD,GAHiB;AAKlByK,EAAAA,mBALkB,+BAKEjN,OALF,EAKWyE,GALX,EAKgB;AAChCzE,IAAAA,OAAO,CAACkN,eAAR,WAAgCL,gBAAgB,CAACpI,GAAD,CAAhD;AACD,GAPiB;AASlB0I,EAAAA,iBATkB,6BASAnN,OATA,EASS;AACzB,QAAI,CAACA,OAAL,EAAc;AACZ,aAAO,EAAP;AACD;;AAED,QAAMoN,UAAU,gBACXpN,OAAO,CAACqN,OADG,CAAhB;;AAIAlL,IAAAA,MAAM,CAACC,IAAP,CAAYgL,UAAZ,EAAwB/K,OAAxB,CAAgC,UAAAoC,GAAG,EAAI;AACrC2I,MAAAA,UAAU,CAAC3I,GAAD,CAAV,GAAkBiI,aAAa,CAACU,UAAU,CAAC3I,GAAD,CAAX,CAA/B;AACD,KAFD;AAIA,WAAO2I,UAAP;AACD,GAvBiB;AAyBlBE,EAAAA,gBAzBkB,4BAyBDtN,OAzBC,EAyBQyE,GAzBR,EAyBa;AAC7B,WAAOiI,aAAa,CAAC1M,OAAO,CAACE,YAAR,WAA6B2M,gBAAgB,CAACpI,GAAD,CAA7C,CAAD,CAApB;AACD,GA3BiB;AA6BlB8I,EAAAA,MA7BkB,kBA6BXvN,OA7BW,EA6BF;AACd,QAAMwN,IAAI,GAAGxN,OAAO,CAACyN,qBAAR,EAAb;AAEA,WAAO;AACLC,MAAAA,GAAG,EAAEF,IAAI,CAACE,GAAL,GAAW7N,QAAQ,CAACmE,IAAT,CAAc2J,SADzB;AAELC,MAAAA,IAAI,EAAEJ,IAAI,CAACI,IAAL,GAAY/N,QAAQ,CAACmE,IAAT,CAAc6J;AAF3B,KAAP;AAID,GApCiB;AAsClBC,EAAAA,QAtCkB,oBAsCT9N,OAtCS,EAsCA;AAChB,WAAO;AACL0N,MAAAA,GAAG,EAAE1N,OAAO,CAAC+N,SADR;AAELH,MAAAA,IAAI,EAAE5N,OAAO,CAACgO;AAFT,KAAP;AAID;AA3CiB,CAApB;;AC/BA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAEA,IAAMC,SAAS,GAAG,CAAlB;AAEA,IAAMC,cAAc,GAAG;AACrBC,EAAAA,OADqB,mBACbnO,OADa,EACJC,QADI,EACM;AACzB,WAAOD,OAAO,CAACmO,OAAR,CAAgBlO,QAAhB,CAAP;AACD,GAHoB;AAKrBmO,EAAAA,IALqB,gBAKhBnO,QALgB,EAKND,OALM,EAK8B;AAAA;;AAAA,QAApCA,OAAoC;AAApCA,MAAAA,OAAoC,GAA1BH,QAAQ,CAACyD,eAAiB;AAAA;;AACjD,WAAO,YAAG+K,MAAH,aAAaC,OAAO,CAACC,SAAR,CAAkB3H,gBAAlB,CAAmCvH,IAAnC,CAAwCW,OAAxC,EAAiDC,QAAjD,CAAb,CAAP;AACD,GAPoB;AASrBuO,EAAAA,OATqB,mBASbvO,QATa,EASHD,OATG,EASiC;AAAA,QAApCA,OAAoC;AAApCA,MAAAA,OAAoC,GAA1BH,QAAQ,CAACyD,eAAiB;AAAA;;AACpD,WAAOgL,OAAO,CAACC,SAAR,CAAkBjO,aAAlB,CAAgCjB,IAAhC,CAAqCW,OAArC,EAA8CC,QAA9C,CAAP;AACD,GAXoB;AAarBwO,EAAAA,QAbqB,oBAaZzO,OAbY,EAaHC,QAbG,EAaO;AAAA;;AAC1B,QAAMwO,QAAQ,GAAG,aAAGJ,MAAH,cAAarO,OAAO,CAACyO,QAArB,CAAjB;;AAEA,WAAOA,QAAQ,CAACC,MAAT,CAAgB,UAAAC,KAAK;AAAA,aAAIA,KAAK,CAACR,OAAN,CAAclO,QAAd,CAAJ;AAAA,KAArB,CAAP;AACD,GAjBoB;AAmBrB2O,EAAAA,OAnBqB,mBAmBb5O,OAnBa,EAmBJC,QAnBI,EAmBM;AACzB,QAAM2O,OAAO,GAAG,EAAhB;AAEA,QAAIC,QAAQ,GAAG7O,OAAO,CAACgD,UAAvB;;AAEA,WAAO6L,QAAQ,IAAIA,QAAQ,CAACxN,QAAT,KAAsByN,IAAI,CAACC,YAAvC,IAAuDF,QAAQ,CAACxN,QAAT,KAAsB4M,SAApF,EAA+F;AAC7F,UAAI,KAAKE,OAAL,CAAaU,QAAb,EAAuB5O,QAAvB,CAAJ,EAAsC;AACpC2O,QAAAA,OAAO,CAACI,IAAR,CAAaH,QAAb;AACD;;AAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAAC7L,UAApB;AACD;;AAED,WAAO4L,OAAP;AACD,GAjCoB;AAmCrBK,EAAAA,IAnCqB,gBAmChBjP,OAnCgB,EAmCPC,QAnCO,EAmCG;AACtB,QAAIiP,QAAQ,GAAGlP,OAAO,CAACmP,sBAAvB;;AAEA,WAAOD,QAAP,EAAiB;AACf,UAAIA,QAAQ,CAACf,OAAT,CAAiBlO,QAAjB,CAAJ,EAAgC;AAC9B,eAAO,CAACiP,QAAD,CAAP;AACD;;AAEDA,MAAAA,QAAQ,GAAGA,QAAQ,CAACC,sBAApB;AACD;;AAED,WAAO,EAAP;AACD,GA/CoB;AAiDrBC,EAAAA,IAjDqB,gBAiDhBpP,OAjDgB,EAiDPC,QAjDO,EAiDG;AACtB,QAAImP,IAAI,GAAGpP,OAAO,CAACqP,kBAAnB;;AAEA,WAAOD,IAAP,EAAa;AACX,UAAI,KAAKjB,OAAL,CAAaiB,IAAb,EAAmBnP,QAAnB,CAAJ,EAAkC;AAChC,eAAO,CAACmP,IAAD,CAAP;AACD;;AAEDA,MAAAA,IAAI,GAAGA,IAAI,CAACC,kBAAZ;AACD;;AAED,WAAO,EAAP;AACD;AA7DoB,CAAvB;;ACSA;AACA;AACA;AACA;AACA;;AAEA,IAAMpF,MAAI,GAAG,UAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,aAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAME,cAAY,GAAG,WAArB;AAEA,IAAMiF,cAAc,GAAG,WAAvB;AACA,IAAMC,eAAe,GAAG,YAAxB;AACA,IAAMC,sBAAsB,GAAG,GAA/B;;AACA,IAAMC,eAAe,GAAG,EAAxB;AAEA,IAAMC,OAAO,GAAG;AACdC,EAAAA,QAAQ,EAAE,IADI;AAEdC,EAAAA,QAAQ,EAAE,IAFI;AAGdC,EAAAA,KAAK,EAAE,KAHO;AAIdC,EAAAA,KAAK,EAAE,OAJO;AAKdC,EAAAA,IAAI,EAAE,IALQ;AAMdC,EAAAA,KAAK,EAAE;AANO,CAAhB;AASA,IAAMC,WAAW,GAAG;AAClBN,EAAAA,QAAQ,EAAE,kBADQ;AAElBC,EAAAA,QAAQ,EAAE,SAFQ;AAGlBC,EAAAA,KAAK,EAAE,kBAHW;AAIlBC,EAAAA,KAAK,EAAE,kBAJW;AAKlBC,EAAAA,IAAI,EAAE,SALY;AAMlBC,EAAAA,KAAK,EAAE;AANW,CAApB;AASA,IAAME,cAAc,GAAG,MAAvB;AACA,IAAMC,cAAc,GAAG,MAAvB;AACA,IAAMC,cAAc,GAAG,MAAvB;AACA,IAAMC,eAAe,GAAG,OAAxB;AAEA,IAAMC,WAAW,aAAWlG,WAA5B;AACA,IAAMmG,UAAU,YAAUnG,WAA1B;AACA,IAAMoG,aAAa,eAAapG,WAAhC;AACA,IAAMqG,gBAAgB,kBAAgBrG,WAAtC;AACA,IAAMsG,gBAAgB,kBAAgBtG,WAAtC;AACA,IAAMuG,gBAAgB,kBAAgBvG,WAAtC;AACA,IAAMwG,eAAe,iBAAexG,WAApC;AACA,IAAMyG,cAAc,gBAAczG,WAAlC;AACA,IAAM0G,iBAAiB,mBAAiB1G,WAAxC;AACA,IAAM2G,eAAe,iBAAe3G,WAApC;AACA,IAAM4G,gBAAgB,iBAAe5G,WAArC;AACA,IAAM6G,mBAAmB,YAAU7G,WAAV,GAAsBC,cAA/C;AACA,IAAMI,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AAEA,IAAM6G,mBAAmB,GAAG,UAA5B;AACA,IAAM9E,mBAAiB,GAAG,QAA1B;AACA,IAAM+E,gBAAgB,GAAG,OAAzB;AACA,IAAMC,gBAAgB,GAAG,qBAAzB;AACA,IAAMC,eAAe,GAAG,oBAAxB;AACA,IAAMC,eAAe,GAAG,oBAAxB;AACA,IAAMC,eAAe,GAAG,oBAAxB;AACA,IAAMC,wBAAwB,GAAG,eAAjC;AAEA,IAAMC,eAAe,GAAG,SAAxB;AACA,IAAMC,oBAAoB,GAAG,uBAA7B;AACA,IAAMC,aAAa,GAAG,gBAAtB;AACA,IAAMC,iBAAiB,GAAG,oBAA1B;AACA,IAAMC,kBAAkB,GAAG,0CAA3B;AACA,IAAMC,mBAAmB,GAAG,sBAA5B;AACA,IAAMC,mBAAmB,GAAG,+BAA5B;AACA,IAAMC,kBAAkB,GAAG,wBAA3B;AAEA,IAAMC,WAAW,GAAG;AAClBC,EAAAA,KAAK,EAAE,OADW;AAElBC,EAAAA,GAAG,EAAE;AAFa,CAApB;AAKA;AACA;AACA;AACA;AACA;;IACMC;AACJ,oBAAYpS,OAAZ,EAAqBiC,MAArB,EAA6B;AAC3B,SAAKoQ,MAAL,GAAc,IAAd;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA,SAAKC,cAAL,GAAsB,IAAtB;AACA,SAAKC,SAAL,GAAiB,KAAjB;AACA,SAAKC,UAAL,GAAkB,KAAlB;AACA,SAAKC,YAAL,GAAoB,IAApB;AACA,SAAKC,WAAL,GAAmB,CAAnB;AACA,SAAKC,WAAL,GAAmB,CAAnB;AAEA,SAAKC,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;AACA,SAAK6I,QAAL,GAAgB9K,OAAhB;AACA,SAAK+S,kBAAL,GAA0B7E,cAAc,CAACM,OAAf,CAAuBsD,mBAAvB,EAA4C,KAAKhH,QAAjD,CAA1B;AACA,SAAKkI,eAAL,GAAuB,kBAAkBnT,QAAQ,CAACyD,eAA3B,IAA8C2P,SAAS,CAACC,cAAV,GAA2B,CAAhG;AACA,SAAKC,aAAL,GAAqBhL,OAAO,CAAC1H,MAAM,CAAC2S,YAAR,CAA5B;;AAEA,SAAKC,kBAAL;;AACAtO,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;AACD;;;;;AAYD;SAEAiF,OAAA,gBAAO;AACL,QAAI,CAAC,KAAKqD,UAAV,EAAsB;AACpB,WAAKa,MAAL,CAAYpD,cAAZ;AACD;AACF;;SAEDqD,kBAAA,2BAAkB;AAChB;AACA;AACA,QAAI,CAAC1T,QAAQ,CAAC2T,MAAV,IAAoB1Q,SAAS,CAAC,KAAKgI,QAAN,CAAjC,EAAkD;AAChD,WAAKsE,IAAL;AACD;AACF;;SAEDH,OAAA,gBAAO;AACL,QAAI,CAAC,KAAKwD,UAAV,EAAsB;AACpB,WAAKa,MAAL,CAAYnD,cAAZ;AACD;AACF;;SAEDL,QAAA,eAAM3J,KAAN,EAAa;AACX,QAAI,CAACA,KAAL,EAAY;AACV,WAAKqM,SAAL,GAAiB,IAAjB;AACD;;AAED,QAAItE,cAAc,CAACM,OAAf,CAAuBqD,kBAAvB,EAA2C,KAAK/G,QAAhD,CAAJ,EAA+D;AAC7D7J,MAAAA,oBAAoB,CAAC,KAAK6J,QAAN,CAApB;AACA,WAAK2I,KAAL,CAAW,IAAX;AACD;;AAEDC,IAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;AACA,SAAKA,SAAL,GAAiB,IAAjB;AACD;;SAEDmB,QAAA,eAAMtN,KAAN,EAAa;AACX,QAAI,CAACA,KAAL,EAAY;AACV,WAAKqM,SAAL,GAAiB,KAAjB;AACD;;AAED,QAAI,KAAKF,SAAT,EAAoB;AAClBoB,MAAAA,aAAa,CAAC,KAAKpB,SAAN,CAAb;AACA,WAAKA,SAAL,GAAiB,IAAjB;AACD;;AAED,QAAI,KAAKO,OAAL,IAAgB,KAAKA,OAAL,CAAalD,QAA7B,IAAyC,CAAC,KAAK6C,SAAnD,EAA8D;AAC5D,WAAKmB,eAAL;;AAEA,WAAKrB,SAAL,GAAiBsB,WAAW,CAC1B,CAAC/T,QAAQ,CAACgU,eAAT,GAA2B,KAAKN,eAAhC,GAAkD,KAAKnE,IAAxD,EAA8D0E,IAA9D,CAAmE,IAAnE,CAD0B,EAE1B,KAAKjB,OAAL,CAAalD,QAFa,CAA5B;AAID;AACF;;SAEDoE,KAAA,YAAGC,KAAH,EAAU;AAAA;;AACR,SAAKzB,cAAL,GAAsBrE,cAAc,CAACM,OAAf,CAAuBkD,oBAAvB,EAA6C,KAAK5G,QAAlD,CAAtB;;AACA,QAAMmJ,WAAW,GAAG,KAAKC,aAAL,CAAmB,KAAK3B,cAAxB,CAApB;;AAEA,QAAIyB,KAAK,GAAG,KAAK3B,MAAL,CAAYtL,MAAZ,GAAqB,CAA7B,IAAkCiN,KAAK,GAAG,CAA9C,EAAiD;AAC/C;AACD;;AAED,QAAI,KAAKvB,UAAT,EAAqB;AACnBnM,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgCyF,UAAhC,EAA4C;AAAA,eAAM,KAAI,CAACwD,EAAL,CAAQC,KAAR,CAAN;AAAA,OAA5C;AACA;AACD;;AAED,QAAIC,WAAW,KAAKD,KAApB,EAA2B;AACzB,WAAKlE,KAAL;AACA,WAAK2D,KAAL;AACA;AACD;;AAED,QAAMU,SAAS,GAAGH,KAAK,GAAGC,WAAR,GAChB/D,cADgB,GAEhBC,cAFF;;AAIA,SAAKmD,MAAL,CAAYa,SAAZ,EAAuB,KAAK9B,MAAL,CAAY2B,KAAZ,CAAvB;AACD;;SAED3I,UAAA,mBAAU;AACR/E,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgCV,WAAhC;AACArF,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;AAEA,SAAKkI,MAAL,GAAc,IAAd;AACA,SAAKQ,OAAL,GAAe,IAAf;AACA,SAAK/H,QAAL,GAAgB,IAAhB;AACA,SAAKwH,SAAL,GAAiB,IAAjB;AACA,SAAKE,SAAL,GAAiB,IAAjB;AACA,SAAKC,UAAL,GAAkB,IAAlB;AACA,SAAKF,cAAL,GAAsB,IAAtB;AACA,SAAKQ,kBAAL,GAA0B,IAA1B;AACD;;;SAIDD,aAAA,oBAAW7Q,MAAX,EAAmB;AACjBA,IAAAA,MAAM,gBACDyN,OADC,EAEDzN,MAFC,CAAN;AAIAF,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAegO,WAAf,CAAf;AACA,WAAOhO,MAAP;AACD;;SAEDmS,eAAA,wBAAe;AACb,QAAMC,SAAS,GAAG3U,IAAI,CAAC4U,GAAL,CAAS,KAAK1B,WAAd,CAAlB;;AAEA,QAAIyB,SAAS,IAAI5E,eAAjB,EAAkC;AAChC;AACD;;AAED,QAAM0E,SAAS,GAAGE,SAAS,GAAG,KAAKzB,WAAnC;AAEA,SAAKA,WAAL,GAAmB,CAAnB,CATa;;AAYb,QAAIuB,SAAS,GAAG,CAAhB,EAAmB;AACjB,WAAKlF,IAAL;AACD,KAdY;;;AAiBb,QAAIkF,SAAS,GAAG,CAAhB,EAAmB;AACjB,WAAK/E,IAAL;AACD;AACF;;SAEDiE,qBAAA,8BAAqB;AAAA;;AACnB,QAAI,KAAKR,OAAL,CAAajD,QAAjB,EAA2B;AACzBtJ,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B0F,aAA/B,EAA8C,UAAArK,KAAK;AAAA,eAAI,MAAI,CAACoO,QAAL,CAAcpO,KAAd,CAAJ;AAAA,OAAnD;AACD;;AAED,QAAI,KAAK0M,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;AAClCxJ,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B2F,gBAA/B,EAAiD,UAAAtK,KAAK;AAAA,eAAI,MAAI,CAAC2J,KAAL,CAAW3J,KAAX,CAAJ;AAAA,OAAtD;AACAG,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B4F,gBAA/B,EAAiD,UAAAvK,KAAK;AAAA,eAAI,MAAI,CAACsN,KAAL,CAAWtN,KAAX,CAAJ;AAAA,OAAtD;AACD;;AAED,QAAI,KAAK0M,OAAL,CAAa7C,KAAb,IAAsB,KAAKgD,eAA/B,EAAgD;AAC9C,WAAKwB,uBAAL;AACD;AACF;;SAEDA,0BAAA,mCAA0B;AAAA;;AACxB,QAAMC,KAAK,GAAG,SAARA,KAAQ,CAAAtO,KAAK,EAAI;AACrB,UAAI,MAAI,CAACgN,aAAL,IAAsBlB,WAAW,CAAC9L,KAAK,CAACuO,WAAN,CAAkB7R,WAAlB,EAAD,CAArC,EAAwE;AACtE,QAAA,MAAI,CAAC8P,WAAL,GAAmBxM,KAAK,CAACwO,OAAzB;AACD,OAFD,MAEO,IAAI,CAAC,MAAI,CAACxB,aAAV,EAAyB;AAC9B,QAAA,MAAI,CAACR,WAAL,GAAmBxM,KAAK,CAACyO,OAAN,CAAc,CAAd,EAAiBD,OAApC;AACD;AACF,KAND;;AAQA,QAAME,IAAI,GAAG,SAAPA,IAAO,CAAA1O,KAAK,EAAI;AACpB;AACA,UAAIA,KAAK,CAACyO,OAAN,IAAiBzO,KAAK,CAACyO,OAAN,CAAc7N,MAAd,GAAuB,CAA5C,EAA+C;AAC7C,QAAA,MAAI,CAAC6L,WAAL,GAAmB,CAAnB;AACD,OAFD,MAEO;AACL,QAAA,MAAI,CAACA,WAAL,GAAmBzM,KAAK,CAACyO,OAAN,CAAc,CAAd,EAAiBD,OAAjB,GAA2B,MAAI,CAAChC,WAAnD;AACD;AACF,KAPD;;AASA,QAAMmC,GAAG,GAAG,SAANA,GAAM,CAAA3O,KAAK,EAAI;AACnB,UAAI,MAAI,CAACgN,aAAL,IAAsBlB,WAAW,CAAC9L,KAAK,CAACuO,WAAN,CAAkB7R,WAAlB,EAAD,CAArC,EAAwE;AACtE,QAAA,MAAI,CAAC+P,WAAL,GAAmBzM,KAAK,CAACwO,OAAN,GAAgB,MAAI,CAAChC,WAAxC;AACD;;AAED,MAAA,MAAI,CAACyB,YAAL;;AACA,UAAI,MAAI,CAACvB,OAAL,CAAa/C,KAAb,KAAuB,OAA3B,EAAoC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,QAAA,MAAI,CAACA,KAAL;;AACA,YAAI,MAAI,CAAC4C,YAAT,EAAuB;AACrBqC,UAAAA,YAAY,CAAC,MAAI,CAACrC,YAAN,CAAZ;AACD;;AAED,QAAA,MAAI,CAACA,YAAL,GAAoB5Q,UAAU,CAAC,UAAAqE,KAAK;AAAA,iBAAI,MAAI,CAACsN,KAAL,CAAWtN,KAAX,CAAJ;AAAA,SAAN,EAA6BqJ,sBAAsB,GAAG,MAAI,CAACqD,OAAL,CAAalD,QAAnE,CAA9B;AACD;AACF,KAtBD;;AAwBAzB,IAAAA,cAAc,CAACE,IAAf,CAAoBwD,iBAApB,EAAuC,KAAK9G,QAA5C,EAAsDzI,OAAtD,CAA8D,UAAA2S,OAAO,EAAI;AACvE1O,MAAAA,YAAY,CAACkC,EAAb,CAAgBwM,OAAhB,EAAyBhE,gBAAzB,EAA2C,UAAAiE,CAAC;AAAA,eAAIA,CAAC,CAACjL,cAAF,EAAJ;AAAA,OAA5C;AACD,KAFD;;AAIA,QAAI,KAAKmJ,aAAT,EAAwB;AACtB7M,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+BgG,iBAA/B,EAAkD,UAAA3K,KAAK;AAAA,eAAIsO,KAAK,CAACtO,KAAD,CAAT;AAAA,OAAvD;AACAG,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+BiG,eAA/B,EAAgD,UAAA5K,KAAK;AAAA,eAAI2O,GAAG,CAAC3O,KAAD,CAAP;AAAA,OAArD;;AAEA,WAAK2E,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4B1D,wBAA5B;AACD,KALD,MAKO;AACLlL,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B6F,gBAA/B,EAAiD,UAAAxK,KAAK;AAAA,eAAIsO,KAAK,CAACtO,KAAD,CAAT;AAAA,OAAtD;AACAG,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B8F,eAA/B,EAAgD,UAAAzK,KAAK;AAAA,eAAI0O,IAAI,CAAC1O,KAAD,CAAR;AAAA,OAArD;AACAG,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B+F,cAA/B,EAA+C,UAAA1K,KAAK;AAAA,eAAI2O,GAAG,CAAC3O,KAAD,CAAP;AAAA,OAApD;AACD;AACF;;SAEDoO,WAAA,kBAASpO,KAAT,EAAgB;AACd,QAAI,kBAAkBxD,IAAlB,CAAuBwD,KAAK,CAACU,MAAN,CAAasO,OAApC,CAAJ,EAAkD;AAChD;AACD;;AAED,YAAQhP,KAAK,CAAC1B,GAAd;AACE,WAAK6K,cAAL;AACEnJ,QAAAA,KAAK,CAAC6D,cAAN;AACA,aAAKiF,IAAL;AACA;;AACF,WAAKM,eAAL;AACEpJ,QAAAA,KAAK,CAAC6D,cAAN;AACA,aAAKoF,IAAL;AACA;AARJ;AAWD;;SAED8E,gBAAA,uBAAclU,OAAd,EAAuB;AACrB,SAAKqS,MAAL,GAAcrS,OAAO,IAAIA,OAAO,CAACgD,UAAnB,GACZkL,cAAc,CAACE,IAAf,CAAoBuD,aAApB,EAAmC3R,OAAO,CAACgD,UAA3C,CADY,GAEZ,EAFF;AAIA,WAAO,KAAKqP,MAAL,CAAYvK,OAAZ,CAAoB9H,OAApB,CAAP;AACD;;SAEDoV,sBAAA,6BAAoBjB,SAApB,EAA+BkB,aAA/B,EAA8C;AAC5C,QAAMC,eAAe,GAAGnB,SAAS,KAAKjE,cAAtC;AACA,QAAMqF,eAAe,GAAGpB,SAAS,KAAKhE,cAAtC;;AACA,QAAM8D,WAAW,GAAG,KAAKC,aAAL,CAAmBmB,aAAnB,CAApB;;AACA,QAAMG,aAAa,GAAG,KAAKnD,MAAL,CAAYtL,MAAZ,GAAqB,CAA3C;AACA,QAAM0O,aAAa,GAAIF,eAAe,IAAItB,WAAW,KAAK,CAApC,IACGqB,eAAe,IAAIrB,WAAW,KAAKuB,aAD5D;;AAGA,QAAIC,aAAa,IAAI,CAAC,KAAK5C,OAAL,CAAa9C,IAAnC,EAAyC;AACvC,aAAOsF,aAAP;AACD;;AAED,QAAMK,KAAK,GAAGvB,SAAS,KAAKhE,cAAd,GAA+B,CAAC,CAAhC,GAAoC,CAAlD;AACA,QAAMwF,SAAS,GAAG,CAAC1B,WAAW,GAAGyB,KAAf,IAAwB,KAAKrD,MAAL,CAAYtL,MAAtD;AAEA,WAAO4O,SAAS,KAAK,CAAC,CAAf,GACL,KAAKtD,MAAL,CAAY,KAAKA,MAAL,CAAYtL,MAAZ,GAAqB,CAAjC,CADK,GAEL,KAAKsL,MAAL,CAAYsD,SAAZ,CAFF;AAGD;;SAEDC,qBAAA,4BAAmBC,aAAnB,EAAkCC,kBAAlC,EAAsD;AACpD,QAAMC,WAAW,GAAG,KAAK7B,aAAL,CAAmB2B,aAAnB,CAApB;;AACA,QAAMG,SAAS,GAAG,KAAK9B,aAAL,CAAmBhG,cAAc,CAACM,OAAf,CAAuBkD,oBAAvB,EAA6C,KAAK5G,QAAlD,CAAnB,CAAlB;;AAEA,WAAOxE,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCwF,WAApC,EAAiD;AACtDuF,MAAAA,aAAa,EAAbA,aADsD;AAEtD1B,MAAAA,SAAS,EAAE2B,kBAF2C;AAGtDG,MAAAA,IAAI,EAAED,SAHgD;AAItDjC,MAAAA,EAAE,EAAEgC;AAJkD,KAAjD,CAAP;AAMD;;SAEDG,6BAAA,oCAA2BlW,OAA3B,EAAoC;AAClC,QAAI,KAAK+S,kBAAT,EAA6B;AAC3B,UAAMoD,UAAU,GAAGjI,cAAc,CAACE,IAAf,CAAoBqD,eAApB,EAAqC,KAAKsB,kBAA1C,CAAnB;;AACA,WAAK,IAAIjM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGqP,UAAU,CAACpP,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;AAC1CqP,QAAAA,UAAU,CAACrP,CAAD,CAAV,CAAcyE,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B;AACD;;AAED,UAAMgK,aAAa,GAAG,KAAKrD,kBAAL,CAAwBtE,QAAxB,CACpB,KAAKyF,aAAL,CAAmBlU,OAAnB,CADoB,CAAtB;;AAIA,UAAIoW,aAAJ,EAAmB;AACjBA,QAAAA,aAAa,CAAC7K,SAAd,CAAwB2J,GAAxB,CAA4B9I,mBAA5B;AACD;AACF;AACF;;SAEDuH,kBAAA,2BAAkB;AAChB,QAAM3T,OAAO,GAAG,KAAKuS,cAAL,IAAuBrE,cAAc,CAACM,OAAf,CAAuBkD,oBAAvB,EAA6C,KAAK5G,QAAlD,CAAvC;;AAEA,QAAI,CAAC9K,OAAL,EAAc;AACZ;AACD;;AAED,QAAMqW,eAAe,GAAGC,QAAQ,CAACtW,OAAO,CAACE,YAAR,CAAqB,eAArB,CAAD,EAAwC,EAAxC,CAAhC;;AAEA,QAAImW,eAAJ,EAAqB;AACnB,WAAKxD,OAAL,CAAa0D,eAAb,GAA+B,KAAK1D,OAAL,CAAa0D,eAAb,IAAgC,KAAK1D,OAAL,CAAalD,QAA5E;AACA,WAAKkD,OAAL,CAAalD,QAAb,GAAwB0G,eAAxB;AACD,KAHD,MAGO;AACL,WAAKxD,OAAL,CAAalD,QAAb,GAAwB,KAAKkD,OAAL,CAAa0D,eAAb,IAAgC,KAAK1D,OAAL,CAAalD,QAArE;AACD;AACF;;SAED2D,SAAA,gBAAOa,SAAP,EAAkBnU,OAAlB,EAA2B;AAAA;;AACzB,QAAMqV,aAAa,GAAGnH,cAAc,CAACM,OAAf,CAAuBkD,oBAAvB,EAA6C,KAAK5G,QAAlD,CAAtB;;AACA,QAAM0L,kBAAkB,GAAG,KAAKtC,aAAL,CAAmBmB,aAAnB,CAA3B;;AACA,QAAMoB,WAAW,GAAGzW,OAAO,IAAKqV,aAAa,IAC3C,KAAKD,mBAAL,CAAyBjB,SAAzB,EAAoCkB,aAApC,CADF;;AAGA,QAAMqB,gBAAgB,GAAG,KAAKxC,aAAL,CAAmBuC,WAAnB,CAAzB;;AACA,QAAME,SAAS,GAAGxO,OAAO,CAAC,KAAKmK,SAAN,CAAzB;AAEA,QAAIsE,oBAAJ;AACA,QAAIC,cAAJ;AACA,QAAIf,kBAAJ;;AAEA,QAAI3B,SAAS,KAAKjE,cAAlB,EAAkC;AAChC0G,MAAAA,oBAAoB,GAAGvF,eAAvB;AACAwF,MAAAA,cAAc,GAAGvF,eAAjB;AACAwE,MAAAA,kBAAkB,GAAG1F,cAArB;AACD,KAJD,MAIO;AACLwG,MAAAA,oBAAoB,GAAGxF,gBAAvB;AACAyF,MAAAA,cAAc,GAAGtF,eAAjB;AACAuE,MAAAA,kBAAkB,GAAGzF,eAArB;AACD;;AAED,QAAIoG,WAAW,IAAIA,WAAW,CAAClL,SAAZ,CAAsBE,QAAtB,CAA+BW,mBAA/B,CAAnB,EAAsE;AACpE,WAAKqG,UAAL,GAAkB,KAAlB;AACA;AACD;;AAED,QAAMqE,UAAU,GAAG,KAAKlB,kBAAL,CAAwBa,WAAxB,EAAqCX,kBAArC,CAAnB;;AACA,QAAIgB,UAAU,CAACxN,gBAAf,EAAiC;AAC/B;AACD;;AAED,QAAI,CAAC+L,aAAD,IAAkB,CAACoB,WAAvB,EAAoC;AAClC;AACA;AACD;;AAED,SAAKhE,UAAL,GAAkB,IAAlB;;AAEA,QAAIkE,SAAJ,EAAe;AACb,WAAK7G,KAAL;AACD;;AAED,SAAKoG,0BAAL,CAAgCO,WAAhC;;AACA,SAAKlE,cAAL,GAAsBkE,WAAtB;;AAEA,QAAI,KAAK3L,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC0F,gBAAjC,CAAJ,EAAwD;AACtDsF,MAAAA,WAAW,CAAClL,SAAZ,CAAsB2J,GAAtB,CAA0B2B,cAA1B;AAEAjT,MAAAA,MAAM,CAAC6S,WAAD,CAAN;AAEApB,MAAAA,aAAa,CAAC9J,SAAd,CAAwB2J,GAAxB,CAA4B0B,oBAA5B;AACAH,MAAAA,WAAW,CAAClL,SAAZ,CAAsB2J,GAAtB,CAA0B0B,oBAA1B;AAEA,UAAMjW,kBAAkB,GAAGH,gCAAgC,CAAC6U,aAAD,CAA3D;AAEA/O,MAAAA,YAAY,CAACmC,GAAb,CAAiB4M,aAAjB,EAAgCrW,cAAhC,EAAgD,YAAM;AACpDyX,QAAAA,WAAW,CAAClL,SAAZ,CAAsBC,MAAtB,CAA6BoL,oBAA7B,EAAmDC,cAAnD;AACAJ,QAAAA,WAAW,CAAClL,SAAZ,CAAsB2J,GAAtB,CAA0B9I,mBAA1B;AAEAiJ,QAAAA,aAAa,CAAC9J,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B,EAAkDyK,cAAlD,EAAkED,oBAAlE;AAEA,QAAA,MAAI,CAACnE,UAAL,GAAkB,KAAlB;AAEA3Q,QAAAA,UAAU,CAAC,YAAM;AACfwE,UAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAC8B,QAA1B,EAAoCyF,UAApC,EAAgD;AAC9CsF,YAAAA,aAAa,EAAEY,WAD+B;AAE9CtC,YAAAA,SAAS,EAAE2B,kBAFmC;AAG9CG,YAAAA,IAAI,EAAEO,kBAHwC;AAI9CzC,YAAAA,EAAE,EAAE2C;AAJ0C,WAAhD;AAMD,SAPS,EAOP,CAPO,CAAV;AAQD,OAhBD;AAkBApV,MAAAA,oBAAoB,CAAC+T,aAAD,EAAgB1U,kBAAhB,CAApB;AACD,KA7BD,MA6BO;AACL0U,MAAAA,aAAa,CAAC9J,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B;AACAqK,MAAAA,WAAW,CAAClL,SAAZ,CAAsB2J,GAAtB,CAA0B9I,mBAA1B;AAEA,WAAKqG,UAAL,GAAkB,KAAlB;AACAnM,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyF,UAApC,EAAgD;AAC9CsF,QAAAA,aAAa,EAAEY,WAD+B;AAE9CtC,QAAAA,SAAS,EAAE2B,kBAFmC;AAG9CG,QAAAA,IAAI,EAAEO,kBAHwC;AAI9CzC,QAAAA,EAAE,EAAE2C;AAJ0C,OAAhD;AAMD;;AAED,QAAIC,SAAJ,EAAe;AACb,WAAKlD,KAAL;AACD;AACF;;;WAIMsD,oBAAP,2BAAyB/W,OAAzB,EAAkCiC,MAAlC,EAA0C;AACxC,QAAIyC,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAX;;AACA,QAAI0I,OAAO,gBACNnD,OADM,EAEN3C,WAAW,CAACI,iBAAZ,CAA8BnN,OAA9B,CAFM,CAAX;;AAKA,QAAI,OAAOiC,MAAP,KAAkB,QAAtB,EAAgC;AAC9B4Q,MAAAA,OAAO,gBACFA,OADE,EAEF5Q,MAFE,CAAP;AAID;;AAED,QAAM+U,MAAM,GAAG,OAAO/U,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC4Q,OAAO,CAAChD,KAA7D;;AAEA,QAAI,CAACnL,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAI0N,QAAJ,CAAapS,OAAb,EAAsB6S,OAAtB,CAAP;AACD;;AAED,QAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;AAC9ByC,MAAAA,IAAI,CAACqP,EAAL,CAAQ9R,MAAR;AACD,KAFD,MAEO,IAAI,OAAO+U,MAAP,KAAkB,QAAtB,EAAgC;AACrC,UAAI,OAAOtS,IAAI,CAACsS,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIC,SAAJ,wBAAkCD,MAAlC,QAAN;AACD;;AAEDtS,MAAAA,IAAI,CAACsS,MAAD,CAAJ;AACD,KANM,MAMA,IAAInE,OAAO,CAAClD,QAAR,IAAoBkD,OAAO,CAACqE,IAAhC,EAAsC;AAC3CxS,MAAAA,IAAI,CAACoL,KAAL;AACApL,MAAAA,IAAI,CAAC+O,KAAL;AACD;AACF;;WAEM7H,kBAAP,yBAAuB3J,MAAvB,EAA+B;AAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;AAC3BuG,MAAAA,QAAQ,CAAC2E,iBAAT,CAA2B,IAA3B,EAAiC9U,MAAjC;AACD,KAFM,CAAP;AAGD;;WAEMkV,sBAAP,6BAA2BhR,KAA3B,EAAkC;AAChC,QAAMU,MAAM,GAAGtG,sBAAsB,CAAC,IAAD,CAArC;;AAEA,QAAI,CAACsG,MAAD,IAAW,CAACA,MAAM,CAAC0E,SAAP,CAAiBE,QAAjB,CAA0ByF,mBAA1B,CAAhB,EAAgE;AAC9D;AACD;;AAED,QAAMjP,MAAM,gBACP8K,WAAW,CAACI,iBAAZ,CAA8BtG,MAA9B,CADO,EAEPkG,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAFO,CAAZ;;AAIA,QAAMiK,UAAU,GAAG,KAAKlX,YAAL,CAAkB,eAAlB,CAAnB;;AAEA,QAAIkX,UAAJ,EAAgB;AACdnV,MAAAA,MAAM,CAAC0N,QAAP,GAAkB,KAAlB;AACD;;AAEDyC,IAAAA,QAAQ,CAAC2E,iBAAT,CAA2BlQ,MAA3B,EAAmC5E,MAAnC;;AAEA,QAAImV,UAAJ,EAAgB;AACdrS,MAAAA,IAAI,CAACG,OAAL,CAAa2B,MAAb,EAAqBsD,UAArB,EAA+B4J,EAA/B,CAAkCqD,UAAlC;AACD;;AAEDjR,IAAAA,KAAK,CAAC6D,cAAN;AACD;;WAEMgC,cAAP,qBAAmBhM,OAAnB,EAA4B;AAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;AACD;;;;wBAldoB;AACnB,aAAOD,SAAP;AACD;;;wBAEoB;AACnB,aAAOwF,OAAP;AACD;;;;;AA+cH;AACA;AACA;AACA;AACA;;;AAEApJ,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgDsH,mBAAhD,EAAqEK,QAAQ,CAAC+E,mBAA9E;AAEA7Q,YAAY,CAACkC,EAAb,CAAgB/H,MAAhB,EAAwBwQ,mBAAxB,EAA6C,YAAM;AACjD,MAAMoG,SAAS,GAAGnJ,cAAc,CAACE,IAAf,CAAoB4D,kBAApB,CAAlB;;AAEA,OAAK,IAAIlL,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGiQ,SAAS,CAACtQ,MAAhC,EAAwCD,CAAC,GAAGM,GAA5C,EAAiDN,CAAC,EAAlD,EAAsD;AACpDsL,IAAAA,QAAQ,CAAC2E,iBAAT,CAA2BM,SAAS,CAACvQ,CAAD,CAApC,EAAyC/B,IAAI,CAACG,OAAL,CAAamS,SAAS,CAACvQ,CAAD,CAAtB,EAA2BqD,UAA3B,CAAzC;AACD;AACF,CAND;AAQA;AACA;AACA;AACA;AACA;AACA;;AAEAjG,kBAAkB,CAAC,YAAM;AACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;AACA;;AACA,MAAIoF,CAAJ,EAAO;AACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;AACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAamI,QAAQ,CAACxG,eAAtB;AACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBkG,QAAzB;;AACAlJ,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;AACA,aAAOmG,QAAQ,CAACxG,eAAhB;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;ACrlBA;AACA;AACA;AACA;AACA;;AAEA,IAAM3B,MAAI,GAAG,UAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,aAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAME,cAAY,GAAG,WAArB;AAEA,IAAMqF,SAAO,GAAG;AACdnD,EAAAA,MAAM,EAAE,IADM;AAEd+K,EAAAA,MAAM,EAAE;AAFM,CAAhB;AAKA,IAAMrH,aAAW,GAAG;AAClB1D,EAAAA,MAAM,EAAE,SADU;AAElB+K,EAAAA,MAAM,EAAE;AAFU,CAApB;AAKA,IAAMC,UAAU,YAAUnN,WAA1B;AACA,IAAMoN,WAAW,aAAWpN,WAA5B;AACA,IAAMqN,UAAU,YAAUrN,WAA1B;AACA,IAAMsN,YAAY,cAAYtN,WAA9B;AACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AAEA,IAAMsN,eAAe,GAAG,MAAxB;AACA,IAAMC,mBAAmB,GAAG,UAA5B;AACA,IAAMC,qBAAqB,GAAG,YAA9B;AACA,IAAMC,oBAAoB,GAAG,WAA7B;AAEA,IAAMC,KAAK,GAAG,OAAd;AACA,IAAMC,MAAM,GAAG,QAAf;AAEA,IAAMC,gBAAgB,GAAG,oBAAzB;AACA,IAAM5L,sBAAoB,GAAG,0BAA7B;AAEA;AACA;AACA;AACA;AACA;;IAEM6L;AACJ,oBAAYlY,OAAZ,EAAqBiC,MAArB,EAA6B;AAC3B,SAAKkW,gBAAL,GAAwB,KAAxB;AACA,SAAKrN,QAAL,GAAgB9K,OAAhB;AACA,SAAK6S,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;AACA,SAAKmW,aAAL,GAAqBlK,cAAc,CAACE,IAAf,CAChB/B,sBAAH,iBAAkCrM,OAAO,CAACuE,EAA1C,aACG8H,sBADH,wBACyCrM,OAAO,CAACuE,EADjD,SADmB,CAArB;AAKA,QAAM8T,UAAU,GAAGnK,cAAc,CAACE,IAAf,CAAoB/B,sBAApB,CAAnB;;AAEA,SAAK,IAAIvF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGiR,UAAU,CAACtR,MAAjC,EAAyCD,CAAC,GAAGM,GAA7C,EAAkDN,CAAC,EAAnD,EAAuD;AACrD,UAAMwR,IAAI,GAAGD,UAAU,CAACvR,CAAD,CAAvB;AACA,UAAM7G,QAAQ,GAAGI,sBAAsB,CAACiY,IAAD,CAAvC;AACA,UAAMC,aAAa,GAAGrK,cAAc,CAACE,IAAf,CAAoBnO,QAApB,EACnByO,MADmB,CACZ,UAAA8J,SAAS;AAAA,eAAIA,SAAS,KAAKxY,OAAlB;AAAA,OADG,CAAtB;;AAGA,UAAIC,QAAQ,KAAK,IAAb,IAAqBsY,aAAa,CAACxR,MAAvC,EAA+C;AAC7C,aAAK0R,SAAL,GAAiBxY,QAAjB;;AACA,aAAKmY,aAAL,CAAmBpJ,IAAnB,CAAwBsJ,IAAxB;AACD;AACF;;AAED,SAAKI,OAAL,GAAe,KAAK7F,OAAL,CAAayE,MAAb,GAAsB,KAAKqB,UAAL,EAAtB,GAA0C,IAAzD;;AAEA,QAAI,CAAC,KAAK9F,OAAL,CAAayE,MAAlB,EAA0B;AACxB,WAAKsB,yBAAL,CAA+B,KAAK9N,QAApC,EAA8C,KAAKsN,aAAnD;AACD;;AAED,QAAI,KAAKvF,OAAL,CAAatG,MAAjB,EAAyB;AACvB,WAAKA,MAAL;AACD;;AAEDxH,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;AACD;;;;;AAYD;SAEAoC,SAAA,kBAAS;AACP,QAAI,KAAKzB,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,eAAjC,CAAJ,EAAuD;AACrD,WAAKkB,IAAL;AACD,KAFD,MAEO;AACL,WAAKC,IAAL;AACD;AACF;;SAEDA,OAAA,gBAAO;AAAA;;AACL,QAAI,KAAKX,gBAAL,IACF,KAAKrN,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,eAAjC,CADF,EACqD;AACnD;AACD;;AAED,QAAIoB,OAAJ;AACA,QAAIC,WAAJ;;AAEA,QAAI,KAAKN,OAAT,EAAkB;AAChBK,MAAAA,OAAO,GAAG7K,cAAc,CAACE,IAAf,CAAoB6J,gBAApB,EAAsC,KAAKS,OAA3C,EACPhK,MADO,CACA,UAAA4J,IAAI,EAAI;AACd,YAAI,OAAO,KAAI,CAACzF,OAAL,CAAayE,MAApB,KAA+B,QAAnC,EAA6C;AAC3C,iBAAOgB,IAAI,CAACpY,YAAL,CAAkB,aAAlB,MAAqC,KAAI,CAAC2S,OAAL,CAAayE,MAAzD;AACD;;AAED,eAAOgB,IAAI,CAAC/M,SAAL,CAAeE,QAAf,CAAwBmM,mBAAxB,CAAP;AACD,OAPO,CAAV;;AASA,UAAImB,OAAO,CAAChS,MAAR,KAAmB,CAAvB,EAA0B;AACxBgS,QAAAA,OAAO,GAAG,IAAV;AACD;AACF;;AAED,QAAME,SAAS,GAAG/K,cAAc,CAACM,OAAf,CAAuB,KAAKiK,SAA5B,CAAlB;;AACA,QAAIM,OAAJ,EAAa;AACX,UAAMG,cAAc,GAAGH,OAAO,CAACrK,MAAR,CAAe,UAAA4J,IAAI;AAAA,eAAIW,SAAS,KAAKX,IAAlB;AAAA,OAAnB,CAAvB;AACAU,MAAAA,WAAW,GAAGE,cAAc,CAAC,CAAD,CAAd,GAAoBnU,IAAI,CAACG,OAAL,CAAagU,cAAc,CAAC,CAAD,CAA3B,EAAgC/O,UAAhC,CAApB,GAAgE,IAA9E;;AAEA,UAAI6O,WAAW,IAAIA,WAAW,CAACb,gBAA/B,EAAiD;AAC/C;AACD;AACF;;AAED,QAAMgB,UAAU,GAAG7S,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyM,UAApC,CAAnB;;AACA,QAAI4B,UAAU,CAAC7P,gBAAf,EAAiC;AAC/B;AACD;;AAED,QAAIyP,OAAJ,EAAa;AACXA,MAAAA,OAAO,CAAC1W,OAAR,CAAgB,UAAA+W,UAAU,EAAI;AAC5B,YAAIH,SAAS,KAAKG,UAAlB,EAA8B;AAC5BlB,UAAAA,QAAQ,CAACmB,iBAAT,CAA2BD,UAA3B,EAAuC,MAAvC;AACD;;AAED,YAAI,CAACJ,WAAL,EAAkB;AAChBjU,UAAAA,IAAI,CAACC,OAAL,CAAaoU,UAAb,EAAyBjP,UAAzB,EAAmC,IAAnC;AACD;AACF,OARD;AASD;;AAED,QAAMmP,SAAS,GAAG,KAAKC,aAAL,EAAlB;;AAEA,SAAKzO,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BoM,mBAA/B;;AACA,SAAK9M,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4B2C,qBAA5B;;AAEA,SAAK/M,QAAL,CAAc/H,KAAd,CAAoBuW,SAApB,IAAiC,CAAjC;;AAEA,QAAI,KAAKlB,aAAL,CAAmBrR,MAAvB,EAA+B;AAC7B,WAAKqR,aAAL,CAAmB/V,OAAnB,CAA2B,UAAArC,OAAO,EAAI;AACpCA,QAAAA,OAAO,CAACuL,SAAR,CAAkBC,MAAlB,CAAyBsM,oBAAzB;AACA9X,QAAAA,OAAO,CAACwM,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD,OAHD;AAID;;AAED,SAAKgN,gBAAL,CAAsB,IAAtB;;AAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,MAAA,KAAI,CAAC3O,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BqM,qBAA/B;;AACA,MAAA,KAAI,CAAC/M,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4B0C,mBAA5B,EAAiDD,eAAjD;;AAEA,MAAA,KAAI,CAAC7M,QAAL,CAAc/H,KAAd,CAAoBuW,SAApB,IAAiC,EAAjC;;AAEA,MAAA,KAAI,CAACE,gBAAL,CAAsB,KAAtB;;AAEAlT,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAI,CAAC8B,QAA1B,EAAoC0M,WAApC;AACD,KATD;;AAWA,QAAMkC,oBAAoB,GAAGJ,SAAS,CAAC,CAAD,CAAT,CAAazW,WAAb,KAA6ByW,SAAS,CAACxQ,KAAV,CAAgB,CAAhB,CAA1D;AACA,QAAM6Q,UAAU,cAAYD,oBAA5B;AACA,QAAM/Y,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKsK,QAAN,CAA3D;AAEAxE,IAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgDya,QAAhD;AAEAnY,IAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBnK,kBAAhB,CAApB;AACA,SAAKmK,QAAL,CAAc/H,KAAd,CAAoBuW,SAApB,IAAoC,KAAKxO,QAAL,CAAc6O,UAAd,CAApC;AACD;;SAEDd,OAAA,gBAAO;AAAA;;AACL,QAAI,KAAKV,gBAAL,IACF,CAAC,KAAKrN,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,eAAjC,CADH,EACsD;AACpD;AACD;;AAED,QAAMwB,UAAU,GAAG7S,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoC2M,UAApC,CAAnB;;AACA,QAAI0B,UAAU,CAAC7P,gBAAf,EAAiC;AAC/B;AACD;;AAED,QAAMgQ,SAAS,GAAG,KAAKC,aAAL,EAAlB;;AAEA,SAAKzO,QAAL,CAAc/H,KAAd,CAAoBuW,SAApB,IAAoC,KAAKxO,QAAL,CAAc2C,qBAAd,GAAsC6L,SAAtC,CAApC;AAEA1V,IAAAA,MAAM,CAAC,KAAKkH,QAAN,CAAN;;AAEA,SAAKA,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4B2C,qBAA5B;;AACA,SAAK/M,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BoM,mBAA/B,EAAoDD,eAApD;;AAEA,QAAMiC,kBAAkB,GAAG,KAAKxB,aAAL,CAAmBrR,MAA9C;;AACA,QAAI6S,kBAAkB,GAAG,CAAzB,EAA4B;AAC1B,WAAK,IAAI9S,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8S,kBAApB,EAAwC9S,CAAC,EAAzC,EAA6C;AAC3C,YAAMkC,OAAO,GAAG,KAAKoP,aAAL,CAAmBtR,CAAnB,CAAhB;AACA,YAAMwR,IAAI,GAAG/X,sBAAsB,CAACyI,OAAD,CAAnC;;AAEA,YAAIsP,IAAI,IAAI,CAACA,IAAI,CAAC/M,SAAL,CAAeE,QAAf,CAAwBkM,eAAxB,CAAb,EAAuD;AACrD3O,UAAAA,OAAO,CAACuC,SAAR,CAAkB2J,GAAlB,CAAsB4C,oBAAtB;AACA9O,UAAAA,OAAO,CAACwD,YAAR,CAAqB,eAArB,EAAsC,KAAtC;AACD;AACF;AACF;;AAED,SAAKgN,gBAAL,CAAsB,IAAtB;;AAEA,QAAMC,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,MAAA,MAAI,CAACD,gBAAL,CAAsB,KAAtB;;AACA,MAAA,MAAI,CAAC1O,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BqM,qBAA/B;;AACA,MAAA,MAAI,CAAC/M,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4B0C,mBAA5B;;AACAtR,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAC8B,QAA1B,EAAoC4M,YAApC;AACD,KALD;;AAOA,SAAK5M,QAAL,CAAc/H,KAAd,CAAoBuW,SAApB,IAAiC,EAAjC;AACA,QAAM3Y,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKsK,QAAN,CAA3D;AAEAxE,IAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgDya,QAAhD;AACAnY,IAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBnK,kBAAhB,CAApB;AACD;;SAED6Y,mBAAA,0BAAiBK,eAAjB,EAAkC;AAChC,SAAK1B,gBAAL,GAAwB0B,eAAxB;AACD;;SAEDxO,UAAA,mBAAU;AACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;AAEA,SAAK0I,OAAL,GAAe,IAAf;AACA,SAAK6F,OAAL,GAAe,IAAf;AACA,SAAK5N,QAAL,GAAgB,IAAhB;AACA,SAAKsN,aAAL,GAAqB,IAArB;AACA,SAAKD,gBAAL,GAAwB,IAAxB;AACD;;;SAIDrF,aAAA,oBAAW7Q,MAAX,EAAmB;AACjBA,IAAAA,MAAM,gBACDyN,SADC,EAEDzN,MAFC,CAAN;AAIAA,IAAAA,MAAM,CAACsK,MAAP,GAAgBpE,OAAO,CAAClG,MAAM,CAACsK,MAAR,CAAvB,CALiB;;AAMjBxK,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAegO,aAAf,CAAf;AACA,WAAOhO,MAAP;AACD;;SAEDsX,gBAAA,yBAAgB;AACd,WAAO,KAAKzO,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCsM,KAAjC,IAA0CA,KAA1C,GAAkDC,MAAzD;AACD;;SAEDW,aAAA,sBAAa;AAAA;;AAAA,QACLrB,MADK,GACM,KAAKzE,OADX,CACLyE,MADK;;AAGX,QAAIlW,SAAS,CAACkW,MAAD,CAAb,EAAuB;AACrB;AACA,UAAI,OAAOA,MAAM,CAACwC,MAAd,KAAyB,WAAzB,IAAwC,OAAOxC,MAAM,CAAC,CAAD,CAAb,KAAqB,WAAjE,EAA8E;AAC5EA,QAAAA,MAAM,GAAGA,MAAM,CAAC,CAAD,CAAf;AACD;AACF,KALD,MAKO;AACLA,MAAAA,MAAM,GAAGpJ,cAAc,CAACM,OAAf,CAAuB8I,MAAvB,CAAT;AACD;;AAED,QAAMrX,QAAQ,GAAMoM,sBAAN,uBAA2CiL,MAA3C,QAAd;AAEApJ,IAAAA,cAAc,CAACE,IAAf,CAAoBnO,QAApB,EAA8BqX,MAA9B,EACGjV,OADH,CACW,UAAArC,OAAO,EAAI;AAClB,UAAM+Z,QAAQ,GAAGxZ,sBAAsB,CAACP,OAAD,CAAvC;;AAEA,MAAA,MAAI,CAAC4Y,yBAAL,CACEmB,QADF,EAEE,CAAC/Z,OAAD,CAFF;AAID,KARH;AAUA,WAAOsX,MAAP;AACD;;SAEDsB,4BAAA,mCAA0B5Y,OAA1B,EAAmCga,YAAnC,EAAiD;AAC/C,QAAI,CAACha,OAAD,IAAY,CAACga,YAAY,CAACjT,MAA9B,EAAsC;AACpC;AACD;;AAED,QAAMkT,MAAM,GAAGja,OAAO,CAACuL,SAAR,CAAkBE,QAAlB,CAA2BkM,eAA3B,CAAf;AAEAqC,IAAAA,YAAY,CAAC3X,OAAb,CAAqB,UAAAiW,IAAI,EAAI;AAC3B,UAAI2B,MAAJ,EAAY;AACV3B,QAAAA,IAAI,CAAC/M,SAAL,CAAeC,MAAf,CAAsBsM,oBAAtB;AACD,OAFD,MAEO;AACLQ,QAAAA,IAAI,CAAC/M,SAAL,CAAe2J,GAAf,CAAmB4C,oBAAnB;AACD;;AAEDQ,MAAAA,IAAI,CAAC9L,YAAL,CAAkB,eAAlB,EAAmCyN,MAAnC;AACD,KARD;AASD;;;WAIMZ,oBAAP,2BAAyBrZ,OAAzB,EAAkCiC,MAAlC,EAA0C;AACxC,QAAIyC,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAX;;AACA,QAAM0I,OAAO,gBACRnD,SADQ,EAER3C,WAAW,CAACI,iBAAZ,CAA8BnN,OAA9B,CAFQ,EAGP,OAAOiC,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHzC,CAAb;;AAMA,QAAI,CAACyC,IAAD,IAASmO,OAAO,CAACtG,MAAjB,IAA2B,OAAOtK,MAAP,KAAkB,QAA7C,IAAyD,YAAYU,IAAZ,CAAiBV,MAAjB,CAA7D,EAAuF;AACrF4Q,MAAAA,OAAO,CAACtG,MAAR,GAAiB,KAAjB;AACD;;AAED,QAAI,CAAC7H,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAIwT,QAAJ,CAAalY,OAAb,EAAsB6S,OAAtB,CAAP;AACD;;AAED,QAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,UAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;AACD;;AAEDyC,MAAAA,IAAI,CAACzC,MAAD,CAAJ;AACD;AACF;;WAEM2J,kBAAP,yBAAuB3J,MAAvB,EAA+B;AAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;AAC3BqM,MAAAA,QAAQ,CAACmB,iBAAT,CAA2B,IAA3B,EAAiCpX,MAAjC;AACD,KAFM,CAAP;AAGD;;WAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;AAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;AACD;;;;wBAzQoB;AACnB,aAAOD,SAAP;AACD;;;wBAEoB;AACnB,aAAOwF,SAAP;AACD;;;;;AAsQH;AACA;AACA;AACA;AACA;;;AAEApJ,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUlG,KAAV,EAAiB;AACrF;AACA,MAAIA,KAAK,CAACU,MAAN,CAAasO,OAAb,KAAyB,GAA7B,EAAkC;AAChChP,IAAAA,KAAK,CAAC6D,cAAN;AACD;;AAED,MAAMkQ,WAAW,GAAGnN,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAApB;AACA,MAAMlN,QAAQ,GAAGI,sBAAsB,CAAC,IAAD,CAAvC;AACA,MAAM8Z,gBAAgB,GAAGjM,cAAc,CAACE,IAAf,CAAoBnO,QAApB,CAAzB;AAEAka,EAAAA,gBAAgB,CAAC9X,OAAjB,CAAyB,UAAArC,OAAO,EAAI;AAClC,QAAM0E,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAb;AACA,QAAIlI,MAAJ;;AACA,QAAIyC,IAAJ,EAAU;AACR;AACA,UAAIA,IAAI,CAACgU,OAAL,KAAiB,IAAjB,IAAyB,OAAOwB,WAAW,CAAC5C,MAAnB,KAA8B,QAA3D,EAAqE;AACnE5S,QAAAA,IAAI,CAACmO,OAAL,CAAayE,MAAb,GAAsB4C,WAAW,CAAC5C,MAAlC;AACA5S,QAAAA,IAAI,CAACgU,OAAL,GAAehU,IAAI,CAACiU,UAAL,EAAf;AACD;;AAED1W,MAAAA,MAAM,GAAG,QAAT;AACD,KARD,MAQO;AACLA,MAAAA,MAAM,GAAGiY,WAAT;AACD;;AAEDhC,IAAAA,QAAQ,CAACmB,iBAAT,CAA2BrZ,OAA3B,EAAoCiC,MAApC;AACD,GAhBD;AAiBD,CA3BD;AA6BA;AACA;AACA;AACA;AACA;AACA;;AAEAiC,kBAAkB,CAAC,YAAM;AACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;AACA;;AACA,MAAIoF,CAAJ,EAAO;AACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;AACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAaiO,QAAQ,CAACtM,eAAtB;AACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBgM,QAAzB;;AACAhP,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;AACA,aAAOiM,QAAQ,CAACtM,eAAhB;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;AC5YA;AACA;AACA;AACA;AACA;;AAEA,IAAM3B,MAAI,GAAG,UAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,aAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAME,cAAY,GAAG,WAArB;AAEA,IAAM+P,UAAU,GAAG,QAAnB;AACA,IAAMC,SAAS,GAAG,OAAlB;AACA,IAAMC,OAAO,GAAG,KAAhB;AACA,IAAMC,YAAY,GAAG,SAArB;AACA,IAAMC,cAAc,GAAG,WAAvB;AACA,IAAMC,kBAAkB,GAAG,CAA3B;;AAEA,IAAMC,cAAc,GAAG,IAAIhY,MAAJ,CAAc6X,YAAd,SAA8BC,cAA9B,SAAgDJ,UAAhD,CAAvB;AAEA,IAAM3C,YAAU,YAAUrN,WAA1B;AACA,IAAMsN,cAAY,cAAYtN,WAA9B;AACA,IAAMmN,YAAU,YAAUnN,WAA1B;AACA,IAAMoN,aAAW,aAAWpN,WAA5B;AACA,IAAMuQ,WAAW,aAAWvQ,WAA5B;AACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AACA,IAAMuQ,sBAAsB,eAAaxQ,WAAb,GAAyBC,cAArD;AACA,IAAMwQ,oBAAoB,aAAWzQ,WAAX,GAAuBC,cAAjD;AAEA,IAAMyQ,mBAAmB,GAAG,UAA5B;AACA,IAAMnD,iBAAe,GAAG,MAAxB;AACA,IAAMoD,iBAAiB,GAAG,QAA1B;AACA,IAAMC,oBAAoB,GAAG,WAA7B;AACA,IAAMC,mBAAmB,GAAG,UAA5B;AACA,IAAMC,oBAAoB,GAAG,qBAA7B;AACA,IAAMC,iBAAiB,GAAG,QAA1B;AACA,IAAMC,0BAA0B,GAAG,iBAAnC;AAEA,IAAM/O,sBAAoB,GAAG,0BAA7B;AACA,IAAMgP,mBAAmB,GAAG,gBAA5B;AACA,IAAMC,aAAa,GAAG,gBAAtB;AACA,IAAMC,mBAAmB,GAAG,aAA5B;AACA,IAAMC,sBAAsB,GAAG,6DAA/B;AAEA,IAAMC,aAAa,GAAG,WAAtB;AACA,IAAMC,gBAAgB,GAAG,SAAzB;AACA,IAAMC,gBAAgB,GAAG,cAAzB;AACA,IAAMC,mBAAmB,GAAG,YAA5B;AACA,IAAMC,eAAe,GAAG,aAAxB;AACA,IAAMC,cAAc,GAAG,YAAvB;AAEA,IAAMpM,SAAO,GAAG;AACdnC,EAAAA,MAAM,EAAE,CADM;AAEdwO,EAAAA,IAAI,EAAE,IAFQ;AAGdC,EAAAA,QAAQ,EAAE,cAHI;AAIdC,EAAAA,SAAS,EAAE,QAJG;AAKd9Y,EAAAA,OAAO,EAAE,SALK;AAMd+Y,EAAAA,YAAY,EAAE;AANA,CAAhB;AASA,IAAMjM,aAAW,GAAG;AAClB1C,EAAAA,MAAM,EAAE,0BADU;AAElBwO,EAAAA,IAAI,EAAE,SAFY;AAGlBC,EAAAA,QAAQ,EAAE,kBAHQ;AAIlBC,EAAAA,SAAS,EAAE,kBAJO;AAKlB9Y,EAAAA,OAAO,EAAE,QALS;AAMlB+Y,EAAAA,YAAY,EAAE;AANI,CAApB;AASA;AACA;AACA;AACA;AACA;;IAEMC;AACJ,oBAAYnc,OAAZ,EAAqBiC,MAArB,EAA6B;AAC3B,SAAK6I,QAAL,GAAgB9K,OAAhB;AACA,SAAKoc,OAAL,GAAe,IAAf;AACA,SAAKvJ,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;AACA,SAAKoa,KAAL,GAAa,KAAKC,eAAL,EAAb;AACA,SAAKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;AAEA,SAAKnJ,kBAAL;;AACAtO,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;AACD;;;;;AAgBD;SAEAoC,SAAA,kBAAS;AACP,QAAI,KAAKzB,QAAL,CAAc2R,QAAd,IAA0B,KAAK3R,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCqP,mBAAjC,CAA9B,EAAqF;AACnF;AACD;;AAED,QAAM4B,QAAQ,GAAG,KAAK5R,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,iBAAjC,CAAjB;;AAEAwE,IAAAA,QAAQ,CAACQ,UAAT;;AAEA,QAAID,QAAJ,EAAc;AACZ;AACD;;AAED,SAAK5D,IAAL;AACD;;SAEDA,OAAA,gBAAO;AACL,QAAI,KAAKhO,QAAL,CAAc2R,QAAd,IAA0B,KAAK3R,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCqP,mBAAjC,CAA1B,IAAmF,KAAKuB,KAAL,CAAW9Q,SAAX,CAAqBE,QAArB,CAA8BkM,iBAA9B,CAAvF,EAAuI;AACrI;AACD;;AAED,QAAML,MAAM,GAAG6E,QAAQ,CAACS,oBAAT,CAA8B,KAAK9R,QAAnC,CAAf;AACA,QAAM+K,aAAa,GAAG;AACpBA,MAAAA,aAAa,EAAE,KAAK/K;AADA,KAAtB;AAIA,QAAM+R,SAAS,GAAGvW,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyM,YAApC,EAAgD1B,aAAhD,CAAlB;;AAEA,QAAIgH,SAAS,CAACvT,gBAAd,EAAgC;AAC9B;AACD,KAdI;;;AAiBL,QAAI,CAAC,KAAKiT,SAAV,EAAqB;AACnB,UAAI,OAAOO,MAAP,KAAkB,WAAtB,EAAmC;AACjC,cAAM,IAAI7F,SAAJ,CAAc,kEAAd,CAAN;AACD;;AAED,UAAI8F,gBAAgB,GAAG,KAAKjS,QAA5B;;AAEA,UAAI,KAAK+H,OAAL,CAAaoJ,SAAb,KAA2B,QAA/B,EAAyC;AACvCc,QAAAA,gBAAgB,GAAGzF,MAAnB;AACD,OAFD,MAEO,IAAIlW,SAAS,CAAC,KAAKyR,OAAL,CAAaoJ,SAAd,CAAb,EAAuC;AAC5Cc,QAAAA,gBAAgB,GAAG,KAAKlK,OAAL,CAAaoJ,SAAhC,CAD4C;;AAI5C,YAAI,OAAO,KAAKpJ,OAAL,CAAaoJ,SAAb,CAAuBnC,MAA9B,KAAyC,WAA7C,EAA0D;AACxDiD,UAAAA,gBAAgB,GAAG,KAAKlK,OAAL,CAAaoJ,SAAb,CAAuB,CAAvB,CAAnB;AACD;AACF,OAhBkB;AAmBnB;AACA;;;AACA,UAAI,KAAKpJ,OAAL,CAAamJ,QAAb,KAA0B,cAA9B,EAA8C;AAC5C1E,QAAAA,MAAM,CAAC/L,SAAP,CAAiB2J,GAAjB,CAAqBkG,0BAArB;AACD;;AAED,WAAKgB,OAAL,GAAe,IAAIU,MAAJ,CAAWC,gBAAX,EAA6B,KAAKV,KAAlC,EAAyC,KAAKW,gBAAL,EAAzC,CAAf;AACD,KA3CI;AA8CL;AACA;AACA;;;AACA,QAAI,kBAAkBnd,QAAQ,CAACyD,eAA3B,IACF,CAACgU,MAAM,CAAChM,OAAP,CAAeiQ,mBAAf,CADH,EACwC;AAAA;;AACtC,kBAAGlN,MAAH,aAAaxO,QAAQ,CAACmE,IAAT,CAAcyK,QAA3B,EACGpM,OADH,CACW,UAAAiW,IAAI;AAAA,eAAIhS,YAAY,CAACkC,EAAb,CAAgB8P,IAAhB,EAAsB,WAAtB,EAAmC,IAAnC,EAAyC3U,IAAI,EAA7C,CAAJ;AAAA,OADf;AAED;;AAED,SAAKmH,QAAL,CAAcmS,KAAd;;AACA,SAAKnS,QAAL,CAAc0B,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;AAEA,SAAK6P,KAAL,CAAW9Q,SAAX,CAAqBgB,MAArB,CAA4BoL,iBAA5B;;AACA,SAAK7M,QAAL,CAAcS,SAAd,CAAwBgB,MAAxB,CAA+BoL,iBAA/B;;AACArR,IAAAA,YAAY,CAAC0C,OAAb,CAAqBsO,MAArB,EAA6BE,aAA7B,EAA0C3B,aAA1C;AACD;;SAEDgD,OAAA,gBAAO;AACL,QAAI,KAAK/N,QAAL,CAAc2R,QAAd,IAA0B,KAAK3R,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCqP,mBAAjC,CAA1B,IAAmF,CAAC,KAAKuB,KAAL,CAAW9Q,SAAX,CAAqBE,QAArB,CAA8BkM,iBAA9B,CAAxF,EAAwI;AACtI;AACD;;AAED,QAAML,MAAM,GAAG6E,QAAQ,CAACS,oBAAT,CAA8B,KAAK9R,QAAnC,CAAf;AACA,QAAM+K,aAAa,GAAG;AACpBA,MAAAA,aAAa,EAAE,KAAK/K;AADA,KAAtB;AAIA,QAAMoS,SAAS,GAAG5W,YAAY,CAAC0C,OAAb,CAAqBsO,MAArB,EAA6BG,YAA7B,EAAyC5B,aAAzC,CAAlB;;AAEA,QAAIqH,SAAS,CAAC5T,gBAAd,EAAgC;AAC9B;AACD;;AAED,QAAI,KAAK8S,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAae,OAAb;AACD;;AAED,SAAKd,KAAL,CAAW9Q,SAAX,CAAqBgB,MAArB,CAA4BoL,iBAA5B;;AACA,SAAK7M,QAAL,CAAcS,SAAd,CAAwBgB,MAAxB,CAA+BoL,iBAA/B;;AACArR,IAAAA,YAAY,CAAC0C,OAAb,CAAqBsO,MAArB,EAA6BI,cAA7B,EAA2C7B,aAA3C;AACD;;SAEDxK,UAAA,mBAAU;AACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;AACA7D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgCV,WAAhC;AACA,SAAKU,QAAL,GAAgB,IAAhB;AACA,SAAKuR,KAAL,GAAa,IAAb;;AACA,QAAI,KAAKD,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAae,OAAb;;AACA,WAAKf,OAAL,GAAe,IAAf;AACD;AACF;;SAEDgB,SAAA,kBAAS;AACP,SAAKb,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;AACA,QAAI,KAAKJ,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAaiB,cAAb;AACD;AACF;;;SAIDhK,qBAAA,8BAAqB;AAAA;;AACnB/M,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B6P,WAA/B,EAA4C,UAAAxU,KAAK,EAAI;AACnDA,MAAAA,KAAK,CAAC6D,cAAN;AACA7D,MAAAA,KAAK,CAACmX,eAAN;;AACA,MAAA,KAAI,CAAC/Q,MAAL;AACD,KAJD;AAKD;;SAEDuG,aAAA,oBAAW7Q,MAAX,EAAmB;AACjBA,IAAAA,MAAM,gBACD,KAAKsb,WAAL,CAAiB7N,OADhB,EAED3C,WAAW,CAACI,iBAAZ,CAA8B,KAAKrC,QAAnC,CAFC,EAGD7I,MAHC,CAAN;AAMAF,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAe,KAAKsb,WAAL,CAAiBtN,WAAhC,CAAf;AAEA,WAAOhO,MAAP;AACD;;SAEDqa,kBAAA,2BAAkB;AAChB,WAAOpO,cAAc,CAACkB,IAAf,CAAoB,KAAKtE,QAAzB,EAAmCwQ,aAAnC,EAAkD,CAAlD,CAAP;AACD;;SAEDkC,gBAAA,yBAAgB;AACd,QAAMC,cAAc,GAAG,KAAK3S,QAAL,CAAc9H,UAArC;AACA,QAAI0a,SAAS,GAAG/B,gBAAhB,CAFc;;AAKd,QAAI8B,cAAc,CAAClS,SAAf,CAAyBE,QAAzB,CAAkCsP,iBAAlC,CAAJ,EAA0D;AACxD2C,MAAAA,SAAS,GAAG,KAAKrB,KAAL,CAAW9Q,SAAX,CAAqBE,QAArB,CAA8ByP,oBAA9B,IACVQ,gBADU,GAEVD,aAFF;AAGD,KAJD,MAIO,IAAIgC,cAAc,CAAClS,SAAf,CAAyBE,QAAzB,CAAkCuP,oBAAlC,CAAJ,EAA6D;AAClE0C,MAAAA,SAAS,GAAG7B,eAAZ;AACD,KAFM,MAEA,IAAI4B,cAAc,CAAClS,SAAf,CAAyBE,QAAzB,CAAkCwP,mBAAlC,CAAJ,EAA4D;AACjEyC,MAAAA,SAAS,GAAG5B,cAAZ;AACD,KAFM,MAEA,IAAI,KAAKO,KAAL,CAAW9Q,SAAX,CAAqBE,QAArB,CAA8ByP,oBAA9B,CAAJ,EAAyD;AAC9DwC,MAAAA,SAAS,GAAG9B,mBAAZ;AACD;;AAED,WAAO8B,SAAP;AACD;;SAEDlB,gBAAA,yBAAgB;AACd,WAAOrU,OAAO,CAAC,KAAK2C,QAAL,CAAcQ,OAAd,OAA0B6P,iBAA1B,CAAD,CAAd;AACD;;SAEDwC,aAAA,sBAAa;AAAA;;AACX,QAAMpQ,MAAM,GAAG,EAAf;;AAEA,QAAI,OAAO,KAAKsF,OAAL,CAAatF,MAApB,KAA+B,UAAnC,EAA+C;AAC7CA,MAAAA,MAAM,CAACtH,EAAP,GAAY,UAAAvB,IAAI,EAAI;AAClBA,QAAAA,IAAI,CAACkZ,OAAL,gBACKlZ,IAAI,CAACkZ,OADV,EAEM,MAAI,CAAC/K,OAAL,CAAatF,MAAb,CAAoB7I,IAAI,CAACkZ,OAAzB,EAAkC,MAAI,CAAC9S,QAAvC,KAAoD,EAF1D;AAKA,eAAOpG,IAAP;AACD,OAPD;AAQD,KATD,MASO;AACL6I,MAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKsF,OAAL,CAAatF,MAA7B;AACD;;AAED,WAAOA,MAAP;AACD;;SAEDyP,mBAAA,4BAAmB;AACjB,QAAMd,YAAY,GAAG;AACnBwB,MAAAA,SAAS,EAAE,KAAKF,aAAL,EADQ;AAEnBK,MAAAA,SAAS,EAAE;AACTtQ,QAAAA,MAAM,EAAE,KAAKoQ,UAAL,EADC;AAET5B,QAAAA,IAAI,EAAE;AACJ+B,UAAAA,OAAO,EAAE,KAAKjL,OAAL,CAAakJ;AADlB,SAFG;AAKTgC,QAAAA,eAAe,EAAE;AACfC,UAAAA,iBAAiB,EAAE,KAAKnL,OAAL,CAAamJ;AADjB;AALR;AAFQ,KAArB,CADiB;;AAejB,QAAI,KAAKnJ,OAAL,CAAa1P,OAAb,KAAyB,QAA7B,EAAuC;AACrC+Y,MAAAA,YAAY,CAAC2B,SAAb,CAAuBI,UAAvB,GAAoC;AAClCH,QAAAA,OAAO,EAAE;AADyB,OAApC;AAGD;;AAED,wBACK5B,YADL,EAEK,KAAKrJ,OAAL,CAAaqJ,YAFlB;AAID;;;WAIMgC,oBAAP,2BAAyBle,OAAzB,EAAkCiC,MAAlC,EAA0C;AACxC,QAAIyC,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAX;;AACA,QAAM0I,OAAO,GAAG,OAAO5Q,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;AAEA,QAAI,CAACyC,IAAL,EAAW;AACTA,MAAAA,IAAI,GAAG,IAAIyX,QAAJ,CAAanc,OAAb,EAAsB6S,OAAtB,CAAP;AACD;;AAED,QAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,UAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,cAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;AACD;;AAEDyC,MAAAA,IAAI,CAACzC,MAAD,CAAJ;AACD;AACF;;WAEM2J,kBAAP,yBAAuB3J,MAAvB,EAA+B;AAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;AAC3BsQ,MAAAA,QAAQ,CAAC+B,iBAAT,CAA2B,IAA3B,EAAiCjc,MAAjC;AACD,KAFM,CAAP;AAGD;;WAEM0a,aAAP,oBAAkBxW,KAAlB,EAAyB;AACvB,QAAIA,KAAK,KAAKA,KAAK,CAACsG,MAAN,KAAiBgO,kBAAjB,IACXtU,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAAC1B,GAAN,KAAc6V,OADlC,CAAT,EACsD;AACpD;AACD;;AAED,QAAM6D,OAAO,GAAGjQ,cAAc,CAACE,IAAf,CAAoB/B,sBAApB,CAAhB;;AAEA,SAAK,IAAIvF,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAG+W,OAAO,CAACpX,MAA9B,EAAsCD,CAAC,GAAGM,GAA1C,EAA+CN,CAAC,EAAhD,EAAoD;AAClD,UAAMwQ,MAAM,GAAG6E,QAAQ,CAACS,oBAAT,CAA8BuB,OAAO,CAACrX,CAAD,CAArC,CAAf;AACA,UAAMsX,OAAO,GAAGrZ,IAAI,CAACG,OAAL,CAAaiZ,OAAO,CAACrX,CAAD,CAApB,EAAyBqD,UAAzB,CAAhB;AACA,UAAM0L,aAAa,GAAG;AACpBA,QAAAA,aAAa,EAAEsI,OAAO,CAACrX,CAAD;AADF,OAAtB;;AAIA,UAAIX,KAAK,IAAIA,KAAK,CAACK,IAAN,KAAe,OAA5B,EAAqC;AACnCqP,QAAAA,aAAa,CAACwI,UAAd,GAA2BlY,KAA3B;AACD;;AAED,UAAI,CAACiY,OAAL,EAAc;AACZ;AACD;;AAED,UAAME,YAAY,GAAGF,OAAO,CAAC/B,KAA7B;;AACA,UAAI,CAAC8B,OAAO,CAACrX,CAAD,CAAP,CAAWyE,SAAX,CAAqBE,QAArB,CAA8BkM,iBAA9B,CAAL,EAAqD;AACnD;AACD;;AAED,UAAIxR,KAAK,KAAMA,KAAK,CAACK,IAAN,KAAe,OAAf,IACX,kBAAkB7D,IAAlB,CAAuBwD,KAAK,CAACU,MAAN,CAAasO,OAApC,CADU,IAEThP,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAAC1B,GAAN,KAAc6V,OAFpC,CAAL,IAGAgE,YAAY,CAAC7S,QAAb,CAAsBtF,KAAK,CAACU,MAA5B,CAHJ,EAGyC;AACvC;AACD;;AAED,UAAMqW,SAAS,GAAG5W,YAAY,CAAC0C,OAAb,CAAqBsO,MAArB,EAA6BG,YAA7B,EAAyC5B,aAAzC,CAAlB;;AACA,UAAIqH,SAAS,CAAC5T,gBAAd,EAAgC;AAC9B;AACD,OA9BiD;AAiClD;;;AACA,UAAI,kBAAkBzJ,QAAQ,CAACyD,eAA/B,EAAgD;AAAA;;AAC9C,qBAAG+K,MAAH,cAAaxO,QAAQ,CAACmE,IAAT,CAAcyK,QAA3B,EACGpM,OADH,CACW,UAAAiW,IAAI;AAAA,iBAAIhS,YAAY,CAACC,GAAb,CAAiB+R,IAAjB,EAAuB,WAAvB,EAAoC,IAApC,EAA0C3U,IAAI,EAA9C,CAAJ;AAAA,SADf;AAED;;AAEDwa,MAAAA,OAAO,CAACrX,CAAD,CAAP,CAAW0F,YAAX,CAAwB,eAAxB,EAAyC,OAAzC;;AAEA,UAAI4R,OAAO,CAAChC,OAAZ,EAAqB;AACnBgC,QAAAA,OAAO,CAAChC,OAAR,CAAgBe,OAAhB;AACD;;AAEDmB,MAAAA,YAAY,CAAC/S,SAAb,CAAuBC,MAAvB,CAA8BmM,iBAA9B;AACAwG,MAAAA,OAAO,CAACrX,CAAD,CAAP,CAAWyE,SAAX,CAAqBC,MAArB,CAA4BmM,iBAA5B;AACArR,MAAAA,YAAY,CAAC0C,OAAb,CAAqBsO,MAArB,EAA6BI,cAA7B,EAA2C7B,aAA3C;AACD;AACF;;WAEM+G,uBAAP,8BAA4B5c,OAA5B,EAAqC;AACnC,WAAOO,sBAAsB,CAACP,OAAD,CAAtB,IAAmCA,OAAO,CAACgD,UAAlD;AACD;;WAEMub,wBAAP,+BAA6BpY,KAA7B,EAAoC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAI,kBAAkBxD,IAAlB,CAAuBwD,KAAK,CAACU,MAAN,CAAasO,OAApC,IACFhP,KAAK,CAAC1B,GAAN,KAAc4V,SAAd,IAA4BlU,KAAK,CAAC1B,GAAN,KAAc2V,UAAd,KAC1BjU,KAAK,CAAC1B,GAAN,KAAc+V,cAAd,IAAgCrU,KAAK,CAAC1B,GAAN,KAAc8V,YAA/C,IACCpU,KAAK,CAACU,MAAN,CAAayE,OAAb,CAAqBgQ,aAArB,CAF0B,CAD1B,GAIF,CAACZ,cAAc,CAAC/X,IAAf,CAAoBwD,KAAK,CAAC1B,GAA1B,CAJH,EAImC;AACjC;AACD;;AAED0B,IAAAA,KAAK,CAAC6D,cAAN;AACA7D,IAAAA,KAAK,CAACmX,eAAN;;AAEA,QAAI,KAAKb,QAAL,IAAiB,KAAKlR,SAAL,CAAeE,QAAf,CAAwBqP,mBAAxB,CAArB,EAAmE;AACjE;AACD;;AAED,QAAMxD,MAAM,GAAG6E,QAAQ,CAACS,oBAAT,CAA8B,IAA9B,CAAf;AACA,QAAMF,QAAQ,GAAG,KAAKnR,SAAL,CAAeE,QAAf,CAAwBkM,iBAAxB,CAAjB;;AAEA,QAAIxR,KAAK,CAAC1B,GAAN,KAAc2V,UAAlB,EAA8B;AAC5B,UAAM3N,MAAM,GAAG,KAAK0B,OAAL,CAAa9B,sBAAb,IAAqC,IAArC,GAA4C6B,cAAc,CAACe,IAAf,CAAoB,IAApB,EAA0B5C,sBAA1B,EAAgD,CAAhD,CAA3D;AACAI,MAAAA,MAAM,CAACwQ,KAAP;AACAd,MAAAA,QAAQ,CAACQ,UAAT;AACA;AACD;;AAED,QAAI,CAACD,QAAD,IAAavW,KAAK,CAAC1B,GAAN,KAAc4V,SAA/B,EAA0C;AACxC8B,MAAAA,QAAQ,CAACQ,UAAT;AACA;AACD;;AAED,QAAM6B,KAAK,GAAGtQ,cAAc,CAACE,IAAf,CAAoBoN,sBAApB,EAA4ClE,MAA5C,EAAoD5I,MAApD,CAA2D5L,SAA3D,CAAd;;AAEA,QAAI,CAAC0b,KAAK,CAACzX,MAAX,EAAmB;AACjB;AACD;;AAED,QAAIiN,KAAK,GAAGwK,KAAK,CAAC1W,OAAN,CAAc3B,KAAK,CAACU,MAApB,CAAZ;;AAEA,QAAIV,KAAK,CAAC1B,GAAN,KAAc8V,YAAd,IAA8BvG,KAAK,GAAG,CAA1C,EAA6C;AAAE;AAC7CA,MAAAA,KAAK;AACN;;AAED,QAAI7N,KAAK,CAAC1B,GAAN,KAAc+V,cAAd,IAAgCxG,KAAK,GAAGwK,KAAK,CAACzX,MAAN,GAAe,CAA3D,EAA8D;AAAE;AAC9DiN,MAAAA,KAAK;AACN,KApDiC;;;AAuDlCA,IAAAA,KAAK,GAAGA,KAAK,KAAK,CAAC,CAAX,GAAe,CAAf,GAAmBA,KAA3B;AAEAwK,IAAAA,KAAK,CAACxK,KAAD,CAAL,CAAaiJ,KAAb;AACD;;WAEMjR,cAAP,qBAAmBhM,OAAnB,EAA4B;AAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;AACD;;;;wBA9XoB;AACnB,aAAOD,SAAP;AACD;;;wBAEoB;AACnB,aAAOwF,SAAP;AACD;;;wBAEwB;AACvB,aAAOO,aAAP;AACD;;;;;AAuXH;AACA;AACA;AACA;AACA;;;AAEA3J,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B+a,sBAA1B,EAAkDvO,sBAAlD,EAAwE8P,QAAQ,CAACoC,qBAAjF;AACAjY,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B+a,sBAA1B,EAAkDU,aAAlD,EAAiEa,QAAQ,CAACoC,qBAA1E;AACAjY,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD0R,QAAQ,CAACQ,UAAzD;AACArW,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0Bgb,oBAA1B,EAAgDsB,QAAQ,CAACQ,UAAzD;AACArW,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUlG,KAAV,EAAiB;AACrFA,EAAAA,KAAK,CAAC6D,cAAN;AACA7D,EAAAA,KAAK,CAACmX,eAAN;AACAnB,EAAAA,QAAQ,CAAC+B,iBAAT,CAA2B,IAA3B,EAAiC,QAAjC;AACD,CAJD;AAKA5X,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4Q,mBAAhD,EAAqE,UAAApG,CAAC;AAAA,SAAIA,CAAC,CAACqI,eAAF,EAAJ;AAAA,CAAtE;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEApZ,kBAAkB,CAAC,YAAM;AACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;AACA;;AACA,MAAIoF,CAAJ,EAAO;AACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;AACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAakS,QAAQ,CAACvQ,eAAtB;AACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBiQ,QAAzB;;AACAjT,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;AACA,aAAOkQ,QAAQ,CAACvQ,eAAhB;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;AClfA;AACA;AACA;AACA;AACA;;AAEA,IAAM3B,MAAI,GAAG,OAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,UAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAME,cAAY,GAAG,WAArB;AACA,IAAM+P,YAAU,GAAG,QAAnB;AAEA,IAAM1K,SAAO,GAAG;AACd+O,EAAAA,QAAQ,EAAE,IADI;AAEd7O,EAAAA,QAAQ,EAAE,IAFI;AAGdqN,EAAAA,KAAK,EAAE,IAHO;AAIdnE,EAAAA,IAAI,EAAE;AAJQ,CAAhB;AAOA,IAAM7I,aAAW,GAAG;AAClBwO,EAAAA,QAAQ,EAAE,kBADQ;AAElB7O,EAAAA,QAAQ,EAAE,SAFQ;AAGlBqN,EAAAA,KAAK,EAAE,SAHW;AAIlBnE,EAAAA,IAAI,EAAE;AAJY,CAApB;AAOA,IAAMrB,YAAU,YAAUrN,WAA1B;AACA,IAAMsU,oBAAoB,qBAAmBtU,WAA7C;AACA,IAAMsN,cAAY,cAAYtN,WAA9B;AACA,IAAMmN,YAAU,YAAUnN,WAA1B;AACA,IAAMoN,aAAW,aAAWpN,WAA5B;AACA,IAAMuU,aAAa,eAAavU,WAAhC;AACA,IAAMwU,YAAY,cAAYxU,WAA9B;AACA,IAAMyU,mBAAmB,qBAAmBzU,WAA5C;AACA,IAAM0U,qBAAqB,uBAAqB1U,WAAhD;AACA,IAAM2U,qBAAqB,uBAAqB3U,WAAhD;AACA,IAAM4U,uBAAuB,yBAAuB5U,WAApD;AACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AAEA,IAAM4U,6BAA6B,GAAG,yBAAtC;AACA,IAAMC,mBAAmB,GAAG,gBAA5B;AACA,IAAMC,eAAe,GAAG,YAAxB;AACA,IAAMC,eAAe,GAAG,MAAxB;AACA,IAAMzH,iBAAe,GAAG,MAAxB;AACA,IAAM0H,iBAAiB,GAAG,cAA1B;AAEA,IAAMC,eAAe,GAAG,eAAxB;AACA,IAAMC,mBAAmB,GAAG,aAA5B;AACA,IAAMlT,sBAAoB,GAAG,uBAA7B;AACA,IAAMmT,qBAAqB,GAAG,wBAA9B;AACA,IAAMC,sBAAsB,GAAG,mDAA/B;AACA,IAAMC,uBAAuB,GAAG,aAAhC;AAEA;AACA;AACA;AACA;AACA;;IAEMC;AACJ,iBAAY3f,OAAZ,EAAqBiC,MAArB,EAA6B;AAC3B,SAAK4Q,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;AACA,SAAK6I,QAAL,GAAgB9K,OAAhB;AACA,SAAK4f,OAAL,GAAe1R,cAAc,CAACM,OAAf,CAAuB8Q,eAAvB,EAAwCtf,OAAxC,CAAf;AACA,SAAK6f,SAAL,GAAiB,IAAjB;AACA,SAAKC,QAAL,GAAgB,KAAhB;AACA,SAAKC,kBAAL,GAA0B,KAA1B;AACA,SAAKC,oBAAL,GAA4B,KAA5B;AACA,SAAK7H,gBAAL,GAAwB,KAAxB;AACA,SAAK8H,eAAL,GAAuB,CAAvB;AACAlb,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;AACD;;;;;AAYD;SAEAoC,SAAA,gBAAOsJ,aAAP,EAAsB;AACpB,WAAO,KAAKiK,QAAL,GAAgB,KAAKjH,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAUjD,aAAV,CAArC;AACD;;SAEDiD,OAAA,cAAKjD,aAAL,EAAoB;AAAA;;AAClB,QAAI,KAAKiK,QAAL,IAAiB,KAAK3H,gBAA1B,EAA4C;AAC1C;AACD;;AAED,QAAI,KAAKrN,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC2T,eAAjC,CAAJ,EAAuD;AACrD,WAAKjH,gBAAL,GAAwB,IAAxB;AACD;;AAED,QAAM0E,SAAS,GAAGvW,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyM,YAApC,EAAgD;AAChE1B,MAAAA,aAAa,EAAbA;AADgE,KAAhD,CAAlB;;AAIA,QAAI,KAAKiK,QAAL,IAAiBjD,SAAS,CAACvT,gBAA/B,EAAiD;AAC/C;AACD;;AAED,SAAKwW,QAAL,GAAgB,IAAhB;;AAEA,SAAKI,eAAL;;AACA,SAAKC,aAAL;;AAEA,SAAKC,aAAL;;AAEA,SAAKC,eAAL;;AACA,SAAKC,eAAL;;AAEAha,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EACE+T,mBADF,EAEEW,qBAFF,EAGE,UAAArZ,KAAK;AAAA,aAAI,KAAI,CAAC0S,IAAL,CAAU1S,KAAV,CAAJ;AAAA,KAHP;AAMAG,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKoX,OAArB,EAA8BZ,uBAA9B,EAAuD,YAAM;AAC3D1Y,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAI,CAACqC,QAAtB,EAAgCiU,qBAAhC,EAAuD,UAAA5Y,KAAK,EAAI;AAC9D,YAAIA,KAAK,CAACU,MAAN,KAAiB,KAAI,CAACiE,QAA1B,EAAoC;AAClC,UAAA,KAAI,CAACkV,oBAAL,GAA4B,IAA5B;AACD;AACF,OAJD;AAKD,KAND;;AAQA,SAAKO,aAAL,CAAmB;AAAA,aAAM,KAAI,CAACC,YAAL,CAAkB3K,aAAlB,CAAN;AAAA,KAAnB;AACD;;SAEDgD,OAAA,cAAK1S,KAAL,EAAY;AAAA;;AACV,QAAIA,KAAJ,EAAW;AACTA,MAAAA,KAAK,CAAC6D,cAAN;AACD;;AAED,QAAI,CAAC,KAAK8V,QAAN,IAAkB,KAAK3H,gBAA3B,EAA6C;AAC3C;AACD;;AAED,QAAM+E,SAAS,GAAG5W,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoC2M,YAApC,CAAlB;;AAEA,QAAIyF,SAAS,CAAC5T,gBAAd,EAAgC;AAC9B;AACD;;AAED,SAAKwW,QAAL,GAAgB,KAAhB;;AACA,QAAMW,UAAU,GAAG,KAAK3V,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC2T,eAAjC,CAAnB;;AAEA,QAAIqB,UAAJ,EAAgB;AACd,WAAKtI,gBAAL,GAAwB,IAAxB;AACD;;AAED,SAAKkI,eAAL;;AACA,SAAKC,eAAL;;AAEAha,IAAAA,YAAY,CAACC,GAAb,CAAiB1G,QAAjB,EAA2B8e,aAA3B;;AAEA,SAAK7T,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BmM,iBAA/B;;AAEArR,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgC+T,mBAAhC;AACAvY,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKqZ,OAAtB,EAA+BZ,uBAA/B;;AAEA,QAAIyB,UAAJ,EAAgB;AACd,UAAM9f,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKsK,QAAN,CAA3D;AAEAxE,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgD,UAAAmH,KAAK;AAAA,eAAI,MAAI,CAACua,UAAL,CAAgBva,KAAhB,CAAJ;AAAA,OAArD;AACA7E,MAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBnK,kBAAhB,CAApB;AACD,KALD,MAKO;AACL,WAAK+f,UAAL;AACD;AACF;;SAEDrV,UAAA,mBAAU;AACR,KAAC5K,MAAD,EAAS,KAAKqK,QAAd,EAAwB,KAAK8U,OAA7B,EACGvd,OADH,CACW,UAAAse,WAAW;AAAA,aAAIra,YAAY,CAACC,GAAb,CAAiBoa,WAAjB,EAA8BvW,WAA9B,CAAJ;AAAA,KADtB;AAGA;AACJ;AACA;AACA;AACA;;AACI9D,IAAAA,YAAY,CAACC,GAAb,CAAiB1G,QAAjB,EAA2B8e,aAA3B;AAEA5Z,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;AAEA,SAAK0I,OAAL,GAAe,IAAf;AACA,SAAK/H,QAAL,GAAgB,IAAhB;AACA,SAAK8U,OAAL,GAAe,IAAf;AACA,SAAKC,SAAL,GAAiB,IAAjB;AACA,SAAKC,QAAL,GAAgB,IAAhB;AACA,SAAKC,kBAAL,GAA0B,IAA1B;AACA,SAAKC,oBAAL,GAA4B,IAA5B;AACA,SAAK7H,gBAAL,GAAwB,IAAxB;AACA,SAAK8H,eAAL,GAAuB,IAAvB;AACD;;SAEDW,eAAA,wBAAe;AACb,SAAKR,aAAL;AACD;;;SAIDtN,aAAA,oBAAW7Q,MAAX,EAAmB;AACjBA,IAAAA,MAAM,gBACDyN,SADC,EAEDzN,MAFC,CAAN;AAIAF,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAegO,aAAf,CAAf;AACA,WAAOhO,MAAP;AACD;;SAEDue,eAAA,sBAAa3K,aAAb,EAA4B;AAAA;;AAC1B,QAAM4K,UAAU,GAAG,KAAK3V,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC2T,eAAjC,CAAnB;;AACA,QAAMyB,SAAS,GAAG3S,cAAc,CAACM,OAAf,CAAuB+Q,mBAAvB,EAA4C,KAAKK,OAAjD,CAAlB;;AAEA,QAAI,CAAC,KAAK9U,QAAL,CAAc9H,UAAf,IACA,KAAK8H,QAAL,CAAc9H,UAAd,CAAyB3B,QAAzB,KAAsCyN,IAAI,CAACC,YAD/C,EAC6D;AAC3D;AACAlP,MAAAA,QAAQ,CAACmE,IAAT,CAAc8c,WAAd,CAA0B,KAAKhW,QAA/B;AACD;;AAED,SAAKA,QAAL,CAAc/H,KAAd,CAAoBI,OAApB,GAA8B,OAA9B;;AACA,SAAK2H,QAAL,CAAcoC,eAAd,CAA8B,aAA9B;;AACA,SAAKpC,QAAL,CAAc0B,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;AACA,SAAK1B,QAAL,CAAc0B,YAAd,CAA2B,MAA3B,EAAmC,QAAnC;;AACA,SAAK1B,QAAL,CAAc6C,SAAd,GAA0B,CAA1B;;AAEA,QAAIkT,SAAJ,EAAe;AACbA,MAAAA,SAAS,CAAClT,SAAV,GAAsB,CAAtB;AACD;;AAED,QAAI8S,UAAJ,EAAgB;AACd7c,MAAAA,MAAM,CAAC,KAAKkH,QAAN,CAAN;AACD;;AAED,SAAKA,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4ByC,iBAA5B;;AAEA,QAAI,KAAK9E,OAAL,CAAaoK,KAAjB,EAAwB;AACtB,WAAK8D,aAAL;AACD;;AAED,QAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,GAAM;AAC/B,UAAI,MAAI,CAACnO,OAAL,CAAaoK,KAAjB,EAAwB;AACtB,QAAA,MAAI,CAACnS,QAAL,CAAcmS,KAAd;AACD;;AAED,MAAA,MAAI,CAAC9E,gBAAL,GAAwB,KAAxB;AACA7R,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAC8B,QAA1B,EAAoC0M,aAApC,EAAiD;AAC/C3B,QAAAA,aAAa,EAAbA;AAD+C,OAAjD;AAGD,KATD;;AAWA,QAAI4K,UAAJ,EAAgB;AACd,UAAM9f,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKof,OAAN,CAA3D;AAEAtZ,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKmX,OAAtB,EAA+B5gB,cAA/B,EAA+CgiB,kBAA/C;AACA1f,MAAAA,oBAAoB,CAAC,KAAKse,OAAN,EAAejf,kBAAf,CAApB;AACD,KALD,MAKO;AACLqgB,MAAAA,kBAAkB;AACnB;AACF;;SAEDD,gBAAA,yBAAgB;AAAA;;AACdza,IAAAA,YAAY,CAACC,GAAb,CAAiB1G,QAAjB,EAA2B8e,aAA3B,EADc;;AAEdrY,IAAAA,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B8e,aAA1B,EAAyC,UAAAxY,KAAK,EAAI;AAChD,UAAItG,QAAQ,KAAKsG,KAAK,CAACU,MAAnB,IACA,MAAI,CAACiE,QAAL,KAAkB3E,KAAK,CAACU,MADxB,IAEA,CAAC,MAAI,CAACiE,QAAL,CAAcW,QAAd,CAAuBtF,KAAK,CAACU,MAA7B,CAFL,EAE2C;AACzC,QAAA,MAAI,CAACiE,QAAL,CAAcmS,KAAd;AACD;AACF,KAND;AAOD;;SAEDoD,kBAAA,2BAAkB;AAAA;;AAChB,QAAI,KAAKP,QAAT,EAAmB;AACjBxZ,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+BgU,qBAA/B,EAAsD,UAAA3Y,KAAK,EAAI;AAC7D,YAAI,MAAI,CAAC0M,OAAL,CAAajD,QAAb,IAAyBzJ,KAAK,CAAC1B,GAAN,KAAc2V,YAA3C,EAAuD;AACrDjU,UAAAA,KAAK,CAAC6D,cAAN;;AACA,UAAA,MAAI,CAAC6O,IAAL;AACD,SAHD,MAGO,IAAI,CAAC,MAAI,CAAChG,OAAL,CAAajD,QAAd,IAA0BzJ,KAAK,CAAC1B,GAAN,KAAc2V,YAA5C,EAAwD;AAC7D,UAAA,MAAI,CAAC6G,0BAAL;AACD;AACF,OAPD;AAQD,KATD,MASO;AACL3a,MAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgCgU,qBAAhC;AACD;AACF;;SAEDwB,kBAAA,2BAAkB;AAAA;;AAChB,QAAI,KAAKR,QAAT,EAAmB;AACjBxZ,MAAAA,YAAY,CAACkC,EAAb,CAAgB/H,MAAhB,EAAwBme,YAAxB,EAAsC;AAAA,eAAM,MAAI,CAACwB,aAAL,EAAN;AAAA,OAAtC;AACD,KAFD,MAEO;AACL9Z,MAAAA,YAAY,CAACC,GAAb,CAAiB9F,MAAjB,EAAyBme,YAAzB;AACD;AACF;;SAED8B,aAAA,sBAAa;AAAA;;AACX,SAAK5V,QAAL,CAAc/H,KAAd,CAAoBI,OAApB,GAA8B,MAA9B;;AACA,SAAK2H,QAAL,CAAc0B,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;AACA,SAAK1B,QAAL,CAAcoC,eAAd,CAA8B,YAA9B;;AACA,SAAKpC,QAAL,CAAcoC,eAAd,CAA8B,MAA9B;;AACA,SAAKiL,gBAAL,GAAwB,KAAxB;;AACA,SAAKoI,aAAL,CAAmB,YAAM;AACvB1gB,MAAAA,QAAQ,CAACmE,IAAT,CAAcuH,SAAd,CAAwBC,MAAxB,CAA+B2T,eAA/B;;AACA,MAAA,MAAI,CAAC+B,iBAAL;;AACA,MAAA,MAAI,CAACC,eAAL;;AACA7a,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAC8B,QAA1B,EAAoC4M,cAApC;AACD,KALD;AAMD;;SAED0J,kBAAA,2BAAkB;AAChB,SAAKvB,SAAL,CAAe7c,UAAf,CAA0B2I,WAA1B,CAAsC,KAAKkU,SAA3C;;AACA,SAAKA,SAAL,GAAiB,IAAjB;AACD;;SAEDU,gBAAA,uBAAcpc,QAAd,EAAwB;AAAA;;AACtB,QAAMkd,OAAO,GAAG,KAAKvW,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC2T,eAAjC,IACdA,eADc,GAEd,EAFF;;AAIA,QAAI,KAAKU,QAAL,IAAiB,KAAKjN,OAAL,CAAa4L,QAAlC,EAA4C;AAC1C,WAAKoB,SAAL,GAAiBhgB,QAAQ,CAACyhB,aAAT,CAAuB,KAAvB,CAAjB;AACA,WAAKzB,SAAL,CAAe0B,SAAf,GAA2BrC,mBAA3B;;AAEA,UAAImC,OAAJ,EAAa;AACX,aAAKxB,SAAL,CAAetU,SAAf,CAAyB2J,GAAzB,CAA6BmM,OAA7B;AACD;;AAEDxhB,MAAAA,QAAQ,CAACmE,IAAT,CAAc8c,WAAd,CAA0B,KAAKjB,SAA/B;AAEAvZ,MAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B+T,mBAA/B,EAAoD,UAAA1Y,KAAK,EAAI;AAC3D,YAAI,MAAI,CAAC6Z,oBAAT,EAA+B;AAC7B,UAAA,MAAI,CAACA,oBAAL,GAA4B,KAA5B;AACA;AACD;;AAED,YAAI7Z,KAAK,CAACU,MAAN,KAAiBV,KAAK,CAACqb,aAA3B,EAA0C;AACxC;AACD;;AAED,QAAA,MAAI,CAACP,0BAAL;AACD,OAXD;;AAaA,UAAII,OAAJ,EAAa;AACXzd,QAAAA,MAAM,CAAC,KAAKic,SAAN,CAAN;AACD;;AAED,WAAKA,SAAL,CAAetU,SAAf,CAAyB2J,GAAzB,CAA6ByC,iBAA7B;;AAEA,UAAI,CAAC0J,OAAL,EAAc;AACZld,QAAAA,QAAQ;AACR;AACD;;AAED,UAAMsd,0BAA0B,GAAGjhB,gCAAgC,CAAC,KAAKqf,SAAN,CAAnE;AAEAvZ,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKoX,SAAtB,EAAiC7gB,cAAjC,EAAiDmF,QAAjD;AACA7C,MAAAA,oBAAoB,CAAC,KAAKue,SAAN,EAAiB4B,0BAAjB,CAApB;AACD,KAtCD,MAsCO,IAAI,CAAC,KAAK3B,QAAN,IAAkB,KAAKD,SAA3B,EAAsC;AAC3C,WAAKA,SAAL,CAAetU,SAAf,CAAyBC,MAAzB,CAAgCmM,iBAAhC;;AAEA,UAAM+J,cAAc,GAAG,SAAjBA,cAAiB,GAAM;AAC3B,QAAA,MAAI,CAACN,eAAL;;AACAjd,QAAAA,QAAQ;AACT,OAHD;;AAKA,UAAI,KAAK2G,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiC2T,eAAjC,CAAJ,EAAuD;AACrD,YAAMqC,2BAA0B,GAAGjhB,gCAAgC,CAAC,KAAKqf,SAAN,CAAnE;;AACAvZ,QAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKoX,SAAtB,EAAiC7gB,cAAjC,EAAiD0iB,cAAjD;AACApgB,QAAAA,oBAAoB,CAAC,KAAKue,SAAN,EAAiB4B,2BAAjB,CAApB;AACD,OAJD,MAIO;AACLC,QAAAA,cAAc;AACf;AACF,KAfM,MAeA;AACLvd,MAAAA,QAAQ;AACT;AACF;;SAED8c,6BAAA,sCAA6B;AAAA;;AAC3B,QAAI,KAAKpO,OAAL,CAAa4L,QAAb,KAA0B,QAA9B,EAAwC;AACtC,UAAMvB,SAAS,GAAG5W,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoC4T,oBAApC,CAAlB;;AACA,UAAIxB,SAAS,CAAC5T,gBAAd,EAAgC;AAC9B;AACD;;AAED,UAAMqY,kBAAkB,GAAG,KAAK7W,QAAL,CAAc8W,YAAd,GAA6B/hB,QAAQ,CAACyD,eAAT,CAAyBue,YAAjF;;AAEA,UAAI,CAACF,kBAAL,EAAyB;AACvB,aAAK7W,QAAL,CAAc/H,KAAd,CAAoB+e,SAApB,GAAgC,QAAhC;AACD;;AAED,WAAKhX,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4BmK,iBAA5B;;AACA,UAAM0C,uBAAuB,GAAGvhB,gCAAgC,CAAC,KAAKof,OAAN,CAAhE;AACAtZ,MAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgC9L,cAAhC;AACAsH,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgD,YAAM;AACpD,QAAA,MAAI,CAAC8L,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B6T,iBAA/B;;AACA,YAAI,CAACsC,kBAAL,EAAyB;AACvBrb,UAAAA,YAAY,CAACmC,GAAb,CAAiB,MAAI,CAACqC,QAAtB,EAAgC9L,cAAhC,EAAgD,YAAM;AACpD,YAAA,MAAI,CAAC8L,QAAL,CAAc/H,KAAd,CAAoB+e,SAApB,GAAgC,EAAhC;AACD,WAFD;AAGAxgB,UAAAA,oBAAoB,CAAC,MAAI,CAACwJ,QAAN,EAAgBiX,uBAAhB,CAApB;AACD;AACF,OARD;AASAzgB,MAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBiX,uBAAhB,CAApB;;AACA,WAAKjX,QAAL,CAAcmS,KAAd;AACD,KA1BD,MA0BO;AACL,WAAKpE,IAAL;AACD;AACF;AAGD;AACA;;;SAEAuH,gBAAA,yBAAgB;AACd,QAAMuB,kBAAkB,GACtB,KAAK7W,QAAL,CAAc8W,YAAd,GAA6B/hB,QAAQ,CAACyD,eAAT,CAAyBue,YADxD;;AAGA,QAAI,CAAC,KAAK9B,kBAAN,IAA4B4B,kBAAhC,EAAoD;AAClD,WAAK7W,QAAL,CAAc/H,KAAd,CAAoBif,WAApB,GAAqC,KAAK/B,eAA1C;AACD;;AAED,QAAI,KAAKF,kBAAL,IAA2B,CAAC4B,kBAAhC,EAAoD;AAClD,WAAK7W,QAAL,CAAc/H,KAAd,CAAoBkf,YAApB,GAAsC,KAAKhC,eAA3C;AACD;AACF;;SAEDiB,oBAAA,6BAAoB;AAClB,SAAKpW,QAAL,CAAc/H,KAAd,CAAoBif,WAApB,GAAkC,EAAlC;AACA,SAAKlX,QAAL,CAAc/H,KAAd,CAAoBkf,YAApB,GAAmC,EAAnC;AACD;;SAED/B,kBAAA,2BAAkB;AAChB,QAAM1S,IAAI,GAAG3N,QAAQ,CAACmE,IAAT,CAAcyJ,qBAAd,EAAb;AACA,SAAKsS,kBAAL,GAA0BrgB,IAAI,CAACwiB,KAAL,CAAW1U,IAAI,CAACI,IAAL,GAAYJ,IAAI,CAAC2U,KAA5B,IAAqC1hB,MAAM,CAAC2hB,UAAtE;AACA,SAAKnC,eAAL,GAAuB,KAAKoC,kBAAL,EAAvB;AACD;;SAEDlC,gBAAA,yBAAgB;AAAA;;AACd,QAAI,KAAKJ,kBAAT,EAA6B;AAC3B;AACA;AAEA;AACA7R,MAAAA,cAAc,CAACE,IAAf,CAAoBqR,sBAApB,EACGpd,OADH,CACW,UAAArC,OAAO,EAAI;AAClB,YAAMsiB,aAAa,GAAGtiB,OAAO,CAAC+C,KAAR,CAAckf,YAApC;AACA,YAAMM,iBAAiB,GAAG9hB,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,EAAiC,eAAjC,CAA1B;AACA+M,QAAAA,WAAW,CAACC,gBAAZ,CAA6BhN,OAA7B,EAAsC,eAAtC,EAAuDsiB,aAAvD;AACAtiB,QAAAA,OAAO,CAAC+C,KAAR,CAAckf,YAAd,GAAgCnhB,UAAU,CAACyhB,iBAAD,CAAV,GAAgC,OAAI,CAACtC,eAArE;AACD,OANH,EAL2B;;AAc3B/R,MAAAA,cAAc,CAACE,IAAf,CAAoBsR,uBAApB,EACGrd,OADH,CACW,UAAArC,OAAO,EAAI;AAClB,YAAMwiB,YAAY,GAAGxiB,OAAO,CAAC+C,KAAR,CAAc0f,WAAnC;AACA,YAAMC,gBAAgB,GAAGjiB,MAAM,CAACC,gBAAP,CAAwBV,OAAxB,EAAiC,cAAjC,CAAzB;AACA+M,QAAAA,WAAW,CAACC,gBAAZ,CAA6BhN,OAA7B,EAAsC,cAAtC,EAAsDwiB,YAAtD;AACAxiB,QAAAA,OAAO,CAAC+C,KAAR,CAAc0f,WAAd,GAA+B3hB,UAAU,CAAC4hB,gBAAD,CAAV,GAA+B,OAAI,CAACzC,eAAnE;AACD,OANH,EAd2B;;AAuB3B,UAAMqC,aAAa,GAAGziB,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBkf,YAA1C;AACA,UAAMM,iBAAiB,GAAG9hB,MAAM,CAACC,gBAAP,CAAwBb,QAAQ,CAACmE,IAAjC,EAAuC,eAAvC,CAA1B;AAEA+I,MAAAA,WAAW,CAACC,gBAAZ,CAA6BnN,QAAQ,CAACmE,IAAtC,EAA4C,eAA5C,EAA6Dse,aAA7D;AACAziB,MAAAA,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBkf,YAApB,GAAsCnhB,UAAU,CAACyhB,iBAAD,CAAV,GAAgC,KAAKtC,eAA3E;AACD;;AAEDpgB,IAAAA,QAAQ,CAACmE,IAAT,CAAcuH,SAAd,CAAwB2J,GAAxB,CAA4BiK,eAA5B;AACD;;SAEDgC,kBAAA,2BAAkB;AAChB;AACAjT,IAAAA,cAAc,CAACE,IAAf,CAAoBqR,sBAApB,EACGpd,OADH,CACW,UAAArC,OAAO,EAAI;AAClB,UAAM2iB,OAAO,GAAG5V,WAAW,CAACO,gBAAZ,CAA6BtN,OAA7B,EAAsC,eAAtC,CAAhB;;AACA,UAAI,OAAO2iB,OAAP,KAAmB,WAAvB,EAAoC;AAClC5V,QAAAA,WAAW,CAACE,mBAAZ,CAAgCjN,OAAhC,EAAyC,eAAzC;AACAA,QAAAA,OAAO,CAAC+C,KAAR,CAAckf,YAAd,GAA6BU,OAA7B;AACD;AACF,KAPH,EAFgB;;AAYhBzU,IAAAA,cAAc,CAACE,IAAf,MAAuBsR,uBAAvB,EACGrd,OADH,CACW,UAAArC,OAAO,EAAI;AAClB,UAAM4iB,MAAM,GAAG7V,WAAW,CAACO,gBAAZ,CAA6BtN,OAA7B,EAAsC,cAAtC,CAAf;;AACA,UAAI,OAAO4iB,MAAP,KAAkB,WAAtB,EAAmC;AACjC7V,QAAAA,WAAW,CAACE,mBAAZ,CAAgCjN,OAAhC,EAAyC,cAAzC;AACAA,QAAAA,OAAO,CAAC+C,KAAR,CAAc0f,WAAd,GAA4BG,MAA5B;AACD;AACF,KAPH,EAZgB;;AAsBhB,QAAMD,OAAO,GAAG5V,WAAW,CAACO,gBAAZ,CAA6BzN,QAAQ,CAACmE,IAAtC,EAA4C,eAA5C,CAAhB;;AACA,QAAI,OAAO2e,OAAP,KAAmB,WAAvB,EAAoC;AAClC9iB,MAAAA,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBkf,YAApB,GAAmC,EAAnC;AACD,KAFD,MAEO;AACLlV,MAAAA,WAAW,CAACE,mBAAZ,CAAgCpN,QAAQ,CAACmE,IAAzC,EAA+C,eAA/C;AACAnE,MAAAA,QAAQ,CAACmE,IAAT,CAAcjB,KAAd,CAAoBkf,YAApB,GAAmCU,OAAnC;AACD;AACF;;SAEDN,qBAAA,8BAAqB;AAAE;AACrB,QAAMQ,SAAS,GAAGhjB,QAAQ,CAACyhB,aAAT,CAAuB,KAAvB,CAAlB;AACAuB,IAAAA,SAAS,CAACtB,SAAV,GAAsBtC,6BAAtB;AACApf,IAAAA,QAAQ,CAACmE,IAAT,CAAc8c,WAAd,CAA0B+B,SAA1B;AACA,QAAMC,cAAc,GAAGD,SAAS,CAACpV,qBAAV,GAAkCsV,KAAlC,GAA0CF,SAAS,CAACG,WAA3E;AACAnjB,IAAAA,QAAQ,CAACmE,IAAT,CAAc2H,WAAd,CAA0BkX,SAA1B;AACA,WAAOC,cAAP;AACD;;;QAIMlX,kBAAP,yBAAuB3J,MAAvB,EAA+B4T,aAA/B,EAA8C;AAC5C,WAAO,KAAKhK,IAAL,CAAU,YAAY;AAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;AACA,UAAM0I,OAAO,gBACRnD,SADQ,EAER3C,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAFQ,EAGP,OAAOlL,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHzC,CAAb;;AAMA,UAAI,CAACyC,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIib,KAAJ,CAAU,IAAV,EAAgB9M,OAAhB,CAAP;AACD;;AAED,UAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;AACD;;AAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ,CAAa4T,aAAb;AACD,OAND,MAMO,IAAIhD,OAAO,CAACiG,IAAZ,EAAkB;AACvBpU,QAAAA,IAAI,CAACoU,IAAL,CAAUjD,aAAV;AACD;AACF,KArBM,CAAP;AAsBD;;QAEM7J,cAAP,qBAAmBhM,OAAnB,EAA4B;AAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;AACD;;;;wBArdoB;AACnB,aAAOD,SAAP;AACD;;;wBAEoB;AACnB,aAAOwF,SAAP;AACD;;;;;AAkdH;AACA;AACA;AACA;AACA;;;AAEApJ,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUlG,KAAV,EAAiB;AAAA;;AACrF,MAAMU,MAAM,GAAGtG,sBAAsB,CAAC,IAAD,CAArC;;AAEA,MAAI,KAAK4U,OAAL,KAAiB,GAAjB,IAAwB,KAAKA,OAAL,KAAiB,MAA7C,EAAqD;AACnDhP,IAAAA,KAAK,CAAC6D,cAAN;AACD;;AAED1D,EAAAA,YAAY,CAACmC,GAAb,CAAiB5B,MAAjB,EAAyB0Q,YAAzB,EAAqC,UAAAsF,SAAS,EAAI;AAChD,QAAIA,SAAS,CAACvT,gBAAd,EAAgC;AAC9B;AACA;AACD;;AAEDhD,IAAAA,YAAY,CAACmC,GAAb,CAAiB5B,MAAjB,EAAyB6Q,cAAzB,EAAuC,YAAM;AAC3C,UAAI5U,SAAS,CAAC,OAAD,CAAb,EAAqB;AACnB,QAAA,OAAI,CAACma,KAAL;AACD;AACF,KAJD;AAKD,GAXD;AAaA,MAAIvY,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa2B,MAAb,EAAqBsD,UAArB,CAAX;;AACA,MAAI,CAACzF,IAAL,EAAW;AACT,QAAMzC,MAAM,gBACP8K,WAAW,CAACI,iBAAZ,CAA8BtG,MAA9B,CADO,EAEPkG,WAAW,CAACI,iBAAZ,CAA8B,IAA9B,CAFO,CAAZ;;AAKAzI,IAAAA,IAAI,GAAG,IAAIib,KAAJ,CAAU9Y,MAAV,EAAkB5E,MAAlB,CAAP;AACD;;AAEDyC,EAAAA,IAAI,CAACoU,IAAL,CAAU,IAAV;AACD,CA/BD;AAiCA;AACA;AACA;AACA;AACA;AACA;;AAEA5U,kBAAkB,CAAC,YAAM;AACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;AACA;;AACA,MAAIoF,CAAJ,EAAO;AACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;AACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAa0V,KAAK,CAAC/T,eAAnB;AACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyByT,KAAzB;;AACAzW,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;AACA,aAAO0T,KAAK,CAAC/T,eAAb;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;ACzmBA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAMqX,QAAQ,GAAG,CACf,YADe,EAEf,MAFe,EAGf,MAHe,EAIf,UAJe,EAKf,UALe,EAMf,QANe,EAOf,KAPe,EAQf,YARe,CAAjB;AAWA,IAAMC,sBAAsB,GAAG,gBAA/B;AAEA;AACA;AACA;AACA;AACA;;AACA,IAAMC,gBAAgB,GAAG,6DAAzB;AAEA;AACA;AACA;AACA;AACA;;AACA,IAAMC,gBAAgB,GAAG,oIAAzB;;AAEA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAmB,CAACC,IAAD,EAAOC,oBAAP,EAAgC;AACvD,MAAMC,QAAQ,GAAGF,IAAI,CAACG,QAAL,CAAclkB,WAAd,EAAjB;;AAEA,MAAIgkB,oBAAoB,CAACzb,OAArB,CAA6B0b,QAA7B,MAA2C,CAAC,CAAhD,EAAmD;AACjD,QAAIP,QAAQ,CAACnb,OAAT,CAAiB0b,QAAjB,MAA+B,CAAC,CAApC,EAAuC;AACrC,aAAOrb,OAAO,CAACmb,IAAI,CAACI,SAAL,CAAepkB,KAAf,CAAqB6jB,gBAArB,KAA0CG,IAAI,CAACI,SAAL,CAAepkB,KAAf,CAAqB8jB,gBAArB,CAA3C,CAAd;AACD;;AAED,WAAO,IAAP;AACD;;AAED,MAAMO,MAAM,GAAGJ,oBAAoB,CAAC7U,MAArB,CAA4B,UAAAkV,SAAS;AAAA,WAAIA,SAAS,YAAYlhB,MAAzB;AAAA,GAArC,CAAf,CAXuD;;AAcvD,OAAK,IAAIoE,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAGuc,MAAM,CAAC5c,MAA7B,EAAqCD,CAAC,GAAGM,GAAzC,EAA8CN,CAAC,EAA/C,EAAmD;AACjD,QAAI0c,QAAQ,CAAClkB,KAAT,CAAeqkB,MAAM,CAAC7c,CAAD,CAArB,CAAJ,EAA+B;AAC7B,aAAO,IAAP;AACD;AACF;;AAED,SAAO,KAAP;AACD,CArBD;;AAuBO,IAAM+c,gBAAgB,GAAG;AAC9B;AACA,OAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCX,sBAAvC,CAFyB;AAG9BY,EAAAA,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;AAI9BC,EAAAA,IAAI,EAAE,EAJwB;AAK9BC,EAAAA,CAAC,EAAE,EAL2B;AAM9BC,EAAAA,EAAE,EAAE,EAN0B;AAO9BC,EAAAA,GAAG,EAAE,EAPyB;AAQ9BC,EAAAA,IAAI,EAAE,EARwB;AAS9BC,EAAAA,GAAG,EAAE,EATyB;AAU9BC,EAAAA,EAAE,EAAE,EAV0B;AAW9BC,EAAAA,EAAE,EAAE,EAX0B;AAY9BC,EAAAA,EAAE,EAAE,EAZ0B;AAa9BC,EAAAA,EAAE,EAAE,EAb0B;AAc9BC,EAAAA,EAAE,EAAE,EAd0B;AAe9BC,EAAAA,EAAE,EAAE,EAf0B;AAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;AAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;AAkB9B9d,EAAAA,CAAC,EAAE,EAlB2B;AAmB9B+d,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;AAoB9BC,EAAAA,EAAE,EAAE,EApB0B;AAqB9BC,EAAAA,EAAE,EAAE,EArB0B;AAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;AAuB9BC,EAAAA,GAAG,EAAE,EAvByB;AAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;AAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;AA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;AA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;AA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;AA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;AA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;AA+B9BC,EAAAA,EAAE,EAAE;AA/B0B,CAAzB;AAkCA,SAASC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,UAA7C,EAAyD;AAAA;;AAC9D,MAAI,CAACF,UAAU,CAAC5e,MAAhB,EAAwB;AACtB,WAAO4e,UAAP;AACD;;AAED,MAAIE,UAAU,IAAI,OAAOA,UAAP,KAAsB,UAAxC,EAAoD;AAClD,WAAOA,UAAU,CAACF,UAAD,CAAjB;AACD;;AAED,MAAMG,SAAS,GAAG,IAAIrlB,MAAM,CAACslB,SAAX,EAAlB;AACA,MAAMC,eAAe,GAAGF,SAAS,CAACG,eAAV,CAA0BN,UAA1B,EAAsC,WAAtC,CAAxB;AACA,MAAMO,aAAa,GAAG/jB,MAAM,CAACC,IAAP,CAAYwjB,SAAZ,CAAtB;;AACA,MAAMO,QAAQ,GAAG,YAAG9X,MAAH,aAAa2X,eAAe,CAAChiB,IAAhB,CAAqB4C,gBAArB,CAAsC,GAAtC,CAAb,CAAjB;;AAZ8D,6BAcrDE,CAdqD,EAc9CM,GAd8C;AAAA;;AAe5D,QAAMgf,EAAE,GAAGD,QAAQ,CAACrf,CAAD,CAAnB;AACA,QAAMuf,MAAM,GAAGD,EAAE,CAAC3C,QAAH,CAAYlkB,WAAZ,EAAf;;AAEA,QAAI2mB,aAAa,CAACpe,OAAd,CAAsBue,MAAtB,MAAkC,CAAC,CAAvC,EAA0C;AACxCD,MAAAA,EAAE,CAACpjB,UAAH,CAAc2I,WAAd,CAA0Bya,EAA1B;AAEA;AACD;;AAED,QAAME,aAAa,GAAG,aAAGjY,MAAH,cAAa+X,EAAE,CAAChZ,UAAhB,CAAtB;;AACA,QAAMmZ,iBAAiB,GAAG,GAAGlY,MAAH,CAAUuX,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACS,MAAD,CAAT,IAAqB,EAArD,CAA1B;AAEAC,IAAAA,aAAa,CAACjkB,OAAd,CAAsB,UAAAihB,IAAI,EAAI;AAC5B,UAAI,CAACD,gBAAgB,CAACC,IAAD,EAAOiD,iBAAP,CAArB,EAAgD;AAC9CH,QAAAA,EAAE,CAAClZ,eAAH,CAAmBoW,IAAI,CAACG,QAAxB;AACD;AACF,KAJD;AA3B4D;;AAc9D,OAAK,IAAI3c,CAAC,GAAG,CAAR,EAAWM,GAAG,GAAG+e,QAAQ,CAACpf,MAA/B,EAAuCD,CAAC,GAAGM,GAA3C,EAAgDN,CAAC,EAAjD,EAAqD;AAAA,qBAA5CA,CAA4C;;AAAA,6BAOjD;AAWH;;AAED,SAAOkf,eAAe,CAAChiB,IAAhB,CAAqBwiB,SAA5B;AACD;;ACjGD;AACA;AACA;AACA;AACA;;AAEA,IAAMvc,MAAI,GAAG,SAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,YAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAMsc,YAAY,GAAG,YAArB;AACA,IAAMC,kBAAkB,GAAG,IAAIhkB,MAAJ,aAAqB+jB,YAArB,WAAyC,GAAzC,CAA3B;AACA,IAAME,qBAAqB,GAAG,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAA9B;AAEA,IAAM1W,aAAW,GAAG;AAClB2W,EAAAA,SAAS,EAAE,SADO;AAElBC,EAAAA,QAAQ,EAAE,QAFQ;AAGlBC,EAAAA,KAAK,EAAE,2BAHW;AAIlB9d,EAAAA,OAAO,EAAE,QAJS;AAKlB+d,EAAAA,KAAK,EAAE,iBALW;AAMlBC,EAAAA,IAAI,EAAE,SANY;AAOlB/mB,EAAAA,QAAQ,EAAE,kBAPQ;AAQlByd,EAAAA,SAAS,EAAE,mBARO;AASlBnQ,EAAAA,MAAM,EAAE,0BATU;AAUlB0L,EAAAA,SAAS,EAAE,0BAVO;AAWlBgO,EAAAA,iBAAiB,EAAE,gBAXD;AAYlBjL,EAAAA,QAAQ,EAAE,kBAZQ;AAalBkL,EAAAA,QAAQ,EAAE,SAbQ;AAclBrB,EAAAA,UAAU,EAAE,iBAdM;AAelBD,EAAAA,SAAS,EAAE,QAfO;AAgBlB1J,EAAAA,YAAY,EAAE;AAhBI,CAApB;AAmBA,IAAMiL,aAAa,GAAG;AACpBC,EAAAA,IAAI,EAAE,MADc;AAEpBC,EAAAA,GAAG,EAAE,KAFe;AAGpBC,EAAAA,KAAK,EAAE,OAHa;AAIpBC,EAAAA,MAAM,EAAE,QAJY;AAKpBC,EAAAA,IAAI,EAAE;AALc,CAAtB;AAQA,IAAM9X,SAAO,GAAG;AACdkX,EAAAA,SAAS,EAAE,IADG;AAEdC,EAAAA,QAAQ,EAAE,yCACQ,mCADR,GAEQ,yCAJJ;AAKd7d,EAAAA,OAAO,EAAE,aALK;AAMd8d,EAAAA,KAAK,EAAE,EANO;AAOdC,EAAAA,KAAK,EAAE,CAPO;AAQdC,EAAAA,IAAI,EAAE,KARQ;AASd/mB,EAAAA,QAAQ,EAAE,KATI;AAUdyd,EAAAA,SAAS,EAAE,KAVG;AAWdnQ,EAAAA,MAAM,EAAE,CAXM;AAYd0L,EAAAA,SAAS,EAAE,KAZG;AAadgO,EAAAA,iBAAiB,EAAE,MAbL;AAcdjL,EAAAA,QAAQ,EAAE,cAdI;AAedkL,EAAAA,QAAQ,EAAE,IAfI;AAgBdrB,EAAAA,UAAU,EAAE,IAhBE;AAiBdD,EAAAA,SAAS,EAAE/B,gBAjBG;AAkBd3H,EAAAA,YAAY,EAAE;AAlBA,CAAhB;AAqBA,IAAM/a,OAAK,GAAG;AACZsmB,EAAAA,IAAI,WAASrd,WADD;AAEZsd,EAAAA,MAAM,aAAWtd,WAFL;AAGZud,EAAAA,IAAI,WAASvd,WAHD;AAIZwd,EAAAA,KAAK,YAAUxd,WAJH;AAKZyd,EAAAA,QAAQ,eAAazd,WALT;AAMZ0d,EAAAA,KAAK,YAAU1d,WANH;AAOZ2d,EAAAA,OAAO,cAAY3d,WAPP;AAQZ4d,EAAAA,QAAQ,eAAa5d,WART;AASZ6d,EAAAA,UAAU,iBAAe7d,WATb;AAUZ8d,EAAAA,UAAU,iBAAe9d;AAVb,CAAd;AAaA,IAAMgV,iBAAe,GAAG,MAAxB;AACA,IAAM+I,gBAAgB,GAAG,OAAzB;AACA,IAAMxQ,iBAAe,GAAG,MAAxB;AAEA,IAAMyQ,gBAAgB,GAAG,MAAzB;AACA,IAAMC,eAAe,GAAG,KAAxB;AAEA,IAAMC,sBAAsB,GAAG,gBAA/B;AAEA,IAAMC,aAAa,GAAG,OAAtB;AACA,IAAMC,aAAa,GAAG,OAAtB;AACA,IAAMC,aAAa,GAAG,OAAtB;AACA,IAAMC,cAAc,GAAG,QAAvB;AAEA;AACA;AACA;AACA;AACA;;IAEMC;AACJ,mBAAY3oB,OAAZ,EAAqBiC,MAArB,EAA6B;AAC3B,QAAI,OAAO6a,MAAP,KAAkB,WAAtB,EAAmC;AACjC,YAAM,IAAI7F,SAAJ,CAAc,iEAAd,CAAN;AACD,KAH0B;;;AAM3B,SAAK2R,UAAL,GAAkB,IAAlB;AACA,SAAKC,QAAL,GAAgB,CAAhB;AACA,SAAKC,WAAL,GAAmB,EAAnB;AACA,SAAKC,cAAL,GAAsB,EAAtB;AACA,SAAK3M,OAAL,GAAe,IAAf,CAV2B;;AAa3B,SAAKpc,OAAL,GAAeA,OAAf;AACA,SAAKiC,MAAL,GAAc,KAAK6Q,UAAL,CAAgB7Q,MAAhB,CAAd;AACA,SAAK+mB,GAAL,GAAW,IAAX;;AAEA,SAAKC,aAAL;;AACAlkB,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsB,KAAKud,WAAL,CAAiBpT,QAAvC,EAAiD,IAAjD;AACD;;;;;AAgCD;SAEA+e,SAAA,kBAAS;AACP,SAAKN,UAAL,GAAkB,IAAlB;AACD;;SAEDO,UAAA,mBAAU;AACR,SAAKP,UAAL,GAAkB,KAAlB;AACD;;SAEDQ,gBAAA,yBAAgB;AACd,SAAKR,UAAL,GAAkB,CAAC,KAAKA,UAAxB;AACD;;SAEDrc,SAAA,gBAAOpG,KAAP,EAAc;AACZ,QAAI,CAAC,KAAKyiB,UAAV,EAAsB;AACpB;AACD;;AAED,QAAIziB,KAAJ,EAAW;AACT,UAAMkjB,OAAO,GAAG,KAAK9L,WAAL,CAAiBpT,QAAjC;AACA,UAAIiU,OAAO,GAAGrZ,IAAI,CAACG,OAAL,CAAaiB,KAAK,CAACC,cAAnB,EAAmCijB,OAAnC,CAAd;;AAEA,UAAI,CAACjL,OAAL,EAAc;AACZA,QAAAA,OAAO,GAAG,IAAI,KAAKb,WAAT,CACRpX,KAAK,CAACC,cADE,EAER,KAAKkjB,kBAAL,EAFQ,CAAV;AAIAvkB,QAAAA,IAAI,CAACC,OAAL,CAAamB,KAAK,CAACC,cAAnB,EAAmCijB,OAAnC,EAA4CjL,OAA5C;AACD;;AAEDA,MAAAA,OAAO,CAAC2K,cAAR,CAAuBQ,KAAvB,GAA+B,CAACnL,OAAO,CAAC2K,cAAR,CAAuBQ,KAAvD;;AAEA,UAAInL,OAAO,CAACoL,oBAAR,EAAJ,EAAoC;AAClCpL,QAAAA,OAAO,CAACqL,MAAR,CAAe,IAAf,EAAqBrL,OAArB;AACD,OAFD,MAEO;AACLA,QAAAA,OAAO,CAACsL,MAAR,CAAe,IAAf,EAAqBtL,OAArB;AACD;AACF,KAnBD,MAmBO;AACL,UAAI,KAAKuL,aAAL,GAAqBpe,SAArB,CAA+BE,QAA/B,CAAwCkM,iBAAxC,CAAJ,EAA8D;AAC5D,aAAK+R,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;AACA;AACD;;AAED,WAAKD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;AACD;AACF;;SAEDpe,UAAA,mBAAU;AACR0J,IAAAA,YAAY,CAAC,KAAK8T,QAAN,CAAZ;AAEA9jB,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAKnF,OAArB,EAA8B,KAAKud,WAAL,CAAiBpT,QAA/C;AAEA7D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKvG,OAAtB,EAA+B,KAAKud,WAAL,CAAiBnT,SAAhD;AACA9D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKvG,OAAL,CAAasL,OAAb,OAAyB6c,gBAAzB,CAAjB,EAA+D,eAA/D,EAAgF,KAAKyB,iBAArF;;AAEA,QAAI,KAAKZ,GAAT,EAAc;AACZ,WAAKA,GAAL,CAAShmB,UAAT,CAAoB2I,WAApB,CAAgC,KAAKqd,GAArC;AACD;;AAED,SAAKJ,UAAL,GAAkB,IAAlB;AACA,SAAKC,QAAL,GAAgB,IAAhB;AACA,SAAKC,WAAL,GAAmB,IAAnB;AACA,SAAKC,cAAL,GAAsB,IAAtB;;AACA,QAAI,KAAK3M,OAAT,EAAkB;AAChB,WAAKA,OAAL,CAAae,OAAb;AACD;;AAED,SAAKf,OAAL,GAAe,IAAf;AACA,SAAKpc,OAAL,GAAe,IAAf;AACA,SAAKiC,MAAL,GAAc,IAAd;AACA,SAAK+mB,GAAL,GAAW,IAAX;AACD;;SAEDlQ,OAAA,gBAAO;AAAA;;AACL,QAAI,KAAK9Y,OAAL,CAAa+C,KAAb,CAAmBI,OAAnB,KAA+B,MAAnC,EAA2C;AACzC,YAAM,IAAIP,KAAJ,CAAU,qCAAV,CAAN;AACD;;AAED,QAAI,KAAKinB,aAAL,MAAwB,KAAKjB,UAAjC,EAA6C;AAC3C,UAAM/L,SAAS,GAAGvW,YAAY,CAAC0C,OAAb,CAAqB,KAAKhJ,OAA1B,EAAmC,KAAKud,WAAL,CAAiBpc,KAAjB,CAAuBwmB,IAA1D,CAAlB;AACA,UAAMmC,UAAU,GAAGzmB,cAAc,CAAC,KAAKrD,OAAN,CAAjC;AACA,UAAM+pB,UAAU,GAAGD,UAAU,KAAK,IAAf,GACjB,KAAK9pB,OAAL,CAAagqB,aAAb,CAA2B1mB,eAA3B,CAA2CmI,QAA3C,CAAoD,KAAKzL,OAAzD,CADiB,GAEjB8pB,UAAU,CAACre,QAAX,CAAoB,KAAKzL,OAAzB,CAFF;;AAIA,UAAI6c,SAAS,CAACvT,gBAAV,IAA8B,CAACygB,UAAnC,EAA+C;AAC7C;AACD;;AAED,UAAMf,GAAG,GAAG,KAAKW,aAAL,EAAZ;AACA,UAAMM,KAAK,GAAGzqB,MAAM,CAAC,KAAK+d,WAAL,CAAiBtT,IAAlB,CAApB;AAEA+e,MAAAA,GAAG,CAACxc,YAAJ,CAAiB,IAAjB,EAAuByd,KAAvB;AACA,WAAKjqB,OAAL,CAAawM,YAAb,CAA0B,kBAA1B,EAA8Cyd,KAA9C;AAEA,WAAKC,UAAL;;AAEA,UAAI,KAAKjoB,MAAL,CAAY2kB,SAAhB,EAA2B;AACzBoC,QAAAA,GAAG,CAACzd,SAAJ,CAAc2J,GAAd,CAAkBkK,iBAAlB;AACD;;AAED,UAAM1B,SAAS,GAAG,OAAO,KAAKzb,MAAL,CAAYyb,SAAnB,KAAiC,UAAjC,GAChB,KAAKzb,MAAL,CAAYyb,SAAZ,CAAsBre,IAAtB,CAA2B,IAA3B,EAAiC2pB,GAAjC,EAAsC,KAAKhpB,OAA3C,CADgB,GAEhB,KAAKiC,MAAL,CAAYyb,SAFd;;AAIA,UAAMyM,UAAU,GAAG,KAAKC,cAAL,CAAoB1M,SAApB,CAAnB;;AACA,WAAK2M,mBAAL,CAAyBF,UAAzB;;AAEA,UAAMlR,SAAS,GAAG,KAAKqR,aAAL,EAAlB;;AACAvlB,MAAAA,IAAI,CAACC,OAAL,CAAagkB,GAAb,EAAkB,KAAKzL,WAAL,CAAiBpT,QAAnC,EAA6C,IAA7C;;AAEA,UAAI,CAAC,KAAKnK,OAAL,CAAagqB,aAAb,CAA2B1mB,eAA3B,CAA2CmI,QAA3C,CAAoD,KAAKud,GAAzD,CAAL,EAAoE;AAClE/P,QAAAA,SAAS,CAAC6H,WAAV,CAAsBkI,GAAtB;AACD;;AAED1iB,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAKhJ,OAA1B,EAAmC,KAAKud,WAAL,CAAiBpc,KAAjB,CAAuB0mB,QAA1D;AAEA,WAAKzL,OAAL,GAAe,IAAIU,MAAJ,CAAW,KAAK9c,OAAhB,EAAyBgpB,GAAzB,EAA8B,KAAKhM,gBAAL,CAAsBmN,UAAtB,CAA9B,CAAf;AAEAnB,MAAAA,GAAG,CAACzd,SAAJ,CAAc2J,GAAd,CAAkByC,iBAAlB,EAzC2C;AA4C3C;AACA;AACA;;AACA,UAAI,kBAAkB9X,QAAQ,CAACyD,eAA/B,EAAgD;AAAA;;AAC9C,oBAAG+K,MAAH,aAAaxO,QAAQ,CAACmE,IAAT,CAAcyK,QAA3B,EAAqCpM,OAArC,CAA6C,UAAArC,OAAO,EAAI;AACtDsG,UAAAA,YAAY,CAACkC,EAAb,CAAgBxI,OAAhB,EAAyB,WAAzB,EAAsC2D,IAAI,EAA1C;AACD,SAFD;AAGD;;AAED,UAAM8V,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,YAAI,KAAI,CAACxX,MAAL,CAAY2kB,SAAhB,EAA2B;AACzB,UAAA,KAAI,CAAC2D,cAAL;AACD;;AAED,YAAMC,cAAc,GAAG,KAAI,CAAC1B,WAA5B;AACA,QAAA,KAAI,CAACA,WAAL,GAAmB,IAAnB;AAEAxiB,QAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAI,CAAChJ,OAA1B,EAAmC,KAAI,CAACud,WAAL,CAAiBpc,KAAjB,CAAuBymB,KAA1D;;AAEA,YAAI4C,cAAc,KAAKnC,eAAvB,EAAwC;AACtC,UAAA,KAAI,CAACqB,MAAL,CAAY,IAAZ,EAAkB,KAAlB;AACD;AACF,OAbD;;AAeA,UAAI,KAAKV,GAAL,CAASzd,SAAT,CAAmBE,QAAnB,CAA4B2T,iBAA5B,CAAJ,EAAkD;AAChD,YAAMze,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKwoB,GAAN,CAA3D;AACA1iB,QAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKugB,GAAtB,EAA2BhqB,cAA3B,EAA2Cya,QAA3C;AACAnY,QAAAA,oBAAoB,CAAC,KAAK0nB,GAAN,EAAWroB,kBAAX,CAApB;AACD,OAJD,MAIO;AACL8Y,QAAAA,QAAQ;AACT;AACF;AACF;;SAEDZ,OAAA,gBAAO;AAAA;;AACL,QAAI,CAAC,KAAKuD,OAAV,EAAmB;AACjB;AACD;;AAED,QAAM4M,GAAG,GAAG,KAAKW,aAAL,EAAZ;;AACA,QAAMlQ,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,UAAI,MAAI,CAACqP,WAAL,KAAqBV,gBAArB,IAAyCY,GAAG,CAAChmB,UAAjD,EAA6D;AAC3DgmB,QAAAA,GAAG,CAAChmB,UAAJ,CAAe2I,WAAf,CAA2Bqd,GAA3B;AACD;;AAED,MAAA,MAAI,CAACyB,cAAL;;AACA,MAAA,MAAI,CAACzqB,OAAL,CAAakN,eAAb,CAA6B,kBAA7B;;AACA5G,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAChJ,OAA1B,EAAmC,MAAI,CAACud,WAAL,CAAiBpc,KAAjB,CAAuBumB,MAA1D;;AACA,MAAA,MAAI,CAACtL,OAAL,CAAae,OAAb;AACD,KATD;;AAWA,QAAMD,SAAS,GAAG5W,YAAY,CAAC0C,OAAb,CAAqB,KAAKhJ,OAA1B,EAAmC,KAAKud,WAAL,CAAiBpc,KAAjB,CAAuBsmB,IAA1D,CAAlB;;AACA,QAAIvK,SAAS,CAAC5T,gBAAd,EAAgC;AAC9B;AACD;;AAED0f,IAAAA,GAAG,CAACzd,SAAJ,CAAcC,MAAd,CAAqBmM,iBAArB,EAtBK;AAyBL;;AACA,QAAI,kBAAkB9X,QAAQ,CAACyD,eAA/B,EAAgD;AAAA;;AAC9C,mBAAG+K,MAAH,cAAaxO,QAAQ,CAACmE,IAAT,CAAcyK,QAA3B,EACGpM,OADH,CACW,UAAArC,OAAO;AAAA,eAAIsG,YAAY,CAACC,GAAb,CAAiBvG,OAAjB,EAA0B,WAA1B,EAAuC2D,IAAvC,CAAJ;AAAA,OADlB;AAED;;AAED,SAAKolB,cAAL,CAAoBN,aAApB,IAAqC,KAArC;AACA,SAAKM,cAAL,CAAoBP,aAApB,IAAqC,KAArC;AACA,SAAKO,cAAL,CAAoBR,aAApB,IAAqC,KAArC;;AAEA,QAAI,KAAKS,GAAL,CAASzd,SAAT,CAAmBE,QAAnB,CAA4B2T,iBAA5B,CAAJ,EAAkD;AAChD,UAAMze,kBAAkB,GAAGH,gCAAgC,CAACwoB,GAAD,CAA3D;AAEA1iB,MAAAA,YAAY,CAACmC,GAAb,CAAiBugB,GAAjB,EAAsBhqB,cAAtB,EAAsCya,QAAtC;AACAnY,MAAAA,oBAAoB,CAAC0nB,GAAD,EAAMroB,kBAAN,CAApB;AACD,KALD,MAKO;AACL8Y,MAAAA,QAAQ;AACT;;AAED,SAAKqP,WAAL,GAAmB,EAAnB;AACD;;SAED1L,SAAA,kBAAS;AACP,QAAI,KAAKhB,OAAL,KAAiB,IAArB,EAA2B;AACzB,WAAKA,OAAL,CAAaiB,cAAb;AACD;AACF;;;SAIDwM,gBAAA,yBAAgB;AACd,WAAO1hB,OAAO,CAAC,KAAKuiB,QAAL,EAAD,CAAd;AACD;;SAEDf,gBAAA,yBAAgB;AACd,QAAI,KAAKX,GAAT,EAAc;AACZ,aAAO,KAAKA,GAAZ;AACD;;AAED,QAAMhpB,OAAO,GAAGH,QAAQ,CAACyhB,aAAT,CAAuB,KAAvB,CAAhB;AACAthB,IAAAA,OAAO,CAACwmB,SAAR,GAAoB,KAAKvkB,MAAL,CAAY4kB,QAAhC;AAEA,SAAKmC,GAAL,GAAWhpB,OAAO,CAACyO,QAAR,CAAiB,CAAjB,CAAX;AACA,WAAO,KAAKua,GAAZ;AACD;;SAEDkB,aAAA,sBAAa;AACX,QAAMlB,GAAG,GAAG,KAAKW,aAAL,EAAZ;AACA,SAAKgB,iBAAL,CAAuBzc,cAAc,CAACM,OAAf,CAAuB8Z,sBAAvB,EAA+CU,GAA/C,CAAvB,EAA4E,KAAK0B,QAAL,EAA5E;AACA1B,IAAAA,GAAG,CAACzd,SAAJ,CAAcC,MAAd,CAAqB4T,iBAArB,EAAsCzH,iBAAtC;AACD;;SAEDgT,oBAAA,2BAAkB3qB,OAAlB,EAA2B4qB,OAA3B,EAAoC;AAClC,QAAI5qB,OAAO,KAAK,IAAhB,EAAsB;AACpB;AACD;;AAED,QAAI,OAAO4qB,OAAP,KAAmB,QAAnB,IAA+BxpB,SAAS,CAACwpB,OAAD,CAA5C,EAAuD;AACrD,UAAIA,OAAO,CAAC9Q,MAAZ,EAAoB;AAClB8Q,QAAAA,OAAO,GAAGA,OAAO,CAAC,CAAD,CAAjB;AACD,OAHoD;;;AAMrD,UAAI,KAAK3oB,MAAL,CAAY+kB,IAAhB,EAAsB;AACpB,YAAI4D,OAAO,CAAC5nB,UAAR,KAAuBhD,OAA3B,EAAoC;AAClCA,UAAAA,OAAO,CAACwmB,SAAR,GAAoB,EAApB;AACAxmB,UAAAA,OAAO,CAAC8gB,WAAR,CAAoB8J,OAApB;AACD;AACF,OALD,MAKO;AACL5qB,QAAAA,OAAO,CAAC6qB,WAAR,GAAsBD,OAAO,CAACC,WAA9B;AACD;;AAED;AACD;;AAED,QAAI,KAAK5oB,MAAL,CAAY+kB,IAAhB,EAAsB;AACpB,UAAI,KAAK/kB,MAAL,CAAYilB,QAAhB,EAA0B;AACxB0D,QAAAA,OAAO,GAAGlF,YAAY,CAACkF,OAAD,EAAU,KAAK3oB,MAAL,CAAY2jB,SAAtB,EAAiC,KAAK3jB,MAAL,CAAY4jB,UAA7C,CAAtB;AACD;;AAED7lB,MAAAA,OAAO,CAACwmB,SAAR,GAAoBoE,OAApB;AACD,KAND,MAMO;AACL5qB,MAAAA,OAAO,CAAC6qB,WAAR,GAAsBD,OAAtB;AACD;AACF;;SAEDF,WAAA,oBAAW;AACT,QAAI5D,KAAK,GAAG,KAAK9mB,OAAL,CAAaE,YAAb,CAA0B,qBAA1B,CAAZ;;AAEA,QAAI,CAAC4mB,KAAL,EAAY;AACVA,MAAAA,KAAK,GAAG,OAAO,KAAK7kB,MAAL,CAAY6kB,KAAnB,KAA6B,UAA7B,GACN,KAAK7kB,MAAL,CAAY6kB,KAAZ,CAAkBznB,IAAlB,CAAuB,KAAKW,OAA5B,CADM,GAEN,KAAKiC,MAAL,CAAY6kB,KAFd;AAGD;;AAED,WAAOA,KAAP;AACD;;;SAID9J,mBAAA,0BAAiBmN,UAAjB,EAA6B;AAAA;;AAC3B,QAAMW,eAAe,GAAG;AACtBpN,MAAAA,SAAS,EAAEyM,UADW;AAEtBtM,MAAAA,SAAS,EAAE;AACTtQ,QAAAA,MAAM,EAAE,KAAKoQ,UAAL,EADC;AAET5B,QAAAA,IAAI,EAAE;AACJgP,UAAAA,QAAQ,EAAE,KAAK9oB,MAAL,CAAYglB;AADlB,SAFG;AAKT+D,QAAAA,KAAK,EAAE;AACLhrB,UAAAA,OAAO,QAAM,KAAKud,WAAL,CAAiBtT,IAAvB;AADF,SALE;AAQT8T,QAAAA,eAAe,EAAE;AACfC,UAAAA,iBAAiB,EAAE,KAAK/b,MAAL,CAAY+Z;AADhB;AARR,OAFW;AActBiP,MAAAA,QAAQ,EAAE,kBAAAvmB,IAAI,EAAI;AAChB,YAAIA,IAAI,CAACwmB,iBAAL,KAA2BxmB,IAAI,CAACgZ,SAApC,EAA+C;AAC7C,UAAA,MAAI,CAACyN,4BAAL,CAAkCzmB,IAAlC;AACD;AACF,OAlBqB;AAmBtB0mB,MAAAA,QAAQ,EAAE,kBAAA1mB,IAAI;AAAA,eAAI,MAAI,CAACymB,4BAAL,CAAkCzmB,IAAlC,CAAJ;AAAA;AAnBQ,KAAxB;AAsBA,wBACKomB,eADL,EAEK,KAAK7oB,MAAL,CAAYia,YAFjB;AAID;;SAEDmO,sBAAA,6BAAoBF,UAApB,EAAgC;AAC9B,SAAKR,aAAL,GAAqBpe,SAArB,CAA+B2J,GAA/B,CAAsCuR,YAAtC,SAAsD0D,UAAtD;AACD;;SAEDxM,aAAA,sBAAa;AAAA;;AACX,QAAMpQ,MAAM,GAAG,EAAf;;AAEA,QAAI,OAAO,KAAKtL,MAAL,CAAYsL,MAAnB,KAA8B,UAAlC,EAA8C;AAC5CA,MAAAA,MAAM,CAACtH,EAAP,GAAY,UAAAvB,IAAI,EAAI;AAClBA,QAAAA,IAAI,CAACkZ,OAAL,gBACKlZ,IAAI,CAACkZ,OADV,EAEM,MAAI,CAAC3b,MAAL,CAAYsL,MAAZ,CAAmB7I,IAAI,CAACkZ,OAAxB,EAAiC,MAAI,CAAC5d,OAAtC,KAAkD,EAFxD;AAKA,eAAO0E,IAAP;AACD,OAPD;AAQD,KATD,MASO;AACL6I,MAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKtL,MAAL,CAAYsL,MAA5B;AACD;;AAED,WAAOA,MAAP;AACD;;SAED+c,gBAAA,yBAAgB;AACd,QAAI,KAAKroB,MAAL,CAAYgX,SAAZ,KAA0B,KAA9B,EAAqC;AACnC,aAAOpZ,QAAQ,CAACmE,IAAhB;AACD;;AAED,QAAI5C,SAAS,CAAC,KAAKa,MAAL,CAAYgX,SAAb,CAAb,EAAsC;AACpC,aAAO,KAAKhX,MAAL,CAAYgX,SAAnB;AACD;;AAED,WAAO/K,cAAc,CAACM,OAAf,CAAuB,KAAKvM,MAAL,CAAYgX,SAAnC,CAAP;AACD;;SAEDmR,iBAAA,wBAAe1M,SAAf,EAA0B;AACxB,WAAOyJ,aAAa,CAACzJ,SAAS,CAAC7a,WAAV,EAAD,CAApB;AACD;;SAEDomB,gBAAA,yBAAgB;AAAA;;AACd,QAAMoC,QAAQ,GAAG,KAAKppB,MAAL,CAAY+G,OAAZ,CAAoBhI,KAApB,CAA0B,GAA1B,CAAjB;AAEAqqB,IAAAA,QAAQ,CAAChpB,OAAT,CAAiB,UAAA2G,OAAO,EAAI;AAC1B,UAAIA,OAAO,KAAK,OAAhB,EAAyB;AACvB1C,QAAAA,YAAY,CAACkC,EAAb,CAAgB,MAAI,CAACxI,OAArB,EACE,MAAI,CAACud,WAAL,CAAiBpc,KAAjB,CAAuB2mB,KADzB,EAEE,MAAI,CAAC7lB,MAAL,CAAYhC,QAFd,EAGE,UAAAkG,KAAK;AAAA,iBAAI,MAAI,CAACoG,MAAL,CAAYpG,KAAZ,CAAJ;AAAA,SAHP;AAKD,OAND,MAMO,IAAI6C,OAAO,KAAK0f,cAAhB,EAAgC;AACrC,YAAM4C,OAAO,GAAGtiB,OAAO,KAAKuf,aAAZ,GACd,MAAI,CAAChL,WAAL,CAAiBpc,KAAjB,CAAuB8mB,UADT,GAEd,MAAI,CAAC1K,WAAL,CAAiBpc,KAAjB,CAAuB4mB,OAFzB;AAGA,YAAMwD,QAAQ,GAAGviB,OAAO,KAAKuf,aAAZ,GACf,MAAI,CAAChL,WAAL,CAAiBpc,KAAjB,CAAuB+mB,UADR,GAEf,MAAI,CAAC3K,WAAL,CAAiBpc,KAAjB,CAAuB6mB,QAFzB;AAIA1hB,QAAAA,YAAY,CAACkC,EAAb,CAAgB,MAAI,CAACxI,OAArB,EACEsrB,OADF,EAEE,MAAI,CAACrpB,MAAL,CAAYhC,QAFd,EAGE,UAAAkG,KAAK;AAAA,iBAAI,MAAI,CAACsjB,MAAL,CAAYtjB,KAAZ,CAAJ;AAAA,SAHP;AAKAG,QAAAA,YAAY,CAACkC,EAAb,CAAgB,MAAI,CAACxI,OAArB,EACEurB,QADF,EAEE,MAAI,CAACtpB,MAAL,CAAYhC,QAFd,EAGE,UAAAkG,KAAK;AAAA,iBAAI,MAAI,CAACujB,MAAL,CAAYvjB,KAAZ,CAAJ;AAAA,SAHP;AAKD;AACF,KA1BD;;AA4BA,SAAKyjB,iBAAL,GAAyB,YAAM;AAC7B,UAAI,MAAI,CAAC5pB,OAAT,EAAkB;AAChB,QAAA,MAAI,CAAC6Y,IAAL;AACD;AACF,KAJD;;AAMAvS,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKxI,OAAL,CAAasL,OAAb,OAAyB6c,gBAAzB,CAAhB,EACE,eADF,EAEE,KAAKyB,iBAFP;;AAKA,QAAI,KAAK3nB,MAAL,CAAYhC,QAAhB,EAA0B;AACxB,WAAKgC,MAAL,gBACK,KAAKA,MADV;AAEE+G,QAAAA,OAAO,EAAE,QAFX;AAGE/I,QAAAA,QAAQ,EAAE;AAHZ;AAKD,KAND,MAMO;AACL,WAAKurB,SAAL;AACD;AACF;;SAEDA,YAAA,qBAAY;AACV,QAAMC,SAAS,GAAG,OAAO,KAAKzrB,OAAL,CAAaE,YAAb,CAA0B,qBAA1B,CAAzB;;AAEA,QAAI,KAAKF,OAAL,CAAaE,YAAb,CAA0B,OAA1B,KAAsCurB,SAAS,KAAK,QAAxD,EAAkE;AAChE,WAAKzrB,OAAL,CAAawM,YAAb,CACE,qBADF,EAEE,KAAKxM,OAAL,CAAaE,YAAb,CAA0B,OAA1B,KAAsC,EAFxC;AAKA,WAAKF,OAAL,CAAawM,YAAb,CAA0B,OAA1B,EAAmC,EAAnC;AACD;AACF;;SAEDid,SAAA,gBAAOtjB,KAAP,EAAciY,OAAd,EAAuB;AACrB,QAAMiL,OAAO,GAAG,KAAK9L,WAAL,CAAiBpT,QAAjC;AACAiU,IAAAA,OAAO,GAAGA,OAAO,IAAIrZ,IAAI,CAACG,OAAL,CAAaiB,KAAK,CAACC,cAAnB,EAAmCijB,OAAnC,CAArB;;AAEA,QAAI,CAACjL,OAAL,EAAc;AACZA,MAAAA,OAAO,GAAG,IAAI,KAAKb,WAAT,CACRpX,KAAK,CAACC,cADE,EAER,KAAKkjB,kBAAL,EAFQ,CAAV;AAIAvkB,MAAAA,IAAI,CAACC,OAAL,CAAamB,KAAK,CAACC,cAAnB,EAAmCijB,OAAnC,EAA4CjL,OAA5C;AACD;;AAED,QAAIjY,KAAJ,EAAW;AACTiY,MAAAA,OAAO,CAAC2K,cAAR,CACE5iB,KAAK,CAACK,IAAN,KAAe,SAAf,GAA2BgiB,aAA3B,GAA2CD,aAD7C,IAEI,IAFJ;AAGD;;AAED,QAAInK,OAAO,CAACuL,aAAR,GAAwBpe,SAAxB,CAAkCE,QAAlC,CAA2CkM,iBAA3C,KACAyG,OAAO,CAAC0K,WAAR,KAAwBV,gBAD5B,EAC8C;AAC5ChK,MAAAA,OAAO,CAAC0K,WAAR,GAAsBV,gBAAtB;AACA;AACD;;AAEDrT,IAAAA,YAAY,CAACqJ,OAAO,CAACyK,QAAT,CAAZ;AAEAzK,IAAAA,OAAO,CAAC0K,WAAR,GAAsBV,gBAAtB;;AAEA,QAAI,CAAChK,OAAO,CAACnc,MAAR,CAAe8kB,KAAhB,IAAyB,CAAC3I,OAAO,CAACnc,MAAR,CAAe8kB,KAAf,CAAqBjO,IAAnD,EAAyD;AACvDsF,MAAAA,OAAO,CAACtF,IAAR;AACA;AACD;;AAEDsF,IAAAA,OAAO,CAACyK,QAAR,GAAmB/mB,UAAU,CAAC,YAAM;AAClC,UAAIsc,OAAO,CAAC0K,WAAR,KAAwBV,gBAA5B,EAA8C;AAC5ChK,QAAAA,OAAO,CAACtF,IAAR;AACD;AACF,KAJ4B,EAI1BsF,OAAO,CAACnc,MAAR,CAAe8kB,KAAf,CAAqBjO,IAJK,CAA7B;AAKD;;SAED4Q,SAAA,gBAAOvjB,KAAP,EAAciY,OAAd,EAAuB;AACrB,QAAMiL,OAAO,GAAG,KAAK9L,WAAL,CAAiBpT,QAAjC;AACAiU,IAAAA,OAAO,GAAGA,OAAO,IAAIrZ,IAAI,CAACG,OAAL,CAAaiB,KAAK,CAACC,cAAnB,EAAmCijB,OAAnC,CAArB;;AAEA,QAAI,CAACjL,OAAL,EAAc;AACZA,MAAAA,OAAO,GAAG,IAAI,KAAKb,WAAT,CACRpX,KAAK,CAACC,cADE,EAER,KAAKkjB,kBAAL,EAFQ,CAAV;AAIAvkB,MAAAA,IAAI,CAACC,OAAL,CAAamB,KAAK,CAACC,cAAnB,EAAmCijB,OAAnC,EAA4CjL,OAA5C;AACD;;AAED,QAAIjY,KAAJ,EAAW;AACTiY,MAAAA,OAAO,CAAC2K,cAAR,CACE5iB,KAAK,CAACK,IAAN,KAAe,UAAf,GAA4BgiB,aAA5B,GAA4CD,aAD9C,IAEI,KAFJ;AAGD;;AAED,QAAInK,OAAO,CAACoL,oBAAR,EAAJ,EAAoC;AAClC;AACD;;AAEDzU,IAAAA,YAAY,CAACqJ,OAAO,CAACyK,QAAT,CAAZ;AAEAzK,IAAAA,OAAO,CAAC0K,WAAR,GAAsBT,eAAtB;;AAEA,QAAI,CAACjK,OAAO,CAACnc,MAAR,CAAe8kB,KAAhB,IAAyB,CAAC3I,OAAO,CAACnc,MAAR,CAAe8kB,KAAf,CAAqBlO,IAAnD,EAAyD;AACvDuF,MAAAA,OAAO,CAACvF,IAAR;AACA;AACD;;AAEDuF,IAAAA,OAAO,CAACyK,QAAR,GAAmB/mB,UAAU,CAAC,YAAM;AAClC,UAAIsc,OAAO,CAAC0K,WAAR,KAAwBT,eAA5B,EAA6C;AAC3CjK,QAAAA,OAAO,CAACvF,IAAR;AACD;AACF,KAJ4B,EAI1BuF,OAAO,CAACnc,MAAR,CAAe8kB,KAAf,CAAqBlO,IAJK,CAA7B;AAKD;;SAED2Q,uBAAA,gCAAuB;AACrB,SAAK,IAAMxgB,OAAX,IAAsB,KAAK+f,cAA3B,EAA2C;AACzC,UAAI,KAAKA,cAAL,CAAoB/f,OAApB,CAAJ,EAAkC;AAChC,eAAO,IAAP;AACD;AACF;;AAED,WAAO,KAAP;AACD;;SAED8J,aAAA,oBAAW7Q,MAAX,EAAmB;AACjB,QAAMypB,cAAc,GAAG3e,WAAW,CAACI,iBAAZ,CAA8B,KAAKnN,OAAnC,CAAvB;AAEAmC,IAAAA,MAAM,CAACC,IAAP,CAAYspB,cAAZ,EAA4BrpB,OAA5B,CAAoC,UAAAspB,QAAQ,EAAI;AAC9C,UAAIhF,qBAAqB,CAAC7e,OAAtB,CAA8B6jB,QAA9B,MAA4C,CAAC,CAAjD,EAAoD;AAClD,eAAOD,cAAc,CAACC,QAAD,CAArB;AACD;AACF,KAJD;;AAMA,QAAI1pB,MAAM,IAAI,OAAOA,MAAM,CAACgX,SAAd,KAA4B,QAAtC,IAAkDhX,MAAM,CAACgX,SAAP,CAAiBa,MAAvE,EAA+E;AAC7E7X,MAAAA,MAAM,CAACgX,SAAP,GAAmBhX,MAAM,CAACgX,SAAP,CAAiB,CAAjB,CAAnB;AACD;;AAEDhX,IAAAA,MAAM,gBACD,KAAKsb,WAAL,CAAiB7N,OADhB,EAEDgc,cAFC,EAGA,OAAOzpB,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHhD,CAAN;;AAMA,QAAI,OAAOA,MAAM,CAAC8kB,KAAd,KAAwB,QAA5B,EAAsC;AACpC9kB,MAAAA,MAAM,CAAC8kB,KAAP,GAAe;AACbjO,QAAAA,IAAI,EAAE7W,MAAM,CAAC8kB,KADA;AAEblO,QAAAA,IAAI,EAAE5W,MAAM,CAAC8kB;AAFA,OAAf;AAID;;AAED,QAAI,OAAO9kB,MAAM,CAAC6kB,KAAd,KAAwB,QAA5B,EAAsC;AACpC7kB,MAAAA,MAAM,CAAC6kB,KAAP,GAAe7kB,MAAM,CAAC6kB,KAAP,CAAa1nB,QAAb,EAAf;AACD;;AAED,QAAI,OAAO6C,MAAM,CAAC2oB,OAAd,KAA0B,QAA9B,EAAwC;AACtC3oB,MAAAA,MAAM,CAAC2oB,OAAP,GAAiB3oB,MAAM,CAAC2oB,OAAP,CAAexrB,QAAf,EAAjB;AACD;;AAED2C,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAe,KAAKsb,WAAL,CAAiBtN,WAAhC,CAAf;;AAEA,QAAIhO,MAAM,CAACilB,QAAX,EAAqB;AACnBjlB,MAAAA,MAAM,CAAC4kB,QAAP,GAAkBnB,YAAY,CAACzjB,MAAM,CAAC4kB,QAAR,EAAkB5kB,MAAM,CAAC2jB,SAAzB,EAAoC3jB,MAAM,CAAC4jB,UAA3C,CAA9B;AACD;;AAED,WAAO5jB,MAAP;AACD;;SAEDqnB,qBAAA,8BAAqB;AACnB,QAAMrnB,MAAM,GAAG,EAAf;;AAEA,QAAI,KAAKA,MAAT,EAAiB;AACf,WAAK,IAAMwC,GAAX,IAAkB,KAAKxC,MAAvB,EAA+B;AAC7B,YAAI,KAAKsb,WAAL,CAAiB7N,OAAjB,CAAyBjL,GAAzB,MAAkC,KAAKxC,MAAL,CAAYwC,GAAZ,CAAtC,EAAwD;AACtDxC,UAAAA,MAAM,CAACwC,GAAD,CAAN,GAAc,KAAKxC,MAAL,CAAYwC,GAAZ,CAAd;AACD;AACF;AACF;;AAED,WAAOxC,MAAP;AACD;;SAEDwoB,iBAAA,0BAAiB;AACf,QAAMzB,GAAG,GAAG,KAAKW,aAAL,EAAZ;AACA,QAAMiC,QAAQ,GAAG5C,GAAG,CAAC9oB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgConB,kBAAhC,CAAjB;;AACA,QAAIkF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAC7kB,MAAT,GAAkB,CAA3C,EAA8C;AAC5C6kB,MAAAA,QAAQ,CAACC,GAAT,CAAa,UAAAC,KAAK;AAAA,eAAIA,KAAK,CAAC1rB,IAAN,EAAJ;AAAA,OAAlB,EACGiC,OADH,CACW,UAAA0pB,MAAM;AAAA,eAAI/C,GAAG,CAACzd,SAAJ,CAAcC,MAAd,CAAqBugB,MAArB,CAAJ;AAAA,OADjB;AAED;AACF;;SAEDZ,+BAAA,sCAA6Ba,UAA7B,EAAyC;AACvC,SAAKhD,GAAL,GAAWgD,UAAU,CAAC/mB,QAAX,CAAoBgnB,MAA/B;;AACA,SAAKxB,cAAL;;AACA,SAAKJ,mBAAL,CAAyB,KAAKD,cAAL,CAAoB4B,UAAU,CAACtO,SAA/B,CAAzB;AACD;;SAED6M,iBAAA,0BAAiB;AACf,QAAMvB,GAAG,GAAG,KAAKW,aAAL,EAAZ;AACA,QAAMuC,mBAAmB,GAAG,KAAKjqB,MAAL,CAAY2kB,SAAxC;;AACA,QAAIoC,GAAG,CAAC9oB,YAAJ,CAAiB,aAAjB,MAAoC,IAAxC,EAA8C;AAC5C;AACD;;AAED8oB,IAAAA,GAAG,CAACzd,SAAJ,CAAcC,MAAd,CAAqB4T,iBAArB;AACA,SAAKnd,MAAL,CAAY2kB,SAAZ,GAAwB,KAAxB;AACA,SAAK/N,IAAL;AACA,SAAKC,IAAL;AACA,SAAK7W,MAAL,CAAY2kB,SAAZ,GAAwBsF,mBAAxB;AACD;;;UAIMtgB,kBAAP,yBAAuB3J,MAAvB,EAA+B;AAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;AAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;AACA,UAAM0I,OAAO,GAAG,OAAO5Q,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;AAEA,UAAI,CAACyC,IAAD,IAAS,eAAe/B,IAAf,CAAoBV,MAApB,CAAb,EAA0C;AACxC;AACD;;AAED,UAAI,CAACyC,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAIikB,OAAJ,CAAY,IAAZ,EAAkB9V,OAAlB,CAAP;AACD;;AAED,UAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;AACD;;AAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ;AACD;AACF,KAnBM,CAAP;AAoBD;;UAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;AAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;AACD;;;;wBAroBoB;AACnB,aAAOD,SAAP;AACD;;;wBAEoB;AACnB,aAAOwF,SAAP;AACD;;;wBAEiB;AAChB,aAAOzF,MAAP;AACD;;;wBAEqB;AACpB,aAAOE,UAAP;AACD;;;wBAEkB;AACjB,aAAOhJ,OAAP;AACD;;;wBAEsB;AACrB,aAAOiJ,WAAP;AACD;;;wBAEwB;AACvB,aAAO6F,aAAP;AACD;;;;;AA8mBH;AACA;AACA;AACA;AACA;AACA;;;AAEA/L,kBAAkB,CAAC,YAAM;AACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;AACA;;AACA,MAAIoF,CAAJ,EAAO;AACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;AACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAa0e,OAAO,CAAC/c,eAArB;AACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyByc,OAAzB;;AACAzf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;AACA,aAAO0c,OAAO,CAAC/c,eAAf;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;ACvxBA;AACA;AACA;AACA;AACA;;AAEA,IAAM3B,MAAI,GAAG,SAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,YAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAMsc,cAAY,GAAG,YAArB;AACA,IAAMC,oBAAkB,GAAG,IAAIhkB,MAAJ,aAAqB+jB,cAArB,WAAyC,GAAzC,CAA3B;;AAEA,IAAM/W,SAAO,gBACRiZ,OAAO,CAACjZ,OADA;AAEXgO,EAAAA,SAAS,EAAE,OAFA;AAGX1U,EAAAA,OAAO,EAAE,OAHE;AAIX4hB,EAAAA,OAAO,EAAE,EAJE;AAKX/D,EAAAA,QAAQ,EAAE,yCACE,mCADF,GAEE,kCAFF,GAGE;AARD,EAAb;;AAWA,IAAM5W,aAAW,gBACZ0Y,OAAO,CAAC1Y,WADI;AAEf2a,EAAAA,OAAO,EAAE;AAFM,EAAjB;;AAKA,IAAMzpB,OAAK,GAAG;AACZsmB,EAAAA,IAAI,WAASrd,WADD;AAEZsd,EAAAA,MAAM,aAAWtd,WAFL;AAGZud,EAAAA,IAAI,WAASvd,WAHD;AAIZwd,EAAAA,KAAK,YAAUxd,WAJH;AAKZyd,EAAAA,QAAQ,eAAazd,WALT;AAMZ0d,EAAAA,KAAK,YAAU1d,WANH;AAOZ2d,EAAAA,OAAO,cAAY3d,WAPP;AAQZ4d,EAAAA,QAAQ,eAAa5d,WART;AASZ6d,EAAAA,UAAU,iBAAe7d,WATb;AAUZ8d,EAAAA,UAAU,iBAAe9d;AAVb,CAAd;AAaA,IAAMgV,iBAAe,GAAG,MAAxB;AACA,IAAMzH,iBAAe,GAAG,MAAxB;AAEA,IAAMwU,cAAc,GAAG,iBAAvB;AACA,IAAMC,gBAAgB,GAAG,eAAzB;AAEA;AACA;AACA;AACA;AACA;;IAEMC;;;;;;;;;AA+BJ;SAEAxC,gBAAA,yBAAgB;AACd,WAAO,KAAKa,QAAL,MAAmB,KAAK4B,WAAL,EAA1B;AACD;;SAEDpC,aAAA,sBAAa;AACX,QAAMlB,GAAG,GAAG,KAAKW,aAAL,EAAZ,CADW;;AAIX,SAAKgB,iBAAL,CAAuBzc,cAAc,CAACM,OAAf,CAAuB2d,cAAvB,EAAuCnD,GAAvC,CAAvB,EAAoE,KAAK0B,QAAL,EAApE;;AACA,QAAIE,OAAO,GAAG,KAAK0B,WAAL,EAAd;;AACA,QAAI,OAAO1B,OAAP,KAAmB,UAAvB,EAAmC;AACjCA,MAAAA,OAAO,GAAGA,OAAO,CAACvrB,IAAR,CAAa,KAAKW,OAAlB,CAAV;AACD;;AAED,SAAK2qB,iBAAL,CAAuBzc,cAAc,CAACM,OAAf,CAAuB4d,gBAAvB,EAAyCpD,GAAzC,CAAvB,EAAsE4B,OAAtE;AAEA5B,IAAAA,GAAG,CAACzd,SAAJ,CAAcC,MAAd,CAAqB4T,iBAArB,EAAsCzH,iBAAtC;AACD;;;SAID0S,sBAAA,6BAAoBF,UAApB,EAAgC;AAC9B,SAAKR,aAAL,GAAqBpe,SAArB,CAA+B2J,GAA/B,CAAsCuR,cAAtC,SAAsD0D,UAAtD;AACD;;SAEDmC,cAAA,uBAAc;AACZ,WAAO,KAAKtsB,OAAL,CAAaE,YAAb,CAA0B,cAA1B,KACL,KAAK+B,MAAL,CAAY2oB,OADd;AAED;;SAEDH,iBAAA,0BAAiB;AACf,QAAMzB,GAAG,GAAG,KAAKW,aAAL,EAAZ;AACA,QAAMiC,QAAQ,GAAG5C,GAAG,CAAC9oB,YAAJ,CAAiB,OAAjB,EAA0BZ,KAA1B,CAAgConB,oBAAhC,CAAjB;;AACA,QAAIkF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAAC7kB,MAAT,GAAkB,CAA3C,EAA8C;AAC5C6kB,MAAAA,QAAQ,CAACC,GAAT,CAAa,UAAAC,KAAK;AAAA,eAAIA,KAAK,CAAC1rB,IAAN,EAAJ;AAAA,OAAlB,EACGiC,OADH,CACW,UAAA0pB,MAAM;AAAA,eAAI/C,GAAG,CAACzd,SAAJ,CAAcC,MAAd,CAAqBugB,MAArB,CAAJ;AAAA,OADjB;AAED;AACF;;;UAIMngB,kBAAP,yBAAuB3J,MAAvB,EAA+B;AAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;AAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;AACA,UAAM0I,OAAO,GAAG,OAAO5Q,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;AAEA,UAAI,CAACyC,IAAD,IAAS,eAAe/B,IAAf,CAAoBV,MAApB,CAAb,EAA0C;AACxC;AACD;;AAED,UAAI,CAACyC,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAI2nB,OAAJ,CAAY,IAAZ,EAAkBxZ,OAAlB,CAAP;AACA9N,QAAAA,IAAI,CAACC,OAAL,CAAa,IAAb,EAAmBmF,UAAnB,EAA6BzF,IAA7B;AACD;;AAED,UAAI,OAAOzC,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;AACD;;AAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ;AACD;AACF,KApBM,CAAP;AAqBD;;UAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;AAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;AACD;;;;AAnGD;wBAEqB;AACnB,aAAOD,SAAP;AACD;;;wBAEoB;AACnB,aAAOwF,SAAP;AACD;;;wBAEiB;AAChB,aAAOzF,MAAP;AACD;;;wBAEqB;AACpB,aAAOE,UAAP;AACD;;;wBAEkB;AACjB,aAAOhJ,OAAP;AACD;;;wBAEsB;AACrB,aAAOiJ,WAAP;AACD;;;wBAEwB;AACvB,aAAO6F,aAAP;AACD;;;;EA7BmB0Y;AAuGtB;AACA;AACA;AACA;AACA;AACA;;;AAEAzkB,kBAAkB,CAAC,YAAM;AACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;AACA;;AACA,MAAIoF,CAAJ,EAAO;AACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;AACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAaoiB,OAAO,CAACzgB,eAArB;AACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBmgB,OAAzB;;AACAnjB,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;AACA,aAAOogB,OAAO,CAACzgB,eAAf;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;AC5JA;AACA;AACA;AACA;AACA;;AAEA,IAAM3B,MAAI,GAAG,WAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,cAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAME,cAAY,GAAG,WAArB;AAEA,IAAMqF,SAAO,GAAG;AACdnC,EAAAA,MAAM,EAAE,EADM;AAEdgf,EAAAA,MAAM,EAAE,MAFM;AAGd1lB,EAAAA,MAAM,EAAE;AAHM,CAAhB;AAMA,IAAMoJ,aAAW,GAAG;AAClB1C,EAAAA,MAAM,EAAE,QADU;AAElBgf,EAAAA,MAAM,EAAE,QAFU;AAGlB1lB,EAAAA,MAAM,EAAE;AAHU,CAApB;AAMA,IAAM2lB,cAAc,gBAAcpiB,WAAlC;AACA,IAAMqiB,YAAY,cAAYriB,WAA9B;AACA,IAAM6G,qBAAmB,YAAU7G,WAAV,GAAsBC,cAA/C;AAEA,IAAMqiB,wBAAwB,GAAG,eAAjC;AACA,IAAMtgB,mBAAiB,GAAG,QAA1B;AAEA,IAAMugB,iBAAiB,GAAG,qBAA1B;AACA,IAAMC,uBAAuB,GAAG,mBAAhC;AACA,IAAMC,kBAAkB,GAAG,WAA3B;AACA,IAAMC,kBAAkB,GAAG,WAA3B;AACA,IAAMC,mBAAmB,GAAG,kBAA5B;AACA,IAAMC,iBAAiB,GAAG,WAA1B;AACA,IAAMC,wBAAwB,GAAG,kBAAjC;AAEA,IAAMC,aAAa,GAAG,QAAtB;AACA,IAAMC,eAAe,GAAG,UAAxB;AAEA;AACA;AACA;AACA;AACA;;IAEMC;AACJ,qBAAYptB,OAAZ,EAAqBiC,MAArB,EAA6B;AAAA;;AAC3B,SAAK6I,QAAL,GAAgB9K,OAAhB;AACA,SAAKqtB,cAAL,GAAsBrtB,OAAO,CAACmV,OAAR,KAAoB,MAApB,GAA6B1U,MAA7B,GAAsCT,OAA5D;AACA,SAAK6S,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;AACA,SAAKwW,SAAL,GAAoB,KAAK5F,OAAL,CAAahM,MAAjC,SAA2CgmB,kBAA3C,UAAkE,KAAKha,OAAL,CAAahM,MAA/E,SAAyFkmB,mBAAzF,UAAiH,KAAKla,OAAL,CAAahM,MAA9H,UAAyI6lB,wBAAzI;AACA,SAAKY,QAAL,GAAgB,EAAhB;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKC,aAAL,GAAqB,IAArB;AACA,SAAKC,aAAL,GAAqB,CAArB;AAEAnnB,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAK6kB,cAArB,EAAqCZ,YAArC,EAAmD,UAAAtmB,KAAK;AAAA,aAAI,KAAI,CAACunB,QAAL,CAAcvnB,KAAd,CAAJ;AAAA,KAAxD;AAEA,SAAKwnB,OAAL;;AACA,SAAKD,QAAL;;AAEA3oB,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;AACD;;;;;AAYD;SAEAwjB,UAAA,mBAAU;AAAA;;AACR,QAAMC,UAAU,GAAG,KAAKP,cAAL,KAAwB,KAAKA,cAAL,CAAoB5sB,MAA5C,GACjBysB,aADiB,GAEjBC,eAFF;AAIA,QAAMU,YAAY,GAAG,KAAKhb,OAAL,CAAa0Z,MAAb,KAAwB,MAAxB,GACnBqB,UADmB,GAEnB,KAAK/a,OAAL,CAAa0Z,MAFf;AAIA,QAAMuB,UAAU,GAAGD,YAAY,KAAKV,eAAjB,GACjB,KAAKY,aAAL,EADiB,GAEjB,CAFF;AAIA,SAAKT,QAAL,GAAgB,EAAhB;AACA,SAAKC,QAAL,GAAgB,EAAhB;AACA,SAAKE,aAAL,GAAqB,KAAKO,gBAAL,EAArB;AAEA,QAAMC,OAAO,GAAG/f,cAAc,CAACE,IAAf,CAAoB,KAAKqK,SAAzB,CAAhB;AAEAwV,IAAAA,OAAO,CAACpC,GAAR,CAAY,UAAA7rB,OAAO,EAAI;AACrB,UAAMkuB,cAAc,GAAG7tB,sBAAsB,CAACL,OAAD,CAA7C;AACA,UAAM6G,MAAM,GAAGqnB,cAAc,GAAGhgB,cAAc,CAACM,OAAf,CAAuB0f,cAAvB,CAAH,GAA4C,IAAzE;;AAEA,UAAIrnB,MAAJ,EAAY;AACV,YAAMsnB,SAAS,GAAGtnB,MAAM,CAAC4G,qBAAP,EAAlB;;AACA,YAAI0gB,SAAS,CAACpL,KAAV,IAAmBoL,SAAS,CAACC,MAAjC,EAAyC;AACvC,iBAAO,CACLrhB,WAAW,CAAC8gB,YAAD,CAAX,CAA0BhnB,MAA1B,EAAkC6G,GAAlC,GAAwCogB,UADnC,EAELI,cAFK,CAAP;AAID;AACF;;AAED,aAAO,IAAP;AACD,KAfD,EAgBGxf,MAhBH,CAgBU,UAAA2f,IAAI;AAAA,aAAIA,IAAJ;AAAA,KAhBd,EAiBGC,IAjBH,CAiBQ,UAACxK,CAAD,EAAIE,CAAJ;AAAA,aAAUF,CAAC,CAAC,CAAD,CAAD,GAAOE,CAAC,CAAC,CAAD,CAAlB;AAAA,KAjBR,EAkBG3hB,OAlBH,CAkBW,UAAAgsB,IAAI,EAAI;AACf,MAAA,MAAI,CAACf,QAAL,CAActe,IAAd,CAAmBqf,IAAI,CAAC,CAAD,CAAvB;;AACA,MAAA,MAAI,CAACd,QAAL,CAAcve,IAAd,CAAmBqf,IAAI,CAAC,CAAD,CAAvB;AACD,KArBH;AAsBD;;SAEDhjB,UAAA,mBAAU;AACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;AACA7D,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAK8mB,cAAtB,EAAsCjjB,WAAtC;AAEA,SAAKU,QAAL,GAAgB,IAAhB;AACA,SAAKuiB,cAAL,GAAsB,IAAtB;AACA,SAAKxa,OAAL,GAAe,IAAf;AACA,SAAK4F,SAAL,GAAiB,IAAjB;AACA,SAAK6U,QAAL,GAAgB,IAAhB;AACA,SAAKC,QAAL,GAAgB,IAAhB;AACA,SAAKC,aAAL,GAAqB,IAArB;AACA,SAAKC,aAAL,GAAqB,IAArB;AACD;;;SAID3a,aAAA,oBAAW7Q,MAAX,EAAmB;AACjBA,IAAAA,MAAM,gBACDyN,SADC,EAEA,OAAOzN,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAFhD,CAAN;;AAKA,QAAI,OAAOA,MAAM,CAAC4E,MAAd,KAAyB,QAAzB,IAAqCzF,SAAS,CAACa,MAAM,CAAC4E,MAAR,CAAlD,EAAmE;AAAA,UAC3DtC,EAD2D,GACpDtC,MAAM,CAAC4E,MAD6C,CAC3DtC,EAD2D;;AAEjE,UAAI,CAACA,EAAL,EAAS;AACPA,QAAAA,EAAE,GAAG/E,MAAM,CAACyK,MAAD,CAAX;AACAhI,QAAAA,MAAM,CAAC4E,MAAP,CAActC,EAAd,GAAmBA,EAAnB;AACD;;AAEDtC,MAAAA,MAAM,CAAC4E,MAAP,SAAoBtC,EAApB;AACD;;AAEDxC,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAegO,aAAf,CAAf;AAEA,WAAOhO,MAAP;AACD;;SAED8rB,gBAAA,yBAAgB;AACd,WAAO,KAAKV,cAAL,KAAwB5sB,MAAxB,GACL,KAAK4sB,cAAL,CAAoBkB,WADf,GAEL,KAAKlB,cAAL,CAAoB1f,SAFtB;AAGD;;SAEDqgB,mBAAA,4BAAmB;AACjB,WAAO,KAAKX,cAAL,CAAoBzL,YAApB,IAAoCliB,IAAI,CAAC8uB,GAAL,CACzC3uB,QAAQ,CAACmE,IAAT,CAAc4d,YAD2B,EAEzC/hB,QAAQ,CAACyD,eAAT,CAAyBse,YAFgB,CAA3C;AAID;;SAED6M,mBAAA,4BAAmB;AACjB,WAAO,KAAKpB,cAAL,KAAwB5sB,MAAxB,GACLA,MAAM,CAACiuB,WADF,GAEL,KAAKrB,cAAL,CAAoB5f,qBAApB,GAA4C2gB,MAF9C;AAGD;;SAEDV,WAAA,oBAAW;AACT,QAAM/f,SAAS,GAAG,KAAKogB,aAAL,KAAuB,KAAKlb,OAAL,CAAatF,MAAtD;;AACA,QAAMqU,YAAY,GAAG,KAAKoM,gBAAL,EAArB;;AACA,QAAMW,SAAS,GAAG,KAAK9b,OAAL,CAAatF,MAAb,GAChBqU,YADgB,GAEhB,KAAK6M,gBAAL,EAFF;;AAIA,QAAI,KAAKhB,aAAL,KAAuB7L,YAA3B,EAAyC;AACvC,WAAK+L,OAAL;AACD;;AAED,QAAIhgB,SAAS,IAAIghB,SAAjB,EAA4B;AAC1B,UAAM9nB,MAAM,GAAG,KAAK0mB,QAAL,CAAc,KAAKA,QAAL,CAAcxmB,MAAd,GAAuB,CAArC,CAAf;;AAEA,UAAI,KAAKymB,aAAL,KAAuB3mB,MAA3B,EAAmC;AACjC,aAAK+nB,SAAL,CAAe/nB,MAAf;AACD;;AAED;AACD;;AAED,QAAI,KAAK2mB,aAAL,IAAsB7f,SAAS,GAAG,KAAK2f,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;AAC9E,WAAKE,aAAL,GAAqB,IAArB;;AACA,WAAKqB,MAAL;;AACA;AACD;;AAED,SAAK,IAAI/nB,CAAC,GAAG,KAAKwmB,QAAL,CAAcvmB,MAA3B,EAAmCD,CAAC,EAApC,GAAyC;AACvC,UAAMgoB,cAAc,GAAG,KAAKtB,aAAL,KAAuB,KAAKD,QAAL,CAAczmB,CAAd,CAAvB,IACnB6G,SAAS,IAAI,KAAK2f,QAAL,CAAcxmB,CAAd,CADM,KAElB,OAAO,KAAKwmB,QAAL,CAAcxmB,CAAC,GAAG,CAAlB,CAAP,KAAgC,WAAhC,IACG6G,SAAS,GAAG,KAAK2f,QAAL,CAAcxmB,CAAC,GAAG,CAAlB,CAHG,CAAvB;;AAKA,UAAIgoB,cAAJ,EAAoB;AAClB,aAAKF,SAAL,CAAe,KAAKrB,QAAL,CAAczmB,CAAd,CAAf;AACD;AACF;AACF;;SAED8nB,YAAA,mBAAU/nB,MAAV,EAAkB;AAChB,SAAK2mB,aAAL,GAAqB3mB,MAArB;;AAEA,SAAKgoB,MAAL;;AAEA,QAAME,OAAO,GAAG,KAAKtW,SAAL,CAAezX,KAAf,CAAqB,GAArB,EACb6qB,GADa,CACT,UAAA5rB,QAAQ;AAAA,aAAOA,QAAP,uBAAgC4G,MAAhC,YAA4C5G,QAA5C,gBAA8D4G,MAA9D;AAAA,KADC,CAAhB;;AAGA,QAAMmoB,IAAI,GAAG9gB,cAAc,CAACM,OAAf,CAAuBugB,OAAO,CAACE,IAAR,CAAa,GAAb,CAAvB,CAAb;;AAEA,QAAID,IAAI,CAACzjB,SAAL,CAAeE,QAAf,CAAwBihB,wBAAxB,CAAJ,EAAuD;AACrDxe,MAAAA,cAAc,CAACM,OAAf,CAAuBye,wBAAvB,EAAiD+B,IAAI,CAAC1jB,OAAL,CAAa0hB,iBAAb,CAAjD,EACGzhB,SADH,CACa2J,GADb,CACiB9I,mBADjB;AAGA4iB,MAAAA,IAAI,CAACzjB,SAAL,CAAe2J,GAAf,CAAmB9I,mBAAnB;AACD,KALD,MAKO;AACL;AACA4iB,MAAAA,IAAI,CAACzjB,SAAL,CAAe2J,GAAf,CAAmB9I,mBAAnB;AAEA8B,MAAAA,cAAc,CAACU,OAAf,CAAuBogB,IAAvB,EAA6BpC,uBAA7B,EACGvqB,OADH,CACW,UAAA6sB,SAAS,EAAI;AACpB;AACA;AACAhhB,QAAAA,cAAc,CAACe,IAAf,CAAoBigB,SAApB,EAAkCrC,kBAAlC,UAAyDE,mBAAzD,EACG1qB,OADH,CACW,UAAAgsB,IAAI;AAAA,iBAAIA,IAAI,CAAC9iB,SAAL,CAAe2J,GAAf,CAAmB9I,mBAAnB,CAAJ;AAAA,SADf,EAHoB;;AAOpB8B,QAAAA,cAAc,CAACe,IAAf,CAAoBigB,SAApB,EAA+BpC,kBAA/B,EACGzqB,OADH,CACW,UAAA8sB,OAAO,EAAI;AAClBjhB,UAAAA,cAAc,CAACO,QAAf,CAAwB0gB,OAAxB,EAAiCtC,kBAAjC,EACGxqB,OADH,CACW,UAAAgsB,IAAI;AAAA,mBAAIA,IAAI,CAAC9iB,SAAL,CAAe2J,GAAf,CAAmB9I,mBAAnB,CAAJ;AAAA,WADf;AAED,SAJH;AAKD,OAbH;AAcD;;AAED9F,IAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAKqkB,cAA1B,EAA0Cb,cAA1C,EAA0D;AACxD3W,MAAAA,aAAa,EAAEhP;AADyC,KAA1D;AAGD;;SAEDgoB,SAAA,kBAAS;AACP3gB,IAAAA,cAAc,CAACE,IAAf,CAAoB,KAAKqK,SAAzB,EACG/J,MADH,CACU,UAAA0gB,IAAI;AAAA,aAAIA,IAAI,CAAC7jB,SAAL,CAAeE,QAAf,CAAwBW,mBAAxB,CAAJ;AAAA,KADd,EAEG/J,OAFH,CAEW,UAAA+sB,IAAI;AAAA,aAAIA,IAAI,CAAC7jB,SAAL,CAAeC,MAAf,CAAsBY,mBAAtB,CAAJ;AAAA,KAFf;AAGD;;;YAIMR,kBAAP,yBAAuB3J,MAAvB,EAA+B;AAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;AAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;AACA,UAAM0I,OAAO,GAAG,OAAO5Q,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;AAEA,UAAI,CAACyC,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAI0oB,SAAJ,CAAc,IAAd,EAAoBva,OAApB,CAAP;AACD;;AAED,UAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;AACD;;AAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ;AACD;AACF,KAfM,CAAP;AAgBD;;YAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;AAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;AACD;;;;wBAzNoB;AACnB,aAAOD,SAAP;AACD;;;wBAEoB;AACnB,aAAOwF,SAAP;AACD;;;;;AAsNH;AACA;AACA;AACA;AACA;;;AAEApJ,YAAY,CAACkC,EAAb,CAAgB/H,MAAhB,EAAwBwQ,qBAAxB,EAA6C,YAAM;AACjD/C,EAAAA,cAAc,CAACE,IAAf,CAAoBue,iBAApB,EACGtqB,OADH,CACW,UAAAgtB,GAAG;AAAA,WAAI,IAAIjC,SAAJ,CAAciC,GAAd,EAAmBtiB,WAAW,CAACI,iBAAZ,CAA8BkiB,GAA9B,CAAnB,CAAJ;AAAA,GADd;AAED,CAHD;AAKA;AACA;AACA;AACA;AACA;AACA;;AAEAnrB,kBAAkB,CAAC,YAAM;AACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;AACA;;AACA,MAAIoF,CAAJ,EAAO;AACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;AACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAamjB,SAAS,CAACxhB,eAAvB;AACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBkhB,SAAzB;;AACAlkB,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;AACA,aAAOmhB,SAAS,CAACxhB,eAAjB;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;ACnTA;AACA;AACA;AACA;AACA;;AAEA,IAAM3B,MAAI,GAAG,KAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,QAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AACA,IAAME,cAAY,GAAG,WAArB;AAEA,IAAMoN,YAAU,YAAUrN,WAA1B;AACA,IAAMsN,cAAY,cAAYtN,WAA9B;AACA,IAAMmN,YAAU,YAAUnN,WAA1B;AACA,IAAMoN,aAAW,aAAWpN,WAA5B;AACA,IAAMK,sBAAoB,aAAWL,WAAX,GAAuBC,cAAjD;AAEA,IAAMilB,wBAAwB,GAAG,eAAjC;AACA,IAAMljB,mBAAiB,GAAG,QAA1B;AACA,IAAM0O,qBAAmB,GAAG,UAA5B;AACA,IAAMsE,iBAAe,GAAG,MAAxB;AACA,IAAMzH,iBAAe,GAAG,MAAxB;AAEA,IAAMqV,mBAAiB,GAAG,WAA1B;AACA,IAAMJ,yBAAuB,GAAG,mBAAhC;AACA,IAAMnb,iBAAe,GAAG,SAAxB;AACA,IAAM8d,kBAAkB,GAAG,uBAA3B;AACA,IAAMljB,sBAAoB,GAAG,iEAA7B;AACA,IAAM4gB,0BAAwB,GAAG,kBAAjC;AACA,IAAMuC,8BAA8B,GAAG,iCAAvC;AAEA;AACA;AACA;AACA;AACA;;IAEMC;AACJ,eAAYzvB,OAAZ,EAAqB;AACnB,SAAK8K,QAAL,GAAgB9K,OAAhB;AAEA+E,IAAAA,IAAI,CAACC,OAAL,CAAa,KAAK8F,QAAlB,EAA4BX,UAA5B,EAAsC,IAAtC;AACD;;;;;AAQD;SAEA2O,OAAA,gBAAO;AAAA;;AACL,QAAK,KAAKhO,QAAL,CAAc9H,UAAd,IACH,KAAK8H,QAAL,CAAc9H,UAAd,CAAyB3B,QAAzB,KAAsCyN,IAAI,CAACC,YADxC,IAEH,KAAKjE,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCW,mBAAjC,CAFE,IAGF,KAAKtB,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCqP,qBAAjC,CAHF,EAGyD;AACvD;AACD;;AAED,QAAI5L,QAAJ;AACA,QAAMrI,MAAM,GAAGtG,sBAAsB,CAAC,KAAKuK,QAAN,CAArC;;AACA,QAAM4kB,WAAW,GAAG,KAAK5kB,QAAL,CAAcQ,OAAd,CAAsBshB,yBAAtB,CAApB;;AAEA,QAAI8C,WAAJ,EAAiB;AACf,UAAMC,YAAY,GAAGD,WAAW,CAACjM,QAAZ,KAAyB,IAAzB,IAAiCiM,WAAW,CAACjM,QAAZ,KAAyB,IAA1D,GAAiE8L,kBAAjE,GAAsF9d,iBAA3G;AACAvC,MAAAA,QAAQ,GAAGhB,cAAc,CAACE,IAAf,CAAoBuhB,YAApB,EAAkCD,WAAlC,CAAX;AACAxgB,MAAAA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,CAACnI,MAAT,GAAkB,CAAnB,CAAnB;AACD;;AAED,QAAImW,SAAS,GAAG,IAAhB;;AAEA,QAAIhO,QAAJ,EAAc;AACZgO,MAAAA,SAAS,GAAG5W,YAAY,CAAC0C,OAAb,CAAqBkG,QAArB,EAA+BuI,YAA/B,EAA2C;AACrD5B,QAAAA,aAAa,EAAE,KAAK/K;AADiC,OAA3C,CAAZ;AAGD;;AAED,QAAM+R,SAAS,GAAGvW,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyM,YAApC,EAAgD;AAChE1B,MAAAA,aAAa,EAAE3G;AADiD,KAAhD,CAAlB;;AAIA,QAAI2N,SAAS,CAACvT,gBAAV,IACD4T,SAAS,KAAK,IAAd,IAAsBA,SAAS,CAAC5T,gBADnC,EACsD;AACpD;AACD;;AAED,SAAKslB,SAAL,CACE,KAAK9jB,QADP,EAEE4kB,WAFF;;AAKA,QAAMjW,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrBnT,MAAAA,YAAY,CAAC0C,OAAb,CAAqBkG,QAArB,EAA+BwI,cAA/B,EAA6C;AAC3C7B,QAAAA,aAAa,EAAE,KAAI,CAAC/K;AADuB,OAA7C;AAGAxE,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAI,CAAC8B,QAA1B,EAAoC0M,aAApC,EAAiD;AAC/C3B,QAAAA,aAAa,EAAE3G;AADgC,OAAjD;AAGD,KAPD;;AASA,QAAIrI,MAAJ,EAAY;AACV,WAAK+nB,SAAL,CAAe/nB,MAAf,EAAuBA,MAAM,CAAC7D,UAA9B,EAA0CyW,QAA1C;AACD,KAFD,MAEO;AACLA,MAAAA,QAAQ;AACT;AACF;;SAEDpO,UAAA,mBAAU;AACRtG,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;AACA,SAAKW,QAAL,GAAgB,IAAhB;AACD;;;SAID8jB,YAAA,mBAAU5uB,OAAV,EAAmBiZ,SAAnB,EAA8B9U,QAA9B,EAAwC;AAAA;;AACtC,QAAMyrB,cAAc,GAAG3W,SAAS,KAAKA,SAAS,CAACwK,QAAV,KAAuB,IAAvB,IAA+BxK,SAAS,CAACwK,QAAV,KAAuB,IAA3D,CAAT,GACrBvV,cAAc,CAACE,IAAf,CAAoBmhB,kBAApB,EAAwCtW,SAAxC,CADqB,GAErB/K,cAAc,CAACO,QAAf,CAAwBwK,SAAxB,EAAmCxH,iBAAnC,CAFF;AAIA,QAAMoe,MAAM,GAAGD,cAAc,CAAC,CAAD,CAA7B;AACA,QAAM/V,eAAe,GAAG1V,QAAQ,IAC7B0rB,MAAM,IAAIA,MAAM,CAACtkB,SAAP,CAAiBE,QAAjB,CAA0B2T,iBAA1B,CADb;;AAGA,QAAM3F,QAAQ,GAAG,SAAXA,QAAW;AAAA,aAAM,MAAI,CAACqW,mBAAL,CACrB9vB,OADqB,EAErB6vB,MAFqB,EAGrB1rB,QAHqB,CAAN;AAAA,KAAjB;;AAMA,QAAI0rB,MAAM,IAAIhW,eAAd,EAA+B;AAC7B,UAAMlZ,kBAAkB,GAAGH,gCAAgC,CAACqvB,MAAD,CAA3D;AACAA,MAAAA,MAAM,CAACtkB,SAAP,CAAiBC,MAAjB,CAAwBmM,iBAAxB;AAEArR,MAAAA,YAAY,CAACmC,GAAb,CAAiBonB,MAAjB,EAAyB7wB,cAAzB,EAAyCya,QAAzC;AACAnY,MAAAA,oBAAoB,CAACuuB,MAAD,EAASlvB,kBAAT,CAApB;AACD,KAND,MAMO;AACL8Y,MAAAA,QAAQ;AACT;AACF;;SAEDqW,sBAAA,6BAAoB9vB,OAApB,EAA6B6vB,MAA7B,EAAqC1rB,QAArC,EAA+C;AAC7C,QAAI0rB,MAAJ,EAAY;AACVA,MAAAA,MAAM,CAACtkB,SAAP,CAAiBC,MAAjB,CAAwBY,mBAAxB;AAEA,UAAM2jB,aAAa,GAAG7hB,cAAc,CAACM,OAAf,CAAuBghB,8BAAvB,EAAuDK,MAAM,CAAC7sB,UAA9D,CAAtB;;AAEA,UAAI+sB,aAAJ,EAAmB;AACjBA,QAAAA,aAAa,CAACxkB,SAAd,CAAwBC,MAAxB,CAA+BY,mBAA/B;AACD;;AAED,UAAIyjB,MAAM,CAAC3vB,YAAP,CAAoB,MAApB,MAAgC,KAApC,EAA2C;AACzC2vB,QAAAA,MAAM,CAACrjB,YAAP,CAAoB,eAApB,EAAqC,KAArC;AACD;AACF;;AAEDxM,IAAAA,OAAO,CAACuL,SAAR,CAAkB2J,GAAlB,CAAsB9I,mBAAtB;;AACA,QAAIpM,OAAO,CAACE,YAAR,CAAqB,MAArB,MAAiC,KAArC,EAA4C;AAC1CF,MAAAA,OAAO,CAACwM,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD;;AAED5I,IAAAA,MAAM,CAAC5D,OAAD,CAAN;;AAEA,QAAIA,OAAO,CAACuL,SAAR,CAAkBE,QAAlB,CAA2B2T,iBAA3B,CAAJ,EAAiD;AAC/Cpf,MAAAA,OAAO,CAACuL,SAAR,CAAkB2J,GAAlB,CAAsByC,iBAAtB;AACD;;AAED,QAAI3X,OAAO,CAACgD,UAAR,IAAsBhD,OAAO,CAACgD,UAAR,CAAmBuI,SAAnB,CAA6BE,QAA7B,CAAsC6jB,wBAAtC,CAA1B,EAA2F;AACzF,UAAMU,eAAe,GAAGhwB,OAAO,CAACsL,OAAR,CAAgB0hB,mBAAhB,CAAxB;;AAEA,UAAIgD,eAAJ,EAAqB;AACnB9hB,QAAAA,cAAc,CAACE,IAAf,CAAoB6e,0BAApB,EACG5qB,OADH,CACW,UAAA4tB,QAAQ;AAAA,iBAAIA,QAAQ,CAAC1kB,SAAT,CAAmB2J,GAAnB,CAAuB9I,mBAAvB,CAAJ;AAAA,SADnB;AAED;;AAEDpM,MAAAA,OAAO,CAACwM,YAAR,CAAqB,eAArB,EAAsC,IAAtC;AACD;;AAED,QAAIrI,QAAJ,EAAc;AACZA,MAAAA,QAAQ;AACT;AACF;;;MAIMyH,kBAAP,yBAAuB3J,MAAvB,EAA+B;AAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;AAC3B,UAAMnH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,KAAgC,IAAIslB,GAAJ,CAAQ,IAAR,CAA7C;;AAEA,UAAI,OAAOxtB,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;AACD;;AAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ;AACD;AACF,KAVM,CAAP;AAWD;;MAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;AAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;AACD;;;;wBA3JoB;AACnB,aAAOD,SAAP;AACD;;;;;AA4JH;AACA;AACA;AACA;AACA;;;AAEA5D,YAAY,CAACkC,EAAb,CAAgB3I,QAAhB,EAA0B4K,sBAA1B,EAAgD4B,sBAAhD,EAAsE,UAAUlG,KAAV,EAAiB;AACrFA,EAAAA,KAAK,CAAC6D,cAAN;AAEA,MAAMtF,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,KAAgC,IAAIslB,GAAJ,CAAQ,IAAR,CAA7C;AACA/qB,EAAAA,IAAI,CAACoU,IAAL;AACD,CALD;AAOA;AACA;AACA;AACA;AACA;AACA;;AAEA5U,kBAAkB,CAAC,YAAM;AACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;AACA;;AACA,MAAIoF,CAAJ,EAAO;AACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;AACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAawlB,GAAG,CAAC7jB,eAAjB;AACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBujB,GAAzB;;AACAvmB,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;AACA,aAAOwjB,GAAG,CAAC7jB,eAAX;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;ACjOA;AACA;AACA;AACA;AACA;;AAEA,IAAM3B,MAAI,GAAG,OAAb;AACA,IAAMC,SAAO,GAAG,cAAhB;AACA,IAAMC,UAAQ,GAAG,UAAjB;AACA,IAAMC,WAAS,SAAOD,UAAtB;AAEA,IAAM0U,qBAAmB,qBAAmBzU,WAA5C;AACA,IAAMqN,YAAU,YAAUrN,WAA1B;AACA,IAAMsN,cAAY,cAAYtN,WAA9B;AACA,IAAMmN,YAAU,YAAUnN,WAA1B;AACA,IAAMoN,aAAW,aAAWpN,WAA5B;AAEA,IAAMgV,iBAAe,GAAG,MAAxB;AACA,IAAM8Q,eAAe,GAAG,MAAxB;AACA,IAAMvY,iBAAe,GAAG,MAAxB;AACA,IAAMwY,kBAAkB,GAAG,SAA3B;AAEA,IAAMlgB,aAAW,GAAG;AAClB2W,EAAAA,SAAS,EAAE,SADO;AAElBwJ,EAAAA,QAAQ,EAAE,SAFQ;AAGlBrJ,EAAAA,KAAK,EAAE;AAHW,CAApB;AAMA,IAAMrX,SAAO,GAAG;AACdkX,EAAAA,SAAS,EAAE,IADG;AAEdwJ,EAAAA,QAAQ,EAAE,IAFI;AAGdrJ,EAAAA,KAAK,EAAE;AAHO,CAAhB;AAMA,IAAMvH,uBAAqB,GAAG,wBAA9B;AAEA;AACA;AACA;AACA;AACA;;IAEM6Q;AACJ,iBAAYrwB,OAAZ,EAAqBiC,MAArB,EAA6B;AAC3B,SAAK6I,QAAL,GAAgB9K,OAAhB;AACA,SAAK6S,OAAL,GAAe,KAAKC,UAAL,CAAgB7Q,MAAhB,CAAf;AACA,SAAK4mB,QAAL,GAAgB,IAAhB;;AACA,SAAKI,aAAL;;AACAlkB,IAAAA,IAAI,CAACC,OAAL,CAAahF,OAAb,EAAsBmK,UAAtB,EAAgC,IAAhC;AACD;;;;;AAgBD;SAEA2O,OAAA,gBAAO;AAAA;;AACL,QAAM+D,SAAS,GAAGvW,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoCyM,YAApC,CAAlB;;AAEA,QAAIsF,SAAS,CAACvT,gBAAd,EAAgC;AAC9B;AACD;;AAED,SAAKgnB,aAAL;;AAEA,QAAI,KAAKzd,OAAL,CAAa+T,SAAjB,EAA4B;AAC1B,WAAK9b,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4BkK,iBAA5B;AACD;;AAED,QAAM3F,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,MAAA,KAAI,CAAC3O,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B2kB,kBAA/B;;AACA,MAAA,KAAI,CAACrlB,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4ByC,iBAA5B;;AAEArR,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,KAAI,CAAC8B,QAA1B,EAAoC0M,aAApC;;AAEA,UAAI,KAAI,CAAC3E,OAAL,CAAaud,QAAjB,EAA2B;AACzB,QAAA,KAAI,CAACvH,QAAL,GAAgB/mB,UAAU,CAAC,YAAM;AAC/B,UAAA,KAAI,CAAC+W,IAAL;AACD,SAFyB,EAEvB,KAAI,CAAChG,OAAL,CAAakU,KAFU,CAA1B;AAGD;AACF,KAXD;;AAaA,SAAKjc,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+B0kB,eAA/B;;AACAtsB,IAAAA,MAAM,CAAC,KAAKkH,QAAN,CAAN;;AACA,SAAKA,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4Bib,kBAA5B;;AACA,QAAI,KAAKtd,OAAL,CAAa+T,SAAjB,EAA4B;AAC1B,UAAMjmB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKsK,QAAN,CAA3D;AAEAxE,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgDya,QAAhD;AACAnY,MAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBnK,kBAAhB,CAApB;AACD,KALD,MAKO;AACL8Y,MAAAA,QAAQ;AACT;AACF;;SAEDZ,OAAA,gBAAO;AAAA;;AACL,QAAI,CAAC,KAAK/N,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,iBAAjC,CAAL,EAAwD;AACtD;AACD;;AAED,QAAMuF,SAAS,GAAG5W,YAAY,CAAC0C,OAAb,CAAqB,KAAK8B,QAA1B,EAAoC2M,YAApC,CAAlB;;AAEA,QAAIyF,SAAS,CAAC5T,gBAAd,EAAgC;AAC9B;AACD;;AAED,QAAMmQ,QAAQ,GAAG,SAAXA,QAAW,GAAM;AACrB,MAAA,MAAI,CAAC3O,QAAL,CAAcS,SAAd,CAAwB2J,GAAxB,CAA4Bgb,eAA5B;;AACA5pB,MAAAA,YAAY,CAAC0C,OAAb,CAAqB,MAAI,CAAC8B,QAA1B,EAAoC4M,cAApC;AACD,KAHD;;AAKA,SAAK5M,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BmM,iBAA/B;;AACA,QAAI,KAAK9E,OAAL,CAAa+T,SAAjB,EAA4B;AAC1B,UAAMjmB,kBAAkB,GAAGH,gCAAgC,CAAC,KAAKsK,QAAN,CAA3D;AAEAxE,MAAAA,YAAY,CAACmC,GAAb,CAAiB,KAAKqC,QAAtB,EAAgC9L,cAAhC,EAAgDya,QAAhD;AACAnY,MAAAA,oBAAoB,CAAC,KAAKwJ,QAAN,EAAgBnK,kBAAhB,CAApB;AACD,KALD,MAKO;AACL8Y,MAAAA,QAAQ;AACT;AACF;;SAEDpO,UAAA,mBAAU;AACR,SAAKilB,aAAL;;AAEA,QAAI,KAAKxlB,QAAL,CAAcS,SAAd,CAAwBE,QAAxB,CAAiCkM,iBAAjC,CAAJ,EAAuD;AACrD,WAAK7M,QAAL,CAAcS,SAAd,CAAwBC,MAAxB,CAA+BmM,iBAA/B;AACD;;AAEDrR,IAAAA,YAAY,CAACC,GAAb,CAAiB,KAAKuE,QAAtB,EAAgC+T,qBAAhC;AACA9Z,IAAAA,IAAI,CAACI,UAAL,CAAgB,KAAK2F,QAArB,EAA+BX,UAA/B;AAEA,SAAKW,QAAL,GAAgB,IAAhB;AACA,SAAK+H,OAAL,GAAe,IAAf;AACD;;;SAIDC,aAAA,oBAAW7Q,MAAX,EAAmB;AACjBA,IAAAA,MAAM,gBACDyN,SADC,EAED3C,WAAW,CAACI,iBAAZ,CAA8B,KAAKrC,QAAnC,CAFC,EAGA,OAAO7I,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHhD,CAAN;AAMAF,IAAAA,eAAe,CAACkI,MAAD,EAAOhI,MAAP,EAAe,KAAKsb,WAAL,CAAiBtN,WAAhC,CAAf;AAEA,WAAOhO,MAAP;AACD;;SAEDgnB,gBAAA,yBAAgB;AAAA;;AACd3iB,IAAAA,YAAY,CAACkC,EAAb,CAAgB,KAAKsC,QAArB,EAA+B+T,qBAA/B,EAAoDW,uBAApD,EAA2E;AAAA,aAAM,MAAI,CAAC3G,IAAL,EAAN;AAAA,KAA3E;AACD;;SAEDyX,gBAAA,yBAAgB;AACdvb,IAAAA,YAAY,CAAC,KAAK8T,QAAN,CAAZ;AACA,SAAKA,QAAL,GAAgB,IAAhB;AACD;;;QAIMjd,kBAAP,yBAAuB3J,MAAvB,EAA+B;AAC7B,WAAO,KAAK4J,IAAL,CAAU,YAAY;AAC3B,UAAInH,IAAI,GAAGK,IAAI,CAACG,OAAL,CAAa,IAAb,EAAmBiF,UAAnB,CAAX;;AACA,UAAM0I,OAAO,GAAG,OAAO5Q,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;AAEA,UAAI,CAACyC,IAAL,EAAW;AACTA,QAAAA,IAAI,GAAG,IAAI2rB,KAAJ,CAAU,IAAV,EAAgBxd,OAAhB,CAAP;AACD;;AAED,UAAI,OAAO5Q,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAI,OAAOyC,IAAI,CAACzC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,gBAAM,IAAIgV,SAAJ,wBAAkChV,MAAlC,QAAN;AACD;;AAEDyC,QAAAA,IAAI,CAACzC,MAAD,CAAJ,CAAa,IAAb;AACD;AACF,KAfM,CAAP;AAgBD;;QAEM+J,cAAP,qBAAmBhM,OAAnB,EAA4B;AAC1B,WAAO+E,IAAI,CAACG,OAAL,CAAalF,OAAb,EAAsBmK,UAAtB,CAAP;AACD;;;;wBA5IoB;AACnB,aAAOD,SAAP;AACD;;;wBAEwB;AACvB,aAAO+F,aAAP;AACD;;;wBAEoB;AACnB,aAAOP,SAAP;AACD;;;;;AAqIH;AACA;AACA;AACA;AACA;AACA;;;AAEAxL,kBAAkB,CAAC,YAAM;AACvB,MAAMgF,CAAC,GAAGpF,SAAS,EAAnB;AACA;;AACA,MAAIoF,CAAJ,EAAO;AACL,QAAM+C,kBAAkB,GAAG/C,CAAC,CAACjD,EAAF,CAAKgE,MAAL,CAA3B;AACAf,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAaomB,KAAK,CAACzkB,eAAnB;AACA1C,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWiC,WAAX,GAAyBmkB,KAAzB;;AACAnnB,IAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,EAAWkC,UAAX,GAAwB,YAAM;AAC5BjD,MAAAA,CAAC,CAACjD,EAAF,CAAKgE,MAAL,IAAagC,kBAAb;AACA,aAAOokB,KAAK,CAACzkB,eAAb;AACD,KAHD;AAID;AACF,CAZiB,CAAlB;;;;"}