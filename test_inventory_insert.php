<?php
// Include database configuration
require_once 'app/config/config.php';

try {
    // Start transaction
    $conn->beginTransaction();

    // Check if inventory table exists
    $result = $conn->query("SHOW TABLES LIKE 'inventory'");
    $tableExists = $result->rowCount() > 0;
    
    if (!$tableExists) {
        // Create inventory table without constraints
        $conn->exec("CREATE TABLE IF NOT EXISTS inventory (
            id INT AUTO_INCREMENT PRIMARY KEY,
            prod_image VARCHAR(255),
            prod_name VARCHAR(100) NOT NULL,
            brand_name VARCHAR(100),
            price DECIMAL(10,2) NOT NULL,
            prod_measure VARCHAR(50),
            pack_type VARCHAR(50),
            expiry_date DATETIME,
            delivered_date DATETIME,
            country VARCHAR(100),
            batch_code VARCHAR(50),
            stocks DECIMAL(10,2) NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'approved',
            order_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        echo "Created inventory table.<br>";
    }
    
    // Insert a test record
    $stmt = $conn->prepare("INSERT INTO inventory 
        (prod_name, brand_name, price, prod_measure, pack_type, expiry_date, delivered_date, country, batch_code, stocks, status) 
        VALUES 
        ('Spices', 'VISKASE', 1.00, 'Grams (g)', 'Jar', '2025-09-09 00:00:00', NOW(), 'Philippines', 'SFGE65', 1.00, 'approved')");
    $stmt->execute();
    $insertedId = $conn->lastInsertId();
    
    // Commit transaction
    $conn->commit();
    
    echo "Inserted new inventory record with ID $insertedId and product name 'Spices'.<br>";
    
    // Get all inventory records
    $stmt = $conn->query("SELECT id, prod_name, brand_name, price, stocks FROM inventory ORDER BY id DESC");
    $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Current Inventory Records:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Product Name</th><th>Brand</th><th>Price</th><th>Stocks</th></tr>";
    
    foreach ($records as $record) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($record['id']) . "</td>";
        echo "<td>" . htmlspecialchars($record['prod_name'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($record['brand_name'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($record['price'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($record['stocks'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<p><a href='app/modules/inventory/inventory.php'>Go to Inventory Page</a></p>";
} catch (PDOException $e) {
    // Rollback transaction on error
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    echo "Error: " . $e->getMessage();
}
?>
