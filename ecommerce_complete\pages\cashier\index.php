<?php
require_once '../../config/ecommerce_db.php';
require_once '../../config/session.php';

// Require login
if (!isLoggedIn()) {
    header("Location: ../account/login.php");
    exit();
}

// Check if user is admin or staff
if (!isAdmin() && !isStaff()) {
    header("Location: ../../index.php");
    exit();
}

// Get all products
$products_sql = "SELECT p.*, c.name as category_name
                FROM e_products p
                LEFT JOIN e_categories c ON p.category_id = c.id
                ORDER BY c.name, p.name";
$products = executeQuery($products_sql)->fetchAll();

// Group products by category
$categorized_products = [];
foreach ($products as $product) {
    $category = $product['category_name'] ?? 'Uncategorized';
    if (!isset($categorized_products[$category])) {
        $categorized_products[$category] = [];
    }
    $categorized_products[$category][] = $product;
}

// Get unpaid orders
$unpaid_sql = "SELECT o.*, CONCAT(u.first_name, ' ', u.last_name) as customer_name
              FROM e_orders o
              JOIN e_users u ON o.user_id = u.id
              WHERE o.payment_status = 'unpaid'
              ORDER BY o.created_at DESC
              LIMIT 3";
$unpaid_orders = executeQuery($unpaid_sql)->fetchAll();

// Add to cart functionality
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_to_cart'])) {
    $product_id = $_POST['product_id'] ?? 0;
    $quantity = $_POST['quantity'] ?? 1;

    if ($product_id > 0 && $quantity > 0) {
        // Check if there's an existing pending order
        $order_sql = "SELECT id FROM e_orders
                     WHERE user_id = :user_id AND status = 'pending' AND payment_status = 'unpaid'
                     LIMIT 1";
        $order = executeQuery($order_sql, [':user_id' => $_SESSION['user_id']])->fetch();

        $order_id = 0;
        if ($order) {
            $order_id = $order['id'];
        } else {
            // Create new order
            $new_order_sql = "INSERT INTO e_orders (user_id, status, payment_status, total_amount)
                             VALUES (:user_id, 'pending', 'unpaid', 0)";
            executeQuery($new_order_sql, [':user_id' => $_SESSION['user_id']]);
            $order_id = $conn->lastInsertId();
        }

        // Check if product already in cart
        $check_sql = "SELECT id, quantity FROM e_order_items
                     WHERE order_id = :order_id AND product_id = :product_id
                     LIMIT 1";
        $existing_item = executeQuery($check_sql, [
            ':order_id' => $order_id,
            ':product_id' => $product_id
        ])->fetch();

        // Get product price
        $price_sql = "SELECT price FROM e_products WHERE id = :id LIMIT 1";
        $product = executeQuery($price_sql, [':id' => $product_id])->fetch();
        $price = $product['price'] ?? 0;

        if ($existing_item) {
            // Update quantity
            $update_sql = "UPDATE e_order_items
                          SET quantity = quantity + :quantity
                          WHERE id = :id";
            executeQuery($update_sql, [
                ':quantity' => $quantity,
                ':id' => $existing_item['id']
            ]);
        } else {
            // Add new item
            $add_sql = "INSERT INTO e_order_items (order_id, product_id, quantity, price)
                       VALUES (:order_id, :product_id, :quantity, :price)";
            executeQuery($add_sql, [
                ':order_id' => $order_id,
                ':product_id' => $product_id,
                ':quantity' => $quantity,
                ':price' => $price
            ]);
        }

        // Update order total
        $update_total_sql = "UPDATE e_orders SET total_amount = (
                            SELECT SUM(price * quantity) FROM e_order_items WHERE order_id = :order_id
                            ) WHERE id = :order_id";
        executeQuery($update_total_sql, [':order_id' => $order_id]);

        // Redirect to prevent form resubmission
        header("Location: " . $_SERVER['PHP_SELF'] . "?added=1");
        exit();
    }
}

// Get cart count
$cart_count = 0;
$cart_sql = "SELECT SUM(oi.quantity) as total_items
            FROM e_order_items oi
            JOIN e_orders o ON oi.order_id = o.id
            WHERE o.user_id = :user_id AND o.status = 'pending' AND o.payment_status = 'unpaid'";
$cart = executeQuery($cart_sql, [':user_id' => $_SESSION['user_id']])->fetch();
if ($cart && $cart['total_items']) {
    $cart_count = $cart['total_items'];
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cashier - Euro Spice</title>
    <link rel="stylesheet" href="../../plugins/bootstrap/css/bootstrap.min.css">
    <script defer src="../../plugins/fontawesome/js/all.js"></script>
    <link href="../../dist/style.css" rel="stylesheet">
    <style>
        .product-card {
            transition: transform 0.2s;
        }

        .product-card:hover {
            transform: translateY(-5px);
        }

        .product-img {
            height: 180px;
            object-fit: cover;
        }
    </style>
</head>

<body>
    <!-- Navbar Menu -->
    <nav class="navbar navbar-expand-lg navbar-light border-bottom site-header sticky-top py-1" style="background-color: #f15b31;">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold fs-4" href="../../index.php" style="color: white;"><b>Euro Spice</b></a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <form class="d-flex mr-auto ml-5 w-100" method="GET">
                    <input class="form-control form-control-sm" type="text" name="search" placeholder="Search product name" aria-label="Search">
                    <button class="btn btn-outline-light btn-sm mr-5" type="submit"><i class="fas fa-search"></i></button>
                </form>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="cartDropdown" role="button" data-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-shopping-cart"></i>
                            <?php if ($cart_count > 0): ?>
                                <span class="badge bg-light text-dark"><?php echo $cart_count; ?></span>
                            <?php endif; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right" style="width:20rem;">
                            <div class="border-bottom pb-1 mb-2 d-flex justify-content-between fw-bold">
                                <span class="ml-3 text-secondary">
                                    Cart (<?php echo $cart_count; ?>)
                                </span>
                                <span class="mr-3">
                                    <a href="cart.php" class="link-success">View Cart</a>
                                </span>
                            </div>
                            <div class="text-center py-2">
                                <a href="cart.php" class="btn btn-primary btn-sm">Go to Checkout</a>
                            </div>
                        </ul>
                    </li>

                    <?php if (isAdmin() || isStaff()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="notificationDropdown" role="button" data-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell"></i>
                                <?php if (count($unpaid_orders) > 0): ?>
                                    <span class="badge bg-light text-dark"><?php echo count($unpaid_orders); ?></span>
                                <?php endif; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-right" style="width:20rem;">
                                <div class="border-bottom pb-1 mb-2 d-flex justify-content-between fw-bold">
                                    <span class="ml-3 text-secondary">
                                        Unpaid Orders (<?php echo count($unpaid_orders); ?>)
                                    </span>
                                    <span class="mr-3">
                                        <a href="unpaid.php" class="link-success">See All</a>
                                    </span>
                                </div>

                                <?php if (empty($unpaid_orders)): ?>
                                    <div class="text-center py-2">
                                        <p>No unpaid orders</p>
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($unpaid_orders as $order): ?>
                                        <div class="ml-3 mr-3 d-flex mb-2 justify-content-between border-bottom">
                                            <span>
                                                <?php echo htmlspecialchars($order['customer_name']); ?> <br>
                                                <small class="fst-italic">Order #<?php echo $order['id']; ?> ~ PHP <?php echo number_format($order['total_amount'], 2); ?></small>
                                            </span>
                                            <span>
                                                <a href="process_payment.php?order_id=<?php echo $order['id']; ?>" class="mt-1 btn btn-outline-success btn-sm">Process</a>
                                            </span>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </ul>
                        </li>
                    <?php endif; ?>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="menuDropdown" role="button" data-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bars"></i> Menu
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li><a href="index.php" class="dropdown-item">Cashier</a></li>
                            <li><a href="../raw-material/index.php" class="dropdown-item">Raw Material</a></li>
                            <li><a href="../product-list/index.php" class="dropdown-item">Product List</a></li>
                            <?php if (isAdmin()): ?>
                                <li><a href="../user-list/index.php" class="dropdown-item">User List</a></li>
                            <?php endif; ?>
                            <li><a href="../sellbuy-report/index.php" class="dropdown-item">Sell/Buy Report</a></li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li><a href="../account/settings.php" class="dropdown-item">Settings</a></li>
                            <li><a href="../account/help.php" class="dropdown-item">Help</a></li>
                            <li><a href="../account/logout.php" class="dropdown-item">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4 mb-5">
        <main>
            <?php if (isset($_GET['added']) && $_GET['added'] == 1): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    Product added to cart successfully!
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Products by Category -->
            <?php foreach ($categorized_products as $category => $products): ?>
                <div class="album">
                    <div class="container">
                        <h4 class="text-secondary border-bottom mb-3 pb-2 pl-3"><?php echo htmlspecialchars($category); ?></h4>
                        <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-3">
                            <?php foreach ($products as $product): ?>
                                <div class="col mb-4">
                                    <div class="card shadow-sm product-card">
                                        <?php if (!empty($product['image_url'])): ?>
                                            <img src="<?php echo htmlspecialchars($product['image_url']); ?>" class="card-img-top product-img" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                        <?php else: ?>
                                            <div class="bd-placeholder-img card-img-top product-img bg-light d-flex align-items-center justify-content-center">
                                                <span class="text-secondary">No Image</span>
                                            </div>
                                        <?php endif; ?>

                                        <div class="card-body">
                                            <h5 class="card-title text-center border-bottom pb-2"><?php echo htmlspecialchars($product['name']); ?></h5>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <p class="fw-bold mb-0">PHP <?php echo number_format($product['price'], 2); ?></p>
                                                    <?php if ($product['stock_quantity'] <= 5): ?>
                                                        <small class="text-danger">Only <?php echo $product['stock_quantity']; ?> left</small>
                                                    <?php endif; ?>
                                                </div>
                                                <div>
                                                    <form method="POST">
                                                        <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                                        <div class="input-group">
                                                            <input type="number" name="quantity" value="1" min="1" max="<?php echo $product['stock_quantity']; ?>" class="form-control form-control-sm" style="width: 60px;">
                                                            <button type="submit" name="add_to_cart" class="btn btn-success btn-sm">
                                                                <i class="fas fa-cart-plus"></i>
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>

            <?php if (empty($categorized_products)): ?>
                <div class="alert alert-info">
                    No products available. Please add products from the Product List section.
                </div>
            <?php endif; ?>
        </main>
    </div>

    <footer class="my-3 pt-3 text-muted text-center border-top">
        <p class="mb-1">&copy; 2023 Euro Spice</p>
    </footer>

    <script src="../../plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Auto-hide alerts after 3 seconds
        $(document).ready(function() {
            setTimeout(function() {
                $('.alert').alert('close');
            }, 3000);
        });
    </script>
</body>

</html>