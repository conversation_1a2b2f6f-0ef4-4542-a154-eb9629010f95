/*!
  * Bootstrap v5.0.0-alpha3 (https://getbootstrap.com/)
  * Copyright 2011-2020 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("popper.js")):"function"==typeof define&&define.amd?define(["popper.js"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).bootstrap=e(t.Popper)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var n=e(t);function i(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function o(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),t}function s(){return(s=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t}).apply(this,arguments)}var r,a,l=function(t){do{t+=Math.floor(1e6*Math.random())}while(document.getElementById(t));return t},c=function(t){var e=t.getAttribute("data-target");if(!e||"#"===e){var n=t.getAttribute("href");e=n&&"#"!==n?n.trim():null}return e},u=function(t){var e=c(t);return e&&document.querySelector(e)?e:null},h=function(t){var e=c(t);return e?document.querySelector(e):null},f=function(t){if(!t)return 0;var e=window.getComputedStyle(t),n=e.transitionDuration,i=e.transitionDelay,o=parseFloat(n),s=parseFloat(i);return o||s?(n=n.split(",")[0],i=i.split(",")[0],1e3*(parseFloat(n)+parseFloat(i))):0},d=function(t){t.dispatchEvent(new Event("transitionend"))},g=function(t){return(t[0]||t).nodeType},p=function(t,e){var n=!1,i=e+5;t.addEventListener("transitionend",(function e(){n=!0,t.removeEventListener("transitionend",e)})),setTimeout((function(){n||d(t)}),i)},m=function(t,e,n){Object.keys(n).forEach((function(i){var o,s=n[i],r=e[i],a=r&&g(r)?"element":null==(o=r)?""+o:{}.toString.call(o).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(s).test(a))throw new Error(t.toUpperCase()+': Option "'+i+'" provided type "'+a+'" but expected type "'+s+'".')}))},_=function(t){if(!t)return!1;if(t.style&&t.parentNode&&t.parentNode.style){var e=getComputedStyle(t),n=getComputedStyle(t.parentNode);return"none"!==e.display&&"none"!==n.display&&"hidden"!==e.visibility}return!1},v=function(){return function(){}},b=function(t){return t.offsetHeight},y=function(){var t=window.jQuery;return t&&!document.body.hasAttribute("data-no-jquery")?t:null},w=function(t){"loading"===document.readyState?document.addEventListener("DOMContentLoaded",t):t()},E=(r={},a=1,{set:function(t,e,n){void 0===t.bsKey&&(t.bsKey={key:e,id:a},a++),r[t.bsKey.id]=n},get:function(t,e){if(!t||void 0===t.bsKey)return null;var n=t.bsKey;return n.key===e?r[n.id]:null},delete:function(t,e){if(void 0!==t.bsKey){var n=t.bsKey;n.key===e&&(delete r[n.id],delete t.bsKey)}}}),T=function(t,e,n){E.set(t,e,n)},k=function(t,e){return E.get(t,e)},L=function(t,e){E.delete(t,e)},C=/[^.]*(?=\..*)\.|.*/,A=/\..*/,S=/::\d+$/,D={},I=1,O={mouseenter:"mouseover",mouseleave:"mouseout"},N=["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"];function j(t,e){return e&&e+"::"+I++||t.uidEvent||I++}function x(t){var e=j(t);return t.uidEvent=e,D[e]=D[e]||{},D[e]}function P(t,e,n){void 0===n&&(n=null);for(var i=Object.keys(t),o=0,s=i.length;o<s;o++){var r=t[i[o]];if(r.originalHandler===e&&r.delegationSelector===n)return r}return null}function R(t,e,n){var i="string"==typeof e,o=i?n:e,s=t.replace(A,""),r=O[s];return r&&(s=r),N.indexOf(s)>-1||(s=t),[i,o,s]}function H(t,e,n,i,o){if("string"==typeof e&&t){n||(n=i,i=null);var s=R(e,n,i),r=s[0],a=s[1],l=s[2],c=x(t),u=c[l]||(c[l]={}),h=P(u,a,r?n:null);if(h)h.oneOff=h.oneOff&&o;else{var f=j(a,e.replace(C,"")),d=r?function(t,e,n){return function i(o){for(var s=t.querySelectorAll(e),r=o.target;r&&r!==this;r=r.parentNode)for(var a=s.length;a--;)if(s[a]===r)return o.delegateTarget=r,i.oneOff&&B.off(t,o.type,n),n.apply(r,[o]);return null}}(t,n,i):function(t,e){return function n(i){return i.delegateTarget=t,n.oneOff&&B.off(t,i.type,e),e.apply(t,[i])}}(t,n);d.delegationSelector=r?n:null,d.originalHandler=a,d.oneOff=o,d.uidEvent=f,u[f]=d,t.addEventListener(l,d,r)}}}function M(t,e,n,i,o){var s=P(e[n],i,o);s&&(t.removeEventListener(n,s,Boolean(o)),delete e[n][s.uidEvent])}var B={on:function(t,e,n,i){H(t,e,n,i,!1)},one:function(t,e,n,i){H(t,e,n,i,!0)},off:function(t,e,n,i){if("string"==typeof e&&t){var o=R(e,n,i),s=o[0],r=o[1],a=o[2],l=a!==e,c=x(t),u="."===e.charAt(0);if(void 0===r){u&&Object.keys(c).forEach((function(n){!function(t,e,n,i){var o=e[n]||{};Object.keys(o).forEach((function(s){if(s.indexOf(i)>-1){var r=o[s];M(t,e,n,r.originalHandler,r.delegationSelector)}}))}(t,c,n,e.slice(1))}));var h=c[a]||{};Object.keys(h).forEach((function(n){var i=n.replace(S,"");if(!l||e.indexOf(i)>-1){var o=h[n];M(t,c,a,o.originalHandler,o.delegationSelector)}}))}else{if(!c||!c[a])return;M(t,c,a,r,s?n:null)}}},trigger:function(t,e,n){if("string"!=typeof e||!t)return null;var i,o=y(),s=e.replace(A,""),r=e!==s,a=N.indexOf(s)>-1,l=!0,c=!0,u=!1,h=null;return r&&o&&(i=o.Event(e,n),o(t).trigger(i),l=!i.isPropagationStopped(),c=!i.isImmediatePropagationStopped(),u=i.isDefaultPrevented()),a?(h=document.createEvent("HTMLEvents")).initEvent(s,l,!0):h=new CustomEvent(e,{bubbles:l,cancelable:!0}),void 0!==n&&Object.keys(n).forEach((function(t){Object.defineProperty(h,t,{get:function(){return n[t]}})})),u&&h.preventDefault(),c&&t.dispatchEvent(h),h.defaultPrevented&&void 0!==i&&i.preventDefault(),h}},Q="alert",U=function(){function t(t){this._element=t,this._element&&T(t,"bs.alert",this)}var e=t.prototype;return e.close=function(t){var e=t?this._getRootElement(t):this._element,n=this._triggerCloseEvent(e);null===n||n.defaultPrevented||this._removeElement(e)},e.dispose=function(){L(this._element,"bs.alert"),this._element=null},e._getRootElement=function(t){return h(t)||t.closest(".alert")},e._triggerCloseEvent=function(t){return B.trigger(t,"close.bs.alert")},e._removeElement=function(t){var e=this;if(t.classList.remove("show"),t.classList.contains("fade")){var n=f(t);B.one(t,"transitionend",(function(){return e._destroyElement(t)})),p(t,n)}else this._destroyElement(t)},e._destroyElement=function(t){t.parentNode&&t.parentNode.removeChild(t),B.trigger(t,"closed.bs.alert")},t.jQueryInterface=function(e){return this.each((function(){var n=k(this,"bs.alert");n||(n=new t(this)),"close"===e&&n[e](this)}))},t.handleDismiss=function(t){return function(e){e&&e.preventDefault(),t.close(this)}},t.getInstance=function(t){return k(t,"bs.alert")},o(t,null,[{key:"VERSION",get:function(){return"5.0.0-alpha3"}}]),t}();B.on(document,"click.bs.alert.data-api",'[data-dismiss="alert"]',U.handleDismiss(new U)),w((function(){var t=y();if(t){var e=t.fn[Q];t.fn[Q]=U.jQueryInterface,t.fn[Q].Constructor=U,t.fn[Q].noConflict=function(){return t.fn[Q]=e,U.jQueryInterface}}}));var F=function(){function t(t){this._element=t,T(t,"bs.button",this)}var e=t.prototype;return e.toggle=function(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))},e.dispose=function(){L(this._element,"bs.button"),this._element=null},t.jQueryInterface=function(e){return this.each((function(){var n=k(this,"bs.button");n||(n=new t(this)),"toggle"===e&&n[e]()}))},t.getInstance=function(t){return k(t,"bs.button")},o(t,null,[{key:"VERSION",get:function(){return"5.0.0-alpha3"}}]),t}();function W(t){return"true"===t||"false"!==t&&(t===Number(t).toString()?Number(t):""===t||"null"===t?null:t)}function K(t){return t.replace(/[A-Z]/g,(function(t){return"-"+t.toLowerCase()}))}B.on(document,"click.bs.button.data-api",'[data-toggle="button"]',(function(t){t.preventDefault();var e=t.target.closest('[data-toggle="button"]'),n=k(e,"bs.button");n||(n=new F(e)),n.toggle()})),w((function(){var t=y();if(t){var e=t.fn.button;t.fn.button=F.jQueryInterface,t.fn.button.Constructor=F,t.fn.button.noConflict=function(){return t.fn.button=e,F.jQueryInterface}}}));var V={setDataAttribute:function(t,e,n){t.setAttribute("data-"+K(e),n)},removeDataAttribute:function(t,e){t.removeAttribute("data-"+K(e))},getDataAttributes:function(t){if(!t)return{};var e=s({},t.dataset);return Object.keys(e).forEach((function(t){e[t]=W(e[t])})),e},getDataAttribute:function(t,e){return W(t.getAttribute("data-"+K(e)))},offset:function(t){var e=t.getBoundingClientRect();return{top:e.top+document.body.scrollTop,left:e.left+document.body.scrollLeft}},position:function(t){return{top:t.offsetTop,left:t.offsetLeft}}},z={matches:function(t,e){return t.matches(e)},find:function(t,e){var n;return void 0===e&&(e=document.documentElement),(n=[]).concat.apply(n,Element.prototype.querySelectorAll.call(e,t))},findOne:function(t,e){return void 0===e&&(e=document.documentElement),Element.prototype.querySelector.call(e,t)},children:function(t,e){var n,i=(n=[]).concat.apply(n,t.children);return i.filter((function(t){return t.matches(e)}))},parents:function(t,e){for(var n=[],i=t.parentNode;i&&i.nodeType===Node.ELEMENT_NODE&&3!==i.nodeType;)this.matches(i,e)&&n.push(i),i=i.parentNode;return n},prev:function(t,e){for(var n=t.previousElementSibling;n;){if(n.matches(e))return[n];n=n.previousElementSibling}return[]},next:function(t,e){for(var n=t.nextElementSibling;n;){if(this.matches(n,e))return[n];n=n.nextElementSibling}return[]}},X="carousel",Y=".bs.carousel",q={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},$={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},G={TOUCH:"touch",PEN:"pen"},Z=function(){function t(t,e){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(e),this._element=t,this._indicatorsElement=z.findOne(".carousel-indicators",this._element),this._touchSupported="ontouchstart"in document.documentElement||navigator.maxTouchPoints>0,this._pointerEvent=Boolean(window.PointerEvent),this._addEventListeners(),T(t,"bs.carousel",this)}var e=t.prototype;return e.next=function(){this._isSliding||this._slide("next")},e.nextWhenVisible=function(){!document.hidden&&_(this._element)&&this.next()},e.prev=function(){this._isSliding||this._slide("prev")},e.pause=function(t){t||(this._isPaused=!0),z.findOne(".carousel-item-next, .carousel-item-prev",this._element)&&(d(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},e.cycle=function(t){t||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config&&this._config.interval&&!this._isPaused&&(this._updateInterval(),this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},e.to=function(t){var e=this;this._activeElement=z.findOne(".active.carousel-item",this._element);var n=this._getItemIndex(this._activeElement);if(!(t>this._items.length-1||t<0))if(this._isSliding)B.one(this._element,"slid.bs.carousel",(function(){return e.to(t)}));else{if(n===t)return this.pause(),void this.cycle();var i=t>n?"next":"prev";this._slide(i,this._items[t])}},e.dispose=function(){B.off(this._element,Y),L(this._element,"bs.carousel"),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},e._getConfig=function(t){return t=s({},q,t),m(X,t,$),t},e._handleSwipe=function(){var t=Math.abs(this.touchDeltaX);if(!(t<=40)){var e=t/this.touchDeltaX;this.touchDeltaX=0,e>0&&this.prev(),e<0&&this.next()}},e._addEventListeners=function(){var t=this;this._config.keyboard&&B.on(this._element,"keydown.bs.carousel",(function(e){return t._keydown(e)})),"hover"===this._config.pause&&(B.on(this._element,"mouseenter.bs.carousel",(function(e){return t.pause(e)})),B.on(this._element,"mouseleave.bs.carousel",(function(e){return t.cycle(e)}))),this._config.touch&&this._touchSupported&&this._addTouchEventListeners()},e._addTouchEventListeners=function(){var t=this,e=function(e){t._pointerEvent&&G[e.pointerType.toUpperCase()]?t.touchStartX=e.clientX:t._pointerEvent||(t.touchStartX=e.touches[0].clientX)},n=function(e){t._pointerEvent&&G[e.pointerType.toUpperCase()]&&(t.touchDeltaX=e.clientX-t.touchStartX),t._handleSwipe(),"hover"===t._config.pause&&(t.pause(),t.touchTimeout&&clearTimeout(t.touchTimeout),t.touchTimeout=setTimeout((function(e){return t.cycle(e)}),500+t._config.interval))};z.find(".carousel-item img",this._element).forEach((function(t){B.on(t,"dragstart.bs.carousel",(function(t){return t.preventDefault()}))})),this._pointerEvent?(B.on(this._element,"pointerdown.bs.carousel",(function(t){return e(t)})),B.on(this._element,"pointerup.bs.carousel",(function(t){return n(t)})),this._element.classList.add("pointer-event")):(B.on(this._element,"touchstart.bs.carousel",(function(t){return e(t)})),B.on(this._element,"touchmove.bs.carousel",(function(e){return function(e){e.touches&&e.touches.length>1?t.touchDeltaX=0:t.touchDeltaX=e.touches[0].clientX-t.touchStartX}(e)})),B.on(this._element,"touchend.bs.carousel",(function(t){return n(t)})))},e._keydown=function(t){if(!/input|textarea/i.test(t.target.tagName))switch(t.key){case"ArrowLeft":t.preventDefault(),this.prev();break;case"ArrowRight":t.preventDefault(),this.next()}},e._getItemIndex=function(t){return this._items=t&&t.parentNode?z.find(".carousel-item",t.parentNode):[],this._items.indexOf(t)},e._getItemByDirection=function(t,e){var n="next"===t,i="prev"===t,o=this._getItemIndex(e),s=this._items.length-1;if((i&&0===o||n&&o===s)&&!this._config.wrap)return e;var r=(o+("prev"===t?-1:1))%this._items.length;return-1===r?this._items[this._items.length-1]:this._items[r]},e._triggerSlideEvent=function(t,e){var n=this._getItemIndex(t),i=this._getItemIndex(z.findOne(".active.carousel-item",this._element));return B.trigger(this._element,"slide.bs.carousel",{relatedTarget:t,direction:e,from:i,to:n})},e._setActiveIndicatorElement=function(t){if(this._indicatorsElement){for(var e=z.find(".active",this._indicatorsElement),n=0;n<e.length;n++)e[n].classList.remove("active");var i=this._indicatorsElement.children[this._getItemIndex(t)];i&&i.classList.add("active")}},e._updateInterval=function(){var t=this._activeElement||z.findOne(".active.carousel-item",this._element);if(t){var e=parseInt(t.getAttribute("data-interval"),10);e?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=e):this._config.interval=this._config.defaultInterval||this._config.interval}},e._slide=function(t,e){var n,i,o,s=this,r=z.findOne(".active.carousel-item",this._element),a=this._getItemIndex(r),l=e||r&&this._getItemByDirection(t,r),c=this._getItemIndex(l),u=Boolean(this._interval);if("next"===t?(n="carousel-item-left",i="carousel-item-next",o="left"):(n="carousel-item-right",i="carousel-item-prev",o="right"),l&&l.classList.contains("active"))this._isSliding=!1;else if(!this._triggerSlideEvent(l,o).defaultPrevented&&r&&l){if(this._isSliding=!0,u&&this.pause(),this._setActiveIndicatorElement(l),this._activeElement=l,this._element.classList.contains("slide")){l.classList.add(i),b(l),r.classList.add(n),l.classList.add(n);var h=f(r);B.one(r,"transitionend",(function(){l.classList.remove(n,i),l.classList.add("active"),r.classList.remove("active",i,n),s._isSliding=!1,setTimeout((function(){B.trigger(s._element,"slid.bs.carousel",{relatedTarget:l,direction:o,from:a,to:c})}),0)})),p(r,h)}else r.classList.remove("active"),l.classList.add("active"),this._isSliding=!1,B.trigger(this._element,"slid.bs.carousel",{relatedTarget:l,direction:o,from:a,to:c});u&&this.cycle()}},t.carouselInterface=function(e,n){var i=k(e,"bs.carousel"),o=s({},q,V.getDataAttributes(e));"object"==typeof n&&(o=s({},o,n));var r="string"==typeof n?n:o.slide;if(i||(i=new t(e,o)),"number"==typeof n)i.to(n);else if("string"==typeof r){if(void 0===i[r])throw new TypeError('No method named "'+r+'"');i[r]()}else o.interval&&o.ride&&(i.pause(),i.cycle())},t.jQueryInterface=function(e){return this.each((function(){t.carouselInterface(this,e)}))},t.dataApiClickHandler=function(e){var n=h(this);if(n&&n.classList.contains("carousel")){var i=s({},V.getDataAttributes(n),V.getDataAttributes(this)),o=this.getAttribute("data-slide-to");o&&(i.interval=!1),t.carouselInterface(n,i),o&&k(n,"bs.carousel").to(o),e.preventDefault()}},t.getInstance=function(t){return k(t,"bs.carousel")},o(t,null,[{key:"VERSION",get:function(){return"5.0.0-alpha3"}},{key:"Default",get:function(){return q}}]),t}();B.on(document,"click.bs.carousel.data-api","[data-slide], [data-slide-to]",Z.dataApiClickHandler),B.on(window,"load.bs.carousel.data-api",(function(){for(var t=z.find('[data-ride="carousel"]'),e=0,n=t.length;e<n;e++)Z.carouselInterface(t[e],k(t[e],"bs.carousel"))})),w((function(){var t=y();if(t){var e=t.fn[X];t.fn[X]=Z.jQueryInterface,t.fn[X].Constructor=Z,t.fn[X].noConflict=function(){return t.fn[X]=e,Z.jQueryInterface}}}));var J="collapse",tt={toggle:!0,parent:""},et={toggle:"boolean",parent:"(string|element)"},nt=function(){function t(t,e){this._isTransitioning=!1,this._element=t,this._config=this._getConfig(e),this._triggerArray=z.find('[data-toggle="collapse"][href="#'+t.id+'"],[data-toggle="collapse"][data-target="#'+t.id+'"]');for(var n=z.find('[data-toggle="collapse"]'),i=0,o=n.length;i<o;i++){var s=n[i],r=u(s),a=z.find(r).filter((function(e){return e===t}));null!==r&&a.length&&(this._selector=r,this._triggerArray.push(s))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle(),T(t,"bs.collapse",this)}var e=t.prototype;return e.toggle=function(){this._element.classList.contains("show")?this.hide():this.show()},e.show=function(){var e=this;if(!this._isTransitioning&&!this._element.classList.contains("show")){var n,i;this._parent&&0===(n=z.find(".show, .collapsing",this._parent).filter((function(t){return"string"==typeof e._config.parent?t.getAttribute("data-parent")===e._config.parent:t.classList.contains("collapse")}))).length&&(n=null);var o=z.findOne(this._selector);if(n){var s=n.filter((function(t){return o!==t}));if((i=s[0]?k(s[0],"bs.collapse"):null)&&i._isTransitioning)return}if(!B.trigger(this._element,"show.bs.collapse").defaultPrevented){n&&n.forEach((function(e){o!==e&&t.collapseInterface(e,"hide"),i||T(e,"bs.collapse",null)}));var r=this._getDimension();this._element.classList.remove("collapse"),this._element.classList.add("collapsing"),this._element.style[r]=0,this._triggerArray.length&&this._triggerArray.forEach((function(t){t.classList.remove("collapsed"),t.setAttribute("aria-expanded",!0)})),this.setTransitioning(!0);var a="scroll"+(r[0].toUpperCase()+r.slice(1)),l=f(this._element);B.one(this._element,"transitionend",(function(){e._element.classList.remove("collapsing"),e._element.classList.add("collapse","show"),e._element.style[r]="",e.setTransitioning(!1),B.trigger(e._element,"shown.bs.collapse")})),p(this._element,l),this._element.style[r]=this._element[a]+"px"}}},e.hide=function(){var t=this;if(!this._isTransitioning&&this._element.classList.contains("show")&&!B.trigger(this._element,"hide.bs.collapse").defaultPrevented){var e=this._getDimension();this._element.style[e]=this._element.getBoundingClientRect()[e]+"px",b(this._element),this._element.classList.add("collapsing"),this._element.classList.remove("collapse","show");var n=this._triggerArray.length;if(n>0)for(var i=0;i<n;i++){var o=this._triggerArray[i],s=h(o);s&&!s.classList.contains("show")&&(o.classList.add("collapsed"),o.setAttribute("aria-expanded",!1))}this.setTransitioning(!0);this._element.style[e]="";var r=f(this._element);B.one(this._element,"transitionend",(function(){t.setTransitioning(!1),t._element.classList.remove("collapsing"),t._element.classList.add("collapse"),B.trigger(t._element,"hidden.bs.collapse")})),p(this._element,r)}},e.setTransitioning=function(t){this._isTransitioning=t},e.dispose=function(){L(this._element,"bs.collapse"),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},e._getConfig=function(t){return(t=s({},tt,t)).toggle=Boolean(t.toggle),m(J,t,et),t},e._getDimension=function(){return this._element.classList.contains("width")?"width":"height"},e._getParent=function(){var t=this,e=this._config.parent;g(e)?void 0===e.jquery&&void 0===e[0]||(e=e[0]):e=z.findOne(e);var n='[data-toggle="collapse"][data-parent="'+e+'"]';return z.find(n,e).forEach((function(e){var n=h(e);t._addAriaAndCollapsedClass(n,[e])})),e},e._addAriaAndCollapsedClass=function(t,e){if(t&&e.length){var n=t.classList.contains("show");e.forEach((function(t){n?t.classList.remove("collapsed"):t.classList.add("collapsed"),t.setAttribute("aria-expanded",n)}))}},t.collapseInterface=function(e,n){var i=k(e,"bs.collapse"),o=s({},tt,V.getDataAttributes(e),"object"==typeof n&&n?n:{});if(!i&&o.toggle&&"string"==typeof n&&/show|hide/.test(n)&&(o.toggle=!1),i||(i=new t(e,o)),"string"==typeof n){if(void 0===i[n])throw new TypeError('No method named "'+n+'"');i[n]()}},t.jQueryInterface=function(e){return this.each((function(){t.collapseInterface(this,e)}))},t.getInstance=function(t){return k(t,"bs.collapse")},o(t,null,[{key:"VERSION",get:function(){return"5.0.0-alpha3"}},{key:"Default",get:function(){return tt}}]),t}();B.on(document,"click.bs.collapse.data-api",'[data-toggle="collapse"]',(function(t){"A"===t.target.tagName&&t.preventDefault();var e=V.getDataAttributes(this),n=u(this);z.find(n).forEach((function(t){var n,i=k(t,"bs.collapse");i?(null===i._parent&&"string"==typeof e.parent&&(i._config.parent=e.parent,i._parent=i._getParent()),n="toggle"):n=e,nt.collapseInterface(t,n)}))})),w((function(){var t=y();if(t){var e=t.fn[J];t.fn[J]=nt.jQueryInterface,t.fn[J].Constructor=nt,t.fn[J].noConflict=function(){return t.fn[J]=e,nt.jQueryInterface}}}));var it="dropdown",ot=new RegExp("ArrowUp|ArrowDown|Escape"),st={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},rt={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},at=function(){function t(t,e){this._element=t,this._popper=null,this._config=this._getConfig(e),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners(),T(t,"bs.dropdown",this)}var e=t.prototype;return e.toggle=function(){if(!this._element.disabled&&!this._element.classList.contains("disabled")){var e=this._element.classList.contains("show");t.clearMenus(),e||this.show()}},e.show=function(){if(!(this._element.disabled||this._element.classList.contains("disabled")||this._menu.classList.contains("show"))){var e=t.getParentFromElement(this._element),i={relatedTarget:this._element};if(!B.trigger(this._element,"show.bs.dropdown",i).defaultPrevented){if(!this._inNavbar){if(void 0===n.default)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org)");var o=this._element;"parent"===this._config.reference?o=e:g(this._config.reference)&&(o=this._config.reference,void 0!==this._config.reference.jquery&&(o=this._config.reference[0])),"scrollParent"!==this._config.boundary&&e.classList.add("position-static"),this._popper=new n.default(o,this._menu,this._getPopperConfig())}var s;if("ontouchstart"in document.documentElement&&!e.closest(".navbar-nav"))(s=[]).concat.apply(s,document.body.children).forEach((function(t){return B.on(t,"mouseover",null,(function(){}))}));this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.toggle("show"),this._element.classList.toggle("show"),B.trigger(e,"shown.bs.dropdown",i)}}},e.hide=function(){if(!this._element.disabled&&!this._element.classList.contains("disabled")&&this._menu.classList.contains("show")){var e=t.getParentFromElement(this._element),n={relatedTarget:this._element};B.trigger(e,"hide.bs.dropdown",n).defaultPrevented||(this._popper&&this._popper.destroy(),this._menu.classList.toggle("show"),this._element.classList.toggle("show"),B.trigger(e,"hidden.bs.dropdown",n))}},e.dispose=function(){L(this._element,"bs.dropdown"),B.off(this._element,".bs.dropdown"),this._element=null,this._menu=null,this._popper&&(this._popper.destroy(),this._popper=null)},e.update=function(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.scheduleUpdate()},e._addEventListeners=function(){var t=this;B.on(this._element,"click.bs.dropdown",(function(e){e.preventDefault(),e.stopPropagation(),t.toggle()}))},e._getConfig=function(t){return t=s({},this.constructor.Default,V.getDataAttributes(this._element),t),m(it,t,this.constructor.DefaultType),t},e._getMenuElement=function(){return z.next(this._element,".dropdown-menu")[0]},e._getPlacement=function(){var t=this._element.parentNode,e="bottom-start";return t.classList.contains("dropup")?e=this._menu.classList.contains("dropdown-menu-right")?"top-end":"top-start":t.classList.contains("dropright")?e="right-start":t.classList.contains("dropleft")?e="left-start":this._menu.classList.contains("dropdown-menu-right")&&(e="bottom-end"),e},e._detectNavbar=function(){return Boolean(this._element.closest(".navbar"))},e._getOffset=function(){var t=this,e={};return"function"==typeof this._config.offset?e.fn=function(e){return e.offsets=s({},e.offsets,t._config.offset(e.offsets,t._element)||{}),e}:e.offset=this._config.offset,e},e._getPopperConfig=function(){var t={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(t.modifiers.applyStyle={enabled:!1}),s({},t,this._config.popperConfig)},t.dropdownInterface=function(e,n){var i=k(e,"bs.dropdown");if(i||(i=new t(e,"object"==typeof n?n:null)),"string"==typeof n){if(void 0===i[n])throw new TypeError('No method named "'+n+'"');i[n]()}},t.jQueryInterface=function(e){return this.each((function(){t.dropdownInterface(this,e)}))},t.clearMenus=function(e){if(!e||2!==e.button&&("keyup"!==e.type||"Tab"===e.key))for(var n=z.find('[data-toggle="dropdown"]'),i=0,o=n.length;i<o;i++){var s=t.getParentFromElement(n[i]),r=k(n[i],"bs.dropdown"),a={relatedTarget:n[i]};if(e&&"click"===e.type&&(a.clickEvent=e),r){var l=r._menu;if(n[i].classList.contains("show"))if(!(e&&("click"===e.type&&/input|textarea/i.test(e.target.tagName)||"keyup"===e.type&&"Tab"===e.key)&&l.contains(e.target)))if(!B.trigger(s,"hide.bs.dropdown",a).defaultPrevented){var c;if("ontouchstart"in document.documentElement)(c=[]).concat.apply(c,document.body.children).forEach((function(t){return B.off(t,"mouseover",null,(function(){}))}));n[i].setAttribute("aria-expanded","false"),r._popper&&r._popper.destroy(),l.classList.remove("show"),n[i].classList.remove("show"),B.trigger(s,"hidden.bs.dropdown",a)}}}},t.getParentFromElement=function(t){return h(t)||t.parentNode},t.dataApiKeydownHandler=function(e){if(!(/input|textarea/i.test(e.target.tagName)?"Space"===e.key||"Escape"!==e.key&&("ArrowDown"!==e.key&&"ArrowUp"!==e.key||e.target.closest(".dropdown-menu")):!ot.test(e.key))&&(e.preventDefault(),e.stopPropagation(),!this.disabled&&!this.classList.contains("disabled"))){var n=t.getParentFromElement(this),i=this.classList.contains("show");if("Escape"===e.key)return(this.matches('[data-toggle="dropdown"]')?this:z.prev(this,'[data-toggle="dropdown"]')[0]).focus(),void t.clearMenus();if(i&&"Space"!==e.key){var o=z.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",n).filter(_);if(o.length){var s=o.indexOf(e.target);"ArrowUp"===e.key&&s>0&&s--,"ArrowDown"===e.key&&s<o.length-1&&s++,o[s=-1===s?0:s].focus()}}else t.clearMenus()}},t.getInstance=function(t){return k(t,"bs.dropdown")},o(t,null,[{key:"VERSION",get:function(){return"5.0.0-alpha3"}},{key:"Default",get:function(){return st}},{key:"DefaultType",get:function(){return rt}}]),t}();B.on(document,"keydown.bs.dropdown.data-api",'[data-toggle="dropdown"]',at.dataApiKeydownHandler),B.on(document,"keydown.bs.dropdown.data-api",".dropdown-menu",at.dataApiKeydownHandler),B.on(document,"click.bs.dropdown.data-api",at.clearMenus),B.on(document,"keyup.bs.dropdown.data-api",at.clearMenus),B.on(document,"click.bs.dropdown.data-api",'[data-toggle="dropdown"]',(function(t){t.preventDefault(),t.stopPropagation(),at.dropdownInterface(this,"toggle")})),B.on(document,"click.bs.dropdown.data-api",".dropdown form",(function(t){return t.stopPropagation()})),w((function(){var t=y();if(t){var e=t.fn[it];t.fn[it]=at.jQueryInterface,t.fn[it].Constructor=at,t.fn[it].noConflict=function(){return t.fn[it]=e,at.jQueryInterface}}}));var lt={backdrop:!0,keyboard:!0,focus:!0,show:!0},ct={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},ut=function(){function t(t,e){this._config=this._getConfig(e),this._element=t,this._dialog=z.findOne(".modal-dialog",t),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0,T(t,"bs.modal",this)}var e=t.prototype;return e.toggle=function(t){return this._isShown?this.hide():this.show(t)},e.show=function(t){var e=this;if(!this._isShown&&!this._isTransitioning){this._element.classList.contains("fade")&&(this._isTransitioning=!0);var n=B.trigger(this._element,"show.bs.modal",{relatedTarget:t});this._isShown||n.defaultPrevented||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),B.on(this._element,"click.dismiss.bs.modal",'[data-dismiss="modal"]',(function(t){return e.hide(t)})),B.on(this._dialog,"mousedown.dismiss.bs.modal",(function(){B.one(e._element,"mouseup.dismiss.bs.modal",(function(t){t.target===e._element&&(e._ignoreBackdropClick=!0)}))})),this._showBackdrop((function(){return e._showElement(t)})))}},e.hide=function(t){var e=this;if((t&&t.preventDefault(),this._isShown&&!this._isTransitioning)&&!B.trigger(this._element,"hide.bs.modal").defaultPrevented){this._isShown=!1;var n=this._element.classList.contains("fade");if(n&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),B.off(document,"focusin.bs.modal"),this._element.classList.remove("show"),B.off(this._element,"click.dismiss.bs.modal"),B.off(this._dialog,"mousedown.dismiss.bs.modal"),n){var i=f(this._element);B.one(this._element,"transitionend",(function(t){return e._hideModal(t)})),p(this._element,i)}else this._hideModal()}},e.dispose=function(){[window,this._element,this._dialog].forEach((function(t){return B.off(t,".bs.modal")})),B.off(document,"focusin.bs.modal"),L(this._element,"bs.modal"),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},e.handleUpdate=function(){this._adjustDialog()},e._getConfig=function(t){return t=s({},lt,t),m("modal",t,ct),t},e._showElement=function(t){var e=this,n=this._element.classList.contains("fade"),i=z.findOne(".modal-body",this._dialog);this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0,i&&(i.scrollTop=0),n&&b(this._element),this._element.classList.add("show"),this._config.focus&&this._enforceFocus();var o=function(){e._config.focus&&e._element.focus(),e._isTransitioning=!1,B.trigger(e._element,"shown.bs.modal",{relatedTarget:t})};if(n){var s=f(this._dialog);B.one(this._dialog,"transitionend",o),p(this._dialog,s)}else o()},e._enforceFocus=function(){var t=this;B.off(document,"focusin.bs.modal"),B.on(document,"focusin.bs.modal",(function(e){document===e.target||t._element===e.target||t._element.contains(e.target)||t._element.focus()}))},e._setEscapeEvent=function(){var t=this;this._isShown?B.on(this._element,"keydown.dismiss.bs.modal",(function(e){t._config.keyboard&&"Escape"===e.key?(e.preventDefault(),t.hide()):t._config.keyboard||"Escape"!==e.key||t._triggerBackdropTransition()})):B.off(this._element,"keydown.dismiss.bs.modal")},e._setResizeEvent=function(){var t=this;this._isShown?B.on(window,"resize.bs.modal",(function(){return t._adjustDialog()})):B.off(window,"resize.bs.modal")},e._hideModal=function(){var t=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop((function(){document.body.classList.remove("modal-open"),t._resetAdjustments(),t._resetScrollbar(),B.trigger(t._element,"hidden.bs.modal")}))},e._removeBackdrop=function(){this._backdrop.parentNode.removeChild(this._backdrop),this._backdrop=null},e._showBackdrop=function(t){var e=this,n=this._element.classList.contains("fade")?"fade":"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",n&&this._backdrop.classList.add(n),document.body.appendChild(this._backdrop),B.on(this._element,"click.dismiss.bs.modal",(function(t){e._ignoreBackdropClick?e._ignoreBackdropClick=!1:t.target===t.currentTarget&&e._triggerBackdropTransition()})),n&&b(this._backdrop),this._backdrop.classList.add("show"),!n)return void t();var i=f(this._backdrop);B.one(this._backdrop,"transitionend",t),p(this._backdrop,i)}else if(!this._isShown&&this._backdrop){this._backdrop.classList.remove("show");var o=function(){e._removeBackdrop(),t()};if(this._element.classList.contains("fade")){var s=f(this._backdrop);B.one(this._backdrop,"transitionend",o),p(this._backdrop,s)}else o()}else t()},e._triggerBackdropTransition=function(){var t=this;if("static"===this._config.backdrop){if(B.trigger(this._element,"hidePrevented.bs.modal").defaultPrevented)return;var e=this._element.scrollHeight>document.documentElement.clientHeight;e||(this._element.style.overflowY="hidden"),this._element.classList.add("modal-static");var n=f(this._dialog);B.off(this._element,"transitionend"),B.one(this._element,"transitionend",(function(){t._element.classList.remove("modal-static"),e||(B.one(t._element,"transitionend",(function(){t._element.style.overflowY=""})),p(t._element,n))})),p(this._element,n),this._element.focus()}else this.hide()},e._adjustDialog=function(){var t=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&t&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!t&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},e._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},e._checkScrollbar=function(){var t=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(t.left+t.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},e._setScrollbar=function(){var t=this;if(this._isBodyOverflowing){z.find(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top").forEach((function(e){var n=e.style.paddingRight,i=window.getComputedStyle(e)["padding-right"];V.setDataAttribute(e,"padding-right",n),e.style.paddingRight=parseFloat(i)+t._scrollbarWidth+"px"})),z.find(".sticky-top").forEach((function(e){var n=e.style.marginRight,i=window.getComputedStyle(e)["margin-right"];V.setDataAttribute(e,"margin-right",n),e.style.marginRight=parseFloat(i)-t._scrollbarWidth+"px"}));var e=document.body.style.paddingRight,n=window.getComputedStyle(document.body)["padding-right"];V.setDataAttribute(document.body,"padding-right",e),document.body.style.paddingRight=parseFloat(n)+this._scrollbarWidth+"px"}document.body.classList.add("modal-open")},e._resetScrollbar=function(){z.find(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top").forEach((function(t){var e=V.getDataAttribute(t,"padding-right");void 0!==e&&(V.removeDataAttribute(t,"padding-right"),t.style.paddingRight=e)})),z.find(".sticky-top").forEach((function(t){var e=V.getDataAttribute(t,"margin-right");void 0!==e&&(V.removeDataAttribute(t,"margin-right"),t.style.marginRight=e)}));var t=V.getDataAttribute(document.body,"padding-right");void 0===t?document.body.style.paddingRight="":(V.removeDataAttribute(document.body,"padding-right"),document.body.style.paddingRight=t)},e._getScrollbarWidth=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e},t.jQueryInterface=function(e,n){return this.each((function(){var i=k(this,"bs.modal"),o=s({},lt,V.getDataAttributes(this),"object"==typeof e&&e?e:{});if(i||(i=new t(this,o)),"string"==typeof e){if(void 0===i[e])throw new TypeError('No method named "'+e+'"');i[e](n)}else o.show&&i.show(n)}))},t.getInstance=function(t){return k(t,"bs.modal")},o(t,null,[{key:"VERSION",get:function(){return"5.0.0-alpha3"}},{key:"Default",get:function(){return lt}}]),t}();B.on(document,"click.bs.modal.data-api",'[data-toggle="modal"]',(function(t){var e=this,n=h(this);"A"!==this.tagName&&"AREA"!==this.tagName||t.preventDefault(),B.one(n,"show.bs.modal",(function(t){t.defaultPrevented||B.one(n,"hidden.bs.modal",(function(){_(e)&&e.focus()}))}));var i=k(n,"bs.modal");if(!i){var o=s({},V.getDataAttributes(n),V.getDataAttributes(this));i=new ut(n,o)}i.show(this)})),w((function(){var t=y();if(t){var e=t.fn.modal;t.fn.modal=ut.jQueryInterface,t.fn.modal.Constructor=ut,t.fn.modal.noConflict=function(){return t.fn.modal=e,ut.jQueryInterface}}}));var ht=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],ft=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi,dt=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i,gt={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]};function pt(t,e,n){var i;if(!t.length)return t;if(n&&"function"==typeof n)return n(t);for(var o=(new window.DOMParser).parseFromString(t,"text/html"),s=Object.keys(e),r=(i=[]).concat.apply(i,o.body.querySelectorAll("*")),a=function(t,n){var i,o=r[t],a=o.nodeName.toLowerCase();if(-1===s.indexOf(a))return o.parentNode.removeChild(o),"continue";var l=(i=[]).concat.apply(i,o.attributes),c=[].concat(e["*"]||[],e[a]||[]);l.forEach((function(t){(function(t,e){var n=t.nodeName.toLowerCase();if(-1!==e.indexOf(n))return-1===ht.indexOf(n)||Boolean(t.nodeValue.match(ft)||t.nodeValue.match(dt));for(var i=e.filter((function(t){return t instanceof RegExp})),o=0,s=i.length;o<s;o++)if(n.match(i[o]))return!0;return!1})(t,c)||o.removeAttribute(t.nodeName)}))},l=0,c=r.length;l<c;l++)a(l);return o.body.innerHTML}var mt="tooltip",_t=new RegExp("(^|\\s)bs-tooltip\\S+","g"),vt=["sanitize","allowList","sanitizeFn"],bt={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",allowList:"object",popperConfig:"(null|object)"},yt={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},wt={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,allowList:gt,popperConfig:null},Et={HIDE:"hide.bs.tooltip",HIDDEN:"hidden.bs.tooltip",SHOW:"show.bs.tooltip",SHOWN:"shown.bs.tooltip",INSERTED:"inserted.bs.tooltip",CLICK:"click.bs.tooltip",FOCUSIN:"focusin.bs.tooltip",FOCUSOUT:"focusout.bs.tooltip",MOUSEENTER:"mouseenter.bs.tooltip",MOUSELEAVE:"mouseleave.bs.tooltip"},Tt=function(){function t(t,e){if(void 0===n.default)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=t,this.config=this._getConfig(e),this.tip=null,this._setListeners(),T(t,this.constructor.DATA_KEY,this)}var e=t.prototype;return e.enable=function(){this._isEnabled=!0},e.disable=function(){this._isEnabled=!1},e.toggleEnabled=function(){this._isEnabled=!this._isEnabled},e.toggle=function(t){if(this._isEnabled)if(t){var e=this.constructor.DATA_KEY,n=k(t.delegateTarget,e);n||(n=new this.constructor(t.delegateTarget,this._getDelegateConfig()),T(t.delegateTarget,e,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)}else{if(this.getTipElement().classList.contains("show"))return void this._leave(null,this);this._enter(null,this)}},e.dispose=function(){clearTimeout(this._timeout),L(this.element,this.constructor.DATA_KEY),B.off(this.element,this.constructor.EVENT_KEY),B.off(this.element.closest(".modal"),"hide.bs.modal",this._hideModalHandler),this.tip&&this.tip.parentNode.removeChild(this.tip),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},e.show=function(){var t=this;if("none"===this.element.style.display)throw new Error("Please use show on visible elements");if(this.isWithContent()&&this._isEnabled){var e=B.trigger(this.element,this.constructor.Event.SHOW),i=function t(e){if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){var n=e.getRootNode();return n instanceof ShadowRoot?n:null}return e instanceof ShadowRoot?e:e.parentNode?t(e.parentNode):null}(this.element),o=null===i?this.element.ownerDocument.documentElement.contains(this.element):i.contains(this.element);if(e.defaultPrevented||!o)return;var s=this.getTipElement(),r=l(this.constructor.NAME);s.setAttribute("id",r),this.element.setAttribute("aria-describedby",r),this.setContent(),this.config.animation&&s.classList.add("fade");var a="function"==typeof this.config.placement?this.config.placement.call(this,s,this.element):this.config.placement,c=this._getAttachment(a);this._addAttachmentClass(c);var u,h=this._getContainer();if(T(s,this.constructor.DATA_KEY,this),this.element.ownerDocument.documentElement.contains(this.tip)||h.appendChild(s),B.trigger(this.element,this.constructor.Event.INSERTED),this._popper=new n.default(this.element,s,this._getPopperConfig(c)),s.classList.add("show"),"ontouchstart"in document.documentElement)(u=[]).concat.apply(u,document.body.children).forEach((function(t){B.on(t,"mouseover",(function(){}))}));var d=function(){t.config.animation&&t._fixTransition();var e=t._hoverState;t._hoverState=null,B.trigger(t.element,t.constructor.Event.SHOWN),"out"===e&&t._leave(null,t)};if(this.tip.classList.contains("fade")){var g=f(this.tip);B.one(this.tip,"transitionend",d),p(this.tip,g)}else d()}},e.hide=function(){var t=this;if(this._popper){var e=this.getTipElement(),n=function(){"show"!==t._hoverState&&e.parentNode&&e.parentNode.removeChild(e),t._cleanTipClass(),t.element.removeAttribute("aria-describedby"),B.trigger(t.element,t.constructor.Event.HIDDEN),t._popper.destroy()};if(!B.trigger(this.element,this.constructor.Event.HIDE).defaultPrevented){var i;if(e.classList.remove("show"),"ontouchstart"in document.documentElement)(i=[]).concat.apply(i,document.body.children).forEach((function(t){return B.off(t,"mouseover",v)}));if(this._activeTrigger.click=!1,this._activeTrigger.focus=!1,this._activeTrigger.hover=!1,this.tip.classList.contains("fade")){var o=f(e);B.one(e,"transitionend",n),p(e,o)}else n();this._hoverState=""}}},e.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},e.isWithContent=function(){return Boolean(this.getTitle())},e.getTipElement=function(){if(this.tip)return this.tip;var t=document.createElement("div");return t.innerHTML=this.config.template,this.tip=t.children[0],this.tip},e.setContent=function(){var t=this.getTipElement();this.setElementContent(z.findOne(".tooltip-inner",t),this.getTitle()),t.classList.remove("fade","show")},e.setElementContent=function(t,e){if(null!==t)return"object"==typeof e&&g(e)?(e.jquery&&(e=e[0]),void(this.config.html?e.parentNode!==t&&(t.innerHTML="",t.appendChild(e)):t.textContent=e.textContent)):void(this.config.html?(this.config.sanitize&&(e=pt(e,this.config.allowList,this.config.sanitizeFn)),t.innerHTML=e):t.textContent=e)},e.getTitle=function(){var t=this.element.getAttribute("data-original-title");return t||(t="function"==typeof this.config.title?this.config.title.call(this.element):this.config.title),t},e._getPopperConfig=function(t){var e=this;return s({},{placement:t,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:"."+this.constructor.NAME+"-arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(t){t.originalPlacement!==t.placement&&e._handlePopperPlacementChange(t)},onUpdate:function(t){return e._handlePopperPlacementChange(t)}},this.config.popperConfig)},e._addAttachmentClass=function(t){this.getTipElement().classList.add("bs-tooltip-"+t)},e._getOffset=function(){var t=this,e={};return"function"==typeof this.config.offset?e.fn=function(e){return e.offsets=s({},e.offsets,t.config.offset(e.offsets,t.element)||{}),e}:e.offset=this.config.offset,e},e._getContainer=function(){return!1===this.config.container?document.body:g(this.config.container)?this.config.container:z.findOne(this.config.container)},e._getAttachment=function(t){return yt[t.toUpperCase()]},e._setListeners=function(){var t=this;this.config.trigger.split(" ").forEach((function(e){if("click"===e)B.on(t.element,t.constructor.Event.CLICK,t.config.selector,(function(e){return t.toggle(e)}));else if("manual"!==e){var n="hover"===e?t.constructor.Event.MOUSEENTER:t.constructor.Event.FOCUSIN,i="hover"===e?t.constructor.Event.MOUSELEAVE:t.constructor.Event.FOCUSOUT;B.on(t.element,n,t.config.selector,(function(e){return t._enter(e)})),B.on(t.element,i,t.config.selector,(function(e){return t._leave(e)}))}})),this._hideModalHandler=function(){t.element&&t.hide()},B.on(this.element.closest(".modal"),"hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=s({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},e._fixTitle=function(){var t=typeof this.element.getAttribute("data-original-title");(this.element.getAttribute("title")||"string"!==t)&&(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},e._enter=function(t,e){var n=this.constructor.DATA_KEY;(e=e||k(t.delegateTarget,n))||(e=new this.constructor(t.delegateTarget,this._getDelegateConfig()),T(t.delegateTarget,n,e)),t&&(e._activeTrigger["focusin"===t.type?"focus":"hover"]=!0),e.getTipElement().classList.contains("show")||"show"===e._hoverState?e._hoverState="show":(clearTimeout(e._timeout),e._hoverState="show",e.config.delay&&e.config.delay.show?e._timeout=setTimeout((function(){"show"===e._hoverState&&e.show()}),e.config.delay.show):e.show())},e._leave=function(t,e){var n=this.constructor.DATA_KEY;(e=e||k(t.delegateTarget,n))||(e=new this.constructor(t.delegateTarget,this._getDelegateConfig()),T(t.delegateTarget,n,e)),t&&(e._activeTrigger["focusout"===t.type?"focus":"hover"]=!1),e._isWithActiveTrigger()||(clearTimeout(e._timeout),e._hoverState="out",e.config.delay&&e.config.delay.hide?e._timeout=setTimeout((function(){"out"===e._hoverState&&e.hide()}),e.config.delay.hide):e.hide())},e._isWithActiveTrigger=function(){for(var t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1},e._getConfig=function(t){var e=V.getDataAttributes(this.element);return Object.keys(e).forEach((function(t){-1!==vt.indexOf(t)&&delete e[t]})),t&&"object"==typeof t.container&&t.container.jquery&&(t.container=t.container[0]),"number"==typeof(t=s({},this.constructor.Default,e,"object"==typeof t&&t?t:{})).delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),m(mt,t,this.constructor.DefaultType),t.sanitize&&(t.template=pt(t.template,t.allowList,t.sanitizeFn)),t},e._getDelegateConfig=function(){var t={};if(this.config)for(var e in this.config)this.constructor.Default[e]!==this.config[e]&&(t[e]=this.config[e]);return t},e._cleanTipClass=function(){var t=this.getTipElement(),e=t.getAttribute("class").match(_t);null!==e&&e.length>0&&e.map((function(t){return t.trim()})).forEach((function(e){return t.classList.remove(e)}))},e._handlePopperPlacementChange=function(t){this.tip=t.instance.popper,this._cleanTipClass(),this._addAttachmentClass(this._getAttachment(t.placement))},e._fixTransition=function(){var t=this.getTipElement(),e=this.config.animation;null===t.getAttribute("x-placement")&&(t.classList.remove("fade"),this.config.animation=!1,this.hide(),this.show(),this.config.animation=e)},t.jQueryInterface=function(e){return this.each((function(){var n=k(this,"bs.tooltip"),i="object"==typeof e&&e;if((n||!/dispose|hide/.test(e))&&(n||(n=new t(this,i)),"string"==typeof e)){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}}))},t.getInstance=function(t){return k(t,"bs.tooltip")},o(t,null,[{key:"VERSION",get:function(){return"5.0.0-alpha3"}},{key:"Default",get:function(){return wt}},{key:"NAME",get:function(){return mt}},{key:"DATA_KEY",get:function(){return"bs.tooltip"}},{key:"Event",get:function(){return Et}},{key:"EVENT_KEY",get:function(){return".bs.tooltip"}},{key:"DefaultType",get:function(){return bt}}]),t}();w((function(){var t=y();if(t){var e=t.fn[mt];t.fn[mt]=Tt.jQueryInterface,t.fn[mt].Constructor=Tt,t.fn[mt].noConflict=function(){return t.fn[mt]=e,Tt.jQueryInterface}}}));var kt="popover",Lt=new RegExp("(^|\\s)bs-popover\\S+","g"),Ct=s({},Tt.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),At=s({},Tt.DefaultType,{content:"(string|element|function)"}),St={HIDE:"hide.bs.popover",HIDDEN:"hidden.bs.popover",SHOW:"show.bs.popover",SHOWN:"shown.bs.popover",INSERTED:"inserted.bs.popover",CLICK:"click.bs.popover",FOCUSIN:"focusin.bs.popover",FOCUSOUT:"focusout.bs.popover",MOUSEENTER:"mouseenter.bs.popover",MOUSELEAVE:"mouseleave.bs.popover"},Dt=function(t){var e,n;function i(){return t.apply(this,arguments)||this}n=t,(e=i).prototype=Object.create(n.prototype),e.prototype.constructor=e,e.__proto__=n;var s=i.prototype;return s.isWithContent=function(){return this.getTitle()||this._getContent()},s.setContent=function(){var t=this.getTipElement();this.setElementContent(z.findOne(".popover-header",t),this.getTitle());var e=this._getContent();"function"==typeof e&&(e=e.call(this.element)),this.setElementContent(z.findOne(".popover-body",t),e),t.classList.remove("fade","show")},s._addAttachmentClass=function(t){this.getTipElement().classList.add("bs-popover-"+t)},s._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},s._cleanTipClass=function(){var t=this.getTipElement(),e=t.getAttribute("class").match(Lt);null!==e&&e.length>0&&e.map((function(t){return t.trim()})).forEach((function(e){return t.classList.remove(e)}))},i.jQueryInterface=function(t){return this.each((function(){var e=k(this,"bs.popover"),n="object"==typeof t?t:null;if((e||!/dispose|hide/.test(t))&&(e||(e=new i(this,n),T(this,"bs.popover",e)),"string"==typeof t)){if(void 0===e[t])throw new TypeError('No method named "'+t+'"');e[t]()}}))},i.getInstance=function(t){return k(t,"bs.popover")},o(i,null,[{key:"VERSION",get:function(){return"5.0.0-alpha3"}},{key:"Default",get:function(){return Ct}},{key:"NAME",get:function(){return kt}},{key:"DATA_KEY",get:function(){return"bs.popover"}},{key:"Event",get:function(){return St}},{key:"EVENT_KEY",get:function(){return".bs.popover"}},{key:"DefaultType",get:function(){return At}}]),i}(Tt);w((function(){var t=y();if(t){var e=t.fn[kt];t.fn[kt]=Dt.jQueryInterface,t.fn[kt].Constructor=Dt,t.fn[kt].noConflict=function(){return t.fn[kt]=e,Dt.jQueryInterface}}}));var It="scrollspy",Ot={offset:10,method:"auto",target:""},Nt={offset:"number",method:"string",target:"(string|element)"},jt=function(){function t(t,e){var n=this;this._element=t,this._scrollElement="BODY"===t.tagName?window:t,this._config=this._getConfig(e),this._selector=this._config.target+" .nav-link, "+this._config.target+" .list-group-item, "+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,B.on(this._scrollElement,"scroll.bs.scrollspy",(function(t){return n._process(t)})),this.refresh(),this._process(),T(t,"bs.scrollspy",this)}var e=t.prototype;return e.refresh=function(){var t=this,e=this._scrollElement===this._scrollElement.window?"offset":"position",n="auto"===this._config.method?e:this._config.method,i="position"===n?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),z.find(this._selector).map((function(t){var e=u(t),o=e?z.findOne(e):null;if(o){var s=o.getBoundingClientRect();if(s.width||s.height)return[V[n](o).top+i,e]}return null})).filter((function(t){return t})).sort((function(t,e){return t[0]-e[0]})).forEach((function(e){t._offsets.push(e[0]),t._targets.push(e[1])}))},e.dispose=function(){L(this._element,"bs.scrollspy"),B.off(this._scrollElement,".bs.scrollspy"),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},e._getConfig=function(t){if("string"!=typeof(t=s({},Ot,"object"==typeof t&&t?t:{})).target&&g(t.target)){var e=t.target.id;e||(e=l(It),t.target.id=e),t.target="#"+e}return m(It,t,Nt),t},e._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},e._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},e._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},e._process=function(){var t=this._getScrollTop()+this._config.offset,e=this._getScrollHeight(),n=this._config.offset+e-this._getOffsetHeight();if(this._scrollHeight!==e&&this.refresh(),t>=n){var i=this._targets[this._targets.length-1];this._activeTarget!==i&&this._activate(i)}else{if(this._activeTarget&&t<this._offsets[0]&&this._offsets[0]>0)return this._activeTarget=null,void this._clear();for(var o=this._offsets.length;o--;){this._activeTarget!==this._targets[o]&&t>=this._offsets[o]&&(void 0===this._offsets[o+1]||t<this._offsets[o+1])&&this._activate(this._targets[o])}}},e._activate=function(t){this._activeTarget=t,this._clear();var e=this._selector.split(",").map((function(e){return e+'[data-target="'+t+'"],'+e+'[href="'+t+'"]'})),n=z.findOne(e.join(","));n.classList.contains("dropdown-item")?(z.findOne(".dropdown-toggle",n.closest(".dropdown")).classList.add("active"),n.classList.add("active")):(n.classList.add("active"),z.parents(n,".nav, .list-group").forEach((function(t){z.prev(t,".nav-link, .list-group-item").forEach((function(t){return t.classList.add("active")})),z.prev(t,".nav-item").forEach((function(t){z.children(t,".nav-link").forEach((function(t){return t.classList.add("active")}))}))}))),B.trigger(this._scrollElement,"activate.bs.scrollspy",{relatedTarget:t})},e._clear=function(){z.find(this._selector).filter((function(t){return t.classList.contains("active")})).forEach((function(t){return t.classList.remove("active")}))},t.jQueryInterface=function(e){return this.each((function(){var n=k(this,"bs.scrollspy");if(n||(n=new t(this,"object"==typeof e&&e)),"string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}}))},t.getInstance=function(t){return k(t,"bs.scrollspy")},o(t,null,[{key:"VERSION",get:function(){return"5.0.0-alpha3"}},{key:"Default",get:function(){return Ot}}]),t}();B.on(window,"load.bs.scrollspy.data-api",(function(){z.find('[data-spy="scroll"]').forEach((function(t){return new jt(t,V.getDataAttributes(t))}))})),w((function(){var t=y();if(t){var e=t.fn[It];t.fn[It]=jt.jQueryInterface,t.fn[It].Constructor=jt,t.fn[It].noConflict=function(){return t.fn[It]=e,jt.jQueryInterface}}}));var xt=function(){function t(t){this._element=t,T(this._element,"bs.tab",this)}var e=t.prototype;return e.show=function(){var t=this;if(!(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&this._element.classList.contains("active")||this._element.classList.contains("disabled"))){var e,n=h(this._element),i=this._element.closest(".nav, .list-group");if(i){var o="UL"===i.nodeName||"OL"===i.nodeName?":scope > li > .active":".active";e=(e=z.find(o,i))[e.length-1]}var s=null;if(e&&(s=B.trigger(e,"hide.bs.tab",{relatedTarget:this._element})),!(B.trigger(this._element,"show.bs.tab",{relatedTarget:e}).defaultPrevented||null!==s&&s.defaultPrevented)){this._activate(this._element,i);var r=function(){B.trigger(e,"hidden.bs.tab",{relatedTarget:t._element}),B.trigger(t._element,"shown.bs.tab",{relatedTarget:e})};n?this._activate(n,n.parentNode,r):r()}}},e.dispose=function(){L(this._element,"bs.tab"),this._element=null},e._activate=function(t,e,n){var i=this,o=(!e||"UL"!==e.nodeName&&"OL"!==e.nodeName?z.children(e,".active"):z.find(":scope > li > .active",e))[0],s=n&&o&&o.classList.contains("fade"),r=function(){return i._transitionComplete(t,o,n)};if(o&&s){var a=f(o);o.classList.remove("show"),B.one(o,"transitionend",r),p(o,a)}else r()},e._transitionComplete=function(t,e,n){if(e){e.classList.remove("active");var i=z.findOne(":scope > .dropdown-menu .active",e.parentNode);i&&i.classList.remove("active"),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!1)}(t.classList.add("active"),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!0),b(t),t.classList.contains("fade")&&t.classList.add("show"),t.parentNode&&t.parentNode.classList.contains("dropdown-menu"))&&(t.closest(".dropdown")&&z.find(".dropdown-toggle").forEach((function(t){return t.classList.add("active")})),t.setAttribute("aria-expanded",!0));n&&n()},t.jQueryInterface=function(e){return this.each((function(){var n=k(this,"bs.tab")||new t(this);if("string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}}))},t.getInstance=function(t){return k(t,"bs.tab")},o(t,null,[{key:"VERSION",get:function(){return"5.0.0-alpha3"}}]),t}();B.on(document,"click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',(function(t){t.preventDefault(),(k(this,"bs.tab")||new xt(this)).show()})),w((function(){var t=y();if(t){var e=t.fn.tab;t.fn.tab=xt.jQueryInterface,t.fn.tab.Constructor=xt,t.fn.tab.noConflict=function(){return t.fn.tab=e,xt.jQueryInterface}}}));var Pt={animation:"boolean",autohide:"boolean",delay:"number"},Rt={animation:!0,autohide:!0,delay:5e3},Ht=function(){function t(t,e){this._element=t,this._config=this._getConfig(e),this._timeout=null,this._setListeners(),T(t,"bs.toast",this)}var e=t.prototype;return e.show=function(){var t=this;if(!B.trigger(this._element,"show.bs.toast").defaultPrevented){this._clearTimeout(),this._config.animation&&this._element.classList.add("fade");var e=function(){t._element.classList.remove("showing"),t._element.classList.add("show"),B.trigger(t._element,"shown.bs.toast"),t._config.autohide&&(t._timeout=setTimeout((function(){t.hide()}),t._config.delay))};if(this._element.classList.remove("hide"),b(this._element),this._element.classList.add("showing"),this._config.animation){var n=f(this._element);B.one(this._element,"transitionend",e),p(this._element,n)}else e()}},e.hide=function(){var t=this;if(this._element.classList.contains("show")&&!B.trigger(this._element,"hide.bs.toast").defaultPrevented){var e=function(){t._element.classList.add("hide"),B.trigger(t._element,"hidden.bs.toast")};if(this._element.classList.remove("show"),this._config.animation){var n=f(this._element);B.one(this._element,"transitionend",e),p(this._element,n)}else e()}},e.dispose=function(){this._clearTimeout(),this._element.classList.contains("show")&&this._element.classList.remove("show"),B.off(this._element,"click.dismiss.bs.toast"),L(this._element,"bs.toast"),this._element=null,this._config=null},e._getConfig=function(t){return t=s({},Rt,V.getDataAttributes(this._element),"object"==typeof t&&t?t:{}),m("toast",t,this.constructor.DefaultType),t},e._setListeners=function(){var t=this;B.on(this._element,"click.dismiss.bs.toast",'[data-dismiss="toast"]',(function(){return t.hide()}))},e._clearTimeout=function(){clearTimeout(this._timeout),this._timeout=null},t.jQueryInterface=function(e){return this.each((function(){var n=k(this,"bs.toast");if(n||(n=new t(this,"object"==typeof e&&e)),"string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e](this)}}))},t.getInstance=function(t){return k(t,"bs.toast")},o(t,null,[{key:"VERSION",get:function(){return"5.0.0-alpha3"}},{key:"DefaultType",get:function(){return Pt}},{key:"Default",get:function(){return Rt}}]),t}();return w((function(){var t=y();if(t){var e=t.fn.toast;t.fn.toast=Ht.jQueryInterface,t.fn.toast.Constructor=Ht,t.fn.toast.noConflict=function(){return t.fn.toast=e,Ht.jQueryInterface}}})),{Alert:U,Button:F,Carousel:Z,Collapse:nt,Dropdown:at,Modal:ut,Popover:Dt,ScrollSpy:jt,Tab:xt,Toast:Ht,Tooltip:Tt}}));
//# sourceMappingURL=bootstrap.min.js.map