<?php

/**
 * Dashboard Inventory Component
 * This file fetches data from the inventory and displays it in the dashboard
 */

// Make sure we have a database connection
if (!isset($conn)) {
    require_once __DIR__ . '/../config/config.php';
}

// No need for use statements here as PDO and PDOException are in global namespace

// Function to get total product count
function getTotalProductCount($conn)
{
    try {
        $query = "SELECT COUNT(*) as total FROM inventory WHERE status = 'approved'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['total'] ?? 0;
        }
        return 0;
    } catch (PDOException $e) {
        error_log("Error in getTotalProductCount: " . $e->getMessage());
        return 0;
    }
}

// Function to get low stock products (less than 10 items)
function getLowStockProducts($conn, $threshold = 10)
{
    try {
        $query = "SELECT COUNT(*) as count FROM inventory WHERE stocks < :threshold AND stocks > 0 AND status = 'approved'";
        $stmt = $conn->prepare($query);
        $stmt->execute(['threshold' => $threshold]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error in getLowStockProducts: " . $e->getMessage());
        return 0;
    }
}

// Function to get out of stock products
function getOutOfStockProducts($conn)
{
    try {
        $query = "SELECT COUNT(*) as count FROM inventory WHERE stocks = 0 AND status = 'approved'";
        $stmt = $conn->query($query);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error in getOutOfStockProducts: " . $e->getMessage());
        return 0;
    }
}

// Function to get total inventory value
function getTotalInventoryValue($conn)
{
    try {
        $query = "SELECT SUM(price * stocks) as total_value FROM inventory WHERE stocks > 0 AND status = 'approved'";
        $stmt = $conn->query($query);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total_value'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error in getTotalInventoryValue: " . $e->getMessage());
        return 0;
    }
}

// Function to get products by category/brand
function getProductsByCategory($conn)
{
    try {
        $query = "SELECT brand_name, COUNT(*) as count FROM inventory WHERE status = 'approved' GROUP BY brand_name ORDER BY count DESC LIMIT 5";
        $stmt = $conn->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error in getProductsByCategory: " . $e->getMessage());
        return [];
    }
}

// Function to get recently added products
function getRecentProducts($conn, $limit = 5)
{
    try {
        $query = "SELECT prod_name, price, created_at FROM inventory WHERE status = 'approved' ORDER BY created_at DESC LIMIT :limit";
        $stmt = $conn->prepare($query);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error in getRecentProducts: " . $e->getMessage());
        return [];
    }
}

// Function to get expiring products (within 30 days)
function getExpiringProducts($conn, $days = 30)
{
    try {
        $today = date('Y-m-d');
        $expiry_date = date('Y-m-d', strtotime("+{$days} days"));

        $query = "SELECT prod_name, brand_name, expiry_date, stocks FROM inventory
                WHERE expiry_date BETWEEN :today AND :expiry_date
                AND status = 'approved'
                ORDER BY expiry_date ASC
                LIMIT 10";

        $stmt = $conn->prepare($query);
        $stmt->execute([
            'today' => $today,
            'expiry_date' => $expiry_date
        ]);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error in getExpiringProducts: " . $e->getMessage());
        return [];
    }
}

// Get all the data
$total_products = getTotalProductCount($conn);
$low_stock_products = getLowStockProducts($conn);
$out_of_stock_products = getOutOfStockProducts($conn);
$total_inventory_value = getTotalInventoryValue($conn);
$products_by_category = getProductsByCategory($conn);
$recent_products = getRecentProducts($conn);
$expiring_products = getExpiringProducts($conn);
?>

<style>
    .dashboard-card {
        height: 100%;
        min-height: 150px;
    }

    .dashboard-card .card-body {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        height: 100%;
    }

    .dashboard-card .card-title {
        margin-bottom: 15px;
        font-size: 1.1rem;
    }

    .dashboard-card .card-text {
        font-size: 1.8rem;
        font-weight: bold;
        margin: 0;
    }
</style>

<div class="row mb-4">
    <!-- Total Products Widget -->
    <div class="col-md-3">
        <div class="card dashboard-card text-white" style="background-color: #f15b31;">
            <div class="card-body">
                <h5 class="card-title">Total Products</h5>
                <h2 class="card-text"><?php echo $total_products; ?></h2>
            </div>
        </div>
    </div>

    <!-- Low Stock Widget -->
    <div class="col-md-3">
        <div class="card dashboard-card text-white" style="background-color: #f15b31;">
            <div class="card-body">
                <h5 class="card-title">Low Stock</h5>
                <h2 class="card-text"><?php echo $low_stock_products; ?></h2>
            </div>
        </div>
    </div>

    <!-- Out of Stock Widget -->
    <div class="col-md-3">
        <div class="card dashboard-card text-white" style="background-color: #f15b31;">
            <div class="card-body">
                <h5 class="card-title">Out of Stock</h5>
                <h2 class="card-text"><?php echo $out_of_stock_products; ?></h2>
            </div>
        </div>
    </div>

    <!-- Total Inventory Value Widget -->
    <div class="col-md-3">
        <div class="card dashboard-card text-white" style="background-color: #f15b31;">
            <div class="card-body">
                <h5 class="card-title">Total Value</h5>
                <h2 class="card-text">₱<?php echo number_format($total_inventory_value, 2); ?></h2>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- Products by Brand Chart -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                Products by Brand
            </div>
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Brand</th>
                            <th>Count</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products_by_category as $category): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($category['brand_name'] ?? ''); ?></td>
                                <td><?php echo $category['count'] ?? 0; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Recent Products -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                Recently Added Products
            </div>
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Price</th>
                            <th>Date Added</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_products as $product): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($product['prod_name'] ?? ''); ?></td>
                                <td>₱<?php echo number_format($product['price'] ?? 0, 2); ?></td>
                                <td><?php echo date('M d, Y', strtotime($product['created_at'] ?? '')); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Expiring Products -->
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                Products Expiring Soon (Next 30 Days)
            </div>
            <div class="card-body">
                <?php if (count($expiring_products) > 0): ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Brand</th>
                                <th>Expiration Date</th>
                                <th>Stock</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($expiring_products as $product): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($product['prod_name'] ?? ''); ?></td>
                                    <td><?php echo htmlspecialchars($product['brand_name'] ?? ''); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($product['expiry_date'] ?? '')); ?></td>
                                    <td><?php echo $product['stocks'] ?? 0; ?></td>
                                    <td>
                                        <a href="../inventory/inventory.php" class="btn btn-sm btn-primary">View</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p class="text-center">No products expiring soon.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add a link to the full inventory -->
<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="../inventory/inventory.php" class="btn btn-primary">View Full Inventory</a>
    </div>
</div>