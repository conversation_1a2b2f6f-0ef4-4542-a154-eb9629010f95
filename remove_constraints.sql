-- Drop and recreate the inventory table without constraints
DROP TABLE IF EXISTS inventory_temp;

-- Create a new table without constraints
CREATE TABLE inventory_temp (
    id INT AUTO_INCREMENT PRIMARY KEY,
    prod_image VARCHAR(255),
    prod_name VARCHAR(100) NOT NULL,
    brand_name VARCHAR(100),
    price DECIMAL(10,2) NOT NULL,
    prod_measure VARCHAR(50),
    pack_type VARCHAR(50),
    expiry_date DATETIME,
    delivered_date DATETIME,
    country VARCHAR(100),
    batch_code VARCHAR(50),
    stocks DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'approved',
    order_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Copy data from old table to new table (if the old table exists)
INSERT IGNORE INTO inventory_temp (
    id, prod_image, prod_name, brand_name, price, prod_measure, 
    pack_type, expiry_date, delivered_date, country, batch_code, 
    stocks, status, order_id, created_at
) 
SELECT 
    id, prod_image, prod_name, brand_name, price, prod_measure, 
    pack_type, expiry_date, delivered_date, country, batch_code, 
    stocks, status, order_id, created_at
FROM inventory;

-- Drop the old table
DROP TABLE IF EXISTS inventory;

-- Rename the new table to the original name
RENAME TABLE inventory_temp TO inventory;
