<?php
session_start();
require_once '../../config/config.php';
require_once '../../includes/export_functions.php';

// Check if user is logged in and has logistics or admin role
if (
    !isset($_SESSION['user_id']) ||
    (isset($_SESSION['role']) && $_SESSION['role'] !== 'logistics' && $_SESSION['role'] !== 'admin') &&
    (isset($_SESSION['user_role']) && $_SESSION['user_role'] !== 'logistics' && $_SESSION['user_role'] !== 'admin')
) {
    header("Location: ../../auth/login.php");
    exit();
}

// Handle driver profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $driver_id = $_SESSION['user_id'];
    $vehicle_type = $_POST['vehicle_type'];
    $license_number = $_POST['license_number'];
    $phone = $_POST['phone'];
    $availability = $_POST['availability'];

    // Check if this driver already has a profile
    $check_sql = "SELECT * FROM drivers WHERE user_id = ?";
    $stmt = $conn->prepare($check_sql);
    $stmt->execute([$driver_id]);
    $existing_profile = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($existing_profile) {
        // Update existing profile
        $sql = "UPDATE drivers SET
                vehicle_type = ?,
                license_number = ?,
                phone = ?,
                availability = ?
                WHERE user_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$vehicle_type, $license_number, $phone, $availability, $driver_id]);
    } else {
        // Create new profile
        $sql = "INSERT INTO drivers (user_id, vehicle_type, license_number, phone, availability)
                VALUES (?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$driver_id, $vehicle_type, $license_number, $phone, $availability]);

        // Update user role to logistics if needed
        $update_role_sql = "UPDATE users SET user_role = 'logistics' WHERE id = ?";
        $stmt = $conn->prepare($update_role_sql);
        $stmt->execute([$driver_id]);
    }

    // Refresh the page to show updated profile
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// Handle order pickup
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['pickup_order'])) {
    $order_id = $_POST['order_id'];
    $driver_id = $_SESSION['user_id'];
    $pickup_time = date('Y-m-d H:i:s');

    $sql = "UPDATE order_requests SET
            status = 'in_transit',
            driver_id = ?,
            pickup_time = ?
            WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$driver_id, $pickup_time, $order_id]);
}

// Handle order delivery
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['deliver_order'])) {
    $order_id = $_POST['order_id'];
    $delivery_time = date('Y-m-d H:i:s');
    $delivery_notes = $_POST['delivery_notes'];

    $sql = "UPDATE order_requests SET
            status = 'delivered',
            delivery_time = ?,
            delivery_notes = ?
            WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$delivery_time, $delivery_notes, $order_id]);
}

// Get all available driver profiles
$all_drivers_sql = "SELECT d.*, CONCAT(u.first_name, ' ', u.last_name) as driver_name
                   FROM drivers d
                   JOIN users u ON d.user_id = u.id
                   ORDER BY driver_name";
$all_drivers = $conn->query($all_drivers_sql)->fetchAll(PDO::FETCH_ASSOC);

// Get current driver profile
$profile_sql = "SELECT * FROM drivers WHERE user_id = ?";
$stmt = $conn->prepare($profile_sql);
$stmt->execute([$_SESSION['user_id']]);
$profile = $stmt->fetch(PDO::FETCH_ASSOC);

// Initialize profile with default values if not found
if (!$profile) {
    $profile = [
        'id' => 0,
        'user_id' => $_SESSION['user_id'],
        'vehicle_type' => '',
        'license_number' => '',
        'phone' => '',
        'availability' => 'available'
    ];
}

// Get available orders - modified to handle orders from approved table
$orders_sql = "SELECT
    orq.*,
    COALESCE(p.prod_name, a.prod_name) as prod_name,
    COALESCE(orq.unit_price, p.price, a.price) as price,
    CONCAT(u.first_name, ' ', u.last_name) as customer_name,
    u.phone_number as customer_phone,
    orq.delivery_address,
    orq.delivery_from,
    orq.total_price
FROM order_requests orq
JOIN users u ON orq.user_id = u.id
LEFT JOIN products p ON orq.product_id = p.id AND p.id IS NOT NULL
LEFT JOIN approved a ON (a.id = orq.product_id OR (p.prod_name = a.prod_name AND p.id IS NOT NULL))
WHERE orq.status = 'processing'
ORDER BY orq.id DESC";
$orders = $conn->query($orders_sql)->fetchAll(PDO::FETCH_ASSOC);

// Get driver's active deliveries - modified to handle orders from approved table
$active_sql = "SELECT
    orq.*,
    COALESCE(p.prod_name, a.prod_name) as prod_name,
    COALESCE(orq.unit_price, p.price, a.price) as price,
    CONCAT(u.first_name, ' ', u.last_name) as customer_name,
    u.phone_number as customer_phone,
    orq.delivery_address,
    orq.delivery_from,
    orq.total_price
FROM order_requests orq
JOIN users u ON orq.user_id = u.id
LEFT JOIN products p ON orq.product_id = p.id AND p.id IS NOT NULL
LEFT JOIN approved a ON (a.id = orq.product_id OR (p.prod_name = a.prod_name AND p.id IS NOT NULL))
WHERE orq.driver_id = ? AND orq.status = 'in_transit'
ORDER BY orq.pickup_time ASC";
$stmt = $conn->prepare($active_sql);
$stmt->execute([$_SESSION['user_id']]);
$active_deliveries = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logistics Management</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        .delivery-card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        #map {
            height: 400px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .status-badge {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 0.9em;
        }

        .status-processing {
            background-color: #17a2b8;
            color: white;
        }

        .status-in-transit {
            background-color: #ffc107;
            color: black;
        }

        .status-delivered {
            background-color: #28a745;
            color: white;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Logistics Management</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../logistics/track_deliveries.php">Trach Deliveries</a>
                <a class="nav-link" href="/index.html">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Driver Profile -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Driver Profile</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="update_profile" value="1">

                            <?php if (count($all_drivers) > 0): ?>
                                <div class="mb-3">
                                    <label><strong>Select Existing Driver Profile</strong></label>
                                    <select id="driver_profile_selector" class="form-control mb-3">
                                        <option value="">-- Select a profile --</option>
                                        <?php foreach ($all_drivers as $driver): ?>
                                            <option value="<?php echo $driver['id']; ?>"
                                                data-vehicle="<?php echo htmlspecialchars($driver['vehicle_type']); ?>"
                                                data-license="<?php echo htmlspecialchars($driver['license_number']); ?>"
                                                data-phone="<?php echo htmlspecialchars($driver['phone']); ?>"
                                                data-availability="<?php echo htmlspecialchars($driver['availability']); ?>"
                                                <?php echo $driver['user_id'] == $_SESSION['user_id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($driver['driver_name']); ?> -
                                                <?php echo ucfirst(htmlspecialchars($driver['vehicle_type'])); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <hr>
                            <?php endif; ?>

                            <div class="mb-3">
                                <label>Vehicle Type</label>
                                <select name="vehicle_type" id="vehicle_type" class="form-control" required>
                                    <option value="motorcycle" <?php echo isset($profile['vehicle_type']) && $profile['vehicle_type'] === 'motorcycle' ? 'selected' : ''; ?>>Motorcycle</option>
                                    <option value="car" <?php echo isset($profile['vehicle_type']) && $profile['vehicle_type'] === 'car' ? 'selected' : ''; ?>>Car</option>
                                    <option value="van" <?php echo isset($profile['vehicle_type']) && $profile['vehicle_type'] === 'van' ? 'selected' : ''; ?>>Van</option>
                                    <option value="truck" <?php echo isset($profile['vehicle_type']) && $profile['vehicle_type'] === 'truck' ? 'selected' : ''; ?>>Truck</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label>License Number</label>
                                <input type="text" name="license_number" id="license_number" class="form-control" value="<?php echo isset($profile['license_number']) ? htmlspecialchars($profile['license_number']) : ''; ?>" required>
                            </div>
                            <div class="mb-3">
                                <label>Phone</label>
                                <input type="tel" name="phone" id="phone" class="form-control" value="<?php echo isset($profile['phone']) ? htmlspecialchars($profile['phone']) : ''; ?>" required>
                            </div>
                            <div class="mb-3">
                                <label>Availability</label>
                                <select name="availability" id="availability" class="form-control" required>
                                    <option value="available" <?php echo isset($profile['availability']) && $profile['availability'] === 'available' ? 'selected' : ''; ?>>Available</option>
                                    <option value="busy" <?php echo isset($profile['availability']) && $profile['availability'] === 'busy' ? 'selected' : ''; ?>>Busy</option>
                                    <option value="offline" <?php echo isset($profile['availability']) && $profile['availability'] === 'offline' ? 'selected' : ''; ?>>Offline</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">Update Profile</button>
                        </form>

                        <script>
                            // JavaScript to populate form fields when a driver profile is selected
                            document.getElementById('driver_profile_selector').addEventListener('change', function() {
                                const selectedOption = this.options[this.selectedIndex];

                                if (selectedOption.value) {
                                    // Populate form fields with selected driver's data
                                    document.getElementById('vehicle_type').value = selectedOption.dataset.vehicle;
                                    document.getElementById('license_number').value = selectedOption.dataset.license;
                                    document.getElementById('phone').value = selectedOption.dataset.phone;
                                    document.getElementById('availability').value = selectedOption.dataset.availability;
                                } else {
                                    // Clear form if "Select a profile" is chosen
                                    document.getElementById('vehicle_type').value = 'motorcycle';
                                    document.getElementById('license_number').value = '';
                                    document.getElementById('phone').value = '';
                                    document.getElementById('availability').value = 'available';
                                }
                            });
                        </script>
                    </div>
                </div>
            </div>

            <!-- Available Orders -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Available Orders</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($orders as $order): ?>
                            <div class="delivery-card p-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6><?php echo htmlspecialchars($order['prod_name']); ?></h6>
                                        <p class="mb-1">
                                            <strong>Customer:</strong> <?php echo htmlspecialchars($order['customer_name']); ?><br>
                                            <strong>Phone:</strong> <?php echo htmlspecialchars($order['customer_phone']); ?><br>
                                            <strong>From:</strong> <?php echo htmlspecialchars($order['delivery_from']); ?><br>
                                            <strong>To:</strong> <?php echo htmlspecialchars($order['delivery_address']); ?><br>
                                            <strong>Quantity:</strong> <?php echo $order['quantity']; ?><br>
                                            <strong>Unit Price:</strong> ₱<?php echo number_format($order['price'], 2); ?><br>
                                            <strong>Total:</strong> ₱<?php echo isset($order['total_price']) ? number_format($order['total_price'], 2) : number_format($order['quantity'] * $order['price'], 2); ?>
                                        </p>
                                        <a href="track_delivery_new.php?order_id=<?php echo $order['id']; ?>" class="btn btn-info btn-sm">
                                            <i class="fas fa-truck"></i> Track
                                        </a>
                                    </div>
                                    <form method="POST" class="ms-3">
                                        <input type="hidden" name="pickup_order" value="1">
                                        <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                        <button type="submit" class="btn btn-primary">Accept Delivery</button>
                                    </form>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Active Deliveries -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Active Deliveries</h5>
                    </div>
                    <div class="card-body">
                        <div id="map"></div>
                        <?php foreach ($active_deliveries as $delivery): ?>
                            <div class="delivery-card p-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6><?php echo htmlspecialchars($delivery['prod_name']); ?></h6>
                                        <p class="mb-1">
                                            <strong>Customer:</strong> <?php echo htmlspecialchars($delivery['customer_name']); ?><br>
                                            <strong>Phone:</strong> <?php echo htmlspecialchars($delivery['customer_phone']); ?><br>
                                            <strong>From:</strong> <?php echo htmlspecialchars($delivery['delivery_from']); ?><br>
                                            <strong>To:</strong> <?php echo htmlspecialchars($delivery['delivery_address']); ?><br>
                                            <strong>Quantity:</strong> <?php echo $delivery['quantity']; ?><br>
                                            <strong>Total:</strong>₱<?php echo isset($delivery['total_price']) ? number_format($delivery['total_price'], 2) : number_format($delivery['quantity'] * $delivery['price'], 2); ?><br>
                                            <strong>Pickup Time:</strong><?php echo date('M d, Y H:i', strtotime($delivery['pickup_time'])); ?> <br>
                                        </p>
                                        <a href="track_delivery_new.php?order_id=<?php echo $delivery['id']; ?>" class="btn btn-info btn-sm">
                                            <i class="fas fa-truck"></i> Track
                                        </a>
                                    </div>
                                    <form method="POST" class="ms-3">
                                        <input type="hidden" name="deliver_order" value="1">
                                        <input type="hidden" name="order_id" value="<?php echo $delivery['id']; ?>">
                                        <div class="mb-2">
                                            <textarea name="delivery_notes" class="form-control" placeholder="Delivery notes..." rows="2"></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-success">Mark as Delivered</button>
                                    </form>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        <a href="../admin/admin_dashboard.php" class="btn btn-secondary mt-3" style="background-color: #f15b31; color: white;">Back to Dashboard</a>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // Initialize map centered on the Philippines
        const map = L.map('map').setView([12.8797, 121.7740], 6); // Philippines coordinates

        // OpenStreetMap Standard
        const osmStandard = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 19
        }).addTo(map);

        // Carto DB Voyager (road-focused, Google Maps-like)
        const cartoVoyager = L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
            subdomains: 'abcd',
            maxZoom: 19
        });

        // Carto DB Positron (light theme with roads)
        const cartoPositron = L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
            subdomains: 'abcd',
            maxZoom: 19
        });

        // OpenStreetMap HOT (Humanitarian style - good for roads)
        const osmHOT = L.tileLayer('https://{s}.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, Tiles style by <a href="https://www.hotosm.org/" target="_blank">Humanitarian OpenStreetMap Team</a> hosted by <a href="https://openstreetmap.fr/" target="_blank">OpenStreetMap France</a>',
            maxZoom: 19
        });

        // Layer control
        const baseLayers = {
            "OpenStreetMap": osmStandard,
            "Carto Voyager (Google-like)": cartoVoyager,
            "Carto Light": cartoPositron,
            "OSM Humanitarian": osmHOT
        };

        L.control.layers(baseLayers, {}).addTo(map);

        // OpenRouteService API key
        const orsApiKey = '5b3ce3597851110001cf6248001793d85d6646c392987d47f8b6a2f7';

        // Define a central hub location (e.g., your warehouse or office)
        // 3708 N Guevarra St., Zone 1 Dasmariñas, Cavite Philippines 4114
        const hubLocation = [14.3294, 120.9367]; // Dasmariñas, Cavite coordinates

        // Add a marker for the hub
        const hubMarker = L.marker(hubLocation)
            .bindPopup('<strong>Distribution Hub</strong><br>3708 N Guevarra St., Zone 1 Dasmariñas, Cavite Philippines 4114')
            .addTo(map);

        // Center map on hub location with closer zoom
        map.setView(hubLocation, 13);

        // Variables for custom destination
        let customDestinationMarker = null;
        let currentRoute = null;
        let isNavigating = false;
        let currentPositionMarker = null;
        let navigationInfo = L.control({
            position: 'bottomleft'
        });

        // Create a custom control for navigation
        const navigationControl = L.control({
            position: 'topleft'
        });
        navigationControl.onAdd = function(map) {
            const div = L.DomUtil.create('div', 'navigation-control');
            div.innerHTML = `
                <div class="leaflet-control leaflet-bar">
                    <a href="#" id="start-navigation" title="Start Navigation" style="background-color: #28a745; color: white; display: none;">
                        <i class="fas fa-play"></i>
                    </a>
                    <a href="#" id="stop-navigation" title="Stop Navigation" style="background-color: #dc3545; color: white; display: none;">
                        <i class="fas fa-stop"></i>
                    </a>
                    <a href="#" id="clear-route" title="Clear Route" style="background-color: #6c757d; color: white; display: none;">
                        <i class="fas fa-trash"></i>
                    </a>
                </div>
                <div id="click-instruction" class="leaflet-control" style="background-color: rgba(255,255,255,0.8); padding: 8px; border-radius: 4px; margin-top: 10px; font-weight: bold;">
                    Click anywhere on the map to set a destination
                </div>
            `;
            return div;
        };
        navigationControl.addTo(map);

        // Create navigation info panel
        navigationInfo.onAdd = function(map) {
            const div = L.DomUtil.create('div', 'navigation-info');
            div.innerHTML = `
                <div class="card" style="display: none;" id="navigation-card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">Navigation</h6>
                    </div>
                    <div class="card-body">
                        <div id="distance-info"></div>
                        <div id="duration-info"></div>
                        <div id="next-instruction" class="mt-2"></div>
                    </div>
                </div>
            `;
            return div;
        };
        navigationInfo.addTo(map);

        // Function to get route from OpenRouteService
        async function getRoute(start, end) {
            try {
                const url = `https://api.openrouteservice.org/v2/directions/driving-car?api_key=${orsApiKey}&start=${start[1]},${start[0]}&end=${end[1]},${end[0]}`;

                const response = await fetch(url);
                const data = await response.json();

                if (data.features && data.features.length > 0) {
                    // Extract the route coordinates
                    const routeCoordinates = data.features[0].geometry.coordinates;

                    // Convert coordinates from [lng, lat] to [lat, lng] for Leaflet
                    const latLngs = routeCoordinates.map(coord => [coord[1], coord[0]]);

                    // Create a polyline for the route
                    const routeLine = L.polyline(latLngs, {
                        color: 'blue',
                        weight: 4,
                        opacity: 0.7
                    }).addTo(map);

                    // Get route distance and duration
                    const distance = (data.features[0].properties.summary.distance / 1000).toFixed(2); // km
                    const duration = Math.round(data.features[0].properties.summary.duration / 60); // minutes

                    // Add route info to popup
                    routeLine.bindPopup(`Distance: ${distance} km<br>Estimated time: ${duration} min`);

                    // Show navigation controls
                    document.getElementById('start-navigation').style.display = 'block';
                    document.getElementById('clear-route').style.display = 'block';

                    // Store route data for navigation
                    routeLine.routeData = {
                        distance: distance,
                        duration: duration,
                        coordinates: latLngs,
                        steps: data.features[0].properties.segments[0].steps
                    };

                    return routeLine;
                }
            } catch (error) {
                console.error('Error fetching route:', error);
                return null;
            }
        }

        // Handle map clicks to set destination
        map.on('click', async function(e) {
            // Only allow setting destination if not currently navigating
            if (!isNavigating) {
                // Remove previous destination marker and route
                if (customDestinationMarker) {
                    map.removeLayer(customDestinationMarker);
                }
                if (currentRoute) {
                    map.removeLayer(currentRoute);
                }

                // Create new destination marker
                const clickedLocation = [e.latlng.lat, e.latlng.lng];
                customDestinationMarker = L.marker(clickedLocation, {
                    icon: L.divIcon({
                        className: 'custom-div-icon',
                        html: '<div style="background-color: #dc3545; width: 15px; height: 15px; border-radius: 50%; border: 2px solid white;"></div>',
                        iconSize: [15, 15],
                        iconAnchor: [7, 7]
                    })
                }).addTo(map);

                // Get current position (for demo, use hub location)
                const currentPosition = hubLocation;

                // Get route from current position to clicked location
                currentRoute = await getRoute(currentPosition, clickedLocation);

                // Update UI
                if (currentRoute && currentRoute.routeData) {
                    document.getElementById('distance-info').innerHTML = `<strong>Distance:</strong> ${currentRoute.routeData.distance} km`;
                    document.getElementById('duration-info').innerHTML = `<strong>ETA:</strong> ${currentRoute.routeData.duration} min`;

                    // Show first instruction if available
                    if (currentRoute.routeData.steps && currentRoute.routeData.steps.length > 0) {
                        const firstStep = currentRoute.routeData.steps[0];
                        document.getElementById('next-instruction').innerHTML = `<strong>Next:</strong> ${firstStep.instruction}`;
                    }
                }
            }
        });

        // Start navigation button
        document.getElementById('start-navigation').addEventListener('click', function(e) {
            e.preventDefault();

            if (currentRoute) {
                isNavigating = true;

                // Show navigation card
                document.getElementById('navigation-card').style.display = 'block';

                // Hide start button, show stop button
                document.getElementById('start-navigation').style.display = 'none';
                document.getElementById('stop-navigation').style.display = 'block';
                document.getElementById('click-instruction').style.display = 'none';

                // Create current position marker (starting at hub for demo)
                if (currentPositionMarker) {
                    map.removeLayer(currentPositionMarker);
                }

                currentPositionMarker = L.marker(hubLocation, {
                    icon: L.divIcon({
                        className: 'current-position-icon',
                        html: '<div style="background-color: #007bff; width: 15px; height: 15px; border-radius: 50%; border: 2px solid white;"></div>',
                        iconSize: [15, 15],
                        iconAnchor: [7, 7]
                    })
                }).addTo(map);

                // In a real app, you would use geolocation to track the driver's position
                // For this demo, we'll simulate movement along the route
                simulateNavigation();
            }
        });

        // Stop navigation button
        document.getElementById('stop-navigation').addEventListener('click', function(e) {
            e.preventDefault();

            isNavigating = false;

            // Hide navigation card
            document.getElementById('navigation-card').style.display = 'none';

            // Show start button, hide stop button
            document.getElementById('start-navigation').style.display = 'block';
            document.getElementById('stop-navigation').style.display = 'none';
            document.getElementById('click-instruction').style.display = 'block';

            // Remove current position marker
            if (currentPositionMarker) {
                map.removeLayer(currentPositionMarker);
                currentPositionMarker = null;
            }
        });

        // Clear route button
        document.getElementById('clear-route').addEventListener('click', function(e) {
            e.preventDefault();

            // Stop navigation if active
            isNavigating = false;

            // Remove markers and route
            if (customDestinationMarker) {
                map.removeLayer(customDestinationMarker);
                customDestinationMarker = null;
            }

            if (currentRoute) {
                map.removeLayer(currentRoute);
                currentRoute = null;
            }

            if (currentPositionMarker) {
                map.removeLayer(currentPositionMarker);
                currentPositionMarker = null;
            }

            // Hide all navigation controls
            document.getElementById('start-navigation').style.display = 'none';
            document.getElementById('stop-navigation').style.display = 'none';
            document.getElementById('clear-route').style.display = 'none';
            document.getElementById('navigation-card').style.display = 'none';
            document.getElementById('click-instruction').style.display = 'block';
        });

        // Function to simulate navigation (for demo purposes)
        function simulateNavigation() {
            // In a real app, you would use the device's geolocation
            // For this demo, we'll just show a static view of the route

            // Zoom to show the entire route
            if (currentRoute) {
                const routeCoords = currentRoute.getLatLngs();
                const bounds = L.latLngBounds(routeCoords);
                map.fitBounds(bounds, {
                    padding: [50, 50]
                });
            }
        }

        // Add markers and routes for active deliveries
        <?php
        foreach ($active_deliveries as $delivery):
            // Generate random coordinates within the Philippines
            // Philippines latitude range: approximately 4.5° to 21° N
            // Philippines longitude range: approximately 116° to 127° E
            $lat = rand(45, 210) / 10; // 4.5 to 21.0
            $lng = rand(1160, 1270) / 10; // 116.0 to 127.0
        ?>
            // Add delivery marker
            const deliveryLocation<?php echo $delivery['id']; ?> = [<?php echo $lat; ?>, <?php echo $lng; ?>];

            const marker<?php echo $delivery['id']; ?> = L.marker(deliveryLocation<?php echo $delivery['id']; ?>)
                .bindPopup(`
                    <strong><?php echo htmlspecialchars($delivery['customer_name']); ?></strong><br>
                    <?php echo htmlspecialchars($delivery['delivery_address']); ?><br>
                    Phone: <?php echo htmlspecialchars($delivery['customer_phone']); ?><br>
                    <button class="btn btn-sm btn-primary mt-2" onclick="showRoute<?php echo $delivery['id']; ?>()">Show Route</button>
                `)
                .addTo(map);

            // Function to show route for this delivery
            let route<?php echo $delivery['id']; ?> = null;
            async function showRoute<?php echo $delivery['id']; ?>() {
                // Remove previous route if exists
                if (route<?php echo $delivery['id']; ?>) {
                    map.removeLayer(route<?php echo $delivery['id']; ?>);
                }

                // Get and display new route
                route<?php echo $delivery['id']; ?> = await getRoute(hubLocation, deliveryLocation<?php echo $delivery['id']; ?>);

                // Fit map to show the entire route
                if (route<?php echo $delivery['id']; ?>) {
                    const bounds = L.latLngBounds([hubLocation, deliveryLocation<?php echo $delivery['id']; ?>]);
                    map.fitBounds(bounds, {
                        padding: [50, 50]
                    });
                }
            }
        <?php endforeach; ?>
    </script>
</body>

</html>