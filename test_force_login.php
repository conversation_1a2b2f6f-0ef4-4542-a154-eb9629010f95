<?php
// Test script to check if the force parameter works correctly

// Function to make a GET request with cookies and follow redirects
function get_request($url, $cookies = []) {
    $options = [
        'http' => [
            'method' => 'GET'
        ]
    ];
    
    // Add cookies if provided
    if (!empty($cookies)) {
        $cookie_string = '';
        foreach ($cookies as $key => $value) {
            $cookie_string .= "$key=$value; ";
        }
        $options['http']['header'] = "Cookie: " . trim($cookie_string) . "\r\n";
    }
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    // Get response headers
    $response_headers = $http_response_header ?? [];
    
    // Check for redirect
    $redirect_url = null;
    foreach ($response_headers as $header) {
        if (preg_match('/^Location: (.*)$/i', $header, $matches)) {
            $redirect_url = trim($matches[1]);
            break;
        }
    }
    
    // Extract cookies from response
    $cookies_out = [];
    foreach ($response_headers as $header) {
        if (preg_match('/^Set-Cookie: (.*?)=(.*?);/i', $header, $matches)) {
            $cookies_out[$matches[1]] = $matches[2];
        }
    }
    
    return [
        'content' => $result,
        'headers' => $response_headers,
        'redirect' => $redirect_url,
        'cookies' => $cookies_out
    ];
}

// Function to follow redirects
function follow_redirects($url, $base_url, $cookies = [], $max_redirects = 5) {
    $redirects = 0;
    $final_url = $url;
    $all_cookies = $cookies;
    $path = [];
    
    while ($redirects < $max_redirects) {
        // Handle relative URLs
        if (strpos($final_url, 'http') !== 0) {
            // If it starts with /, it's relative to the domain root
            if (strpos($final_url, '/') === 0) {
                $parsed_base = parse_url($base_url);
                $final_url = $parsed_base['scheme'] . '://' . $parsed_base['host'] . $final_url;
            } else {
                // It's relative to the current directory
                $dir = dirname($base_url);
                $final_url = $dir . '/' . $final_url;
            }
        }
        
        echo "Requesting URL: $final_url\n";
        $response = get_request($final_url, $all_cookies);
        
        // Add to path
        $path[] = $final_url;
        
        // Merge cookies
        $all_cookies = array_merge($all_cookies, $response['cookies']);
        
        if ($response['redirect']) {
            $final_url = $response['redirect'];
            $redirects++;
            echo "Redirected to: $final_url\n";
            // Update base URL for the next redirect
            $base_url = $final_url;
        } else {
            break;
        }
    }
    
    return [
        'url' => $final_url,
        'content' => $response['content'] ?? '',
        'cookies' => $all_cookies,
        'path' => $path
    ];
}

// Function to login and get session cookies
function login($url, $username, $password) {
    // First get the login page to get cookies
    $login_response = get_request($url);
    $cookies = $login_response['cookies'];
    
    // Prepare login data
    $login_data = [
        'username' => $username,
        'password' => $password
    ];
    
    // Create POST request
    $options = [
        'http' => [
            'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
            'method'  => 'POST',
            'content' => http_build_query($login_data)
        ]
    ];
    
    // Add cookies if provided
    if (!empty($cookies)) {
        $cookie_string = '';
        foreach ($cookies as $key => $value) {
            $cookie_string .= "$key=$value; ";
        }
        $options['http']['header'] .= "Cookie: " . trim($cookie_string) . "\r\n";
    }
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    // Get response headers
    $response_headers = $http_response_header ?? [];
    
    // Check for redirect
    $redirect_url = null;
    foreach ($response_headers as $header) {
        if (preg_match('/^Location: (.*)$/i', $header, $matches)) {
            $redirect_url = trim($matches[1]);
            break;
        }
    }
    
    // Extract cookies from response
    $cookies_out = array_merge($cookies, []);
    foreach ($response_headers as $header) {
        if (preg_match('/^Set-Cookie: (.*?)=(.*?);/i', $header, $matches)) {
            $cookies_out[$matches[1]] = $matches[2];
        }
    }
    
    return [
        'content' => $result,
        'headers' => $response_headers,
        'redirect' => $redirect_url,
        'cookies' => $cookies_out
    ];
}

// Start the test
echo "Testing POS Login with force parameter...\n";

// Step 1: Login to get a session
$login_url = 'http://localhost/finance/ecommerce_complete/pages/account/login.php';
$login_response = login($login_url, 'admin', 'admin123');
$cookies = $login_response['cookies'];

echo "Logged in, got cookies: " . json_encode($cookies) . "\n";

// Step 2: Try to access the login page without force parameter
echo "\nTest 1: Access login page without force parameter\n";
$normal_login_response = get_request($login_url, $cookies);

if ($normal_login_response['redirect']) {
    echo "Redirected to: " . $normal_login_response['redirect'] . " (Expected behavior)\n";
    $final_response = follow_redirects($normal_login_response['redirect'], $login_url, $cookies);
    echo "Final URL: " . $final_response['url'] . "\n";
} else {
    echo "Not redirected. This is unexpected.\n";
}

// Step 3: Try to access the login page with force parameter
echo "\nTest 2: Access login page with force parameter\n";
$force_login_url = 'http://localhost/finance/ecommerce_complete/pages/account/login.php?force=1';
$force_login_response = get_request($force_login_url, $cookies);

if ($force_login_response['redirect']) {
    echo "Redirected to: " . $force_login_response['redirect'] . " (Unexpected behavior)\n";
} else {
    // Check if the response contains the login form
    $has_login_form = strpos($force_login_response['content'], '<form method="POST">') !== false;
    if ($has_login_form) {
        echo "Login form displayed as expected.\n";
    } else {
        echo "Login form not found. This is unexpected.\n";
    }
}

echo "\nTest completed.\n";
?>
