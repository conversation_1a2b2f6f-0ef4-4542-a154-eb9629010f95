<?php
session_start();
require_once '../../config/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: ../../login.php');
    exit();
}

// Fetch all approved products and existing deliveries
try {
    // Simplified query to just get all products from the approved table
    // Removed reference to created_at column since it doesn't exist
    $sql = "SELECT
        id as approved_id,
        prod_name,
        brand_name,
        price,
        prod_measure,
        pack_type,
        batch_code,
        stocks,
        country,
        NULL as order_id,
        'available' as status,
        NULL as delivery_time,
        NULL as customer_name
        FROM approved
        ORDER BY id DESC";

    $orders = $conn->query($sql)->fetchAll(PDO::FETCH_ASSOC);

    // Debug information
    error_log("Number of records found: " . count($orders));
} catch (PDOException $e) {
    // Log error and provide fallback
    error_log("Error fetching delivery data: " . $e->getMessage());
    $orders = [];

    // Display error message on the page
    echo '<div class="alert alert-danger">Error fetching data: ' . $e->getMessage() . '</div>';
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Track Deliveries</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
</head>

<body>
    <div class="container mt-4">
        <h2>All Deliveries</h2>

        <?php if (empty($orders)): ?>
            <div class="alert alert-warning">
                <h4>No products found in the approved table</h4>
                <p>There are no products in the finance approved table to display. Please make sure:</p>
                <ol>
                    <li>The approved table exists in your database</li>
                    <li>There are products in the approved table</li>
                    <li>You have proper permissions to access the table</li>
                </ol>
                <div class="btn-group">
                    <a href="check_data.php" class="btn btn-info">Run Diagnostics</a>
                    <a href="add_test_data.php" class="btn btn-success">Add Test Data</a>
                </div>
            </div>
        <?php else: ?>
            <div class="alert alert-success">
                <p>Found <?php echo count($orders); ?> products in the approved table.</p>
            </div>
        <?php endif; ?>

        <!-- Search and Filter Controls -->
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" id="searchInput" class="form-control" placeholder="Search by product, brand, or customer...">
                    <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                        <i class="fas fa-search"></i> Search
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <select id="statusFilter" class="form-select">
                    <option value="">All Statuses</option>
                    <option value="processing">Processing</option>
                    <option value="in_transit">In Transit</option>
                    <option value="delivered">Delivered</option>
                </select>
            </div>
            <div class="col-md-3">
                <select id="sortOrder" class="form-select">
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                </select>
            </div>
        </div>

        <table class="table table-bordered table-hover mt-3" id="deliveriesTable">
            <thead class="table" style="background-color: #f15b31; color: white;">
                <tr>
                    <th>Order ID</th>
                    <th>Product</th>
                    <th>Brand</th>
                    <th>Details</th>
                    <th>Price</th>
                    <th>Customer</th>
                    <th>Status</th>
                    <th>Order Date</th>
                    <th>Delivery Time</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($orders as $order): ?>
                    <tr class="<?php echo (!isset($order['order_id']) || $order['order_id'] === null) ? 'table-light' : ''; ?>">
                        <td>
                            <?php if (isset($order['order_id']) && $order['order_id'] !== null): ?>
                                #<?php echo $order['order_id']; ?>
                            <?php else: ?>
                                <span class="badge bg-info">New Product</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo htmlspecialchars($order['prod_name']); ?></td>
                        <td><?php echo isset($order['brand_name']) ? htmlspecialchars($order['brand_name']) : '-'; ?></td>
                        <td>
                            <?php if (isset($order['prod_measure']) || isset($order['pack_type']) || isset($order['batch_code'])): ?>
                                <small>
                                    <?php if (isset($order['prod_measure']) && !empty($order['prod_measure'])): ?>
                                        <div><strong>Measure:</strong> <?php echo htmlspecialchars($order['prod_measure']); ?></div>
                                    <?php endif; ?>

                                    <?php if (isset($order['pack_type']) && !empty($order['pack_type'])): ?>
                                        <div><strong>Package:</strong> <?php echo htmlspecialchars($order['pack_type']); ?></div>
                                    <?php endif; ?>

                                    <?php if (isset($order['batch_code']) && !empty($order['batch_code'])): ?>
                                        <div><strong>Batch:</strong> <?php echo htmlspecialchars($order['batch_code']); ?></div>
                                    <?php endif; ?>

                                    <?php if (isset($order['stocks']) && $order['stocks'] > 0): ?>
                                        <div><strong>Stock:</strong> <?php echo htmlspecialchars($order['stocks']); ?></div>
                                    <?php endif; ?>

                                    <?php if (isset($order['country']) && !empty($order['country'])): ?>
                                        <div><strong>Origin:</strong> <?php echo htmlspecialchars($order['country']); ?></div>
                                    <?php endif; ?>
                                </small>
                            <?php else: ?>
                                -
                            <?php endif; ?>
                        </td>
                        <td><?php echo isset($order['price']) ? '$' . number_format($order['price'], 2) : '-'; ?></td>
                        <td>
                            <?php if (isset($order['customer_name']) && !empty($order['customer_name'])): ?>
                                <?php echo htmlspecialchars($order['customer_name']); ?>
                            <?php else: ?>
                                <span class="text-muted">Not assigned</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if (isset($order['status']) && !empty($order['status'])): ?>
                                <span class="badge bg-<?php
                                                        echo $order['status'] === 'delivered' ? 'success' : ($order['status'] === 'in_transit' ? 'warning' : 'secondary');
                                                        ?>"><?php echo ucfirst(str_replace('_', ' ', $order['status'])); ?></span>
                            <?php else: ?>
                                <span class="badge bg-light text-dark">Available</span>
                            <?php endif; ?>
                        </td>
                        <td>-</td> <!-- Placeholder for date since created_at doesn't exist -->
                        <td><?php echo isset($order['delivery_time']) && $order['delivery_time'] ? date('M d, Y H:i', strtotime($order['delivery_time'])) : '-'; ?></td>
                        <td>
                            <?php if (isset($order['order_id']) && $order['order_id'] !== null): ?>
                                <a href="track_delivery.php?order_id=<?php echo $order['order_id']; ?>" class="btn btn-primary btn-sm">
                                    <i class="fas fa-truck"></i> Track
                                </a>
                            <?php else: ?>
                                <button type="button" class="btn btn-success btn-sm create-delivery"
                                    data-product-id="<?php echo $order['approved_id']; ?>"
                                    data-product-name="<?php echo htmlspecialchars($order['prod_name']); ?>">
                                    <i class="fas fa-plus"></i> Create Delivery
                                </button>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <a href="../admin/admin_dashboard.php" class="btn btn-secondary mt-3" style="background-color: #f15b31; color: white;">Back to Dashboard</a>
    </div>

    <!-- Create Delivery Modal -->
    <div class="modal fade" id="createDeliveryModal" tabindex="-1" aria-labelledby="createDeliveryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createDeliveryModalLabel">Create New Delivery</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="createDeliveryForm" action="create_delivery.php" method="post">
                    <div class="modal-body">
                        <input type="hidden" id="product_id" name="product_id">
                        <input type="hidden" id="product_price" name="product_price">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="product_name" class="form-label">Product</label>
                                <input type="text" class="form-control" id="product_name" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="quantity" class="form-label">Quantity</label>
                                <input type="number" class="form-control" id="quantity" name="quantity" min="1" value="1" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="unit_price" class="form-label">Unit Price</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="text" class="form-control" id="unit_price" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="total_price" class="form-label">Total Price</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="text" class="form-control" id="total_price" name="total_price" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="customer" class="form-label">Customer</label>
                            <select class="form-select" id="customer" name="user_id" required>
                                <option value="">Select Customer</option>
                                <?php
                                // Fetch all users who can be customers
                                $users_sql = "SELECT id, CONCAT(first_name, ' ', last_name) as name FROM users ORDER BY name";
                                try {
                                    $users = $conn->query($users_sql)->fetchAll(PDO::FETCH_ASSOC);
                                    foreach ($users as $user) {
                                        echo '<option value="' . $user['id'] . '">' . htmlspecialchars($user['name']) . '</option>';
                                    }
                                } catch (PDOException $e) {
                                    error_log("Error fetching users: " . $e->getMessage());
                                }
                                ?>
                            </select>
                        </div>

                        <h5 class="mt-4 mb-3">Delivery Information</h5>

                        <div class="mb-3">
                            <label for="delivery_from" class="form-label">Delivered From</label>
                            <input type="text" class="form-control" id="delivery_from" name="delivery_from" value="3708 N Guevarra St., Zone 1 Dasmariñas, Cavite Philippines 4114" readonly>
                        </div>

                        <div class="mb-3">
                            <label for="pinpoint_address" class="form-label">Pinpoint Address (House/Building No., Street, etc.)</label>
                            <input type="text" class="form-control" id="pinpoint_address" name="pinpoint_address" placeholder="Enter house number, street, etc.">
                        </div>

                        <div class="mb-3">
                            <label for="delivery_address" class="form-label">Complete Delivery Address</label>
                            <textarea class="form-control" id="delivery_address" name="delivery_address" rows="2" required></textarea>
                            <small class="text-muted">This will be automatically updated when you select a city and province</small>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="province" class="form-label">Province</label>
                                <select class="form-select" id="province" name="province" required>
                                    <option value="">Select Province</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="city" class="form-label">City</label>
                                <select class="form-select" id="city" name="city" required>
                                    <option value="">Select City</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="zip_code" class="form-label">Zip Code</label>
                                <input type="text" class="form-control" id="zip_code" name="zip_code" required>
                            </div>
                            <div class="col-md-6">
                                <label for="country" class="form-label">Country</label>
                                <input type="text" class="form-control" id="country" name="country" value="Philippines" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="special_instructions" class="form-label">Special Instructions (Optional)</label>
                            <textarea class="form-control" id="special_instructions" name="special_instructions" rows="2"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="driver" class="form-label">Assign Driver (Optional)</label>
                            <select class="form-select" id="driver" name="driver_id">
                                <option value="">Select Driver</option>
                                <?php
                                // Fetch all available drivers
                                $drivers_sql = "SELECT d.id, CONCAT(u.first_name, ' ', u.last_name) as name
                                              FROM drivers d
                                              JOIN users u ON d.user_id = u.id
                                              WHERE d.availability = 'available'
                                              ORDER BY name";
                                try {
                                    $drivers = $conn->query($drivers_sql)->fetchAll(PDO::FETCH_ASSOC);
                                    foreach ($drivers as $driver) {
                                        echo '<option value="' . $driver['id'] . '">' . htmlspecialchars($driver['name']) . '</option>';
                                    }
                                } catch (PDOException $e) {
                                    error_log("Error fetching drivers: " . $e->getMessage());
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Delivery</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Bootstrap and jQuery JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script src="basic_locations.js"></script>

    <script>
        $(document).ready(function() {
            // Search functionality
            $("#searchBtn").click(function() {
                applyFilters();
            });

            // Allow search on Enter key
            $("#searchInput").keypress(function(e) {
                if (e.which === 13) {
                    applyFilters();
                }
            });

            // Filter by status
            $("#statusFilter").change(function() {
                applyFilters();
            });

            // Sort order
            $("#sortOrder").change(function() {
                applyFilters();
            });

            // Create delivery button click
            $(document).on("click", ".create-delivery", function() {
                const productId = $(this).data("product-id");
                const productName = $(this).data("product-name");

                // Find the product price from the table row
                const row = $(this).closest("tr");
                const price = row.find("td:eq(4)").text().replace('$', '').trim();
                const numericPrice = parseFloat(price) || 0;

                // Set values in the modal
                $("#product_id").val(productId);
                $("#product_name").val(productName);
                $("#product_price").val(numericPrice);
                $("#unit_price").val(numericPrice.toFixed(2));

                // Calculate initial total price (quantity = 1 by default)
                $("#total_price").val(numericPrice.toFixed(2));

                // Show the modal
                const modal = new bootstrap.Modal(document.getElementById('createDeliveryModal'));
                modal.show();
            });

            // Calculate total price when quantity changes
            $("#quantity").on("input", function() {
                const quantity = parseInt($(this).val()) || 0;
                const unitPrice = parseFloat($("#unit_price").val()) || 0;
                const totalPrice = quantity * unitPrice;

                $("#total_price").val(totalPrice.toFixed(2));
            });

            // Handle form submission
            $("#createDeliveryForm").submit(function(e) {
                e.preventDefault();

                const formData = new FormData(this);

                $.ajax({
                    url: "create_delivery.php",
                    type: "POST",
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        try {
                            // Check if response is already an object
                            let result = response;
                            if (typeof response === 'string') {
                                result = JSON.parse(response);
                            }

                            if (result.success) {
                                alert("Delivery created successfully!");
                                location.reload();
                            } else {
                                alert("Error: " + (result.message || "Unknown error"));
                            }
                        } catch (e) {
                            console.error("JSON parsing error:", e);
                            console.log("Raw response:", response);
                            alert("An error occurred while processing the response. See console for details.");
                        }
                    },
                    error: function() {
                        alert("An error occurred while creating the delivery.");
                    }
                });
            });

            // Function to apply all filters
            function applyFilters() {
                const searchTerm = $("#searchInput").val().toLowerCase();
                const statusFilter = $("#statusFilter").val();
                const sortOrder = $("#sortOrder").val();

                // Get all rows
                const rows = $("#deliveriesTable tbody tr").get();

                // Filter rows
                const filteredRows = rows.filter(function(row) {
                    const productName = $(row).find("td:eq(1)").text().toLowerCase();
                    const brandName = $(row).find("td:eq(2)").text().toLowerCase();
                    const customerName = $(row).find("td:eq(5)").text().toLowerCase();
                    const status = $(row).find("td:eq(6)").text().toLowerCase();

                    // Apply search filter
                    const matchesSearch = searchTerm === "" ||
                        productName.includes(searchTerm) ||
                        brandName.includes(searchTerm) ||
                        customerName.includes(searchTerm);

                    // Apply status filter
                    const matchesStatus = statusFilter === "" || status.includes(statusFilter);

                    return matchesSearch && matchesStatus;
                });

                // Sort rows by ID instead of date since created_at doesn't exist
                filteredRows.sort(function(a, b) {
                    // Extract ID from the first column
                    const getID = function(row) {
                        const idText = $(row).find("td:eq(0)").text().trim();
                        // If it's a number (order ID), use that, otherwise use a default
                        if (idText.startsWith('#')) {
                            return parseInt(idText.substring(1));
                        }
                        return 0;
                    };

                    const idA = getID(a);
                    const idB = getID(b);

                    if (sortOrder === "newest") {
                        return idB - idA; // Higher ID is newer
                    } else {
                        return idA - idB; // Lower ID is older
                    }
                });

                // Clear table and append filtered rows
                $("#deliveriesTable tbody").empty().append(filteredRows);

                // Show message if no results
                if (filteredRows.length === 0) {
                    $("#deliveriesTable tbody").append(
                        '<tr><td colspan="10" class="text-center">No deliveries found matching your criteria</td></tr>'
                    );
                }
            }

            // Auto-fill delivery address when customer is selected
            $("#customer").change(function() {
                const userId = $(this).val();
                if (userId) {
                    // Show loading indicator
                    $("#delivery_address").val("Loading address...");

                    // Fetch user address via AJAX with improved error handling
                    $.ajax({
                        url: "get_user_address.php",
                        type: "GET",
                        data: {
                            user_id: userId
                        },
                        dataType: "json", // Expect JSON response
                        success: function(result) {
                            console.log("Address data received:", result);

                            // Make sure result is an object (not a string)
                            if (typeof result === 'string') {
                                try {
                                    result = JSON.parse(result);
                                    console.log("Parsed result:", result);
                                } catch (e) {
                                    console.error("Failed to parse JSON response:", e);
                                    // Set default values and return
                                    $("#province").val("CAV");
                                    if (document.getElementById('province')) {
                                        document.getElementById('province').dispatchEvent(new Event('change'));
                                    }
                                    setTimeout(function() {
                                        $("#city").val("Dasmariñas");
                                        $("#zip_code").val("4114");
                                        $("#country").val("Philippines");
                                    }, 300);
                                    return;
                                }
                            }

                            if (result.error) {
                                console.error("API error:", result.error);
                                return;
                            }

                            // Fill in the form fields
                            const addressValue = result.address || "";

                            // Only set the address if it's not empty
                            if (addressValue.trim() !== "") {
                                $("#delivery_address").val(addressValue);
                            }

                            $("#zip_code").val(result.zip_code || "");
                            $("#country").val(result.country || "Philippines");

                            // Set province and trigger change to load cities
                            if (result.province) {
                                // Convert old province codes to new format if needed
                                let provinceCode = result.province;

                                // Map old numeric codes to new string codes
                                const provinceCodeMap = {
                                    '0128': 'CAV', // Cavite
                                    '0133': 'LAG', // Laguna
                                    '0174': 'RIZ', // Rizal
                                    '0137': 'MM', // Metro Manila
                                    '0122': 'BAT', // Batangas
                                    '0127': 'BUL' // Bulacan
                                };

                                // Convert if needed
                                if (provinceCodeMap[provinceCode]) {
                                    provinceCode = provinceCodeMap[provinceCode];
                                }

                                // Set the province value and trigger change
                                $("#province").val(provinceCode);

                                // Manually trigger the change event
                                if (document.getElementById('province')) {
                                    document.getElementById('province').dispatchEvent(new Event('change'));
                                }

                                // Set city after a delay to allow province change to complete
                                if (result.city) {
                                    setTimeout(function() {
                                        $("#city").val(result.city);
                                    }, 300);
                                }
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error("AJAX error:", status, error);
                            $("#delivery_address").val("");

                            // Set default values
                            $("#province").val("CAV"); // Default to Cavite

                            // Manually trigger the change event
                            if (document.getElementById('province')) {
                                document.getElementById('province').dispatchEvent(new Event('change'));
                            }

                            setTimeout(function() {
                                $("#city").val("Dasmariñas");
                                $("#zip_code").val("4114");
                                $("#country").val("Philippines");
                            }, 300);
                        }
                    });
                }
            });

            // Local province data for Philippines
            const philippineProvinces = [
                // NCR
                {
                    code: '0137',
                    name: 'Metro Manila',
                    region: 'National Capital Region (NCR)'
                },

                // Luzon - Region 3 (Central Luzon)
                {
                    code: '0127',
                    name: 'Bulacan',
                    region: 'Central Luzon'
                },
                {
                    code: '0177',
                    name: 'Pampanga',
                    region: 'Central Luzon'
                },
                {
                    code: '0181',
                    name: 'Tarlac',
                    region: 'Central Luzon'
                },
                {
                    code: '0183',
                    name: 'Zambales',
                    region: 'Central Luzon'
                },
                {
                    code: '0123',
                    name: 'Bataan',
                    region: 'Central Luzon'
                },
                {
                    code: '0134',
                    name: 'Nueva Ecija',
                    region: 'Central Luzon'
                },
                {
                    code: '0124',
                    name: 'Aurora',
                    region: 'Central Luzon'
                },

                // Luzon - Region 4A (CALABARZON)
                {
                    code: '0128',
                    name: 'Cavite',
                    region: 'CALABARZON',
                    popular: true
                },
                {
                    code: '0133',
                    name: 'Laguna',
                    region: 'CALABARZON',
                    popular: true
                },
                {
                    code: '0122',
                    name: 'Batangas',
                    region: 'CALABARZON',
                    popular: true
                },
                {
                    code: '0174',
                    name: 'Rizal',
                    region: 'CALABARZON',
                    popular: true
                },
                {
                    code: '0175',
                    name: 'Quezon',
                    region: 'CALABARZON'
                },

                // Luzon - Region 4B (MIMAROPA)
                {
                    code: '0178',
                    name: 'Occidental Mindoro',
                    region: 'MIMAROPA'
                },
                {
                    code: '0179',
                    name: 'Oriental Mindoro',
                    region: 'MIMAROPA'
                },
                {
                    code: '0180',
                    name: 'Marinduque',
                    region: 'MIMAROPA'
                },
                {
                    code: '0182',
                    name: 'Romblon',
                    region: 'MIMAROPA'
                },
                {
                    code: '0184',
                    name: 'Palawan',
                    region: 'MIMAROPA'
                },

                // Luzon - Region 1 (Ilocos Region)
                {
                    code: '0129',
                    name: 'Ilocos Norte',
                    region: 'Ilocos Region'
                },
                {
                    code: '0130',
                    name: 'Ilocos Sur',
                    region: 'Ilocos Region'
                },
                {
                    code: '0131',
                    name: 'La Union',
                    region: 'Ilocos Region'
                },
                {
                    code: '0132',
                    name: 'Pangasinan',
                    region: 'Ilocos Region'
                },

                // Luzon - Region 2 (Cagayan Valley)
                {
                    code: '0125',
                    name: 'Batanes',
                    region: 'Cagayan Valley'
                },
                {
                    code: '0126',
                    name: 'Cagayan',
                    region: 'Cagayan Valley'
                },
                {
                    code: '0135',
                    name: 'Nueva Vizcaya',
                    region: 'Cagayan Valley'
                },
                {
                    code: '0136',
                    name: 'Quirino',
                    region: 'Cagayan Valley'
                },
                {
                    code: '0138',
                    name: 'Isabela',
                    region: 'Cagayan Valley'
                },

                // Luzon - CAR (Cordillera Administrative Region)
                {
                    code: '0139',
                    name: 'Abra',
                    region: 'Cordillera Administrative Region'
                },
                {
                    code: '0140',
                    name: 'Apayao',
                    region: 'Cordillera Administrative Region'
                },
                {
                    code: '0141',
                    name: 'Benguet',
                    region: 'Cordillera Administrative Region'
                },
                {
                    code: '0142',
                    name: 'Ifugao',
                    region: 'Cordillera Administrative Region'
                },
                {
                    code: '0143',
                    name: 'Kalinga',
                    region: 'Cordillera Administrative Region'
                },
                {
                    code: '0144',
                    name: 'Mountain Province',
                    region: 'Cordillera Administrative Region'
                },

                // Visayas - Region 5 (Bicol Region)
                {
                    code: '0145',
                    name: 'Albay',
                    region: 'Bicol Region'
                },
                {
                    code: '0146',
                    name: 'Camarines Norte',
                    region: 'Bicol Region'
                },
                {
                    code: '0147',
                    name: 'Camarines Sur',
                    region: 'Bicol Region'
                },
                {
                    code: '0148',
                    name: 'Catanduanes',
                    region: 'Bicol Region'
                },
                {
                    code: '0149',
                    name: 'Masbate',
                    region: 'Bicol Region'
                },
                {
                    code: '0150',
                    name: 'Sorsogon',
                    region: 'Bicol Region'
                }
            ];

            // Initialize province dropdown with local data - simplified version
            function initializeProvinceDropdown() {
                console.log('Initializing province dropdown');

                // Get the province select element using jQuery
                const $provinceSelect = $('#province');

                // Clear existing options
                $provinceSelect.empty();
                $provinceSelect.append('<option value="">Select Province</option>');

                // Add popular provinces first
                const popularProvinces = philippineProvinces.filter(p => p.popular);
                if (popularProvinces.length > 0) {
                    // Create optgroup element
                    const $popularGroup = $('<optgroup>', {
                        label: '★ Popular Provinces'
                    });

                    // Add options to the optgroup
                    popularProvinces.forEach(province => {
                        $popularGroup.append(
                            $('<option>', {
                                value: province.code,
                                text: province.name,
                                class: 'popular-province'
                            })
                        );
                    });

                    // Add the optgroup to the select
                    $provinceSelect.append($popularGroup);
                }

                // Group remaining provinces by region
                const regions = {};
                philippineProvinces.forEach(province => {
                    if (!province.popular) {
                        if (!regions[province.region]) {
                            regions[province.region] = [];
                        }
                        regions[province.region].push(province);
                    }
                });

                // Add provinces by region
                Object.keys(regions).sort().forEach(region => {
                    // Create optgroup element
                    const $regionGroup = $('<optgroup>', {
                        label: region
                    });

                    // Add options to the optgroup
                    regions[region]
                        .sort((a, b) => a.name.localeCompare(b.name))
                        .forEach(province => {
                            $regionGroup.append(
                                $('<option>', {
                                    value: province.code,
                                    text: province.name
                                })
                            );
                        });

                    // Add the optgroup to the select
                    $provinceSelect.append($regionGroup);
                });

                // Check if Select2 is available before initializing
                if (typeof $.fn.select2 === 'function') {
                    try {
                        $provinceSelect.select2({
                            placeholder: 'Select or search for a province',
                            allowClear: true,
                            width: '100%'
                        });

                        console.log('Province Select2 initialized');
                    } catch (e) {
                        console.error('Error initializing province Select2:', e);
                    }
                } else {
                    console.warn('Select2 not available for province dropdown, using standard dropdown');
                }

                // Set up the change event handler
                $provinceSelect.on('change', function() {
                    const provinceCode = $(this).val();
                    console.log('Province changed to:', provinceCode);

                    if (!provinceCode) return;

                    // Populate the city dropdown
                    populateCityDropdown(provinceCode);
                });

                console.log(`Loaded ${philippineProvinces.length} provinces`);
            }

            // Function to populate city dropdown
            function populateCityDropdown(provinceCode) {
                console.log('Populating city dropdown for province:', provinceCode);

                const $citySelect = $('#city');

                // Clear existing options
                $citySelect.empty();
                $citySelect.append('<option value="">Select City</option>');

                // Get cities for this province
                const cities = philippineCities[provinceCode] || philippineCities['default'];

                // Get popular cities
                const popular = popularCities[provinceCode] || [];

                // Add popular cities first
                if (popular.length > 0) {
                    // Create optgroup element
                    const $popularGroup = $('<optgroup>', {
                        label: '★ Popular Cities'
                    });

                    // Add options to the optgroup
                    popular.forEach(city => {
                        if (cities.includes(city)) {
                            $popularGroup.append(
                                $('<option>', {
                                    value: city,
                                    text: city,
                                    class: 'popular-city'
                                })
                            );
                        }
                    });

                    // Add the optgroup to the select
                    $citySelect.append($popularGroup);
                }

                // Group remaining cities by first letter
                const otherCities = cities.filter(city => !popular.includes(city)).sort();
                const cityGroups = {};

                otherCities.forEach(city => {
                    const firstLetter = city.charAt(0).toUpperCase();
                    if (!cityGroups[firstLetter]) {
                        cityGroups[firstLetter] = [];
                    }
                    cityGroups[firstLetter].push(city);
                });

                // Add city groups
                Object.keys(cityGroups).sort().forEach(letter => {
                    // Create optgroup element
                    const $letterGroup = $('<optgroup>', {
                        label: letter
                    });

                    // Add options to the optgroup
                    cityGroups[letter].forEach(city => {
                        $letterGroup.append(
                            $('<option>', {
                                value: city,
                                text: city
                            })
                        );
                    });

                    // Add the optgroup to the select
                    $citySelect.append($letterGroup);
                });

                // Check if Select2 is available before initializing
                if (typeof $.fn.select2 === 'function') {
                    try {
                        $citySelect.select2({
                            placeholder: 'Select or search for a city',
                            allowClear: true,
                            width: '100%'
                        });

                        console.log('City Select2 initialized');
                    } catch (e) {
                        console.error('Error initializing city Select2:', e);
                    }
                } else {
                    console.warn('Select2 not available for city dropdown, using standard dropdown');
                }

                console.log(`Loaded ${cities.length} cities for province ${provinceCode}`);
            }



            // Initialize province dropdown when document is fully loaded
            $(document).ready(function() {
                console.log('Document ready, initializing province dropdown');
                initializeProvinceDropdown();
            });

            // Local city data for Philippines - using this instead of the failing API
            const philippineCities = {
                // Cavite
                '0128': [
                    'Alfonso', 'Amadeo', 'Bacoor', 'Carmona', 'Cavite City', 'Dasmariñas',
                    'General Emilio Aguinaldo', 'General Mariano Alvarez', 'General Trias',
                    'Imus', 'Indang', 'Kawit', 'Magallanes', 'Maragondon', 'Mendez',
                    'Naic', 'Noveleta', 'Rosario', 'Silang', 'Tagaytay', 'Tanza', 'Ternate', 'Trece Martires'
                ],
                // Laguna
                '0133': [
                    'Alaminos', 'Bay', 'Biñan', 'Cabuyao', 'Calamba', 'Calauan',
                    'Cavinti', 'Famy', 'Kalayaan', 'Liliw', 'Los Baños', 'Luisiana',
                    'Lumban', 'Mabitac', 'Magdalena', 'Majayjay', 'Nagcarlan', 'Paete',
                    'Pagsanjan', 'Pakil', 'Pangil', 'Pila', 'Rizal', 'San Pablo',
                    'San Pedro', 'Santa Cruz', 'Santa Maria', 'Santa Rosa', 'Siniloan', 'Victoria'
                ],
                // Rizal
                '0174': [
                    'Angono', 'Antipolo', 'Baras', 'Binangonan', 'Cainta', 'Cardona',
                    'Jalajala', 'Morong', 'Pililla', 'Rodriguez', 'San Mateo', 'Tanay',
                    'Taytay', 'Teresa'
                ],
                // Metro Manila
                '0137': [
                    'Caloocan', 'Las Piñas', 'Makati', 'Malabon', 'Mandaluyong', 'Manila',
                    'Marikina', 'Muntinlupa', 'Navotas', 'Parañaque', 'Pasay', 'Pasig',
                    'Pateros', 'Quezon City', 'San Juan', 'Taguig', 'Valenzuela'
                ],
                // Batangas
                '0122': [
                    'Agoncillo', 'Alitagtag', 'Balayan', 'Balete', 'Batangas City', 'Bauan',
                    'Calaca', 'Calatagan', 'Cuenca', 'Ibaan', 'Laurel', 'Lemery', 'Lian',
                    'Lipa', 'Lobo', 'Mabini', 'Malvar', 'Mataas na Kahoy', 'Nasugbu',
                    'Padre Garcia', 'Rosario', 'San Jose', 'San Juan', 'San Luis',
                    'San Nicolas', 'San Pascual', 'Santa Teresita', 'Santo Tomas',
                    'Taal', 'Talisay', 'Tanauan', 'Taysan', 'Tingloy', 'Tuy'
                ],
                // Bulacan
                '0127': [
                    'Angat', 'Balagtas', 'Baliuag', 'Bocaue', 'Bulakan', 'Bustos',
                    'Calumpit', 'Doña Remedios Trinidad', 'Guiguinto', 'Hagonoy',
                    'Malolos', 'Marilao', 'Meycauayan', 'Norzagaray', 'Obando',
                    'Pandi', 'Paombong', 'Plaridel', 'Pulilan', 'San Ildefonso',
                    'San Jose del Monte', 'San Miguel', 'San Rafael', 'Santa Maria'
                ],
                // Default cities for other provinces
                'default': [
                    'City 1', 'City 2', 'City 3', 'City 4', 'City 5'
                ]
            };

            // Add CSS for enhanced dropdown
            const style = document.createElement('style');
            style.textContent = `
                .city-search-container {
                    position: relative;
                    margin-bottom: 10px;
                }
                .city-search {
                    width: 100%;
                    padding: 8px;
                    border: 1px solid #ced4da;
                    border-radius: 4px;
                }
                .select2-container {
                    width: 100% !important;
                }
                .select2-container--default .select2-results__group {
                    background-color: #f8f9fa;
                    font-weight: bold;
                    padding: 6px 8px;
                }
                .popular-city {
                    font-weight: bold;
                    background-color: #f0f8ff;
                }
                .select2-results__option {
                    padding: 6px 12px;
                }
                .select2-container--default .select2-results__option--highlighted[aria-selected] {
                    background-color: #007bff;
                }
            `;
            document.head.appendChild(style);

            // Define popular cities for each province
            const popularCities = {
                '0128': ['Dasmariñas', 'Bacoor', 'Imus', 'Tagaytay', 'General Trias'], // Cavite
                '0133': ['Santa Rosa', 'Calamba', 'San Pedro', 'Biñan', 'Los Baños'], // Laguna
                '0174': ['Antipolo', 'Cainta', 'Taytay'], // Rizal
                '0137': ['Manila', 'Quezon City', 'Makati', 'Taguig', 'Pasig'], // Metro Manila
                '0122': ['Batangas City', 'Lipa', 'Tanauan'], // Batangas
                '0127': ['Malolos', 'San Jose del Monte', 'Meycauayan'] // Bulacan
            };
        });
    </script>
</body>

</html>