<?php
require_once '../../config/config.php';

// Check if the approved table exists
try {
    $check_table = $conn->query("SHOW TABLES LIKE 'approved'");
    if ($check_table->rowCount() == 0) {
        // Create the approved table if it doesn't exist
        $create_table_sql = "CREATE TABLE IF NOT EXISTS approved (
            id INT AUTO_INCREMENT PRIMARY KEY,
            prod_image VARCHAR(255),
            prod_name VARCHAR(100) NOT NULL,
            brand_name VARCHAR(100) NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            prod_measure VARCHAR(50),
            pack_type VARCHAR(50),
            expiry_date DATETIME,
            delivered_date DATETIME,
            country VARCHAR(100),
            batch_code VARCHAR(50),
            stocks DECIMAL(10,2) NOT NULL,
            status VARCHAR(20) NOT NULL DEFAULT 'approved',
            order_id INT
        )";
        $conn->exec($create_table_sql);
        echo "<p>Created approved table.</p>";
    }

    // Check if there's data in the approved table
    $count_query = "SELECT COUNT(*) FROM approved";
    $count = $conn->query($count_query)->fetchColumn();

    if ($count == 0) {
        // Add sample data
        $conn->beginTransaction();

        $sample_data = [
            [
                'prod_name' => 'Organic Turmeric Powder',
                'brand_name' => 'EuroSpice',
                'price' => 12.99,
                'prod_measure' => '250g',
                'pack_type' => 'Pouch',
                'expiry_date' => date('Y-m-d', strtotime('+1 year')),
                'delivered_date' => date('Y-m-d'),
                'country' => 'India',
                'batch_code' => 'TUR-2023-001',
                'stocks' => 50
            ],
            [
                'prod_name' => 'Premium Cinnamon Sticks',
                'brand_name' => 'EuroSpice',
                'price' => 8.99,
                'prod_measure' => '100g',
                'pack_type' => 'Box',
                'expiry_date' => date('Y-m-d', strtotime('+2 years')),
                'delivered_date' => date('Y-m-d', strtotime('-1 week')),
                'country' => 'Sri Lanka',
                'batch_code' => 'CIN-2023-002',
                'stocks' => 75
            ],
            [
                'prod_name' => 'Black Peppercorns',
                'brand_name' => 'SpiceWorld',
                'price' => 6.49,
                'prod_measure' => '200g',
                'pack_type' => 'Jar',
                'expiry_date' => date('Y-m-d', strtotime('+18 months')),
                'delivered_date' => date('Y-m-d', strtotime('-2 days')),
                'country' => 'Vietnam',
                'batch_code' => 'PEP-2023-003',
                'stocks' => 100
            ],
            [
                'prod_name' => 'Saffron Threads',
                'brand_name' => 'LuxSpice',
                'price' => 24.99,
                'prod_measure' => '5g',
                'pack_type' => 'Glass Vial',
                'expiry_date' => date('Y-m-d', strtotime('+3 years')),
                'delivered_date' => date('Y-m-d'),
                'country' => 'Spain',
                'batch_code' => 'SAF-2023-004',
                'stocks' => 25
            ],
            [
                'prod_name' => 'Cardamom Pods',
                'brand_name' => 'EuroSpice',
                'price' => 9.99,
                'prod_measure' => '50g',
                'pack_type' => 'Pouch',
                'expiry_date' => date('Y-m-d', strtotime('+2 years')),
                'delivered_date' => date('Y-m-d', strtotime('-3 days')),
                'country' => 'Guatemala',
                'batch_code' => 'CAR-2023-005',
                'stocks' => 60
            ]
        ];

        $insert_sql = "INSERT INTO approved (
            prod_name, brand_name, price, prod_measure, pack_type,
            expiry_date, delivered_date, country, batch_code, stocks, status
        ) VALUES (
            :prod_name, :brand_name, :price, :prod_measure, :pack_type,
            :expiry_date, :delivered_date, :country, :batch_code, :stocks, 'approved'
        )";

        $stmt = $conn->prepare($insert_sql);

        foreach ($sample_data as $product) {
            $stmt->bindValue(':prod_name', $product['prod_name']);
            $stmt->bindValue(':brand_name', $product['brand_name']);
            $stmt->bindValue(':price', $product['price']);
            $stmt->bindValue(':prod_measure', $product['prod_measure']);
            $stmt->bindValue(':pack_type', $product['pack_type']);
            $stmt->bindValue(':expiry_date', $product['expiry_date']);
            $stmt->bindValue(':delivered_date', $product['delivered_date']);
            $stmt->bindValue(':country', $product['country']);
            $stmt->bindValue(':batch_code', $product['batch_code']);
            $stmt->bindValue(':stocks', $product['stocks']);
            $stmt->execute();
        }

        $conn->commit();

        echo "<p>Added 5 sample products to the approved table.</p>";
    } else {
        echo "<p>The approved table already has {$count} records.</p>";
    }

    echo "<p><a href='track_deliveries.php'>Go back to Track Deliveries</a></p>";
} catch (PDOException $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
