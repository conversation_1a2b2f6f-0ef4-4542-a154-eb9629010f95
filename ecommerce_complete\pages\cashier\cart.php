<?php
require_once '../../config/database.php';
require_once '../../config/session.php';

// Require login
requireLogin();

// Get cart items
$cart_items = [];
$cart_total = 0;
$order_id = 0;

$cart_sql = "SELECT oi.*, p.name, p.price, p.image_url, p.stock_quantity, o.id as order_id
            FROM e_order_items oi
            JOIN e_products p ON oi.product_id = p.id
            JOIN e_orders o ON oi.order_id = o.id
            WHERE o.user_id = :user_id AND o.status = 'pending' AND o.payment_status = 'unpaid'";

$cart_items = executeQuery($cart_sql, [':user_id' => $_SESSION['user_id']])->fetchAll();

if (!empty($cart_items)) {
    $order_id = $cart_items[0]['order_id'];

    foreach ($cart_items as $item) {
        $cart_total += $item['price'] * $item['quantity'];
    }
}

// Handle update quantity
if (isset($_POST['update_quantity'])) {
    $item_id = $_POST['item_id'];
    $quantity = $_POST['quantity'];

    if ($quantity > 0) {
        $update_sql = "UPDATE e_order_items SET quantity = :quantity WHERE id = :id";
        executeQuery($update_sql, [
            ':quantity' => $quantity,
            ':id' => $item_id
        ]);
    } else {
        // Remove item if quantity is 0
        $delete_sql = "DELETE FROM e_order_items WHERE id = :id";
        executeQuery($delete_sql, [':id' => $item_id]);
    }

    // Update order total
    if ($order_id) {
        $update_total_sql = "UPDATE e_orders SET total_amount = (
                            SELECT SUM(price * quantity) FROM e_order_items WHERE order_id = :order_id
                            ) WHERE id = :order_id";
        executeQuery($update_total_sql, [':order_id' => $order_id]);
    }

    // Redirect to prevent form resubmission
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// Handle remove item
if (isset($_POST['remove_item'])) {
    $item_id = $_POST['item_id'];

    $delete_sql = "DELETE FROM e_order_items WHERE id = :id";
    executeQuery($delete_sql, [':id' => $item_id]);

    // Update order total
    if ($order_id) {
        $update_total_sql = "UPDATE e_orders SET total_amount = (
                            SELECT SUM(price * quantity) FROM e_order_items WHERE order_id = :order_id
                            ) WHERE id = :order_id";
        executeQuery($update_total_sql, [':order_id' => $order_id]);
    }

    // Redirect to prevent form resubmission
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// Handle checkout
if (isset($_POST['checkout']) && $order_id) {
    // Update order status
    $update_sql = "UPDATE e_orders SET status = 'processing' WHERE id = :order_id";
    executeQuery($update_sql, [':order_id' => $order_id]);

    // Redirect to checkout page
    header("Location: checkout.php?order_id=" . $order_id);
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - Euro Spice</title>
    <link rel="stylesheet" href="../../plugins/bootstrap/css/bootstrap.min.css">
    <script defer src="../../plugins/fontawesome/js/all.js"></script>
    <style>
        .cart-item {
            border-bottom: 1px solid #eee;
            padding: 15px 0;
        }

        .product-img {
            width: 80px;
            height: 80px;
            object-fit: cover;
        }

        .quantity-input {
            width: 60px;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-light border-bottom site-header sticky-top py-1"
        style="background-color: #f15b31;">
        <div class="container">
            <a class="navbar-brand fw-bold fs-4" href="../../index.php" style="color: white;"><b>Euro Spice</b></a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
                aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../../index.php">
                            <i class="fas fa-home"></i> Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../account/settings.php">
                            <i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../account/logout.php">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container py-5">
        <h2 class="mb-4">Your Shopping Cart</h2>

        <?php if (empty($cart_items)): ?>
            <div class="alert alert-info">
                <p>Your cart is empty. <a href="../../index.php" class="alert-link">Continue shopping</a></p>
            </div>
        <?php else: ?>
            <div class="row">
                <div class="col-lg-8">
                    <!-- Cart Items -->
                    <?php foreach ($cart_items as $item): ?>
                        <div class="cart-item row">
                            <div class="col-md-2">
                                <?php if (!empty($item['image_url'])): ?>
                                    <img src="<?php echo htmlspecialchars($item['image_url']); ?>" alt="<?php echo htmlspecialchars($item['name']); ?>" class="product-img">
                                <?php else: ?>
                                    <div class="bg-secondary text-white product-img d-flex align-items-center justify-content-center">
                                        <span>No Image</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4">
                                <h5><?php echo htmlspecialchars($item['name']); ?></h5>
                                <p class="text-muted">PHP <?php echo number_format($item['price'], 2); ?></p>
                            </div>
                            <div class="col-md-3">
                                <form method="POST" class="d-flex align-items-center">
                                    <input type="hidden" name="item_id" value="<?php echo $item['id']; ?>">
                                    <input type="number" name="quantity" value="<?php echo $item['quantity']; ?>" min="0" max="<?php echo $item['stock_quantity']; ?>" class="form-control quantity-input me-2">
                                    <button type="submit" name="update_quantity" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-2 text-end">
                                <p class="fw-bold">PHP <?php echo number_format($item['price'] * $item['quantity'], 2); ?></p>
                            </div>
                            <div class="col-md-1 text-end">
                                <form method="POST">
                                    <input type="hidden" name="item_id" value="<?php echo $item['id']; ?>">
                                    <button type="submit" name="remove_item" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="col-lg-4">
                    <!-- Order Summary -->
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Order Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-3">
                                <span>Subtotal</span>
                                <span>PHP <?php echo number_format($cart_total, 2); ?></span>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>Shipping</span>
                                <span>Free</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-3 fw-bold">
                                <span>Total</span>
                                <span>PHP <?php echo number_format($cart_total, 2); ?></span>
                            </div>

                            <form method="POST">
                                <button type="submit" name="checkout" class="btn btn-success w-100">
                                    Proceed to Checkout
                                </button>
                            </form>

                            <a href="../../index.php" class="btn btn-outline-secondary w-100 mt-2">
                                Continue Shopping
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="../../plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
</body>

</html>