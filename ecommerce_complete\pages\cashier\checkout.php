<?php
require_once '../../config/database.php';
require_once '../../config/session.php';

// Require login
requireLogin();

// Check if order ID is provided
if (!isset($_GET['order_id'])) {
    header("Location: cart.php");
    exit();
}

$order_id = $_GET['order_id'];

// Get order details
$order_sql = "SELECT o.*, CONCAT(u.first_name, ' ', u.last_name) as customer_name, u.email, u.phone, u.address
             FROM e_orders o
             JOIN e_users u ON o.user_id = u.id
             WHERE o.id = :order_id AND o.user_id = :user_id";

$order = executeQuery($order_sql, [
    ':order_id' => $order_id,
    ':user_id' => $_SESSION['user_id']
])->fetch();

if (!$order) {
    header("Location: cart.php");
    exit();
}

// Get order items
$items_sql = "SELECT oi.*, p.name, p.image_url
             FROM e_order_items oi
             JOIN e_products p ON oi.product_id = p.id
             WHERE oi.order_id = :order_id";

$order_items = executeQuery($items_sql, [':order_id' => $order_id])->fetchAll();

// Handle form submission
$success = false;
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $address = $_POST['address'];
    $phone = $_POST['phone'];

    // Update user information
    $update_user_sql = "UPDATE e_users SET address = :address, phone = :phone WHERE id = :user_id";
    executeQuery($update_user_sql, [
        ':address' => $address,
        ':phone' => $phone,
        ':user_id' => $_SESSION['user_id']
    ]);

    // Process the order (in a real system, you would integrate with a payment gateway here)
    // For now, we'll just mark the order as completed
    $update_order_sql = "UPDATE e_orders SET status = 'completed', payment_status = 'paid' WHERE id = :order_id";
    executeQuery($update_order_sql, [':order_id' => $order_id]);

    // Update product stock
    foreach ($order_items as $item) {
        $update_stock_sql = "UPDATE e_products SET stock_quantity = stock_quantity - :quantity WHERE id = :product_id";
        executeQuery($update_stock_sql, [
            ':quantity' => $item['quantity'],
            ':product_id' => $item['product_id']
        ]);
    }

    $success = true;
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - Euro Spice</title>
    <link rel="stylesheet" href="../../plugins/bootstrap/css/bootstrap.min.css">
    <script defer src="../../plugins/fontawesome/js/all.js"></script>
    <style>
        .order-item {
            border-bottom: 1px solid #eee;
            padding: 10px 0;
        }
    </style>
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-light border-bottom site-header sticky-top py-1"
        style="background-color: #f15b31;">
        <div class="container">
            <a class="navbar-brand fw-bold fs-4" href="../../index.php" style="color: white;"><b>Euro Spice</b></a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent"
                aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../../index.php">
                            <i class="fas fa-home"></i> Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../account/settings.php">
                            <i class="fas fa-user-circle"></i> <?php echo htmlspecialchars($_SESSION['first_name']); ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../account/logout.php">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container py-5">
        <?php if ($success): ?>
            <div class="alert alert-success">
                <h4 class="alert-heading">Order Completed!</h4>
                <p>Your order has been successfully processed. Thank you for shopping with Euro Spice!</p>
                <hr>
                <p class="mb-0">Order #<?php echo $order_id; ?> - Total: PHP <?php echo number_format($order['total_amount'], 2); ?></p>
                <a href="../../index.php" class="btn btn-primary mt-3">Continue Shopping</a>
            </div>
        <?php else: ?>
            <h2 class="mb-4">Checkout</h2>

            <?php if ($error): ?>
                <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>

            <div class="row">
                <div class="col-lg-8">
                    <!-- Shipping Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Shipping Information</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name</label>
                                    <input type="text" class="form-control" id="name" value="<?php echo htmlspecialchars($order['customer_name']); ?>" readonly>
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" value="<?php echo htmlspecialchars($order['email']); ?>" readonly>
                                </div>

                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($order['phone'] ?? ''); ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">Delivery Address</label>
                                    <textarea class="form-control" id="address" name="address" rows="3" required><?php echo htmlspecialchars($order['address'] ?? ''); ?></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="payment" class="form-label">Payment Method</label>
                                    <select class="form-control" id="payment" required>
                                        <option value="cash">Cash on Delivery</option>
                                        <option value="card">Credit/Debit Card</option>
                                        <option value="bank">Bank Transfer</option>
                                    </select>
                                </div>

                                <button type="submit" class="btn btn-primary">Complete Order</button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Order Summary -->
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Order Summary</h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-2">Order #<?php echo $order_id; ?></p>

                            <?php foreach ($order_items as $item): ?>
                                <div class="order-item d-flex justify-content-between">
                                    <div>
                                        <p class="mb-0"><?php echo htmlspecialchars($item['name']); ?></p>
                                        <small class="text-muted"><?php echo $item['quantity']; ?> x PHP <?php echo number_format($item['price'], 2); ?></small>
                                    </div>
                                    <div>
                                        <p class="mb-0">PHP <?php echo number_format($item['price'] * $item['quantity'], 2); ?></p>
                                    </div>
                                </div>
                            <?php endforeach; ?>

                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <span>Subtotal</span>
                                <span>PHP <?php echo number_format($order['total_amount'], 2); ?></span>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>Shipping</span>
                                <span>Free</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-3 fw-bold">
                                <span>Total</span>
                                <span>PHP <?php echo number_format($order['total_amount'], 2); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="../../plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
</body>

</html>