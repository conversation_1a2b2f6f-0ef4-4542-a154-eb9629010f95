<?php
session_start();
require_once '../../config/config.php';

// Set headers for JSON response
header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

// Get form data
$product_id = isset($_POST['product_id']) ? (int)$_POST['product_id'] : 0;
$user_id = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
$quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 0;
$product_price = isset($_POST['product_price']) ? (float)$_POST['product_price'] : 0;
$total_price = isset($_POST['total_price']) ? (float)$_POST['total_price'] : 0;

// Delivery information
$delivery_from = isset($_POST['delivery_from']) ? trim($_POST['delivery_from']) : '3708 N Guevarra St., Zone 1 Dasmariñas, Cavite Philippines 4114';
$pinpoint_address = isset($_POST['pinpoint_address']) ? trim($_POST['pinpoint_address']) : '';
$delivery_address = isset($_POST['delivery_address']) ? trim($_POST['delivery_address']) : '';
$province = isset($_POST['province']) ? trim($_POST['province']) : '';
$city = isset($_POST['city']) ? trim($_POST['city']) : '';
$zip_code = isset($_POST['zip_code']) ? trim($_POST['zip_code']) : '';
$country = isset($_POST['country']) ? trim($_POST['country']) : 'Philippines';
$special_instructions = isset($_POST['special_instructions']) ? trim($_POST['special_instructions']) : '';
$driver_id = isset($_POST['driver_id']) && !empty($_POST['driver_id']) ? (int)$_POST['driver_id'] : null;

// Use the delivery address as is if it's already set
// Otherwise, combine address fields for storage
if (empty($delivery_address) && !empty($pinpoint_address)) {
    // If delivery address is empty but pinpoint is provided, create a full address
    $full_delivery_address = $pinpoint_address . "\n" . $city . ", " . $province . " " . $zip_code . "\n" . $country;
} else {
    // Use the existing delivery address
    $full_delivery_address = $delivery_address;
}

// Ensure delivery_from has the default value if not provided
if (empty($delivery_from)) {
    $delivery_from = '3708 N Guevarra St., Zone 1 Dasmariñas, Cavite Philippines 4114';
}

// Validate required fields
if ($product_id <= 0 || $user_id <= 0 || $quantity <= 0) {
    echo json_encode(['success' => false, 'message' => 'Missing required product, user, or quantity information']);
    exit();
}

// Validate address fields
if (empty($delivery_address)) {
    echo json_encode(['success' => false, 'message' => 'Delivery address is required']);
    exit();
}

// Validate location fields
if (empty($city) || empty($province) || empty($zip_code)) {
    echo json_encode(['success' => false, 'message' => 'City, province, and zip code are required']);
    exit();
}

try {
    $conn->beginTransaction();

    // First, check if the product exists in the approved table
    $product_sql = "SELECT * FROM approved WHERE id = :product_id";
    $stmt = $conn->prepare($product_sql);
    $stmt->bindValue(':product_id', $product_id, PDO::PARAM_INT);
    $stmt->execute();
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        throw new Exception('Product not found in approved table');
    }

    // Check if there's enough stock
    if ($product['stocks'] < $quantity) {
        throw new Exception('Not enough stock available');
    }

    // Check if the product exists in the products table (for foreign key constraint)
    $check_product_sql = "SELECT id FROM products WHERE id = :product_id";
    $stmt = $conn->prepare($check_product_sql);
    $stmt->bindValue(':product_id', $product_id, PDO::PARAM_INT);
    $stmt->execute();

    if ($stmt->rowCount() === 0) {
        // First, check the structure of the products table
        $check_columns_sql = "SHOW COLUMNS FROM products";
        $stmt = $conn->prepare($check_columns_sql);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // Determine which columns to include in the insert
        $product_columns = ['id'];
        $approved_columns = ['id'];

        if (in_array('name', $columns)) {
            $product_columns[] = 'name';
            $approved_columns[] = 'prod_name';
        }

        if (in_array('price', $columns) && isset($product['price'])) {
            $product_columns[] = 'price';
            $approved_columns[] = 'price';
        }

        if (in_array('description', $columns)) {
            $product_columns[] = 'description';
            $approved_columns[] = 'prod_name'; // Use prod_name as description if no better field
        }

        if (in_array('category_id', $columns) && isset($product['category_id'])) {
            $product_columns[] = 'category_id';
            $approved_columns[] = 'category_id';
        }

        // Build the SQL dynamically
        $product_columns_str = implode(', ', $product_columns);
        $approved_columns_str = implode(', ', $approved_columns);

        // Product doesn't exist in products table, we need to insert it
        $insert_product_sql = "INSERT INTO products ($product_columns_str)
                              SELECT $approved_columns_str FROM approved
                              WHERE id = :product_id";

        $stmt = $conn->prepare($insert_product_sql);
        $stmt->bindValue(':product_id', $product_id, PDO::PARAM_INT);

        try {
            $stmt->execute();

            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to create product reference in products table');
            }
        } catch (PDOException $insertError) {
            // If the insert fails, try a simpler approach with just the ID
            error_log("Failed to insert product with columns: " . $insertError->getMessage());

            $simple_insert_sql = "INSERT INTO products (id) VALUES (:product_id)";
            $stmt = $conn->prepare($simple_insert_sql);
            $stmt->bindValue(':product_id', $product_id, PDO::PARAM_INT);
            $stmt->execute();

            if ($stmt->rowCount() === 0) {
                throw new Exception('Failed to create product reference in products table');
            }
        }
    }

    // Create a new order request
    $order_sql = "INSERT INTO order_requests (
                    user_id,
                    product_id,
                    quantity,
                    unit_price,
                    total_price,
                    delivery_from,
                    delivery_address,
                    special_instructions,
                    status,
                    driver_id
                ) VALUES (
                    :user_id,
                    :product_id,
                    :quantity,
                    :unit_price,
                    :total_price,
                    :delivery_from,
                    :delivery_address,
                    :special_instructions,
                    'processing',
                    :driver_id
                )";

    $stmt = $conn->prepare($order_sql);
    $stmt->bindValue(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->bindValue(':product_id', $product_id, PDO::PARAM_INT);
    $stmt->bindValue(':quantity', $quantity, PDO::PARAM_INT);
    $stmt->bindValue(':unit_price', $product_price, PDO::PARAM_STR);
    $stmt->bindValue(':total_price', $total_price, PDO::PARAM_STR);
    $stmt->bindValue(':delivery_from', $delivery_from, PDO::PARAM_STR);
    $stmt->bindValue(':delivery_address', $full_delivery_address, PDO::PARAM_STR);
    $stmt->bindValue(':special_instructions', $special_instructions, PDO::PARAM_STR);
    $stmt->bindValue(':driver_id', $driver_id, $driver_id ? PDO::PARAM_INT : PDO::PARAM_NULL);
    $stmt->execute();

    $order_id = $conn->lastInsertId();

    // Update the stock in the approved table
    $update_stock_sql = "UPDATE approved
                         SET stocks = stocks - :quantity
                         WHERE id = :product_id";
    $stmt = $conn->prepare($update_stock_sql);
    $stmt->bindValue(':quantity', $quantity, PDO::PARAM_INT);
    $stmt->bindValue(':product_id', $product_id, PDO::PARAM_INT);
    $stmt->execute();

    // If a driver was assigned, update their availability
    if ($driver_id) {
        $update_driver_sql = "UPDATE drivers
                             SET availability = 'busy'
                             WHERE id = :driver_id";
        $stmt = $conn->prepare($update_driver_sql);
        $stmt->bindValue(':driver_id', $driver_id, PDO::PARAM_INT);
        $stmt->execute();
    }

    $conn->commit();

    // Sanitize the response to prevent JSON issues
    $response = [
        'success' => true,
        'message' => 'Delivery created successfully',
        'order_id' => $order_id
    ];

    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
} catch (PDOException $e) {
    $conn->rollBack();
    error_log("Database error in create_delivery.php: " . $e->getMessage());

    // Check for foreign key constraint violation
    if (strpos($e->getMessage(), 'CONSTRAINT `order_requests_ibfk_2`') !== false) {
        // This is the specific foreign key constraint error
        echo json_encode([
            'success' => false,
            'message' => 'The product reference is invalid. Please contact the administrator.'
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    } else {
        // Generic database error
        echo json_encode([
            'success' => false,
            'message' => 'A database error occurred. Please try again later.'
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
} catch (Exception $e) {
    $conn->rollBack();
    error_log("Error in create_delivery.php: " . $e->getMessage());

    // Sanitize the error message
    $errorMessage = preg_replace('/[\x00-\x1F\x7F]/u', '', $e->getMessage());

    echo json_encode([
        'success' => false,
        'message' => $errorMessage
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}
