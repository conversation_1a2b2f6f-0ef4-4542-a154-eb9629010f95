<?php
// Create placeholder images for missing files

// Function to create a simple placeholder image
function createPlaceholderImage($filename, $text = 'Placeholder', $width = 200, $height = 200)
{
    // Create image
    $image = imagecreatetruecolor($width, $height);
    
    // Colors
    $bg_color = imagecolorallocate($image, 240, 240, 240);
    $text_color = imagecolorallocate($image, 50, 50, 50);
    $border_color = imagecolorallocate($image, 200, 200, 200);
    
    // Fill background
    imagefill($image, 0, 0, $bg_color);
    
    // Draw border
    imagerectangle($image, 0, 0, $width - 1, $height - 1, $border_color);
    
    // Add text
    $font_size = 5;
    $text_width = imagefontwidth($font_size) * strlen($text);
    $text_height = imagefontheight($font_size);
    
    $x = ($width - $text_width) / 2;
    $y = ($height - $text_height) / 2;
    
    imagestring($image, $font_size, $x, $y, $text, $text_color);
    
    // Save image
    imagepng($image, $filename);
    imagedestroy($image);
    
    echo "Created placeholder image: $filename<br>";
}

// Create uploads directory if it doesn't exist
if (!file_exists('uploads')) {
    mkdir('uploads', 0777, true);
    echo "Created uploads directory<br>";
}

// Create placeholder images
createPlaceholderImage('eurospice-favicon.png', 'Favicon', 32, 32);
createPlaceholderImage('eurospice-logo.png', 'Logo', 200, 80);
createPlaceholderImage('eurospice-grid.png', 'Grid', 400, 300);

echo "<p>All placeholder images created successfully!</p>";
echo "<p><a href='app/modules/inventory/inventory.php'>Go to Inventory Page</a></p>";
?>
