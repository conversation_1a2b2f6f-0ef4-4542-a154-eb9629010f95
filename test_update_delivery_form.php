<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Update Delivery</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test Update Delivery</h1>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Test Form</h5>
            </div>
            <div class="card-body">
                <form id="updateForm">
                    <div class="mb-3">
                        <label for="order_id" class="form-label">Order ID</label>
                        <input type="number" class="form-control" id="order_id" name="order_id" value="1" required>
                    </div>
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-control" id="status" name="status" required>
                            <option value="processing">Processing</option>
                            <option value="in_transit">In Transit</option>
                            <option value="delivered" selected>Delivered</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes">Delivered successfully via test form</textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Update Delivery</button>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Response</h5>
            </div>
            <div class="card-body">
                <pre id="response" class="bg-light p-3">Response will appear here...</pre>
            </div>
        </div>
        
        <div class="mt-3">
            <a href="app/modules/inventory/inventory.php" class="btn btn-info">View Inventory</a>
            <a href="app/modules/logistics/logistics_management.php" class="btn btn-secondary">Go to Logistics Management</a>
        </div>
    </div>
    
    <script>
        document.getElementById('updateForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const urlParams = new URLSearchParams();
            
            for (const pair of formData.entries()) {
                urlParams.append(pair[0], pair[1]);
            }
            
            fetch('app/modules/logistics/simple_update_delivery.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: urlParams.toString()
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('response').textContent = JSON.stringify(data, null, 2);
                
                // Show success message
                const alert = document.createElement('div');
                alert.className = 'alert alert-success mt-3';
                alert.innerHTML = `<strong>Success!</strong> ${data.message}`;
                document.querySelector('.container').insertBefore(alert, document.querySelector('.card'));
                
                // Remove alert after 5 seconds
                setTimeout(() => {
                    alert.remove();
                }, 5000);
            })
            .catch(error => {
                document.getElementById('response').textContent = 'Error: ' + error.message;
                
                // Show error message
                const alert = document.createElement('div');
                alert.className = 'alert alert-danger mt-3';
                alert.innerHTML = `<strong>Error!</strong> ${error.message}`;
                document.querySelector('.container').insertBefore(alert, document.querySelector('.card'));
                
                // Remove alert after 5 seconds
                setTimeout(() => {
                    alert.remove();
                }, 5000);
            });
        });
    </script>
</body>
</html>
