<?php
require_once '../../config/config.php';

echo "<h1>Diagnostic Information</h1>";

// Check if approved table exists
try {
    $check_table = $conn->query("SHOW TABLES LIKE 'approved'");
    if ($check_table->rowCount() > 0) {
        echo "<p>✅ Approved table exists</p>";
    } else {
        echo "<p>❌ Approved table does not exist</p>";
    }
} catch (PDOException $e) {
    echo "<p>❌ Error checking approved table: " . $e->getMessage() . "</p>";
}

// Check if there's data in the approved table
try {
    $count_query = "SELECT COUNT(*) FROM approved";
    $count = $conn->query($count_query)->fetchColumn();
    echo "<p>Number of records in approved table: <strong>{$count}</strong></p>";

    if ($count > 0) {
        echo "<h2>Sample data from approved table:</h2>";
        $sample_query = "SELECT * FROM approved LIMIT 5";
        $samples = $conn->query($sample_query)->fetchAll(PDO::FETCH_ASSOC);

        echo "<table border='1' cellpadding='5'>";
        echo "<tr>";
        foreach (array_keys($samples[0]) as $column) {
            echo "<th>{$column}</th>";
        }
        echo "</tr>";

        foreach ($samples as $row) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No data found in approved table</p>";
    }
} catch (PDOException $e) {
    echo "<p>❌ Error checking approved data: " . $e->getMessage() . "</p>";
}

// Check if order_requests table exists
try {
    $check_table = $conn->query("SHOW TABLES LIKE 'order_requests'");
    if ($check_table->rowCount() > 0) {
        echo "<p>✅ order_requests table exists</p>";
    } else {
        echo "<p>❌ order_requests table does not exist</p>";
    }
} catch (PDOException $e) {
    echo "<p>❌ Error checking order_requests table: " . $e->getMessage() . "</p>";
}

// Check if there's data in the order_requests table
try {
    $count_query = "SELECT COUNT(*) FROM order_requests";
    $count = $conn->query($count_query)->fetchColumn();
    echo "<p>Number of records in order_requests table: <strong>{$count}</strong></p>";
} catch (PDOException $e) {
    echo "<p>❌ Error checking order_requests data: " . $e->getMessage() . "</p>";
}

// Test the UNION query
try {
    echo "<h2>Testing the UNION query:</h2>";

    // First, get all products from the approved table
    $approved_sql = "SELECT
        a.id as approved_id,
        a.prod_name,
        a.brand_name,
        a.price,
        a.prod_measure,
        a.pack_type,
        a.batch_code,
        a.stocks,
        a.country,
        NULL as order_id,
        NULL as status,
        NULL as delivery_time,
        NULL as customer_name
        FROM approved a
        WHERE a.id NOT IN (
            SELECT DISTINCT IFNULL(orq.product_id, 0)
            FROM order_requests orq
            WHERE orq.product_id IS NOT NULL
        )";

    // Then, get all existing deliveries with their associated approved products
    $deliveries_sql = "SELECT
        orq.id as order_id,
        orq.status,
        orq.delivery_time,
        CONCAT(u.first_name, ' ', u.last_name) as customer_name,
        COALESCE(p.prod_name, a.prod_name) as prod_name,
        a.id as approved_id,
        a.brand_name,
        a.price,
        a.prod_measure,
        a.pack_type,
        a.batch_code,
        a.stocks,
        a.country
        FROM order_requests orq
        JOIN users u ON orq.user_id = u.id
        LEFT JOIN products p ON orq.product_id = p.id
        LEFT JOIN approved a ON a.prod_name = p.prod_name OR a.id = orq.product_id";

    // Test the approved query first
    $approved_count = $conn->query($approved_sql)->rowCount();
    echo "<p>Number of records from approved query: <strong>{$approved_count}</strong></p>";

    // Test if the deliveries query works
    try {
        $deliveries_count = $conn->query($deliveries_sql)->rowCount();
        echo "<p>Number of records from deliveries query: <strong>{$deliveries_count}</strong></p>";
    } catch (PDOException $e) {
        echo "<p>❌ Error in deliveries query: " . $e->getMessage() . "</p>";
        echo "<p>This might be because the products table or users table doesn't exist or has no data.</p>";
    }

    // Try the full UNION query
    try {
        $sql = "($approved_sql) UNION ($deliveries_sql) ORDER BY approved_id DESC";
        $union_count = $conn->query($sql)->rowCount();
        echo "<p>Number of records from UNION query: <strong>{$union_count}</strong></p>";
    } catch (PDOException $e) {
        echo "<p>❌ Error in UNION query: " . $e->getMessage() . "</p>";
    }
} catch (PDOException $e) {
    echo "<p>❌ Error testing queries: " . $e->getMessage() . "</p>";
}
