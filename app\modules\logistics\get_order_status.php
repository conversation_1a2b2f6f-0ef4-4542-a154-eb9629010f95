<?php
session_start();
require_once '../../config/config.php';

// Set the content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Get order ID from request
$order_id = isset($_GET['order_id']) ? (int)$_GET['order_id'] : 0;

// Validate input
if (!$order_id) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Missing order ID']);
    exit();
}

try {
    // Check if order_requests table exists
    $table_exists = $conn->query("SHOW TABLES LIKE 'order_requests'")->rowCount() > 0;

    if ($table_exists) {
        // Get order status
        $sql = "SELECT id, status, created_at, pickup_time, delivery_time FROM order_requests WHERE id = :order_id";
        $stmt = $conn->prepare($sql);
        $stmt->bindValue(':order_id', $order_id, PDO::PARAM_INT);
        $stmt->execute();
        $order = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($order) {
            echo json_encode([
                'success' => true,
                'order_id' => $order['id'],
                'status' => $order['status'],
                'created_at' => $order['created_at'],
                'pickup_time' => $order['pickup_time'],
                'delivery_time' => $order['delivery_time']
            ]);
        } else {
            // Return default status for demo
            echo json_encode([
                'success' => true,
                'order_id' => $order_id,
                'status' => 'processing',
                'message' => 'Order not found, returning default status'
            ]);
        }
    } else {
        // Return default status for demo
        echo json_encode([
            'success' => true,
            'order_id' => $order_id,
            'status' => 'processing',
            'message' => 'Order requests table not found, returning default status'
        ]);
    }
} catch (PDOException $e) {
    error_log("Error in get_order_status.php: " . $e->getMessage());

    // Return default status for demo
    echo json_encode([
        'success' => true,
        'order_id' => $order_id,
        'status' => 'processing',
        'message' => 'Database error, returning default status',
        'error' => $e->getMessage()
    ]);
}
