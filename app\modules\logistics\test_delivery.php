<?php
session_start();
require_once '../../config/config.php';

// Set a user ID for testing if not logged in
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'admin';
}

// Create a test order if it doesn't exist
$order_id = 999; // Use a specific ID for testing
$test_order_exists = false;

try {
    // Check if order_requests table exists
    $table_exists = $conn->query("SHOW TABLES LIKE 'order_requests'")->rowCount() > 0;

    if ($table_exists) {
        // Check if test order exists
        $check_sql = "SELECT id FROM order_requests WHERE id = :order_id";
        $stmt = $conn->prepare($check_sql);
        $stmt->bindValue(':order_id', $order_id, PDO::PARAM_INT);
        $stmt->execute();
        $test_order_exists = $stmt->rowCount() > 0;

        if (!$test_order_exists) {
            // Create test order
            $insert_sql = "INSERT INTO order_requests (id, user_id, product_id, quantity, delivery_address, status, created_at) 
                          VALUES (:id, 1, 1, 5, 'Test Address, Manila, Philippines', 'processing', NOW())";
            $stmt = $conn->prepare($insert_sql);
            $stmt->bindValue(':id', $order_id, PDO::PARAM_INT);
            $stmt->execute();
            $test_order_exists = true;
        }
    }
} catch (PDOException $e) {
    // Ignore errors for testing
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Delivery Status Updates</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
    <style>
        .status-item {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
        }

        .status-processing {
            background-color: #f8f9fa;
            border-left: 5px solid #17a2b8;
        }

        .status-in_transit {
            background-color: #fff3cd;
            border-left: 5px solid #ffc107;
        }

        .status-delivered {
            background-color: #d1e7dd;
            border-left: 5px solid #28a745;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <h1>Test Delivery Status Updates</h1>

        <div class="alert alert-info">
            This page is for testing the delivery status update functionality.
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Order #<?php echo $order_id; ?></h5>
                    </div>
                    <div class="card-body">
                        <h6>Current Status: <span id="currentStatus">Loading...</span></h6>

                        <div class="mt-3">
                            <div id="processingStatus" class="status-item status-processing">
                                <h6>Processing</h6>
                                <p class="mb-0">Order is being processed</p>
                            </div>

                            <div id="inTransitStatus" class="status-item">
                                <h6>In Transit</h6>
                                <p class="mb-0">Order is on the way</p>
                            </div>

                            <div id="deliveredStatus" class="status-item">
                                <h6>Delivered</h6>
                                <p class="mb-0">Order has been delivered</p>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button id="startDeliveryBtn" class="btn btn-primary">Start Delivery (In Transit)</button>
                            <button id="completeDeliveryBtn" class="btn btn-success ms-2">Complete Delivery</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Response Log</h5>
                    </div>
                    <div class="card-body">
                        <pre id="responseLog" class="bg-light p-3" style="max-height: 300px; overflow-y: auto;"></pre>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <a href="track_delivery.php?order_id=<?php echo $order_id; ?>" class="btn btn-info">Go to Track Delivery Page</a>
        </div>
    </div>

    <script>
        // Function to update the UI based on status
        function updateStatusUI(status) {
            document.getElementById('currentStatus').textContent = status;

            // Reset all status items
            document.getElementById('processingStatus').className = 'status-item';
            document.getElementById('inTransitStatus').className = 'status-item';
            document.getElementById('deliveredStatus').className = 'status-item';

            // Update based on current status
            if (status === 'processing') {
                document.getElementById('processingStatus').className = 'status-item status-processing';
            } else if (status === 'in_transit') {
                document.getElementById('processingStatus').className = 'status-item status-processing';
                document.getElementById('inTransitStatus').className = 'status-item status-in_transit';
            } else if (status === 'delivered') {
                document.getElementById('processingStatus').className = 'status-item status-processing';
                document.getElementById('inTransitStatus').className = 'status-item status-in_transit';
                document.getElementById('deliveredStatus').className = 'status-item status-delivered';
            }
        }

        // Function to log responses
        function logResponse(action, response) {
            const log = document.getElementById('responseLog');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${action}:\n${JSON.stringify(response, null, 2)}\n\n`;
            log.scrollTop = log.scrollHeight;
        }

        // Function to get current status
        function getCurrentStatus() {
            fetch(`get_order_status.php?order_id=<?php echo $order_id; ?>`)
                .then(response => response.json())
                .then(data => {
                    updateStatusUI(data.status || 'processing');
                    logResponse('Get Status', data);
                })
                .catch(error => {
                    console.error('Error getting status:', error);
                    document.getElementById('currentStatus').textContent = 'Error';
                });
        }

        // Start delivery button
        document.getElementById('startDeliveryBtn').addEventListener('click', function() {
            fetch('update_order_status.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `order_id=<?php echo $order_id; ?>&status=in_transit`
                })
                .then(response => response.json())
                .then(data => {
                    updateStatusUI('in_transit');
                    logResponse('Start Delivery', data);
                })
                .catch(error => {
                    console.error('Error starting delivery:', error);
                });
        });

        // Complete delivery button
        document.getElementById('completeDeliveryBtn').addEventListener('click', function() {
            fetch('update_order_status.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `order_id=<?php echo $order_id; ?>&status=delivered`
                })
                .then(response => response.json())
                .then(data => {
                    updateStatusUI('delivered');
                    logResponse('Update Status', data);

                    // Also call update_delivery_status.php to add to inventory
                    return fetch('update_delivery_status.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `order_id=<?php echo $order_id; ?>&status=delivered&notes=Test delivery completed`
                    });
                })
                .then(response => response.json())
                .then(data => {
                    logResponse('Complete Delivery', data);
                })
                .catch(error => {
                    console.error('Error completing delivery:', error);
                });
        });

        // Get initial status
        getCurrentStatus();
    </script>
</body>

</html>