<?php

/**
 * Philippine Locations API Proxy
 * 
 * This file serves as a proxy for Philippine location data (regions, provinces, cities)
 * It provides cached data to avoid external API calls and ensure reliability
 */

// Set headers for JSON response
header('Content-Type: application/json');
header('Cache-Control: max-age=86400'); // Cache for 24 hours

// Get the request type
$type = isset($_GET['type']) ? $_GET['type'] : '';
$code = isset($_GET['code']) ? $_GET['code'] : '';

// Validate input
if (!in_array($type, ['regions', 'provinces', 'cities'])) {
    echo json_encode(['error' => 'Invalid request type. Use regions, provinces, or cities.']);
    exit;
}

// Return data based on request type
switch ($type) {
    case 'regions':
        echo json_encode(getRegions());
        break;
    case 'provinces':
        echo json_encode(getProvinces($code));
        break;
    case 'cities':
        echo json_encode(getCities($code));
        break;
}

/**
 * Get regions data
 */
function getRegions()
{
    return [
        ['code' => 'NCR', 'name' => 'National Capital Region (NCR)'],
        ['code' => 'CAR', 'name' => 'Cordillera Administrative Region (CAR)'],
        ['code' => 'R1', 'name' => 'Ilocos Region (Region I)'],
        ['code' => 'R2', 'name' => 'Cagayan Valley (Region II)'],
        ['code' => 'R3', 'name' => 'Central Luzon (Region III)'],
        ['code' => 'R4A', 'name' => 'CALABARZON (Region IV-A)'],
        ['code' => 'R4B', 'name' => 'MIMAROPA (Region IV-B)'],
        ['code' => 'R5', 'name' => 'Bicol Region (Region V)'],
        ['code' => 'R6', 'name' => 'Western Visayas (Region VI)'],
        ['code' => 'R7', 'name' => 'Central Visayas (Region VII)'],
        ['code' => 'R8', 'name' => 'Eastern Visayas (Region VIII)'],
        ['code' => 'R9', 'name' => 'Zamboanga Peninsula (Region IX)'],
        ['code' => 'R10', 'name' => 'Northern Mindanao (Region X)'],
        ['code' => 'R11', 'name' => 'Davao Region (Region XI)'],
        ['code' => 'R12', 'name' => 'SOCCSKSARGEN (Region XII)'],
        ['code' => 'R13', 'name' => 'Caraga (Region XIII)'],
        ['code' => 'BARMM', 'name' => 'Bangsamoro Autonomous Region in Muslim Mindanao (BARMM)']
    ];
}

/**
 * Get provinces data
 */
function getProvinces($regionCode = '')
{
    $provinces = [
        // NCR (technically not provinces but districts)
        'NCR' => [
            ['code' => 'NCR-1', 'name' => 'Metro Manila'],
        ],
        // CALABARZON (Region IV-A)
        'R4A' => [
            ['code' => 'CAV', 'name' => 'Cavite'],
            ['code' => 'LAG', 'name' => 'Laguna'],
            ['code' => 'BAT', 'name' => 'Batangas'],
            ['code' => 'RIZ', 'name' => 'Rizal'],
            ['code' => 'QUE', 'name' => 'Quezon']
        ],
        // Central Luzon (Region III)
        'R3' => [
            ['code' => 'BUL', 'name' => 'Bulacan'],
            ['code' => 'PAM', 'name' => 'Pampanga'],
            ['code' => 'TAR', 'name' => 'Tarlac'],
            ['code' => 'ZAM', 'name' => 'Zambales'],
            ['code' => 'BAT2', 'name' => 'Bataan'],
            ['code' => 'NUE', 'name' => 'Nueva Ecija'],
            ['code' => 'AUR', 'name' => 'Aurora']
        ],
        // Default - return some provinces if region not specified
        'default' => [
            ['code' => 'CAV', 'name' => 'Cavite'],
            ['code' => 'LAG', 'name' => 'Laguna'],
            ['code' => 'RIZ', 'name' => 'Rizal'],
            ['code' => 'NCR-1', 'name' => 'Metro Manila'],
            ['code' => 'BUL', 'name' => 'Bulacan'],
            ['code' => 'BAT', 'name' => 'Batangas']
        ]
    ];

    // Return provinces for the specified region or default list
    if (!empty($regionCode) && isset($provinces[$regionCode])) {
        return $provinces[$regionCode];
    }

    return $provinces['default'];
}

/**
 * Get cities data
 */
function getCities($provinceCode = '')
{
    $cities = [
        // Cavite
        'CAV' => [
            'Alfonso',
            'Amadeo',
            'Bacoor',
            'Carmona',
            'Cavite City',
            'Dasmariñas',
            'General Emilio Aguinaldo',
            'General Mariano Alvarez',
            'General Trias',
            'Imus',
            'Indang',
            'Kawit',
            'Magallanes',
            'Maragondon',
            'Mendez',
            'Naic',
            'Noveleta',
            'Rosario',
            'Silang',
            'Tagaytay',
            'Tanza',
            'Ternate',
            'Trece Martires'
        ],
        // Laguna
        'LAG' => [
            'Alaminos',
            'Bay',
            'Biñan',
            'Cabuyao',
            'Calamba',
            'Calauan',
            'Cavinti',
            'Famy',
            'Kalayaan',
            'Liliw',
            'Los Baños',
            'Luisiana',
            'Lumban',
            'Mabitac',
            'Magdalena',
            'Majayjay',
            'Nagcarlan',
            'Paete',
            'Pagsanjan',
            'Pakil',
            'Pangil',
            'Pila',
            'Rizal',
            'San Pablo',
            'San Pedro',
            'Santa Cruz',
            'Santa Maria',
            'Santa Rosa',
            'Siniloan',
            'Victoria'
        ],
        // Rizal
        'RIZ' => [
            'Angono',
            'Antipolo',
            'Baras',
            'Binangonan',
            'Cainta',
            'Cardona',
            'Jalajala',
            'Morong',
            'Pililla',
            'Rodriguez',
            'San Mateo',
            'Tanay',
            'Taytay',
            'Teresa'
        ],
        // Metro Manila
        'NCR-1' => [
            'Caloocan',
            'Las Piñas',
            'Makati',
            'Malabon',
            'Mandaluyong',
            'Manila',
            'Marikina',
            'Muntinlupa',
            'Navotas',
            'Parañaque',
            'Pasay',
            'Pasig',
            'Pateros',
            'Quezon City',
            'San Juan',
            'Taguig',
            'Valenzuela'
        ],
        // Batangas
        'BAT' => [
            'Agoncillo',
            'Alitagtag',
            'Balayan',
            'Balete',
            'Batangas City',
            'Bauan',
            'Calaca',
            'Calatagan',
            'Cuenca',
            'Ibaan',
            'Laurel',
            'Lemery',
            'Lian',
            'Lipa',
            'Lobo',
            'Mabini',
            'Malvar',
            'Mataas na Kahoy',
            'Nasugbu',
            'Padre Garcia',
            'Rosario',
            'San Jose',
            'San Juan',
            'San Luis',
            'San Nicolas',
            'San Pascual',
            'Santa Teresita',
            'Santo Tomas',
            'Taal',
            'Talisay',
            'Tanauan',
            'Taysan',
            'Tingloy',
            'Tuy'
        ],
        // Bulacan
        'BUL' => [
            'Angat',
            'Balagtas',
            'Baliuag',
            'Bocaue',
            'Bulakan',
            'Bustos',
            'Calumpit',
            'Doña Remedios Trinidad',
            'Guiguinto',
            'Hagonoy',
            'Malolos',
            'Marilao',
            'Meycauayan',
            'Norzagaray',
            'Obando',
            'Pandi',
            'Paombong',
            'Plaridel',
            'Pulilan',
            'San Ildefonso',
            'San Jose del Monte',
            'San Miguel',
            'San Rafael',
            'Santa Maria'
        ],
        // Default cities
        'default' => [
            'Manila',
            'Quezon City',
            'Makati',
            'Taguig',
            'Pasig',
            'Dasmariñas',
            'Bacoor',
            'Imus',
            'Calamba',
            'Santa Rosa'
        ]
    ];

    // Return cities for the specified province or default list
    if (!empty($provinceCode) && isset($cities[$provinceCode])) {
        return $cities[$provinceCode];
    }

    return $cities['default'];
}
