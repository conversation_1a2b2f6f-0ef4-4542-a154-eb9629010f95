<?php
// This script will update the hub coordinates for the default address
// 3708 N Guevarra St., Zone 1 Dasmariñas, Cavite Philippines 4114

// OpenRouteService API key
$orsApiKey = '5b3ce3597851110001cf6248001793d85d6646c392987d47f8b6a2f7';

// Address to geocode
$address = '3708 N Guevarra St., Zone 1 Dasmariñas, Cavite Philippines 4114';

// URL encode the address
$encodedAddress = urlencode($address);

// Build the geocoding API URL
$url = "https://api.openrouteservice.org/geocode/search?api_key={$orsApiKey}&text={$encodedAddress}";

// Make the API request
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
$response = curl_exec($ch);
curl_close($ch);

// Parse the response
$data = json_decode($response, true);

// Check if we got coordinates
if (isset($data['features']) && count($data['features']) > 0) {
    // Get the first result
    $feature = $data['features'][0];
    $coordinates = $feature['geometry']['coordinates'];

    // OpenRouteService returns [longitude, latitude]
    $longitude = $coordinates[0];
    $latitude = $coordinates[1];

    echo "<h1>Geocoding Results</h1>";
    echo "<p><strong>Address:</strong> {$address}</p>";
    echo "<p><strong>Coordinates:</strong> [{$latitude}, {$longitude}]</p>";

    // Instructions for updating the coordinates in logistics_management.php
    echo "<h2>Update Instructions</h2>";
    echo "<p>Replace the hubLocation line in logistics_management.php with:</p>";
    echo "<pre>const hubLocation = [{$latitude}, {$longitude}]; // 3708 N Guevarra St., Zone 1 Dasmariñas, Cavite</pre>";

    // Show a map with the location
    echo "<h2>Map Preview</h2>";
    echo "<div id='map' style='height: 400px;'></div>";
    echo "<script src='https://unpkg.com/leaflet@1.7.1/dist/leaflet.js'></script>";
    echo "<link rel='stylesheet' href='https://unpkg.com/leaflet@1.7.1/dist/leaflet.css' />";
    echo "<script>
        document.addEventListener('DOMContentLoaded', function() {
            const map = L.map('map').setView([{$latitude}, {$longitude}], 15);
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);
            L.marker([{$latitude}, {$longitude}])
                .bindPopup('{$address}')
                .addTo(map)
                .openPopup();
        });
    </script>";
} else {
    echo "<h1>Geocoding Failed</h1>";
    echo "<p>Could not geocode the address: {$address}</p>";
    echo "<p>Response:</p>";
    echo "<pre>" . print_r($data, true) . "</pre>";
}
