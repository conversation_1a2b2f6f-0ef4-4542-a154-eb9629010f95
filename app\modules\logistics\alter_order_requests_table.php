<?php
require_once '../../config/config.php';

echo "<h1>Altering order_requests Table</h1>";

try {
    // Check if the columns already exist
    $checkColumns = $conn->query("SHOW COLUMNS FROM order_requests LIKE 'unit_price'");
    $unitPriceExists = $checkColumns->rowCount() > 0;

    $checkColumns = $conn->query("SHOW COLUMNS FROM order_requests LIKE 'total_price'");
    $totalPriceExists = $checkColumns->rowCount() > 0;

    $checkColumns = $conn->query("SHOW COLUMNS FROM order_requests LIKE 'delivery_from'");
    $deliveryFromExists = $checkColumns->rowCount() > 0;

    // Add unit_price column if it doesn't exist
    if (!$unitPriceExists) {
        $conn->exec("ALTER TABLE order_requests ADD COLUMN unit_price DECIMAL(10,2) DEFAULT NULL AFTER quantity");
        echo "<p>✅ Added unit_price column to order_requests table</p>";
    } else {
        echo "<p>ℹ️ unit_price column already exists</p>";
    }

    // Add total_price column if it doesn't exist
    if (!$totalPriceExists) {
        $conn->exec("ALTER TABLE order_requests ADD COLUMN total_price DECIMAL(10,2) DEFAULT NULL AFTER unit_price");
        echo "<p>✅ Added total_price column to order_requests table</p>";
    } else {
        echo "<p>ℹ️ total_price column already exists</p>";
    }

    // Add delivery_from column if it doesn't exist
    if (!$deliveryFromExists) {
        $conn->exec("ALTER TABLE order_requests ADD COLUMN delivery_from VARCHAR(255) DEFAULT '3708 N Guevarra St., Zone 1 Dasmariñas, Cavite Philippines 4114' AFTER delivery_address");
        echo "<p>✅ Added delivery_from column to order_requests table</p>";
    } else {
        echo "<p>ℹ️ delivery_from column already exists</p>";
    }

    // Update existing records to calculate total_price based on quantity and product price
    if ($unitPriceExists && $totalPriceExists) {
        // For records with products table
        $conn->exec("
            UPDATE order_requests orq
            JOIN products p ON orq.product_id = p.id
            SET 
                orq.unit_price = p.price,
                orq.total_price = orq.quantity * p.price
            WHERE 
                orq.unit_price IS NULL OR orq.total_price IS NULL
        ");

        // For records with approved table
        $conn->exec("
            UPDATE order_requests orq
            JOIN approved a ON orq.product_id = a.id
            SET 
                orq.unit_price = a.price,
                orq.total_price = orq.quantity * a.price
            WHERE 
                (orq.unit_price IS NULL OR orq.total_price IS NULL)
                AND orq.product_id IN (SELECT id FROM approved)
        ");

        echo "<p>✅ Updated existing records with price information</p>";
    }

    // Set default delivery_from for existing records
    if ($deliveryFromExists) {
        $conn->exec("
            UPDATE order_requests 
            SET delivery_from = '3708 N Guevarra St., Zone 1 Dasmariñas, Cavite Philippines 4114'
            WHERE delivery_from IS NULL OR delivery_from = ''
        ");
        echo "<p>✅ Updated existing records with default delivery_from address</p>";
    }

    echo "<p>✅ Table alteration completed successfully!</p>";
    echo "<p><a href='logistics_management.php' class='btn btn-primary'>Go to Logistics Management</a></p>";
} catch (PDOException $e) {
    echo "<p>❌ Error altering table: " . $e->getMessage() . "</p>";
}
