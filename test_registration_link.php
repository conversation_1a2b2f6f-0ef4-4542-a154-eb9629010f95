<?php
// Test script to check if the Create New Account button works correctly

// Function to make a GET request with cookies
function get_request($url, $cookies = []) {
    $options = [
        'http' => [
            'method' => 'GET'
        ]
    ];
    
    // Add cookies if provided
    if (!empty($cookies)) {
        $cookie_string = '';
        foreach ($cookies as $key => $value) {
            $cookie_string .= "$key=$value; ";
        }
        $options['http']['header'] = "Cookie: " . trim($cookie_string) . "\r\n";
    }
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    // Get response headers
    $response_headers = $http_response_header ?? [];
    
    // Check for redirect
    $redirect_url = null;
    foreach ($response_headers as $header) {
        if (preg_match('/^Location: (.*)$/i', $header, $matches)) {
            $redirect_url = trim($matches[1]);
            break;
        }
    }
    
    // Extract cookies from response
    $cookies_out = [];
    foreach ($response_headers as $header) {
        if (preg_match('/^Set-Cookie: (.*?)=(.*?);/i', $header, $matches)) {
            $cookies_out[$matches[1]] = $matches[2];
        }
    }
    
    return [
        'content' => $result,
        'headers' => $response_headers,
        'redirect' => $redirect_url,
        'cookies' => $cookies_out
    ];
}

// Function to login and get session cookies
function login($url, $username, $password) {
    // First get the login page to get cookies
    $login_response = get_request($url);
    $cookies = $login_response['cookies'];
    
    // Prepare login data
    $login_data = [
        'username' => $username,
        'password' => $password
    ];
    
    // Create POST request
    $options = [
        'http' => [
            'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
            'method'  => 'POST',
            'content' => http_build_query($login_data)
        ]
    ];
    
    // Add cookies if provided
    if (!empty($cookies)) {
        $cookie_string = '';
        foreach ($cookies as $key => $value) {
            $cookie_string .= "$key=$value; ";
        }
        $options['http']['header'] .= "Cookie: " . trim($cookie_string) . "\r\n";
    }
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    // Get response headers
    $response_headers = $http_response_header ?? [];
    
    // Check for redirect
    $redirect_url = null;
    foreach ($response_headers as $header) {
        if (preg_match('/^Location: (.*)$/i', $header, $matches)) {
            $redirect_url = trim($matches[1]);
            break;
        }
    }
    
    // Extract cookies from response
    $cookies_out = array_merge($cookies, []);
    foreach ($response_headers as $header) {
        if (preg_match('/^Set-Cookie: (.*?)=(.*?);/i', $header, $matches)) {
            $cookies_out[$matches[1]] = $matches[2];
        }
    }
    
    return [
        'content' => $result,
        'headers' => $response_headers,
        'redirect' => $redirect_url,
        'cookies' => $cookies_out
    ];
}

// Start the test
echo "Testing registration link functionality...\n";

// Step 1: Login to get a session
$login_url = 'http://localhost/finance/ecommerce_complete/pages/account/login.php';
$login_response = login($login_url, 'admin', 'admin123');
$cookies = $login_response['cookies'];

echo "Logged in, got cookies: " . json_encode($cookies) . "\n";

// Step 2: Try to access the login page with force parameter
echo "\nTest 1: Access login page with force parameter\n";
$force_login_url = 'http://localhost/finance/ecommerce_complete/pages/account/login.php?force=1';
$force_login_response = get_request($force_login_url, $cookies);

// Check if the response contains the Create New Account button
$login_content = $force_login_response['content'];
$has_register_link = strpos($login_content, 'register.php?force=1') !== false;

if ($has_register_link) {
    echo "Create New Account button with force parameter found as expected.\n";
} else {
    echo "Create New Account button with force parameter not found. This is unexpected.\n";
    
    // Check if there's any register.php link
    $has_any_register_link = strpos($login_content, 'register.php') !== false;
    if ($has_any_register_link) {
        echo "Found a register.php link but without the force parameter.\n";
    } else {
        echo "No register.php link found at all.\n";
    }
}

// Step 3: Try to access the registration page with force parameter
echo "\nTest 2: Access registration page with force parameter\n";
$force_register_url = 'http://localhost/finance/ecommerce_complete/pages/account/register.php?force=1';
$force_register_response = get_request($force_register_url, $cookies);

// Check if the response contains the registration form
$register_content = $force_register_response['content'];
$has_register_form = strpos($register_content, '<form method="POST">') !== false;

if ($has_register_form) {
    echo "Registration form displayed as expected.\n";
    
    // Check if the login link has the force parameter
    $has_login_link_with_force = strpos($register_content, 'login.php?force=1') !== false;
    if ($has_login_link_with_force) {
        echo "Already have an account? Login button with force parameter found as expected.\n";
    } else {
        echo "Already have an account? Login button with force parameter not found. This is unexpected.\n";
    }
} else {
    echo "Registration form not found. This is unexpected.\n";
    
    if ($force_register_response['redirect']) {
        echo "Redirected to: " . $force_register_response['redirect'] . " (Unexpected behavior)\n";
    }
}

echo "\nTest completed.\n";
?>
