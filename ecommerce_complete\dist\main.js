/**
 * Euro Spice E-commerce - Main JavaScript
 * 
 * This file contains client-side functionality for the Euro Spice e-commerce system.
 */

document.addEventListener('DOMContentLoaded', function () {
    // Enable Bootstrap dropdowns
    var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
    var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
        return new bootstrap.Dropdown(dropdownToggleEl);
    });

    // Add to cart functionality
    const addToCartForms = document.querySelectorAll('form[name="add_to_cart_form"]');

    addToCartForms.forEach(form => {
        form.addEventListener('submit', function (e) {
            // If user is not logged in, redirect to login page
            const isLoggedIn = document.body.dataset.loggedIn === 'true';

            if (!isLoggedIn) {
                e.preventDefault();
                window.location.href = 'pages/account/login.php';
                return false;
            }

            // Show loading indicator or animation if needed
            const submitButton = this.querySelector('input[type="submit"]');
            submitButton.value = 'Adding...';
            submitButton.disabled = true;

            // Form will submit normally, no need to prevent default
        });
    });

    // Quantity input validation
    const quantityInputs = document.querySelectorAll('input[type="number"][name="quantity"]');

    quantityInputs.forEach(input => {
        input.addEventListener('change', function () {
            const max = parseInt(this.getAttribute('max')) || 100;
            const min = parseInt(this.getAttribute('min')) || 1;
            const value = parseInt(this.value) || 0;

            if (value > max) {
                this.value = max;
                showToast('Maximum quantity is ' + max);
            }

            if (value < min) {
                this.value = min;
                showToast('Minimum quantity is ' + min);
            }
        });
    });

    // Search form validation
    const searchForm = document.querySelector('form[method="GET"]');

    if (searchForm) {
        searchForm.addEventListener('submit', function (e) {
            const searchInput = this.querySelector('input[name="search"]');

            if (!searchInput.value.trim()) {
                e.preventDefault();
                showToast('Please enter a search term');
                return false;
            }
        });
    }

    // Toast notification function
    function showToast(message) {
        // Check if toast container exists, if not create it
        let toastContainer = document.getElementById('toast-container');

        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }

        // Create toast element
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <strong class="me-auto">Euro Spice</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // Initialize and show the toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
        toast.show();

        // Remove toast after it's hidden
        toastElement.addEventListener('hidden.bs.toast', function () {
            toastElement.remove();
        });
    }

    // Checkout form validation
    const checkoutForm = document.querySelector('form[action*="checkout.php"]');

    if (checkoutForm) {
        checkoutForm.addEventListener('submit', function (e) {
            const addressInput = this.querySelector('textarea[name="address"]');
            const phoneInput = this.querySelector('input[name="phone"]');

            let isValid = true;

            if (!addressInput.value.trim()) {
                isValid = false;
                addressInput.classList.add('is-invalid');
            } else {
                addressInput.classList.remove('is-invalid');
            }

            if (!phoneInput.value.trim()) {
                isValid = false;
                phoneInput.classList.add('is-invalid');
            } else {
                phoneInput.classList.remove('is-invalid');
            }

            if (!isValid) {
                e.preventDefault();
                showToast('Please fill in all required fields');
                return false;
            }
        });
    }

    // Product image preview
    const productImages = document.querySelectorAll('.product-img');

    productImages.forEach(img => {
        img.addEventListener('click', function () {
            const imgSrc = this.getAttribute('src');
            const imgAlt = this.getAttribute('alt');

            if (imgSrc && !imgSrc.includes('placeholder')) {
                // Create modal for image preview
                const modalHtml = `
                    <div class="modal fade" id="imagePreviewModal" tabindex="-1" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">${imgAlt || 'Product Image'}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body text-center">
                                    <img src="${imgSrc}" class="img-fluid" alt="${imgAlt || 'Product Image'}">
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                document.body.insertAdjacentHTML('beforeend', modalHtml);

                const modal = new bootstrap.Modal(document.getElementById('imagePreviewModal'));
                modal.show();

                // Remove modal from DOM after it's hidden
                document.getElementById('imagePreviewModal').addEventListener('hidden.bs.modal', function () {
                    this.remove();
                });
            }
        });
    });
});
